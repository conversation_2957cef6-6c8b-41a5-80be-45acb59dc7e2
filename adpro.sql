/*
 Navicat Premium Data Transfer

 Source Server         : adpro
 Source Server Type    : MySQL
 Source Server Version : 50742 (5.7.42-log)
 Source Host           : rm-bp1xqhp60et1lx5z92o.mysql.rds.aliyuncs.com:3306
 Source Schema         : adpro

 Target Server Type    : MySQL
 Target Server Version : 50742 (5.7.42-log)
 File Encoding         : 65001

 Date: 12/02/2025 20:28:17
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ad_agents
-- ----------------------------
DROP TABLE IF EXISTS `ad_agents`;
CREATE TABLE `ad_agents` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '代理编号',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '代理名称',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `audit_status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '审核状态：pending-审核中,approved-已通过,rejected-已拒绝',
  `cooperation_status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'not_started' COMMENT '合作状态：not_started-未开始,active-合作中,terminated-已终止',
  `company_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '公司名称',
  `company_address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '公司地址',
  `contact_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系电话',
  `reject_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拒绝原因',
  `remarks` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '创建人ID',
  `updated_by` bigint(20) unsigned DEFAULT NULL COMMENT '更新人ID',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `instance_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '审批实例ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ad_agents_code_unique` (`code`) USING BTREE,
  KEY `ad_agents_audit_status_cooperation_status_index` (`audit_status`,`cooperation_status`) USING BTREE,
  KEY `ad_agents_user_id_index` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_media
-- ----------------------------
DROP TABLE IF EXISTS `ad_media`;
CREATE TABLE `ad_media` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ad_agent_id` bigint(20) unsigned DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `audit_status` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `cooperation_status` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT 'not_started',
  `cooperation_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT 'traffic',
  `account` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_login_at` longtext COLLATE utf8mb4_unicode_ci,
  `balance` double DEFAULT '0',
  `types` json DEFAULT NULL,
  `industry` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `custom_industry` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `daily_activity` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `transaction_volume` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `region_codes` json DEFAULT NULL,
  `company_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reject_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_by` bigint(20) unsigned DEFAULT NULL,
  `updated_by` bigint(20) unsigned DEFAULT NULL,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `instance_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '',
  `platform_config` json DEFAULT NULL COMMENT '平台配置信息',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ad_media_name_unique` (`name`) USING BTREE,
  UNIQUE KEY `ad_media_code_unique` (`code`) USING BTREE,
  UNIQUE KEY `code` (`code`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `idx_ad_media_code` (`code`),
  UNIQUE KEY `idx_ad_media_name` (`name`),
  KEY `ad_media_audit_status_cooperation_status_index` (`audit_status`,`cooperation_status`) USING BTREE,
  KEY `ad_media_ad_agent_id_index` (`ad_agent_id`) USING BTREE,
  KEY `ad_media_user_id_index` (`user_id`) USING BTREE,
  KEY `idx_ad_media_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=79 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_media_balance_logs
-- ----------------------------
DROP TABLE IF EXISTS `ad_media_balance_logs`;
CREATE TABLE `ad_media_balance_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `media_id` bigint(20) unsigned NOT NULL COMMENT '媒体ID',
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '变更类型:settle-结算',
  `settle_month` date DEFAULT NULL COMMENT '结算月份',
  `commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '佣金金额',
  `subsidy` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '补贴金额',
  `amount` decimal(10,2) NOT NULL COMMENT '变更金额',
  `before_balance` decimal(10,2) NOT NULL COMMENT '变更前余额',
  `after_balance` decimal(10,2) NOT NULL COMMENT '变更后余额',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '操作人ID',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ad_media_balance_logs_media_id_settle_month_index` (`media_id`,`settle_month`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_media_monthly_levels
-- ----------------------------
DROP TABLE IF EXISTS `ad_media_monthly_levels`;
CREATE TABLE `ad_media_monthly_levels` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `media_id` bigint(20) unsigned NOT NULL COMMENT '媒体ID',
  `month` date NOT NULL COMMENT '统计月份',
  `orders` int(11) NOT NULL DEFAULT '0' COMMENT '月订单数',
  `daily_orders` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '日均订单数',
  `level` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '等级',
  `subsidy_rate` decimal(5,2) NOT NULL COMMENT '补贴比例',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ad_media_monthly_levels_media_id_month_unique` (`media_id`,`month`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_media_subsidy_rules
-- ----------------------------
DROP TABLE IF EXISTS `ad_media_subsidy_rules`;
CREATE TABLE `ad_media_subsidy_rules` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `level` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '等级:A,B,C,D,E',
  `media_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '媒体类型',
  `cooperation_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '合作类型',
  `orders_from` int(11) NOT NULL COMMENT '订单数起始值',
  `orders_to` int(11) NOT NULL COMMENT '订单数结束值',
  `subsidy_rate` decimal(5,2) NOT NULL COMMENT '补贴比例',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_media_withdraws
-- ----------------------------
DROP TABLE IF EXISTS `ad_media_withdraws`;
CREATE TABLE `ad_media_withdraws` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `media_id` bigint(20) unsigned NOT NULL COMMENT '媒体ID',
  `amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `tax_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '税费金额',
  `actual_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际打款金额',
  `withdraw_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提现方式:alipay-支付宝,bank-对公转账',
  `account_info` json NOT NULL COMMENT '账户信息',
  `invoice_file` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发票文件',
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '状态:pending-待审核,approved-已审核,rejected-已拒绝,paid-已打款',
  `audit_time` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `audit_user_id` bigint(20) unsigned DEFAULT NULL COMMENT '审核人ID',
  `pay_time` timestamp NULL DEFAULT NULL COMMENT '打款时间',
  `pay_user_id` bigint(20) unsigned DEFAULT NULL COMMENT '打款操作人ID',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ad_media_withdraws_media_id_status_index` (`media_id`,`status`),
  KEY `ad_media_withdraws_status_created_at_index` (`status`,`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_orders
-- ----------------------------
DROP TABLE IF EXISTS `ad_orders`;
CREATE TABLE `ad_orders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `ad_media_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒体ID',
  `ad_media_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '媒体名称',
  `ad_slot_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '广告位ID',
  `ad_plan_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '计划ID',
  `bd_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒介ID',
  `supplier_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '供应商ID',
  `order_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单类型',
  `sub_order_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单类型',
  `order_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '订单状态',
  `parent_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '父订单ID',
  `sub_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单ID',
  `item_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品ID',
  `item_title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品标题',
  `item_cover` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品封面',
  `item_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '商品价格',
  `item_num` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '商品数量',
  `order_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '订单金额',
  `pay_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实付金额',
  `rate` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '佣金比例50%即0.5',
  `ori_pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '预估佣金',
  `ori_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '结算佣金',
  `pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际预估佣金',
  `commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际结算佣金',
  `service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '服务费',
  `service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '服务费比例',
  `third_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '三方服务费',
  `third_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '三方服务费比例',
  `activity_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴',
  `activity_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴服务费',
  `activity_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '活动补贴服务费比例',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `receive_time` datetime DEFAULT NULL COMMENT '收货时间',
  `union_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联盟ID',
  `pid` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'PID',
  `adzone_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '推广位名称',
  `relation_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'RID',
  `category` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '类目',
  `city` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '城市',
  `activity_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '活动类型，BRAND、CONSUMPTION',
  `attr_order_desc` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否归因',
  `leak` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否漏单',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `pre_subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '预估补贴金额',
  `subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '结算补贴金额',
  `settle_time` timestamp NULL DEFAULT NULL COMMENT '结算时间',
  `settle_log_id` bigint(20) unsigned DEFAULT NULL COMMENT '结算记录ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cps_orders_unique_index` (`supplier_id`,`order_type`,`parent_order_id`,`sub_order_id`,`item_id`) USING BTREE,
  KEY `idx_media_plan` (`ad_media_id`,`ad_plan_id`) USING BTREE,
  KEY `idx_bd_id` (`bd_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_pay_time` (`receive_time`) USING BTREE,
  KEY `idx_pid` (`pid`) USING BTREE,
  KEY `ad_orders_settle_time_index` (`settle_time`),
  KEY `ad_orders_settle_log_id_index` (`settle_log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='广告订单表';

-- ----------------------------
-- Table structure for ad_orders_202406
-- ----------------------------
DROP TABLE IF EXISTS `ad_orders_202406`;
CREATE TABLE `ad_orders_202406` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `ad_media_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒体ID',
  `ad_media_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '媒体名称',
  `ad_slot_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '广告位ID',
  `ad_plan_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '计划ID',
  `bd_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒介ID',
  `supplier_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '供应商ID',
  `order_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单类型',
  `sub_order_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单类型',
  `order_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '订单状态',
  `parent_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '父订单ID',
  `sub_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单ID',
  `item_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品ID',
  `item_title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品标题',
  `item_cover` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品封面',
  `item_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '商品价格',
  `item_num` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '商品数量',
  `order_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '订单金额',
  `pay_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实付金额',
  `rate` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '佣金比例50%即0.5',
  `ori_pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '预估佣金',
  `ori_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '结算佣金',
  `pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际预估佣金',
  `commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际结算佣金',
  `service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '服务费',
  `service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '服务费比例',
  `third_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '三方服务费',
  `third_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '三方服务费比例',
  `activity_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴',
  `activity_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴服务费',
  `activity_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '活动补贴服务费比例',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `receive_time` datetime DEFAULT NULL COMMENT '收货时间',
  `union_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联盟ID',
  `pid` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'PID',
  `adzone_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '推广位名称',
  `relation_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'RID',
  `category` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '类目',
  `city` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '城市',
  `activity_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '活动类型，BRAND、CONSUMPTION',
  `attr_order_desc` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否归因',
  `leak` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否漏单',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `pre_subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '预估补贴金额',
  `subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '结算补贴金额',
  `settle_time` timestamp NULL DEFAULT NULL COMMENT '结算时间',
  `settle_log_id` bigint(20) unsigned DEFAULT NULL COMMENT '结算记录ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cps_orders_unique_index` (`supplier_id`,`order_type`,`parent_order_id`,`sub_order_id`,`item_id`) USING BTREE,
  KEY `idx_media_plan` (`ad_media_id`,`ad_plan_id`) USING BTREE,
  KEY `idx_bd_id` (`bd_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_pay_time` (`receive_time`) USING BTREE,
  KEY `idx_pid` (`pid`) USING BTREE,
  KEY `ad_orders_202406_settle_time_index` (`settle_time`),
  KEY `ad_orders_202406_settle_log_id_index` (`settle_log_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='广告订单表';

-- ----------------------------
-- Table structure for ad_orders_202407
-- ----------------------------
DROP TABLE IF EXISTS `ad_orders_202407`;
CREATE TABLE `ad_orders_202407` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `ad_media_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒体ID',
  `ad_media_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '媒体名称',
  `ad_slot_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '广告位ID',
  `ad_plan_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '计划ID',
  `bd_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒介ID',
  `supplier_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '供应商ID',
  `order_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单类型',
  `sub_order_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单类型',
  `order_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '订单状态',
  `parent_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '父订单ID',
  `sub_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单ID',
  `item_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品ID',
  `item_title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品标题',
  `item_cover` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品封面',
  `item_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '商品价格',
  `item_num` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '商品数量',
  `order_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '订单金额',
  `pay_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实付金额',
  `rate` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '佣金比例50%即0.5',
  `ori_pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '预估佣金',
  `ori_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '结算佣金',
  `pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际预估佣金',
  `commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际结算佣金',
  `service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '服务费',
  `service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '服务费比例',
  `third_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '三方服务费',
  `third_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '三方服务费比例',
  `activity_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴',
  `activity_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴服务费',
  `activity_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '活动补贴服务费比例',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `receive_time` datetime DEFAULT NULL COMMENT '收货时间',
  `union_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联盟ID',
  `pid` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'PID',
  `adzone_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '推广位名称',
  `relation_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'RID',
  `category` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '类目',
  `city` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '城市',
  `activity_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '活动类型，BRAND、CONSUMPTION',
  `attr_order_desc` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否归因',
  `leak` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否漏单',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `pre_subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '预估补贴金额',
  `subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '结算补贴金额',
  `settle_time` timestamp NULL DEFAULT NULL COMMENT '结算时间',
  `settle_log_id` bigint(20) unsigned DEFAULT NULL COMMENT '结算记录ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cps_orders_unique_index` (`supplier_id`,`order_type`,`parent_order_id`,`sub_order_id`,`item_id`) USING BTREE,
  KEY `idx_media_plan` (`ad_media_id`,`ad_plan_id`) USING BTREE,
  KEY `idx_bd_id` (`bd_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_pay_time` (`receive_time`) USING BTREE,
  KEY `idx_pid` (`pid`) USING BTREE,
  KEY `ad_orders_202407_settle_time_index` (`settle_time`),
  KEY `ad_orders_202407_settle_log_id_index` (`settle_log_id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='广告订单表';

-- ----------------------------
-- Table structure for ad_orders_202408
-- ----------------------------
DROP TABLE IF EXISTS `ad_orders_202408`;
CREATE TABLE `ad_orders_202408` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `ad_media_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒体ID',
  `ad_media_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '媒体名称',
  `ad_slot_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '广告位ID',
  `ad_plan_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '计划ID',
  `bd_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒介ID',
  `supplier_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '供应商ID',
  `order_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单类型',
  `sub_order_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单类型',
  `order_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '订单状态',
  `parent_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '父订单ID',
  `sub_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单ID',
  `item_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品ID',
  `item_title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品标题',
  `item_cover` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品封面',
  `item_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '商品价格',
  `item_num` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '商品数量',
  `order_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '订单金额',
  `pay_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实付金额',
  `rate` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '佣金比例50%即0.5',
  `ori_pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '预估佣金',
  `ori_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '结算佣金',
  `pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际预估佣金',
  `commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际结算佣金',
  `service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '服务费',
  `service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '服务费比例',
  `third_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '三方服务费',
  `third_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '三方服务费比例',
  `activity_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴',
  `activity_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴服务费',
  `activity_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '活动补贴服务费比例',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `receive_time` datetime DEFAULT NULL COMMENT '收货时间',
  `union_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联盟ID',
  `pid` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'PID',
  `adzone_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '推广位名称',
  `relation_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'RID',
  `category` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '类目',
  `city` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '城市',
  `activity_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '活动类型，BRAND、CONSUMPTION',
  `attr_order_desc` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否归因',
  `leak` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否漏单',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `pre_subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '预估补贴金额',
  `subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '结算补贴金额',
  `settle_time` timestamp NULL DEFAULT NULL COMMENT '结算时间',
  `settle_log_id` bigint(20) unsigned DEFAULT NULL COMMENT '结算记录ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cps_orders_unique_index` (`supplier_id`,`order_type`,`parent_order_id`,`sub_order_id`,`item_id`) USING BTREE,
  KEY `idx_media_plan` (`ad_media_id`,`ad_plan_id`) USING BTREE,
  KEY `idx_bd_id` (`bd_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_pay_time` (`receive_time`) USING BTREE,
  KEY `idx_pid` (`pid`) USING BTREE,
  KEY `ad_orders_202408_settle_time_index` (`settle_time`),
  KEY `ad_orders_202408_settle_log_id_index` (`settle_log_id`)
) ENGINE=InnoDB AUTO_INCREMENT=85 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='广告订单表';

-- ----------------------------
-- Table structure for ad_orders_202409
-- ----------------------------
DROP TABLE IF EXISTS `ad_orders_202409`;
CREATE TABLE `ad_orders_202409` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `ad_media_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒体ID',
  `ad_media_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '媒体名称',
  `ad_slot_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '广告位ID',
  `ad_plan_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '计划ID',
  `bd_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒介ID',
  `supplier_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '供应商ID',
  `order_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单类型',
  `sub_order_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单类型',
  `order_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '订单状态',
  `parent_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '父订单ID',
  `sub_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单ID',
  `item_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品ID',
  `item_title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品标题',
  `item_cover` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品封面',
  `item_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '商品价格',
  `item_num` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '商品数量',
  `order_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '订单金额',
  `pay_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实付金额',
  `rate` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '佣金比例50%即0.5',
  `ori_pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '预估佣金',
  `ori_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '结算佣金',
  `pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际预估佣金',
  `commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际结算佣金',
  `service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '服务费',
  `service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '服务费比例',
  `third_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '三方服务费',
  `third_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '三方服务费比例',
  `activity_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴',
  `activity_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴服务费',
  `activity_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '活动补贴服务费比例',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `receive_time` datetime DEFAULT NULL COMMENT '收货时间',
  `union_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联盟ID',
  `pid` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'PID',
  `adzone_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '推广位名称',
  `relation_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'RID',
  `category` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '类目',
  `city` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '城市',
  `activity_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '活动类型，BRAND、CONSUMPTION',
  `attr_order_desc` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否归因',
  `leak` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否漏单',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `pre_subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '预估补贴金额',
  `subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '结算补贴金额',
  `settle_time` timestamp NULL DEFAULT NULL COMMENT '结算时间',
  `settle_log_id` bigint(20) unsigned DEFAULT NULL COMMENT '结算记录ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cps_orders_unique_index` (`supplier_id`,`order_type`,`parent_order_id`,`sub_order_id`,`item_id`) USING BTREE,
  KEY `idx_media_plan` (`ad_media_id`,`ad_plan_id`) USING BTREE,
  KEY `idx_bd_id` (`bd_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_pay_time` (`receive_time`) USING BTREE,
  KEY `idx_pid` (`pid`) USING BTREE,
  KEY `ad_orders_202409_settle_time_index` (`settle_time`),
  KEY `ad_orders_202409_settle_log_id_index` (`settle_log_id`)
) ENGINE=InnoDB AUTO_INCREMENT=332 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='广告订单表';

-- ----------------------------
-- Table structure for ad_orders_202410
-- ----------------------------
DROP TABLE IF EXISTS `ad_orders_202410`;
CREATE TABLE `ad_orders_202410` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `ad_media_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒体ID',
  `ad_media_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '媒体名称',
  `ad_slot_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '广告位ID',
  `ad_plan_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '计划ID',
  `bd_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒介ID',
  `supplier_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '供应商ID',
  `order_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单类型',
  `sub_order_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单类型',
  `order_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '订单状态',
  `parent_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '父订单ID',
  `sub_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单ID',
  `item_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品ID',
  `item_title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品标题',
  `item_cover` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品封面',
  `item_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '商品价格',
  `item_num` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '商品数量',
  `order_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '订单金额',
  `pay_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实付金额',
  `rate` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '佣金比例50%即0.5',
  `ori_pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '预估佣金',
  `ori_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '结算佣金',
  `pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际预估佣金',
  `commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际结算佣金',
  `service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '服务费',
  `service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '服务费比例',
  `third_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '三方服务费',
  `third_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '三方服务费比例',
  `activity_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴',
  `activity_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴服务费',
  `activity_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '活动补贴服务费比例',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `receive_time` datetime DEFAULT NULL COMMENT '收货时间',
  `union_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联盟ID',
  `pid` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'PID',
  `adzone_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '推广位名称',
  `relation_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'RID',
  `category` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '类目',
  `city` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '城市',
  `activity_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '活动类型，BRAND、CONSUMPTION',
  `attr_order_desc` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否归因',
  `leak` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否漏单',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `pre_subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '预估补贴金额',
  `subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '结算补贴金额',
  `settle_time` timestamp NULL DEFAULT NULL COMMENT '结算时间',
  `settle_log_id` bigint(20) unsigned DEFAULT NULL COMMENT '结算记录ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cps_orders_unique_index` (`supplier_id`,`order_type`,`parent_order_id`,`sub_order_id`,`item_id`) USING BTREE,
  KEY `idx_media_plan` (`ad_media_id`,`ad_plan_id`) USING BTREE,
  KEY `idx_bd_id` (`bd_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_pay_time` (`receive_time`) USING BTREE,
  KEY `idx_pid` (`pid`) USING BTREE,
  KEY `ad_orders_202410_settle_time_index` (`settle_time`),
  KEY `ad_orders_202410_settle_log_id_index` (`settle_log_id`)
) ENGINE=InnoDB AUTO_INCREMENT=27850 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='广告订单表';

-- ----------------------------
-- Table structure for ad_orders_202411
-- ----------------------------
DROP TABLE IF EXISTS `ad_orders_202411`;
CREATE TABLE `ad_orders_202411` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `ad_media_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒体ID',
  `ad_media_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '媒体名称',
  `ad_slot_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '广告位ID',
  `ad_plan_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '计划ID',
  `bd_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒介ID',
  `supplier_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '供应商ID',
  `order_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单类型',
  `sub_order_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单类型',
  `order_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '订单状态',
  `parent_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '父订单ID',
  `sub_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单ID',
  `item_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品ID',
  `item_title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品标题',
  `item_cover` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品封面',
  `item_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '商品价格',
  `item_num` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '商品数量',
  `order_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '订单金额',
  `pay_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实付金额',
  `rate` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '佣金比例50%即0.5',
  `ori_pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '预估佣金',
  `ori_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '结算佣金',
  `pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际预估佣金',
  `commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际结算佣金',
  `service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '服务费',
  `service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '服务费比例',
  `third_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '三方服务费',
  `third_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '三方服务费比例',
  `activity_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴',
  `activity_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴服务费',
  `activity_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '活动补贴服务费比例',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `receive_time` datetime DEFAULT NULL COMMENT '收货时间',
  `union_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联盟ID',
  `pid` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'PID',
  `adzone_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '推广位名称',
  `relation_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'RID',
  `category` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '类目',
  `city` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '城市',
  `activity_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '活动类型，BRAND、CONSUMPTION',
  `attr_order_desc` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否归因',
  `leak` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否漏单',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `pre_subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '预估补贴金额',
  `subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '结算补贴金额',
  `settle_time` timestamp NULL DEFAULT NULL COMMENT '结算时间',
  `settle_log_id` bigint(20) unsigned DEFAULT NULL COMMENT '结算记录ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cps_orders_unique_index` (`supplier_id`,`order_type`,`parent_order_id`,`sub_order_id`,`item_id`) USING BTREE,
  KEY `idx_media_plan` (`ad_media_id`,`ad_plan_id`) USING BTREE,
  KEY `idx_bd_id` (`bd_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_pay_time` (`receive_time`) USING BTREE,
  KEY `idx_adplanid_ordertype_createtime_orderstatus` (`ad_plan_id`,`order_type`,`create_time`,`order_status`),
  KEY `idx_adplanid_orderstatus_receivetime` (`ad_plan_id`,`order_status`,`receive_time`) COMMENT 'create by DAS-6a3047c3-4bb7-4ff4-92eb-216f022a187f',
  KEY `idx_adplanid_ordertype_receivetime` (`ad_plan_id`,`order_type`,`receive_time`) COMMENT 'create by DAS-dc6a91c8-f849-46ce-adc2-7676e2e992a3',
  KEY `ad_orders_202411_settle_time_index` (`settle_time`),
  KEY `ad_orders_202411_settle_log_id_index` (`settle_log_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2891086 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='广告订单表';

-- ----------------------------
-- Table structure for ad_orders_202412
-- ----------------------------
DROP TABLE IF EXISTS `ad_orders_202412`;
CREATE TABLE `ad_orders_202412` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `ad_media_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒体ID',
  `ad_media_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '媒体名称',
  `ad_slot_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '广告位ID',
  `ad_plan_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '计划ID',
  `bd_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒介ID',
  `supplier_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '供应商ID',
  `order_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单类型',
  `sub_order_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单类型',
  `order_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '订单状态',
  `parent_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '父订单ID',
  `sub_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单ID',
  `item_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品ID',
  `item_title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品标题',
  `item_cover` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品封面',
  `item_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '商品价格',
  `item_num` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '商品数量',
  `order_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '订单金额',
  `pay_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实付金额',
  `rate` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '佣金比例50%即0.5',
  `ori_pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '预估佣金',
  `ori_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '结算佣金',
  `pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际预估佣金',
  `commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际结算佣金',
  `service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '服务费',
  `service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '服务费比例',
  `third_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '三方服务费',
  `third_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '三方服务费比例',
  `activity_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴',
  `activity_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴服务费',
  `activity_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '活动补贴服务费比例',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `receive_time` datetime DEFAULT NULL COMMENT '收货时间',
  `union_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联盟ID',
  `pid` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'PID',
  `adzone_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '推广位名称',
  `relation_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'RID',
  `category` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '类目',
  `city` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '城市',
  `activity_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '活动类型，BRAND、CONSUMPTION',
  `attr_order_desc` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否归因',
  `leak` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否漏单',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `pre_subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '预估补贴金额',
  `subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '结算补贴金额',
  `settle_time` timestamp NULL DEFAULT NULL COMMENT '结算时间',
  `settle_log_id` bigint(20) unsigned DEFAULT NULL COMMENT '结算记录ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cps_orders_unique_index` (`supplier_id`,`order_type`,`parent_order_id`,`sub_order_id`,`item_id`) USING BTREE,
  KEY `idx_media_plan` (`ad_media_id`,`ad_plan_id`) USING BTREE,
  KEY `idx_bd_id` (`bd_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_pay_time` (`receive_time`) USING BTREE,
  KEY `idx_pid` (`pid`) USING BTREE,
  KEY `idx_adplanid_ordertype_createtime_orderstatus` (`ad_plan_id`,`order_type`,`create_time`,`order_status`),
  KEY `idx_adplanid_orderstatus_receivetime_ordertype` (`ad_plan_id`,`order_status`,`receive_time`,`order_type`) COMMENT 'create by DAS-bd5ce6bd-c669-4596-b72b-bcfe78163c19',
  KEY `ad_orders_202412_settle_time_index` (`settle_time`),
  KEY `ad_orders_202412_settle_log_id_index` (`settle_log_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3768887 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='广告订单表';

-- ----------------------------
-- Table structure for ad_orders_202501
-- ----------------------------
DROP TABLE IF EXISTS `ad_orders_202501`;
CREATE TABLE `ad_orders_202501` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `ad_media_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒体ID',
  `ad_media_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '媒体名称',
  `ad_slot_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '广告位ID',
  `ad_plan_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '计划ID',
  `bd_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒介ID',
  `supplier_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '供应商ID',
  `order_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单类型',
  `sub_order_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单类型',
  `order_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '订单状态',
  `parent_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '父订单ID',
  `sub_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单ID',
  `item_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品ID',
  `item_title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品标题',
  `item_cover` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品封面',
  `item_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '商品价格',
  `item_num` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '商品数量',
  `order_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '订单金额',
  `pay_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实付金额',
  `rate` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '佣金比例50%即0.5',
  `ori_pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '预估佣金',
  `ori_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '结算佣金',
  `pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际预估佣金',
  `commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际结算佣金',
  `service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '服务费',
  `service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '服务费比例',
  `third_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '三方服务费',
  `third_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '三方服务费比例',
  `activity_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴',
  `activity_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴服务费',
  `activity_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '活动补贴服务费比例',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `receive_time` datetime DEFAULT NULL COMMENT '收货时间',
  `union_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联盟ID',
  `pid` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'PID',
  `adzone_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '推广位名称',
  `relation_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'RID',
  `category` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '类目',
  `city` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '城市',
  `activity_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '活动类型，BRAND、CONSUMPTION',
  `attr_order_desc` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否归因',
  `leak` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否漏单',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `pre_subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '预估补贴金额',
  `subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '结算补贴金额',
  `settle_time` timestamp NULL DEFAULT NULL COMMENT '结算时间',
  `settle_log_id` bigint(20) unsigned DEFAULT NULL COMMENT '结算记录ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cps_orders_unique_index` (`supplier_id`,`order_type`,`parent_order_id`,`sub_order_id`,`item_id`) USING BTREE,
  KEY `idx_media_plan` (`ad_media_id`,`ad_plan_id`) USING BTREE,
  KEY `idx_bd_id` (`bd_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_pay_time` (`receive_time`) USING BTREE,
  KEY `idx_pid` (`pid`) USING BTREE,
  KEY `ad_orders_202501_settle_time_index` (`settle_time`),
  KEY `ad_orders_202501_settle_log_id_index` (`settle_log_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3419507 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='广告订单表';

-- ----------------------------
-- Table structure for ad_orders_202502
-- ----------------------------
DROP TABLE IF EXISTS `ad_orders_202502`;
CREATE TABLE `ad_orders_202502` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `ad_media_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒体ID',
  `ad_media_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '媒体名称',
  `ad_slot_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '广告位ID',
  `ad_plan_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '计划ID',
  `bd_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '媒介ID',
  `supplier_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '供应商ID',
  `order_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单类型',
  `sub_order_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单类型',
  `order_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '订单状态',
  `parent_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '父订单ID',
  `sub_order_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '子订单ID',
  `item_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品ID',
  `item_title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品标题',
  `item_cover` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品封面',
  `item_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '商品价格',
  `item_num` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '商品数量',
  `order_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '订单金额',
  `pay_price` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实付金额',
  `rate` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '佣金比例50%即0.5',
  `ori_pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '预估佣金',
  `ori_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '结算佣金',
  `pre_commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际预估佣金',
  `commission` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '实际结算佣金',
  `service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '服务费',
  `service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '服务费比例',
  `third_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '三方服务费',
  `third_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '三方服务费比例',
  `activity_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴',
  `activity_service_fee` decimal(11,3) NOT NULL DEFAULT '0.000' COMMENT '活动补贴服务费',
  `activity_service_ratio` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '活动补贴服务费比例',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `receive_time` datetime DEFAULT NULL COMMENT '收货时间',
  `union_id` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联盟ID',
  `pid` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'PID',
  `adzone_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '推广位名称',
  `relation_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'RID',
  `category` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '类目',
  `city` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '城市',
  `activity_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '活动类型，BRAND、CONSUMPTION',
  `attr_order_desc` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否归因',
  `leak` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否漏单',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `pre_subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '预估补贴金额',
  `subsidy_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '结算补贴金额',
  `settle_time` timestamp NULL DEFAULT NULL COMMENT '结算时间',
  `settle_log_id` bigint(20) unsigned DEFAULT NULL COMMENT '结算记录ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `cps_orders_unique_index` (`supplier_id`,`order_type`,`parent_order_id`,`sub_order_id`,`item_id`) USING BTREE,
  KEY `idx_media_plan` (`ad_media_id`,`ad_plan_id`) USING BTREE,
  KEY `idx_bd_id` (`bd_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_pay_time` (`receive_time`) USING BTREE,
  KEY `idx_pid` (`pid`) USING BTREE,
  KEY `ad_orders_settle_time_index` (`settle_time`),
  KEY `ad_orders_settle_log_id_index` (`settle_log_id`)
) ENGINE=InnoDB AUTO_INCREMENT=909499 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='广告订单表';

-- ----------------------------
-- Table structure for ad_products
-- ----------------------------
DROP TABLE IF EXISTS `ad_products`;
CREATE TABLE `ad_products` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品编号',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '广告产品名称',
  `image` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '广告产品图片',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '广告产品描述',
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '状态：active-启用,inactive-停用',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '创建人ID',
  `updated_by` bigint(20) unsigned DEFAULT NULL COMMENT '更新人ID',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ad_products_code_unique` (`code`) USING BTREE,
  KEY `ad_products_status_index` (`status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_products_copy1
-- ----------------------------
DROP TABLE IF EXISTS `ad_products_copy1`;
CREATE TABLE `ad_products_copy1` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '广告产品名称',
  `image` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '广告产品图片',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '广告产品描述',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_slot_audit_logs
-- ----------------------------
DROP TABLE IF EXISTS `ad_slot_audit_logs`;
CREATE TABLE `ad_slot_audit_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `slot_id` bigint(20) unsigned NOT NULL COMMENT '广告位ID',
  `event` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '事件：create-创建审核,update-变更审核',
  `submitter_id` bigint(20) unsigned NOT NULL COMMENT '发起人ID',
  `audit_status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '审核状态：pending-待审核,approved-已通过,rejected-已拒绝',
  `auditor_id` bigint(20) unsigned DEFAULT NULL COMMENT '审核人ID',
  `audit_at` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `reject_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拒绝理由',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `audit_detail` json DEFAULT NULL COMMENT '审核详情',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ad_slot_audit_logs_slot_id_index` (`slot_id`) USING BTREE,
  KEY `ad_slot_audit_logs_audit_status_created_at_index` (`audit_status`,`created_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_slot_monitor_logs
-- ----------------------------
DROP TABLE IF EXISTS `ad_slot_monitor_logs`;
CREATE TABLE `ad_slot_monitor_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `slot_id` bigint(20) unsigned NOT NULL COMMENT '广告位ID',
  `monitor_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '监控类型：traffic-流量监控,quality-质量监控,cost-成本监控',
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '状态：normal-正常,warning-警告,error-异常',
  `monitor_data` json DEFAULT NULL COMMENT '监控数据',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ad_slot_monitor_logs_slot_id_index` (`slot_id`) USING BTREE,
  KEY `ad_slot_monitor_logs_monitor_type_status_index` (`monitor_type`,`status`) USING BTREE,
  KEY `ad_slot_monitor_logs_created_at_index` (`created_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_slot_plan_audit_logs
-- ----------------------------
DROP TABLE IF EXISTS `ad_slot_plan_audit_logs`;
CREATE TABLE `ad_slot_plan_audit_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `plan_id` bigint(20) unsigned NOT NULL COMMENT '计划ID',
  `event` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '事件：create-创建审核,update-变更审核',
  `submitter_id` bigint(20) unsigned NOT NULL COMMENT '发起人ID',
  `audit_status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '审核状态：pending-待审核,approved-已通过,rejected-已拒绝',
  `auditor_id` bigint(20) unsigned DEFAULT NULL COMMENT '审核人ID',
  `audit_at` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `reject_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拒绝理由',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `audit_detail` json DEFAULT NULL COMMENT '审核详情',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ad_slot_plan_audit_logs_plan_id_event_index` (`plan_id`,`event`) USING BTREE,
  KEY `ad_slot_plan_audit_logs_audit_status_created_at_index` (`audit_status`,`created_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_slot_plan_change_requests
-- ----------------------------
DROP TABLE IF EXISTS `ad_slot_plan_change_requests`;
CREATE TABLE `ad_slot_plan_change_requests` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `plan_id` bigint(20) unsigned NOT NULL COMMENT '投放计划ID',
  `applicant_id` bigint(20) unsigned NOT NULL COMMENT '申请人ID',
  `applicant_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '申请人姓名',
  `old_price` decimal(10,2) DEFAULT NULL COMMENT '原价格',
  `new_price` decimal(10,2) DEFAULT NULL COMMENT '新价格',
  `old_start_date` datetime DEFAULT NULL COMMENT '原开始时间',
  `new_start_date` datetime DEFAULT NULL COMMENT '新开始时间',
  `old_end_date` datetime DEFAULT NULL COMMENT '原结束时间',
  `new_end_date` datetime DEFAULT NULL COMMENT '新结束时间',
  `reason` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '申请原因',
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '审核状态：pending-待审核，approved-已通过，rejected-已拒绝',
  `auditor_id` bigint(20) unsigned DEFAULT NULL COMMENT '审核人ID',
  `auditor_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审核人姓名',
  `audit_at` datetime DEFAULT NULL COMMENT '审核时间',
  `reject_reason` text COLLATE utf8mb4_unicode_ci COMMENT '拒绝原因',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ad_slot_plan_change_requests_plan_id_index` (`plan_id`),
  KEY `ad_slot_plan_change_requests_applicant_id_index` (`applicant_id`),
  KEY `ad_slot_plan_change_requests_status_index` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_slot_plan_costs
-- ----------------------------
DROP TABLE IF EXISTS `ad_slot_plan_costs`;
CREATE TABLE `ad_slot_plan_costs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `plan_id` bigint(20) unsigned NOT NULL COMMENT '投放计划ID',
  `date` date NOT NULL COMMENT '统计日期',
  `media_id` bigint(20) unsigned NOT NULL COMMENT '媒体ID',
  `ad_slot_id` bigint(20) unsigned NOT NULL COMMENT '广告位ID',
  `ad_product_id` bigint(20) unsigned NOT NULL COMMENT '产品ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '媒介ID',
  `clicks` int(11) NOT NULL DEFAULT '0' COMMENT '点击量',
  `cost` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '成本',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `audit_status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '审核状态:pending=待审核,approved=已通过,rejected=已拒绝',
  `audit_by` bigint(20) unsigned DEFAULT NULL COMMENT '审核人',
  `audit_at` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `reject_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拒绝原因',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '创建人',
  `updated_by` bigint(20) unsigned DEFAULT NULL COMMENT '更新人',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ad_slot_plan_costs_plan_id_date_unique` (`plan_id`,`date`) USING BTREE,
  KEY `ad_slot_plan_costs_date_index` (`date`) USING BTREE,
  KEY `ad_slot_plan_costs_plan_id_index` (`plan_id`) USING BTREE,
  KEY `ad_slot_plan_costs_media_id_index` (`media_id`) USING BTREE,
  KEY `ad_slot_plan_costs_ad_slot_id_index` (`ad_slot_id`) USING BTREE,
  KEY `ad_slot_plan_costs_user_id_index` (`user_id`) USING BTREE,
  KEY `ad_slot_plan_costs_audit_status_index` (`audit_status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5243 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_slot_plan_costs_copy1
-- ----------------------------
DROP TABLE IF EXISTS `ad_slot_plan_costs_copy1`;
CREATE TABLE `ad_slot_plan_costs_copy1` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `plan_id` bigint(20) unsigned NOT NULL COMMENT '投放计划ID',
  `date` date NOT NULL COMMENT '统计日期',
  `media_id` bigint(20) unsigned NOT NULL COMMENT '媒体ID',
  `ad_slot_id` bigint(20) unsigned NOT NULL COMMENT '广告位ID',
  `ad_product_id` bigint(20) unsigned NOT NULL COMMENT '产品ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '媒介ID',
  `clicks` int(11) NOT NULL DEFAULT '0' COMMENT '点击量',
  `cost` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '成本',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `audit_status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '审核状态:pending=待审核,approved=已通过,rejected=已拒绝',
  `audit_by` bigint(20) unsigned DEFAULT NULL COMMENT '审核人',
  `audit_at` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `reject_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拒绝原因',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '创建人',
  `updated_by` bigint(20) unsigned DEFAULT NULL COMMENT '更新人',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ad_slot_plan_costs_plan_id_date_unique` (`plan_id`,`date`) USING BTREE,
  KEY `ad_slot_plan_costs_date_index` (`date`) USING BTREE,
  KEY `ad_slot_plan_costs_plan_id_index` (`plan_id`) USING BTREE,
  KEY `ad_slot_plan_costs_media_id_index` (`media_id`) USING BTREE,
  KEY `ad_slot_plan_costs_ad_slot_id_index` (`ad_slot_id`) USING BTREE,
  KEY `ad_slot_plan_costs_user_id_index` (`user_id`) USING BTREE,
  KEY `ad_slot_plan_costs_audit_status_index` (`audit_status`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1372 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_slot_plan_daily_category_stats
-- ----------------------------
DROP TABLE IF EXISTS `ad_slot_plan_daily_category_stats`;
CREATE TABLE `ad_slot_plan_daily_category_stats` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `daily_stat_id` bigint(20) unsigned NOT NULL,
  `category` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品分类',
  `bd_orders` int(11) NOT NULL DEFAULT '0' COMMENT '媒介订单数',
  `admin_orders` int(11) NOT NULL DEFAULT '0' COMMENT '管理员订单数',
  `bd_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介收入',
  `bd_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介激励费用',
  `admin_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '管理员收入',
  `admin_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际激励费用',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `bd_settle_orders` int(11) NOT NULL DEFAULT '0' COMMENT '媒介结算订单数',
  `admin_settle_orders` int(11) NOT NULL DEFAULT '0' COMMENT '实际结算订单数',
  `bd_settle_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介结算收入',
  `admin_settle_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际结算收入',
  `bd_settle_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介结算激励',
  `admin_settle_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际结算激励',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ad_slot_plan_daily_category_stats_daily_stat_id_category_index` (`daily_stat_id`,`category`) USING BTREE,
  CONSTRAINT `ad_slot_plan_daily_category_stats_daily_stat_id_foreign` FOREIGN KEY (`daily_stat_id`) REFERENCES `ad_slot_plan_daily_stats` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=54643248 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_slot_plan_daily_city_stats
-- ----------------------------
DROP TABLE IF EXISTS `ad_slot_plan_daily_city_stats`;
CREATE TABLE `ad_slot_plan_daily_city_stats` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `daily_stat_id` bigint(20) unsigned NOT NULL,
  `city` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '城市名称',
  `bd_orders` int(11) NOT NULL DEFAULT '0' COMMENT '媒介订单数',
  `admin_orders` int(11) NOT NULL DEFAULT '0' COMMENT '管理员订单数',
  `bd_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介收入',
  `bd_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介激励费用',
  `admin_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '管理员收入',
  `admin_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际激励费用',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `bd_settle_orders` int(11) NOT NULL DEFAULT '0' COMMENT '媒介结算订单数',
  `admin_settle_orders` int(11) NOT NULL DEFAULT '0' COMMENT '实际结算订单数',
  `bd_settle_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介结算收入',
  `admin_settle_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际结算收入',
  `bd_settle_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介结算激励',
  `admin_settle_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际结算激励',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ad_slot_plan_daily_city_stats_daily_stat_id_city_index` (`daily_stat_id`,`city`) USING BTREE,
  CONSTRAINT `ad_slot_plan_daily_city_stats_daily_stat_id_foreign` FOREIGN KEY (`daily_stat_id`) REFERENCES `ad_slot_plan_daily_stats` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=42308317 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_slot_plan_daily_order_stats
-- ----------------------------
DROP TABLE IF EXISTS `ad_slot_plan_daily_order_stats`;
CREATE TABLE `ad_slot_plan_daily_order_stats` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `daily_stat_id` bigint(20) unsigned NOT NULL,
  `order_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单类型',
  `bd_orders` int(11) NOT NULL DEFAULT '0' COMMENT '媒介订单数',
  `bd_settle_orders` int(11) NOT NULL DEFAULT '0' COMMENT '媒介结算订单数',
  `admin_orders` int(11) NOT NULL DEFAULT '0' COMMENT '管理员订单数',
  `admin_settle_orders` int(11) NOT NULL DEFAULT '0' COMMENT '实际结算订单数',
  `bd_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介收入',
  `bd_settle_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介结算收入',
  `bd_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介激励费用',
  `bd_settle_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介结算激励',
  `admin_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '管理员收入',
  `admin_settle_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际结算收入',
  `admin_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际激励费用',
  `admin_settle_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际结算��励',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_daily_stat_order_type` (`daily_stat_id`,`order_type`),
  KEY `ad_slot_plan_daily_order_stats_daily_stat_id_order_type_index` (`daily_stat_id`,`order_type`) USING BTREE,
  CONSTRAINT `ad_slot_plan_daily_order_stats_daily_stat_id_foreign` FOREIGN KEY (`daily_stat_id`) REFERENCES `ad_slot_plan_daily_stats` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1831635 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_slot_plan_daily_order_stats_backup_20240318
-- ----------------------------
DROP TABLE IF EXISTS `ad_slot_plan_daily_order_stats_backup_20240318`;
CREATE TABLE `ad_slot_plan_daily_order_stats_backup_20240318` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `daily_stat_id` bigint(20) unsigned NOT NULL,
  `order_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单类型',
  `bd_orders` int(11) NOT NULL DEFAULT '0' COMMENT '媒介订单数',
  `bd_settle_orders` int(11) NOT NULL DEFAULT '0' COMMENT '媒介结算订单数',
  `admin_orders` int(11) NOT NULL DEFAULT '0' COMMENT '管理员订单数',
  `admin_settle_orders` int(11) NOT NULL DEFAULT '0' COMMENT '实际结算订单数',
  `bd_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介收入',
  `bd_settle_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介结算收入',
  `bd_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介激励费用',
  `bd_settle_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介结算激励',
  `admin_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '管理员收入',
  `admin_settle_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际结算收入',
  `admin_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际激励费用',
  `admin_settle_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际结算��励',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ad_slot_plan_daily_order_stats_daily_stat_id_order_type_index` (`daily_stat_id`,`order_type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1694287 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_slot_plan_daily_stats
-- ----------------------------
DROP TABLE IF EXISTS `ad_slot_plan_daily_stats`;
CREATE TABLE `ad_slot_plan_daily_stats` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `plan_id` bigint(20) unsigned NOT NULL COMMENT '计划ID',
  `media_id` bigint(20) unsigned NOT NULL COMMENT '媒体ID',
  `ad_slot_id` bigint(20) unsigned NOT NULL COMMENT '广告位ID',
  `ad_product_id` bigint(20) unsigned NOT NULL COMMENT '投放产品ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '媒介ID',
  `date` date NOT NULL COMMENT '数据日期',
  `clicks` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '点击量',
  `unit_price` decimal(10,4) NOT NULL COMMENT '单价',
  `cost` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '成本',
  `bd_orders` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '媒介订单量',
  `bd_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介收入',
  `bd_profit` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介利润',
  `bd_roi` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '媒介ROI',
  `bd_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介激励费用',
  `bd_settle_orders` int(11) NOT NULL DEFAULT '0' COMMENT '媒介结算订单量',
  `bd_settle_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介结算收入',
  `bd_settle_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介结算激励',
  `bd_settle_profit` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '媒介结算利润',
  `bd_settle_roi` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '媒介结算ROI',
  `admin_orders` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '实际订单量',
  `admin_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际收入',
  `admin_profit` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际利润',
  `admin_roi` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '实际ROI',
  `admin_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际激励费用',
  `admin_settle_orders` int(11) NOT NULL DEFAULT '0' COMMENT '实际结算订单量',
  `admin_settle_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际结算收入',
  `admin_settle_activity_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际结算激励',
  `admin_settle_profit` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际结算利润',
  `admin_settle_roi` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '实际结算ROI',
  `order_type_stats` json DEFAULT NULL COMMENT '收益明细',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '创建人ID',
  `updated_by` bigint(20) unsigned DEFAULT NULL COMMENT '更新人ID',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_plan_date` (`plan_id`,`date`) USING BTREE,
  KEY `ad_slot_plan_daily_stats_date_index` (`date`) USING BTREE,
  KEY `ad_slot_plan_daily_stats_media_id_index` (`media_id`) USING BTREE,
  KEY `ad_slot_plan_daily_stats_ad_slot_id_index` (`ad_slot_id`) USING BTREE,
  KEY `ad_slot_plan_daily_stats_ad_product_id_index` (`ad_product_id`) USING BTREE,
  KEY `ad_slot_plan_daily_stats_user_id_index` (`user_id`) USING BTREE,
  KEY `idx_date_user` (`date`,`user_id`) USING BTREE,
  KEY `idx_slot_date` (`ad_slot_id`,`date`) USING BTREE,
  KEY `idx_media_date` (`media_id`,`date`) USING BTREE,
  KEY `idx_product_date` (`ad_product_id`,`date`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15971 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_slot_plan_operation_logs
-- ----------------------------
DROP TABLE IF EXISTS `ad_slot_plan_operation_logs`;
CREATE TABLE `ad_slot_plan_operation_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `plan_id` bigint(20) unsigned NOT NULL COMMENT '投放计划ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '操作人ID',
  `action` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型：create-创建计划,start-开始投放,stop-手动停止投放,auto_stop-自动停止投放,complete-完成投放',
  `old_status` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '原状态',
  `new_status` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '新状态',
  `remark` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `operation_params` json DEFAULT NULL COMMENT '操作参数',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ad_slot_plan_operation_logs_plan_id_created_at_index` (`plan_id`,`created_at`) USING BTREE,
  KEY `ad_slot_plan_operation_logs_user_id_index` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=267 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_slot_plan_price_logs
-- ----------------------------
DROP TABLE IF EXISTS `ad_slot_plan_price_logs`;
CREATE TABLE `ad_slot_plan_price_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `plan_id` bigint(20) unsigned NOT NULL COMMENT '计划ID',
  `old_price` decimal(10,2) NOT NULL COMMENT '变动前价格',
  `new_price` decimal(10,2) NOT NULL COMMENT '当前价格',
  `operator_id` bigint(20) unsigned NOT NULL COMMENT '操作人ID',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '变动原因',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ad_slot_plan_price_logs_plan_id_created_at_index` (`plan_id`,`created_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_slot_plans
-- ----------------------------
DROP TABLE IF EXISTS `ad_slot_plans`;
CREATE TABLE `ad_slot_plans` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `link_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '历史系统关联ID',
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '计划编码',
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'formal' COMMENT '计划类型：test-测试计划,formal-正式计划',
  `ad_slot_id` bigint(20) unsigned NOT NULL COMMENT '广告位ID',
  `media_id` bigint(20) unsigned DEFAULT NULL,
  `ad_product_id` bigint(20) unsigned NOT NULL COMMENT '投放产品ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `audit_status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending' COMMENT '审核状态：pending-待审核,approved-已通过,rejected-已拒绝',
  `delivery_status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'init' COMMENT '投放状态：init-初始状态,configuring-配置中,wait-待开始,running-投放中,stopped-手动停投,stopped_auto-自动停投,completed-已完结',
  `delivery_mode` tinyint(4) NOT NULL DEFAULT '1' COMMENT '投放策略:1=普通投放,2=分日组合,3=分区域组合',
  `start_date` datetime NOT NULL COMMENT '开始时间',
  `end_date` datetime DEFAULT NULL COMMENT '结束时间',
  `is_end_date_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '结束时间是否生效',
  `promotion_link` text COLLATE utf8mb4_unicode_ci COMMENT '推广链接',
  `promotion_params` json DEFAULT NULL COMMENT '推广参数',
  `promotion_qrcode` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '推广二维码',
  `short_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '短链接',
  `reject_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '拒绝原因',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '创建人ID',
  `updated_by` bigint(20) unsigned DEFAULT NULL COMMENT '更新人ID',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `stop_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '停止投放原因',
  `test_result` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '测试结论：success=测试成功，failed=测试失败',
  `test_failed_reason` text COLLATE utf8mb4_unicode_ci COMMENT '测试失败原因',
  `test_result_at` timestamp NULL DEFAULT NULL COMMENT '测试结论时间',
  `test_result_by` bigint(20) unsigned DEFAULT NULL COMMENT '测试结论操作人',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ad_slot_plans_code_unique` (`code`) USING BTREE,
  KEY `ad_slot_plans_ad_slot_id_index` (`ad_slot_id`) USING BTREE,
  KEY `ad_slot_plans_ad_product_id_index` (`ad_product_id`) USING BTREE,
  KEY `ad_slot_plans_user_id_index` (`user_id`) USING BTREE,
  KEY `ad_slot_plans_audit_status_delivery_status_index` (`audit_status`,`delivery_status`) USING BTREE,
  KEY `ad_slot_plans_type_delivery_status_index` (`type`,`delivery_status`) USING BTREE,
  KEY `ad_slot_plans_start_date_index` (`start_date`) USING BTREE,
  KEY `ad_slot_plans_end_date_index` (`end_date`) USING BTREE,
  KEY `ad_slot_plans_link_id_index` (`link_id`) USING BTREE,
  KEY `ad_slot_plans_media_id_foreign` (`media_id`),
  CONSTRAINT `ad_slot_plans_media_id_foreign` FOREIGN KEY (`media_id`) REFERENCES `ad_media` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=151 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_slot_rate_logs
-- ----------------------------
DROP TABLE IF EXISTS `ad_slot_rate_logs`;
CREATE TABLE `ad_slot_rate_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `ad_slot_id` bigint(20) unsigned NOT NULL COMMENT '广告位ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '操作人ID',
  `field` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '修改字段：leak_rate-泄漏率,discount_rate-折扣率',
  `old_value` decimal(5,2) NOT NULL COMMENT '原值',
  `new_value` decimal(5,2) NOT NULL COMMENT '新值',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改原因',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ad_slot_rate_logs_ad_slot_id_index` (`ad_slot_id`) USING BTREE,
  KEY `ad_slot_rate_logs_user_id_index` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for ad_slots
-- ----------------------------
DROP TABLE IF EXISTS `ad_slots`;
CREATE TABLE `ad_slots` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'wechat_h5',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `media_id` bigint(20) unsigned NOT NULL,
  `media_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `screenshot_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `video_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `audit_status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `reject_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `leak_rate` decimal(5,2) NOT NULL DEFAULT '0.00',
  `discount_rate` decimal(5,2) NOT NULL DEFAULT '0.00',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_by` bigint(20) unsigned DEFAULT NULL,
  `updated_by` bigint(20) unsigned DEFAULT NULL,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `ad_slots_code_unique` (`code`) USING BTREE,
  UNIQUE KEY `code` (`code`),
  UNIQUE KEY `idx_ad_slots_code` (`code`),
  KEY `ad_slots_user_id_index` (`user_id`) USING BTREE,
  KEY `ad_slots_media_id_index` (`media_id`) USING BTREE,
  KEY `ad_slots_type_audit_status_index` (`type`,`audit_status`) USING BTREE,
  KEY `idx_ad_slots_user_id` (`user_id`),
  KEY `idx_ad_slots_media_id` (`media_id`),
  KEY `idx_ad_slots_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=98 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for export_tasks
-- ----------------------------
DROP TABLE IF EXISTS `export_tasks`;
CREATE TABLE `export_tasks` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `media_id` bigint(20) unsigned NOT NULL,
  `type` varchar(50) NOT NULL,
  `status` varchar(20) NOT NULL,
  `file_url` varchar(255) DEFAULT NULL,
  `error` varchar(255) DEFAULT NULL,
  `params` text,
  `total_rows` bigint(20) NOT NULL DEFAULT '0',
  `created_by` bigint(20) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_export_tasks_media_id` (`media_id`)
) ENGINE=InnoDB AUTO_INCREMENT=104 DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for failed_jobs
-- ----------------------------
DROP TABLE IF EXISTS `failed_jobs`;
CREATE TABLE `failed_jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB AUTO_INCREMENT=4448 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for holidays
-- ----------------------------
DROP TABLE IF EXISTS `holidays`;
CREATE TABLE `holidays` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL COMMENT '节假日日期',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '节假日名称',
  `is_workday` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否调休工作日',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `holidays_date_unique` (`date`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for import_histories
-- ----------------------------
DROP TABLE IF EXISTS `import_histories`;
CREATE TABLE `import_histories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_rows` int(11) DEFAULT NULL,
  `success_rows` int(11) DEFAULT NULL,
  `failed_rows` int(11) DEFAULT NULL,
  `error_messages` json DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `import_histories_user_id_foreign` (`user_id`),
  CONSTRAINT `import_histories_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=59 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for job_batches
-- ----------------------------
DROP TABLE IF EXISTS `job_batches`;
CREATE TABLE `job_batches` (
  `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_jobs` int(11) NOT NULL,
  `pending_jobs` int(11) NOT NULL,
  `failed_jobs` int(11) NOT NULL,
  `failed_job_ids` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` mediumtext COLLATE utf8mb4_unicode_ci,
  `cancelled_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `finished_at` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for migrations
-- ----------------------------
DROP TABLE IF EXISTS `migrations`;
CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=62 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for password_reset_tokens
-- ----------------------------
DROP TABLE IF EXISTS `password_reset_tokens`;
CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for personal_access_tokens
-- ----------------------------
DROP TABLE IF EXISTS `personal_access_tokens`;
CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`) USING BTREE,
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for promotion_zones
-- ----------------------------
DROP TABLE IF EXISTS `promotion_zones`;
CREATE TABLE `promotion_zones` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `plan_id` bigint(20) unsigned NOT NULL COMMENT '投放计划ID',
  `ad_product_id` bigint(20) unsigned NOT NULL COMMENT '产品ID',
  `zone_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '推广位ID',
  `zone_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '推广位名称',
  `pid` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'PID',
  `zone_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '推广位类型：ele-饿了么，fliggy-飞猪',
  `promotion_link` text COLLATE utf8mb4_unicode_ci COMMENT '推广链接',
  `promotion_qrcode` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '推广二维码',
  `zone_params` json DEFAULT NULL COMMENT '推广位参数',
  `promotion_params` json DEFAULT NULL COMMENT '推广参数',
  `merged_page_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '融合页面URL',
  `merge_params` json DEFAULT NULL COMMENT '融合参数',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '创建人ID',
  `updated_by` bigint(20) unsigned DEFAULT NULL COMMENT '更新人ID',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `promotion_zones_plan_id_ad_product_id_index` (`plan_id`,`ad_product_id`) USING BTREE,
  KEY `promotion_zones_zone_type_index` (`zone_type`) USING BTREE,
  KEY `promotion_zones_pid_index` (`pid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=212 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for regions
-- ----------------------------
DROP TABLE IF EXISTS `regions`;
CREATE TABLE `regions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` bigint(20) unsigned DEFAULT NULL COMMENT '父级ID',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地区名称',
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地区编码',
  `level` tinyint(4) NOT NULL COMMENT '层级：1=省份，2=城市，3=区县',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `regions_code_unique` (`code`) USING BTREE,
  KEY `regions_parent_id_level_index` (`parent_id`,`level`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for regions_copy1
-- ----------------------------
DROP TABLE IF EXISTS `regions_copy1`;
CREATE TABLE `regions_copy1` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` bigint(20) unsigned DEFAULT NULL COMMENT '父级ID',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地区名称',
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地区编码',
  `level` tinyint(4) NOT NULL COMMENT '层级：1=省份，2=城市，3=区县',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `regions_code_unique` (`code`),
  KEY `regions_parent_id_level_index` (`parent_id`,`level`)
) ENGINE=InnoDB AUTO_INCREMENT=369 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for sync_stats_logs
-- ----------------------------
DROP TABLE IF EXISTS `sync_stats_logs`;
CREATE TABLE `sync_stats_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `procedure_name` varchar(100) NOT NULL COMMENT '存储过程名称',
  `message` text NOT NULL COMMENT '日志消息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='统计同步日志表';

-- ----------------------------
-- Table structure for taobao_order_syncs
-- ----------------------------
DROP TABLE IF EXISTS `taobao_order_syncs`;
CREATE TABLE `taobao_order_syncs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `start_time` timestamp NULL DEFAULT NULL,
  `end_time` timestamp NULL DEFAULT NULL,
  `pid` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_count` int(11) NOT NULL DEFAULT '0',
  `success_count` int(11) NOT NULL DEFAULT '0',
  `failed_count` int(11) NOT NULL DEFAULT '0',
  `error_message` text COLLATE utf8mb4_unicode_ci,
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for taobao_pids
-- ----------------------------
DROP TABLE IF EXISTS `taobao_pids`;
CREATE TABLE `taobao_pids` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `account_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联盟账号',
  `member_id` bigint(20) unsigned NOT NULL COMMENT '会员ID',
  `zone_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '推广位名称',
  `pid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '推广位ID',
  `is_used` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否使用',
  `used_at` timestamp NULL DEFAULT NULL COMMENT '使用时间',
  `used_by` bigint(20) unsigned DEFAULT NULL COMMENT '使用者ID',
  `ad_slot_plan_id` bigint(20) unsigned DEFAULT NULL COMMENT '关联计划ID',
  `ad_creative_id` bigint(20) unsigned DEFAULT NULL COMMENT '关联创意ID',
  `act_type` tinyint(3) unsigned DEFAULT NULL COMMENT '活动类型:1-飞猪 2-福利购',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `taobao_pids_pid_index` (`pid`) USING BTREE,
  KEY `taobao_pids_member_id_index` (`member_id`) USING BTREE,
  KEY `taobao_pids_is_used_index` (`is_used`) USING BTREE,
  KEY `taobao_pids_ad_slot_plan_id_ad_creative_id_index` (`ad_slot_plan_id`,`ad_creative_id`) USING BTREE,
  KEY `taobao_pids_act_type_index` (`act_type`)
) ENGINE=InnoDB AUTO_INCREMENT=83 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for taobao_pids_copy1
-- ----------------------------
DROP TABLE IF EXISTS `taobao_pids_copy1`;
CREATE TABLE `taobao_pids_copy1` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `account_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联盟账号',
  `member_id` bigint(20) unsigned NOT NULL COMMENT '会员ID',
  `zone_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '推广位名称',
  `pid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '推广位ID',
  `is_used` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否使用',
  `used_at` timestamp NULL DEFAULT NULL COMMENT '使用时间',
  `used_by` bigint(20) unsigned DEFAULT NULL COMMENT '使用者ID',
  `ad_slot_plan_id` bigint(20) unsigned DEFAULT NULL COMMENT '关联计划ID',
  `ad_creative_id` bigint(20) unsigned DEFAULT NULL COMMENT '关联创意ID',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `taobao_pids_pid_index` (`pid`),
  KEY `taobao_pids_member_id_index` (`member_id`),
  KEY `taobao_pids_is_used_index` (`is_used`),
  KEY `taobao_pids_ad_slot_plan_id_ad_creative_id_index` (`ad_slot_plan_id`,`ad_creative_id`)
) ENGINE=InnoDB AUTO_INCREMENT=61 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for user_login_logs
-- ----------------------------
DROP TABLE IF EXISTS `user_login_logs`;
CREATE TABLE `user_login_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `ip` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '登录IP',
  `country` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国家',
  `province` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '省份',
  `city` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '城市',
  `district` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '区域',
  `isp` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '网络运营商',
  `user_agent` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浏览器信息',
  `device_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备类型：mobile-移动端，desktop-桌面端，tablet-平板',
  `os` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作系统',
  `browser` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '浏览器',
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'success' COMMENT '登录状态：success-成功，failed-失败',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `user_login_logs_user_id_created_at_index` (`user_id`,`created_at`) USING BTREE,
  KEY `user_login_logs_ip_created_at_index` (`ip`,`created_at`) USING BTREE,
  KEY `user_login_logs_status_created_at_index` (`status`,`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1105 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for user_monthly_targets
-- ----------------------------
DROP TABLE IF EXISTS `user_monthly_targets`;
CREATE TABLE `user_monthly_targets` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL,
  `year` int(11) NOT NULL,
  `month` int(11) NOT NULL,
  `profit_target` decimal(10,2) NOT NULL,
  `actual_profit` decimal(10,2) DEFAULT NULL,
  `remark` text COLLATE utf8mb4_unicode_ci,
  `created_by` bigint(20) unsigned NOT NULL,
  `updated_by` bigint(20) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `user_monthly_targets_user_id_year_month_unique` (`user_id`,`year`,`month`) USING BTREE,
  KEY `user_monthly_targets_created_by_foreign` (`created_by`) USING BTREE,
  KEY `user_monthly_targets_updated_by_foreign` (`updated_by`) USING BTREE,
  CONSTRAINT `user_monthly_targets_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `user_monthly_targets_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`),
  CONSTRAINT `user_monthly_targets_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `real_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户姓名',
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` tinyint(4) NOT NULL DEFAULT '1' COMMENT '角色 1-媒介 2-运营 3-管理层',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1在职 2离职',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最近登录时间',
  `last_login_ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最近登录IP',
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `force_change_password` tinyint(1) NOT NULL DEFAULT '0',
  `password_changed_at` timestamp NULL DEFAULT NULL,
  `ding_user_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `ding_dept_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `users_email_unique` (`email`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Function structure for fn_log_message
-- ----------------------------
DROP FUNCTION IF EXISTS `fn_log_message`;
delimiter ;;
CREATE FUNCTION `fn_log_message`(p_procedure varchar(100), p_message text)
 RETURNS int(11)
  DETERMINISTIC
BEGIN
    INSERT INTO sync_stats_logs (procedure_name, message) VALUES (p_procedure, p_message);
    RETURN 1;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for sp_clean_historical_stats
-- ----------------------------
DROP PROCEDURE IF EXISTS `sp_clean_historical_stats`;
delimiter ;;
CREATE PROCEDURE `sp_clean_historical_stats`(IN p_date date)
BEGIN
    DECLARE v_cutoff_date date;
    DECLARE v_procedure varchar(100) DEFAULT 'sp_clean_historical_stats';
    
    SET v_cutoff_date = DATE_SUB(p_date, INTERVAL 7 DAY);
    
    SELECT fn_log_message(v_procedure, CONCAT('开始清理 ', v_cutoff_date, ' 之前的历史数据'));
    
    -- 获取需要清理的daily_stat_ids
    DELETE FROM ad_slot_plan_daily_city_stats 
    WHERE daily_stat_id IN (
        SELECT id FROM ad_slot_plan_daily_stats 
        WHERE date < v_cutoff_date
    );
    
    DELETE FROM ad_slot_plan_daily_category_stats
    WHERE daily_stat_id IN (
        SELECT id FROM ad_slot_plan_daily_stats 
        WHERE date < v_cutoff_date
    );
    
    SELECT fn_log_message(v_procedure, '历史数据清理完成');
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for sp_get_cost_data
-- ----------------------------
DROP PROCEDURE IF EXISTS `sp_get_cost_data`;
delimiter ;;
CREATE PROCEDURE `sp_get_cost_data`(IN p_plan_id int,
    IN p_date date,
    OUT p_clicks int,
    OUT p_cost decimal(10,2))
BEGIN
    DECLARE v_procedure varchar(100) DEFAULT 'sp_get_cost_data';
    
    SELECT fn_log_message(v_procedure, CONCAT('开始获取计划 ', p_plan_id, ' 在 ', p_date, ' 的成本数据'));
    
    -- 从成本表获取数据(不限制audit_status)
    SELECT 
        IFNULL(clicks, 0),
        IFNULL(cost, 0)
    INTO p_clicks, p_cost
    FROM ad_slot_plan_costs
    WHERE plan_id = p_plan_id
        AND date = p_date
    LIMIT 1;
    
    -- 如果没有数据则设为0
    IF p_clicks IS NULL THEN
        SET p_clicks = 0;
        SET p_cost = 0;
    END IF;
    
    SELECT fn_log_message(v_procedure, CONCAT(
        '计划 ', p_plan_id, ' 成本数据:\n',
        'clicks=', p_clicks, '\n',
        'cost=', p_cost
    ));
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for sp_get_order_stats
-- ----------------------------
DROP PROCEDURE IF EXISTS `sp_get_order_stats`;
delimiter ;;
CREATE PROCEDURE `sp_get_order_stats`(IN p_plan_id int,
    IN p_date date,
    OUT p_bd_orders int,
    OUT p_bd_revenue decimal(10,2),
    OUT p_bd_activity_fee decimal(10,2),
    OUT p_admin_orders int,
    OUT p_admin_revenue decimal(10,2),
    OUT p_admin_activity_fee decimal(10,2),
    OUT p_bd_settle_orders int,
    OUT p_bd_settle_revenue decimal(10,2),
    OUT p_bd_settle_activity_fee decimal(10,2),
    OUT p_admin_settle_orders int,
    OUT p_admin_settle_revenue decimal(10,2),
    OUT p_admin_settle_activity_fee decimal(10,2))
BEGIN
    DECLARE v_procedure varchar(100) DEFAULT 'sp_get_order_stats';
    DECLARE v_table_name varchar(50);
    DECLARE v_start_time datetime;
    DECLARE v_end_time datetime;
    DECLARE v_activity_rate decimal(10,2) DEFAULT 1.33;
    DECLARE v_error_message varchar(200);
    
    -- 设置查询时间范围
    SET v_start_time = CONCAT(p_date, ' 00:00:00');
    SET v_end_time = CONCAT(p_date, ' 23:59:59');
    
    -- 获取需要查询的月份表名
    SET v_table_name = CONCAT('adpro.ad_orders_', DATE_FORMAT(p_date, '%Y%m'));
    
    SET v_error_message = CONCAT(
        '开始从生产库获取计划 ', p_plan_id, ' 的订单数据\n',
        '时间范围: ', v_start_time, ' 至 ', v_end_time, '\n',
        '订单表: ', v_table_name
    );
    SELECT fn_log_message(v_procedure, v_error_message);
    
    -- 检查当月表是否存在
    SET @check_table = CONCAT('SELECT COUNT(*) INTO @table_exists FROM information_schema.tables WHERE table_schema = ''adpro'' AND table_name = ''ad_orders_', DATE_FORMAT(p_date, '%Y%m'), '''');
    PREPARE stmt FROM @check_table;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    IF @table_exists = 0 THEN
        SET v_error_message = CONCAT('订单表 ', v_table_name, ' 不存在');
        SELECT fn_log_message(v_procedure, v_error_message);
        SIGNAL SQLSTATE '45000' 
        SET MESSAGE_TEXT = v_error_message;
    END IF;
    
    -- 获取预估数据(只查询当月)
    SET @sql = CONCAT('
        SELECT 
            -- BD预估数据
            SUM(IF(leak=0,1,0)) as bd_orders,
            SUM(IF(leak=0,pre_commission + IF(order_type=1600, activity_fee * ', v_activity_rate, ', activity_fee) - activity_service_fee,0)) as bd_revenue,
            SUM(IF(leak=0, IF(order_type=1600, activity_fee * ', v_activity_rate, ', activity_fee) - activity_service_fee, 0)) as bd_activity_fee,
            
            -- Admin预估数据
            COUNT(*) as admin_orders,
            SUM(ori_pre_commission + IF(order_type=1600, activity_fee * ', v_activity_rate, ', activity_fee) - activity_service_fee - third_service_fee) as admin_revenue,
            SUM(IF(order_type=1600, activity_fee * ', v_activity_rate, ', activity_fee) - activity_service_fee) as admin_activity_fee
        INTO 
            @bd_orders, @bd_revenue, @bd_activity_fee,
            @admin_orders, @admin_revenue, @admin_activity_fee
        FROM ', v_table_name, '
        WHERE ad_plan_id = ', p_plan_id, '
        AND create_time BETWEEN ''', v_start_time, ''' AND ''', v_end_time, '''
        AND order_status IN (2,3,4)
    ');
    
    SELECT fn_log_message(v_procedure, CONCAT('执行预估数据SQL: ', @sql));
    
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 获取结算数据(查询3个月)
    SET @settle_sql = CONCAT('
        SELECT 
            SUM(bd_settle_orders) as bd_settle_orders,
            SUM(bd_settle_revenue) as bd_settle_revenue,
            SUM(bd_settle_activity_fee) as bd_settle_activity_fee,
            SUM(admin_settle_orders) as admin_settle_orders,
            SUM(admin_settle_revenue) as admin_settle_revenue,
            SUM(admin_settle_activity_fee) as admin_settle_activity_fee
        INTO
            @bd_settle_orders, @bd_settle_revenue, @bd_settle_activity_fee,
            @admin_settle_orders, @admin_settle_revenue, @admin_settle_activity_fee
        FROM (
            -- 当月数据
            SELECT 
                SUM(IF(leak=0,1,0)) as bd_settle_orders,
                SUM(IF(leak=0,commission + IF(order_type=1600, activity_fee * ', v_activity_rate, ', activity_fee) - activity_service_fee,0)) as bd_settle_revenue,
                SUM(IF(leak=0, IF(order_type=1600, activity_fee * ', v_activity_rate, ', activity_fee) - activity_service_fee, 0)) as bd_settle_activity_fee,
                COUNT(*) as admin_settle_orders,
                SUM(ori_commission + IF(order_type=1600, activity_fee * ', v_activity_rate, ', activity_fee) - activity_service_fee - third_service_fee) as admin_settle_revenue,
                SUM(IF(order_type=1600, activity_fee * ', v_activity_rate, ', activity_fee) - activity_service_fee) as admin_settle_activity_fee
            FROM ', v_table_name, '
            WHERE ad_plan_id = ', p_plan_id, '
            AND receive_time BETWEEN ''', v_start_time, ''' AND ''', v_end_time, '''
            AND order_status IN (3,4)
            
            UNION ALL
            
            -- 上月数据
            SELECT 
                SUM(IF(leak=0,1,0)) as bd_settle_orders,
                SUM(IF(leak=0,commission + IF(order_type=1600, activity_fee * ', v_activity_rate, ', activity_fee) - activity_service_fee,0)) as bd_settle_revenue,
                SUM(IF(leak=0, IF(order_type=1600, activity_fee * ', v_activity_rate, ', activity_fee) - activity_service_fee, 0)) as bd_settle_activity_fee,
                COUNT(*) as admin_settle_orders,
                SUM(ori_commission + IF(order_type=1600, activity_fee * ', v_activity_rate, ', activity_fee) - activity_service_fee - third_service_fee) as admin_settle_revenue,
                SUM(IF(order_type=1600, activity_fee * ', v_activity_rate, ', activity_fee) - activity_service_fee) as admin_settle_activity_fee
            FROM adpro.ad_orders_', DATE_FORMAT(DATE_SUB(p_date, INTERVAL 1 MONTH), '%Y%m'), '
            WHERE ad_plan_id = ', p_plan_id, '
            AND receive_time BETWEEN ''', v_start_time, ''' AND ''', v_end_time, '''
            AND order_status IN (3,4)
            
            UNION ALL
            
            -- 上上月数据
            SELECT 
                SUM(IF(leak=0,1,0)) as bd_settle_orders,
                SUM(IF(leak=0,commission + IF(order_type=1600, activity_fee * ', v_activity_rate, ', activity_fee) - activity_service_fee,0)) as bd_settle_revenue,
                SUM(IF(leak=0, IF(order_type=1600, activity_fee * ', v_activity_rate, ', activity_fee) - activity_service_fee, 0)) as bd_settle_activity_fee,
                COUNT(*) as admin_settle_orders,
                SUM(ori_commission + IF(order_type=1600, activity_fee * ', v_activity_rate, ', activity_fee) - activity_service_fee - third_service_fee) as admin_settle_revenue,
                SUM(IF(order_type=1600, activity_fee * ', v_activity_rate, ', activity_fee) - activity_service_fee) as admin_settle_activity_fee
            FROM adpro.ad_orders_', DATE_FORMAT(DATE_SUB(p_date, INTERVAL 2 MONTH), '%Y%m'), '
            WHERE ad_plan_id = ', p_plan_id, '
            AND receive_time BETWEEN ''', v_start_time, ''' AND ''', v_end_time, '''
            AND order_status IN (3,4)
        ) t
    ');
    
    SELECT fn_log_message(v_procedure, CONCAT('执行结算数据SQL: ', @settle_sql));
    
    PREPARE stmt FROM @settle_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 将结果赋值给OUT参数
    SET p_bd_orders = IFNULL(@bd_orders, 0);
    SET p_bd_revenue = IFNULL(@bd_revenue, 0);
    SET p_bd_activity_fee = IFNULL(@bd_activity_fee, 0);
    SET p_admin_orders = IFNULL(@admin_orders, 0);
    SET p_admin_revenue = IFNULL(@admin_revenue, 0);
    SET p_admin_activity_fee = IFNULL(@admin_activity_fee, 0);
    SET p_bd_settle_orders = IFNULL(@bd_settle_orders, 0);
    SET p_bd_settle_revenue = IFNULL(@bd_settle_revenue, 0);
    SET p_bd_settle_activity_fee = IFNULL(@bd_settle_activity_fee, 0);
    SET p_admin_settle_orders = IFNULL(@admin_settle_orders, 0);
    SET p_admin_settle_revenue = IFNULL(@admin_settle_revenue, 0);
    SET p_admin_settle_activity_fee = IFNULL(@admin_settle_activity_fee, 0);
    
    SELECT fn_log_message(v_procedure, CONCAT(
        '获取到的订单数据:\n',
        'BD预估: orders=', p_bd_orders, ', revenue=', p_bd_revenue, ', activity_fee=', p_bd_activity_fee, '\n',
        'Admin预估: orders=', p_admin_orders, ', revenue=', p_admin_revenue, ', activity_fee=', p_admin_activity_fee, '\n',
        'BD结算: orders=', p_bd_settle_orders, ', revenue=', p_bd_settle_revenue, ', activity_fee=', p_bd_settle_activity_fee, '\n',
        'Admin结算: orders=', p_admin_settle_orders, ', revenue=', p_admin_settle_revenue, ', activity_fee=', p_admin_settle_activity_fee
    ));
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for sp_get_plans_to_sync
-- ----------------------------
DROP PROCEDURE IF EXISTS `sp_get_plans_to_sync`;
delimiter ;;
CREATE PROCEDURE `sp_get_plans_to_sync`(IN p_plan_id int)
BEGIN
    DECLARE v_procedure varchar(100) DEFAULT 'sp_get_plans_to_sync';
    DECLARE v_count int;
    
    SELECT fn_log_message(v_procedure, IF(p_plan_id IS NULL, 
        '开始获取所有需要同步的计划', 
        CONCAT('开始获取指定计划: ', p_plan_id)));
    
    IF p_plan_id IS NOT NULL THEN
        SELECT 
            p.id, p.ad_slot_id, p.ad_product_id, p.user_id, p.price,
            s.media_id
        FROM ad_slot_plans p
        LEFT JOIN ad_slots s ON p.ad_slot_id = s.id
        WHERE p.id = p_plan_id;
        
        SET v_count = FOUND_ROWS();
        SELECT fn_log_message(v_procedure, CONCAT('找到指定计划数量: ', v_count));
    ELSE
        SELECT 
            p.id, p.ad_slot_id, p.ad_product_id, p.user_id, p.price,
            s.media_id
        FROM ad_slot_plans p
        LEFT JOIN ad_slots s ON p.ad_slot_id = s.id
        WHERE p.delivery_status IN ('running', 'wait', 'stopped'); -- 投放中、待开始、手动停投
        
        SET v_count = FOUND_ROWS();
        SELECT fn_log_message(v_procedure, CONCAT('找到需要同步的计划数量: ', v_count));
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for sp_sync_plan_stats
-- ----------------------------
DROP PROCEDURE IF EXISTS `sp_sync_plan_stats`;
delimiter ;;
CREATE PROCEDURE `sp_sync_plan_stats`(IN p_date date,
    IN p_plan_id int)
BEGIN
    DECLARE v_procedure varchar(100) DEFAULT 'sp_sync_plan_stats';
    DECLARE v_done int DEFAULT FALSE;
    DECLARE v_current_plan_id int;
    DECLARE v_error_message text;
    
    -- 计划基础数据变量
    DECLARE v_media_id int;
    DECLARE v_ad_slot_id int;
    DECLARE v_ad_product_id int;
    DECLARE v_user_id int;
    DECLARE v_price decimal(10,2);
    
    -- 统计数据变量
    DECLARE v_clicks int;
    DECLARE v_cost decimal(10,2);
    DECLARE v_bd_orders int;
    DECLARE v_bd_revenue decimal(10,2);
    DECLARE v_bd_activity_fee decimal(10,2);
    DECLARE v_admin_orders int;
    DECLARE v_admin_revenue decimal(10,2);
    DECLARE v_admin_activity_fee decimal(10,2);
    DECLARE v_bd_settle_orders int;
    DECLARE v_bd_settle_revenue decimal(10,2);
    DECLARE v_bd_settle_activity_fee decimal(10,2);
    DECLARE v_admin_settle_orders int;
    DECLARE v_admin_settle_revenue decimal(10,2);
    DECLARE v_admin_settle_activity_fee decimal(10,2);
    
    -- ROI相关变量
    DECLARE v_bd_profit decimal(10,2);
    DECLARE v_bd_roi decimal(10,4);
    DECLARE v_admin_profit decimal(10,2);
    DECLARE v_admin_roi decimal(10,4);
    DECLARE v_bd_settle_profit decimal(10,2);
    DECLARE v_bd_settle_roi decimal(10,4);
    DECLARE v_admin_settle_profit decimal(10,2);
    DECLARE v_admin_settle_roi decimal(10,4);
    
    -- 定义游标,获取计划基础数据
    DECLARE cur_plans CURSOR FOR 
        SELECT 
            p.id, p.ad_slot_id, p.ad_product_id, p.user_id, p.price,
            s.media_id
        FROM ad_slot_plans p
        LEFT JOIN ad_slots s ON p.ad_slot_id = s.id
        WHERE p.id = p_plan_id OR (p_plan_id IS NULL AND p.delivery_status IN ('running', 'wait', 'stopped'));
        
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET v_done = TRUE;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 
            v_error_message = MESSAGE_TEXT;
        SELECT fn_log_message(v_procedure, CONCAT('错误: ', v_error_message));
        RESIGNAL;
    END;
    
    SELECT fn_log_message(v_procedure, CONCAT('开始同步 ', p_date, ' 的数据'));
    
    -- 清理历史数据
    CALL sp_clean_historical_stats(p_date);
    
    -- 遍历每个计划进行同步
    OPEN cur_plans;
    read_loop: LOOP
        FETCH cur_plans INTO 
            v_current_plan_id, v_ad_slot_id, v_ad_product_id, v_user_id, v_price, v_media_id;
            
        IF v_done THEN
            LEAVE read_loop;
        END IF;
        
        BEGIN
            DECLARE EXIT HANDLER FOR SQLEXCEPTION
            BEGIN
                SELECT fn_log_message(v_procedure, CONCAT('计划 ', v_current_plan_id, ' 同步失败'));
                ROLLBACK;
            END;
            
            START TRANSACTION;
            
            -- 获取成本数据
            CALL sp_get_cost_data(v_current_plan_id, p_date, v_clicks, v_cost);
            
            -- 获取订单统计(从生产库)
            CALL sp_get_order_stats(
                v_current_plan_id, p_date,
                v_bd_orders, v_bd_revenue, v_bd_activity_fee,
                v_admin_orders, v_admin_revenue, v_admin_activity_fee,
                v_bd_settle_orders, v_bd_settle_revenue, v_bd_settle_activity_fee,
                v_admin_settle_orders, v_admin_settle_revenue, v_admin_settle_activity_fee
            );
            
            -- 计算利润和ROI
            -- BD预估数据
            SET v_bd_profit = v_bd_revenue - v_cost;
            SET v_bd_roi = IF(v_cost > 0, v_bd_revenue / v_cost, 0);
            
            -- Admin预估数据
            SET v_admin_profit = v_admin_revenue - v_cost;
            SET v_admin_roi = IF(v_cost > 0, v_admin_revenue / v_cost, 0);
            
            -- BD结算数据
            SET v_bd_settle_profit = v_bd_settle_revenue - v_cost;
            SET v_bd_settle_roi = IF(v_cost > 0, v_bd_settle_revenue / v_cost, 0);
            
            -- Admin结算数据
            SET v_admin_settle_profit = v_admin_settle_revenue - v_cost;
            SET v_admin_settle_roi = IF(v_cost > 0, v_admin_settle_revenue / v_cost, 0);
            
            -- 记录ROI计算日志
            SELECT fn_log_message(v_procedure, CONCAT(
                '计划 ', v_current_plan_id, ' ROI计算结果:\n',
                'BD预估: profit=', v_bd_profit, ', roi=', v_bd_roi, '\n',
                'Admin预估: profit=', v_admin_profit, ', roi=', v_admin_roi, '\n',
                'BD结算: profit=', v_bd_settle_profit, ', roi=', v_bd_settle_roi, '\n',
                'Admin结算: profit=', v_admin_settle_profit, ', roi=', v_admin_settle_roi
            ));
            
            -- 更新统计数据(到测试库)
            CALL sp_update_plan_daily_stats(
                v_current_plan_id, p_date,
                v_media_id, v_ad_slot_id, v_ad_product_id, v_user_id, v_price,
                v_clicks, v_cost,
                v_bd_orders, v_bd_revenue, v_bd_profit, v_bd_roi, v_bd_activity_fee,
                v_admin_orders, v_admin_revenue, v_admin_profit, v_admin_roi, v_admin_activity_fee,
                v_bd_settle_orders, v_bd_settle_revenue, v_bd_settle_profit, v_bd_settle_roi, v_bd_settle_activity_fee,
                v_admin_settle_orders, v_admin_settle_revenue, v_admin_settle_profit, v_admin_settle_roi, v_admin_settle_activity_fee
            );
            
            COMMIT;
            
            SELECT fn_log_message(v_procedure, CONCAT('计划 ', v_current_plan_id, ' 同步完成'));
        END;
    END LOOP;
    
    CLOSE cur_plans;
    
    SELECT fn_log_message(v_procedure, '所有计划同步完成');
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for sp_sync_stats
-- ----------------------------
DROP PROCEDURE IF EXISTS `sp_sync_stats`;
delimiter ;;
CREATE PROCEDURE `sp_sync_stats`(IN p_date date,
    IN p_plan_id int)
BEGIN
    DECLARE v_procedure varchar(100) DEFAULT 'sp_sync_stats';
    DECLARE v_error_message text;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 
            v_error_message = MESSAGE_TEXT;
        SELECT fn_log_message(v_procedure, CONCAT('同步失败: ', v_error_message));
        RESIGNAL;
    END;
    
    -- 如果没有传入日期,使用当前日期
    IF p_date IS NULL THEN
        SET p_date = CURDATE();
    END IF;
    
    SELECT fn_log_message(v_procedure, CONCAT(
        '开始数据同步 - 日期: ', p_date,
        IF(p_plan_id IS NULL, '', CONCAT(', 计划ID: ', p_plan_id))
    ));
    
    -- 调用主同步过程
    CALL sp_sync_plan_stats(p_date, p_plan_id);
    
    SELECT fn_log_message(v_procedure, '数据同步完成');
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for sp_update_plan_daily_stats
-- ----------------------------
DROP PROCEDURE IF EXISTS `sp_update_plan_daily_stats`;
delimiter ;;
CREATE PROCEDURE `sp_update_plan_daily_stats`(IN p_plan_id int,
    IN p_date date,
    IN p_media_id int,
    IN p_ad_slot_id int,
    IN p_ad_product_id int,
    IN p_user_id int,
    IN p_unit_price decimal(10,2),
    IN p_clicks int,
    IN p_cost decimal(10,2),
    IN p_bd_orders int,
    IN p_bd_revenue decimal(10,2),
    IN p_bd_profit decimal(10,2),
    IN p_bd_roi decimal(10,4),
    IN p_bd_activity_fee decimal(10,2),
    IN p_admin_orders int,
    IN p_admin_revenue decimal(10,2),
    IN p_admin_profit decimal(10,2),
    IN p_admin_roi decimal(10,4),
    IN p_admin_activity_fee decimal(10,2),
    IN p_bd_settle_orders int,
    IN p_bd_settle_revenue decimal(10,2),
    IN p_bd_settle_profit decimal(10,2),
    IN p_bd_settle_roi decimal(10,4),
    IN p_bd_settle_activity_fee decimal(10,2),
    IN p_admin_settle_orders int,
    IN p_admin_settle_revenue decimal(10,2),
    IN p_admin_settle_profit decimal(10,2),
    IN p_admin_settle_roi decimal(10,4),
    IN p_admin_settle_activity_fee decimal(10,2))
BEGIN
    DECLARE v_stat_id int;
    DECLARE v_procedure varchar(100) DEFAULT 'sp_update_plan_daily_stats';
    
    SELECT fn_log_message(v_procedure, CONCAT(
        '开始更新计划 ', p_plan_id, ' 在 ', p_date, ' 的统计数据\n',
        '基础数据: media_id=', p_media_id, ', ad_slot_id=', p_ad_slot_id, 
        ', ad_product_id=', p_ad_product_id, ', user_id=', p_user_id, 
        ', unit_price=', p_unit_price, '\n',
        '成本数据: clicks=', p_clicks, ', cost=', p_cost, '\n',
        'BD预估: orders=', p_bd_orders, ', revenue=', p_bd_revenue, 
        ', profit=', p_bd_profit, ', roi=', p_bd_roi, '\n',
        'Admin预估: orders=', p_admin_orders, ', revenue=', p_admin_revenue, 
        ', profit=', p_admin_profit, ', roi=', p_admin_roi
    ));
    
    -- 查找是否存在记录
    SELECT id INTO v_stat_id
    FROM ad_slot_plan_daily_stats
    WHERE plan_id = p_plan_id AND date = p_date
    LIMIT 1;
    
    IF v_stat_id IS NOT NULL THEN
        -- 更新已存在记录
        UPDATE ad_slot_plan_daily_stats
        SET
            media_id = p_media_id,
            ad_slot_id = p_ad_slot_id,
            ad_product_id = p_ad_product_id,
            user_id = p_user_id,
            unit_price = p_unit_price,
            clicks = p_clicks,
            cost = p_cost,
            bd_orders = p_bd_orders,
            bd_revenue = p_bd_revenue,
            bd_profit = p_bd_profit,
            bd_roi = p_bd_roi,
            bd_activity_fee = p_bd_activity_fee,
            admin_orders = p_admin_orders,
            admin_revenue = p_admin_revenue,
            admin_profit = p_admin_profit,
            admin_roi = p_admin_roi,
            admin_activity_fee = p_admin_activity_fee,
            bd_settle_orders = p_bd_settle_orders,
            bd_settle_revenue = p_bd_settle_revenue,
            bd_settle_profit = p_bd_settle_profit,
            bd_settle_roi = p_bd_settle_roi,
            bd_settle_activity_fee = p_bd_settle_activity_fee,
            admin_settle_orders = p_admin_settle_orders,
            admin_settle_revenue = p_admin_settle_revenue,
            admin_settle_profit = p_admin_settle_profit,
            admin_settle_roi = p_admin_settle_roi,
            admin_settle_activity_fee = p_admin_settle_activity_fee,
            updated_at = NOW()
        WHERE id = v_stat_id;
        
        SELECT fn_log_message(v_procedure, CONCAT('更新统计记录 ID: ', v_stat_id));
    ELSE
        -- 插入新记录
        INSERT INTO ad_slot_plan_daily_stats (
            plan_id, date,
            media_id, ad_slot_id, ad_product_id, user_id, unit_price,
            clicks, cost,
            bd_orders, bd_revenue, bd_profit, bd_roi, bd_activity_fee,
            admin_orders, admin_revenue, admin_profit, admin_roi, admin_activity_fee,
            bd_settle_orders, bd_settle_revenue, bd_settle_profit, bd_settle_roi, bd_settle_activity_fee,
            admin_settle_orders, admin_settle_revenue, admin_settle_profit, admin_settle_roi, admin_settle_activity_fee,
            created_at, updated_at
        ) VALUES (
            p_plan_id, p_date,
            p_media_id, p_ad_slot_id, p_ad_product_id, p_user_id, p_unit_price,
            p_clicks, p_cost,
            p_bd_orders, p_bd_revenue, p_bd_profit, p_bd_roi, p_bd_activity_fee,
            p_admin_orders, p_admin_revenue, p_admin_profit, p_admin_roi, p_admin_activity_fee,
            p_bd_settle_orders, p_bd_settle_revenue, p_bd_settle_profit, p_bd_settle_roi, p_bd_settle_activity_fee,
            p_admin_settle_orders, p_admin_settle_revenue, p_admin_settle_profit, p_admin_settle_roi, p_admin_settle_activity_fee,
            NOW(), NOW()
        );
        
        SET v_stat_id = LAST_INSERT_ID();
        SELECT fn_log_message(v_procedure, CONCAT('创建新统计记录 ID: ', v_stat_id));
    END IF;
    
    -- 返回统计ID
    SELECT v_stat_id as stat_id;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
