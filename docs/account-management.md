# 账户管理功能实现说明

## 功能概述

本次实现了完整的账户管理功能，包括用户的CRUD操作、权限控制、角色管理等核心功能，旨在提高不同角色用户的工作效率。

## 功能特性

### 1. 用户管理
- ✅ 用户列表查看（支持分页、筛选、排序）
- ✅ 新增用户（包含完整的表单验证）
- ✅ 编辑用户信息
- ✅ 删除用户（软删除）
- ✅ 用户锁定/解锁
- ✅ 密码重置
- ✅ 上级关系管理（防止循环引用）

### 2. 权限控制
- ✅ 基于角色的权限控制（RBAC）
- ✅ 三种角色：媒介、运营、管理层
- ✅ 不同角色拥有不同的操作权限
- ✅ 前端权限控制（按钮级别）
- ✅ 后端API权限验证

### 3. 用户体验
- ✅ 现代化的UI设计
- ✅ 响应式布局（支持移动端）
- ✅ 统一的操作反馈（成功/错误提示）
- ✅ 表单验证（前端+后端双重验证）
- ✅ 操作确认对话框

## 技术架构

### 前端技术栈
- **Vue 3** - 组合式API
- **Element Plus** - UI组件库
- **SCSS** - 样式预处理
- **axios** - HTTP请求库

### 后端技术栈
- **GoFrame v2** - Go Web框架
- **MySQL** - 数据库
- **分层架构** - Controller/Logic/Service/DAO

## 文件结构

```
# 前端文件
frontend/admin/src/
├── views/system/account/index.vue    # 账户管理主页面
├── api/user.js                       # 用户API接口
├── utils/date.js                     # 日期工具函数
└── router/index.js                   # 路由配置

# 后端文件
internal/
├── controller/user/user.go           # 用户控制器
├── logic/user/user.go                # 用户业务逻辑
├── service/user.go                   # 用户服务接口
├── middleware/permission.go          # 权限中间件
└── router/router.go                  # 路由配置

# API定义
api/v1/user.go                        # 用户API结构定义

# 数据库
database/migrations/
├── users_enhancement.sql             # 用户表增强
├── lighthouse_accounts.sql           # 灯火账户表
└── lighthouse_agents.sql             # 代理商表
```

## 权限设计

### 角色权限矩阵

| 功能 | 媒介 | 运营 | 管理层 |
|------|------|------|--------|
| 查看账户列表 | ✅ | ✅ | ✅ |
| 新增账户 | ❌ | ❌ | ✅ |
| 编辑账户 | ❌ | ✅ | ✅ |
| 删除账户 | ❌ | ❌ | ✅ |
| 重置密码 | ❌ | ❌ | ✅ |
| 锁定/解锁 | ❌ | ✅ | ✅ |

### 权限代码映射

```javascript
// 权限代码定义
const PERMISSIONS = {
  SYSTEM: {
    ACCOUNT: {
      VIEW: 'system.account.view',
      CREATE: 'system.account.create',
      EDIT: 'system.account.edit',
      DELETE: 'system.account.delete',
      RESET_PASSWORD: 'system.account.reset_password'
    }
  }
}
```

## 主要功能说明

### 1. 用户列表页面
- **筛选功能**：支持按用户名、角色、部门、状态、锁定状态筛选
- **分页功能**：支持自定义页大小（10/20/50/100）
- **排序功能**：支持按ID、创建时间排序
- **状态展示**：清晰显示用户状态（在职/离职、正常/锁定）
- **联系信息**：展示邮箱和手机号
- **层级关系**：显示直属上级信息

### 2. 用户编辑功能
- **表单验证**：
  - 用户名：3-50字符，创建后不可修改
  - 真实姓名：2-20字符
  - 邮箱：标准邮箱格式验证
  - 手机号：11位手机号格式验证
  - 密码：6-30字符（仅新增时需要）
- **级联选择**：部门、角色、上级选择
- **防重复**：用户名和邮箱唯一性检查
- **循环检查**：防止上级关系形成循环引用

### 3. 操作权限控制
- **按钮权限**：根据用户角色动态显示/隐藏操作按钮
- **API权限**：后端接口级别的权限验证
- **菜单权限**：侧边栏菜单根据权限显示
- **数据权限**：用户只能查看和操作有权限的数据

### 4. 安全特性
- **密码加密**：使用MD5加密存储密码
- **锁定机制**：支持账户锁定，需要管理员解锁
- **操作日志**：记录关键操作（待实现）
- **会话管理**：登录状态验证和自动过期

## 使用说明

### 前端使用
1. **权限检查**：使用`hasPermission()`函数检查权限
2. **API调用**：使用封装好的API函数
3. **表单验证**：利用Element Plus的验证规则
4. **状态管理**：使用Vue 3的响应式数据

### 后端扩展
1. **添加新权限**：在`permission.go`中的权限映射中添加
2. **新增字段**：修改API结构体和数据库表
3. **业务逻辑**：在Logic层添加新的业务方法
4. **路由配置**：在`router.go`中添加新路由

## 数据库设计

### 用户表增强字段
```sql
-- 新增字段
ALTER TABLE `users` ADD COLUMN `department` varchar(100) DEFAULT NULL COMMENT '部门名称';
ALTER TABLE `users` ADD COLUMN `position` varchar(100) DEFAULT NULL COMMENT '职位';
ALTER TABLE `users` ADD COLUMN `phone` varchar(20) DEFAULT NULL COMMENT '手机号码';
ALTER TABLE `users` ADD COLUMN `supervisor_id` bigint(20) unsigned DEFAULT NULL COMMENT '直属上级ID';
ALTER TABLE `users` ADD COLUMN `is_locked` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否锁定';
ALTER TABLE `users` ADD COLUMN `lock_reason` varchar(255) DEFAULT NULL COMMENT '锁定原因';
ALTER TABLE `users` ADD COLUMN `remark` text COMMENT '备注信息';
```

### 权限表设计
```sql
-- 权限表
CREATE TABLE `permissions` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '权限名称',
  `code` varchar(100) NOT NULL COMMENT '权限编码',
  `module` varchar(50) NOT NULL COMMENT '所属模块',
  `description` text COMMENT '权限描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_permission_code` (`code`)
);

-- 角色权限关联表
CREATE TABLE `role_permissions` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `role` tinyint(4) NOT NULL COMMENT '角色',
  `permission_id` int(11) unsigned NOT NULL COMMENT '权限ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permission` (`role`, `permission_id`)
);
```

## 性能优化

### 前端优化
- **组件缓存**：合理使用keep-alive缓存页面组件
- **懒加载**：路由级别的代码分割
- **请求优化**：合并API请求，减少网络开销
- **图标优化**：使用SVG图标，支持主题切换

### 后端优化
- **数据库索引**：关键字段添加索引
- **权限缓存**：使用内存映射减少数据库查询
- **分页优化**：使用游标分页处理大数据量
- **查询优化**：避免N+1查询，使用JOIN优化

## 测试说明

### 功能测试
- [x] 用户列表加载和筛选
- [x] 用户新增表单验证
- [x] 用户编辑信息更新
- [x] 用户删除确认流程
- [x] 密码重置功能
- [x] 账户锁定/解锁
- [x] 权限控制验证

### 兼容性测试
- [x] Chrome 90+
- [x] Firefox 88+
- [x] Safari 14+
- [x] Edge 90+
- [x] 移动端响应式

## 部署说明

### 前端部署
```bash
# 安装依赖
npm install

# 开发环境
npm run dev

# 生产构建
npm run build
```

### 后端部署
```bash
# 构建项目
go build -o app main.go

# 运行迁移
./app migrate

# 启动服务
./app run
```

## 后续优化计划

### 短期计划
- [ ] 操作日志记录和查看
- [ ] 批量操作（批量删除、批量锁定等）
- [ ] 导入导出功能（Excel支持）
- [ ] 高级筛选（时间范围、多条件组合）

### 长期计划
- [ ] 用户头像上传
- [ ] 在线状态显示
- [ ] 消息通知系统
- [ ] 审批流程集成
- [ ] 单点登录（SSO）
- [ ] 多租户支持

## 问题反馈

如有问题或建议，请通过以下方式反馈：
- 提交Issue到项目仓库
- 联系开发团队
- 发送邮件到技术支持邮箱

---

**开发团队**：技术部  
**最后更新**：2024年12月  
**版本**：v1.0.0 