# 工作模式权限改造说明

## 改造目标

将工作模式的访问控制从基于角色(role)改为基于权限(permission)，让权限系统更加灵活和精确。

## 改造内容

### 1. User模型扩展

#### 新增方法
- `GetAvailableModesWithPermissions([]*Permission) []string` - 基于权限获取可用工作模式
- `CanAccessModeWithPermissions(mode string, permissions []*Permission) bool` - 基于权限检查模式访问权限
- `ToProfileWithPermissions([]*Permission) *UserProfile` - 基于权限生成用户资料

#### 权限判断逻辑
```go
// 流量采买模式权限
trafficPermissions := []string{"agent:view", "media:view", "slot:view", "plan:view"}

// 广告投放模式权限  
adPermissions := []string{
    "delivery:command:view", 
    "delivery:cost:view", 
    "delivery:report:view", 
    "report:lighthouse:view", 
    "delivery:model:view"
}
```

### 2. WorkModeService改造

#### 依赖注入
- 新增 `roleDAO *dao.RoleDAO` 用于获取用户权限

#### 主要修改方法
- `GetUserModes()` - 使用权限判断可用模式
- `SwitchWorkMode()` - 使用权限验证模式切换

#### 容错机制
- 如果获取权限失败，自动回退到基于角色的判断
- 保证系统的稳定性和向后兼容性

### 3. AuthService改造

#### 登录流程优化
- `Login()` 方法：获取用户权限，生成基于权限的UserProfile
- `GetUserInfo()` 方法：返回包含正确可用模式的用户信息

#### 容错机制
- 权限获取失败时回退到角色判断
- 不影响现有登录流程

## 权限定义

### 流量采买模式
- **代理管理**: `agent:view`
- **媒体管理**: `media:view` 
- **资源位管理**: `slot:view`
- **投放计划**: `plan:view`

### 广告投放模式
- **投放指令**: `delivery:command:view`
- **投放成本**: `delivery:cost:view`
- **投放报表**: `delivery:report:view`
- **灯火报表**: `report:lighthouse:view`
- **投放模型**: `delivery:model:view`

## 使用方式

### 用户登录后
- 系统自动根据用户权限计算可用工作模式
- 前端展示对应的模式选择

### 切换工作模式
- 系统验证用户是否有对应模式的权限
- 权限不足时拒绝切换

## 优势

1. **更精确**: 不再依赖固定的角色映射，可以精确控制功能访问
2. **更灵活**: 可以给任何角色分配特定权限
3. **更易维护**: 权限配置可以动态调整，无需修改代码
4. **更安全**: 基于最小权限原则，默认无权限

## 向后兼容

- 保留原有基于角色的判断方法
- 权限系统失效时自动回退
- 不影响现有功能使用

## 测试建议

1. **权限配置测试**
   - 为不同角色配置不同的权限组合
   - 验证工作模式显示是否正确

2. **边界情况测试**
   - 权限系统故障时的回退机制
   - 用户无任何权限时的表现

3. **功能集成测试**
   - 登录后的模式显示
   - 模式切换的权限验证
   - 菜单权限的正确过滤

## 注意事项

1. 确保权限数据的正确性和完整性
2. 定期审查权限配置的合理性
3. 监控权限系统的性能影响
4. 建立权限变更的审批流程 