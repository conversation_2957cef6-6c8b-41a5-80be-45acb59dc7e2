package main

import (
	_ "ad-pro-v2/internal/packed"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"

	"net/http"
	_ "net/http/pprof"

	"ad-pro-v2/internal/cmd"
)

func main() {
	// 启动 pprof 监控服务
	go func() {
		g.Log().Info(gctx.New(), "pprof 监控服务启动在 :6060")
		if err := http.ListenAndServe(":6060", nil); err != nil {
			g.Log().Error(gctx.New(), "pprof 监控服务启动失败:", err)
		}
	}()

	cmd.Main.Run(gctx.New())
}
