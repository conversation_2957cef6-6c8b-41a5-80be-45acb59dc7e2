-- 检查用户ID为13的完整权限信息

-- 1. 查看用户基本信息和角色
SELECT 
    u.id as user_id,
    u.name as user_name,
    u.email,
    r.id as role_id,
    r.name as role_name,
    r.code as role_code
FROM users u
LEFT JOIN roles r ON u.role_id = r.id
WHERE u.id = 13;

-- 2. 查看该用户角色的所有权限
SELECT 
    p.id as permission_id,
    p.name as permission_name,
    p.code as permission_code,
    p.module,
    p.type,
    p.description
FROM users u
JOIN roles r ON u.role_id = r.id
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE u.id = 13
ORDER BY p.module, p.code;

-- 3. 检查plan模块的所有权限
SELECT 
    p.id,
    p.name,
    p.code,
    p.description,
    CASE 
        WHEN rp.permission_id IS NOT NULL THEN '✅ 已分配'
        ELSE '❌ 未分配'
    END as assignment_status
FROM permissions p
LEFT JOIN (
    SELECT DISTINCT rp.permission_id
    FROM users u
    JOIN roles r ON u.role_id = r.id
    JOIN role_permissions rp ON r.id = rp.role_id
    WHERE u.id = 13
) rp ON p.id = rp.permission_id
WHERE p.module = 'plan'
ORDER BY p.code;

-- 4. 检查是否存在plan:create权限
SELECT 
    id,
    name,
    code,
    description,
    status
FROM permissions
WHERE code = 'plan:create'; 