-- ----------------------------
-- Table structure for ad_creatives
-- ----------------------------
DROP TABLE IF EXISTS `ad_creatives`;
CREATE TABLE `ad_creatives` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '创意名称',
  `hot_areas` text COLLATE utf8mb4_unicode_ci COMMENT '热区，JSON字符串',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_ad_creatives_name` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='创意表'; 