# https://goframe.org/docs/web/server-config-file-template
server:
  address:     ":8000"
  openapiPath: "/api.json"
  swaggerPath: "/swagger"
  env: "dev"

# https://goframe.org/docs/core/glog-config
logger:
  level : "INFO"
  stdout: true

# https://goframe.org/docs/core/gdb-config-file
database:
  default:
    link: "mysql:adpro:Ddkj2024@tcp(rm-bp1xqhp60et1lx5z92o.mysql.rds.aliyuncs.com:3306)/adpro"
    debug: true
  warehouse:
    link: "mysql:adpro:Ddkj2024@tcp(rm-bp1xqhp60et1lx5z92o.mysql.rds.aliyuncs.com:3306)/warehouse"
    debug: true
  rebate_new:
    link: "mysql:adpro:Ddkj2024@tcp(rm-bp1xqhp60et1lx5z92o.mysql.rds.aliyuncs.com:3306)/rebate_new"
    debug: true
  byn_data:
    link: "mysql:adpro:Ddkj2024@tcp(rm-bp1xqhp60et1lx5z92o.mysql.rds.aliyuncs.com:3306)/byn_data"
    debug: true
  analytics:
    link: "mysql:adpro:Ddkj2024@tcp(rm-bp1xqhp60et1lx5z92o.mysql.rds.aliyuncs.com:3306)/adpro"
    debug: true
  # analytics:
  #   link: "mysql:canel:Zxcv2468@tcp(am-bp108429doo330y46167320o.ads.aliyuncs.com:3306)/adpro"
  #   debug: true

# Redis配置
# https://goframe.org/pages/viewpage.action?pageId=1114217
redis:
  default:
    # address: r-bp119493295e9444pd.redis.rds.aliyuncs.com:6379
    # pass: "Zxcv2468"
    address: 127.0.0.1:6379
    db: 0


oss:
  endpoint: oss-cn-hangzhou.aliyuncs.com
  access_key_id: LTAI5tHVcLrTDZSbGwJwokvT
  access_key_secret: ******************************
  bucket_name: rebate-robot
