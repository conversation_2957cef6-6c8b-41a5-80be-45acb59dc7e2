-- 计划表
CREATE TABLE `platform_plans` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `agent_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT '代理id',
    `media_id` bigint unsigned NOT NULL DEFAULT 0  COMMENT'媒体id',
    `platform_type` varchar(32) NOT NULL  COMMENT '平台类型',
    `remark` text COMMENT '备注',
    `data_id` varchar(64) NOT NULL COMMENT '计划ID',
    `name` varchar(128) NOT NULL COMMENT '计划名称',
    `principal_account` varchar(64) NOT NULL DEFAULT '' COMMENT '商家账户',
    `principal_name` varchar(64) NOT NULL DEFAULT '' COMMENT '商家名称',
    `market_target_name` varchar(64) DEFAULT '' COMMENT '营销目标名称',
    `scene_name` varchar(64) DEFAULT '' COMMENT '投放产品名称',
    `ad_creative_id` bigint unsigned DEFAULT NULL COMMENT '创意ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_platform_data_id` (`platform_type`, `data_id`),
    KEY `idx_ad_creative_id` (`ad_creative_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='广告计划表';

-- 单元表
CREATE TABLE `platform_groups` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `platform_type` varchar(32) NOT NULL COMMENT '平台类型',
    `agent_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT '代理id',
    `media_id` bigint unsigned NOT NULL DEFAULT 0  COMMENT '媒体id',
    `data_id` varchar(64) NOT NULL COMMENT '单元ID',
    `plan_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT '关联的计划ID',
    `name` varchar(128) NOT NULL COMMENT '单元名称',
    `remark` text COMMENT '备注', 
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_platform_data_id` (`platform_type`, `data_id`),
    KEY `idx_plan_id` (`plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='广告单元表';

-- 创意表
CREATE TABLE `platform_creatives` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `platform_type` varchar(32) NOT NULL COMMENT '平台类型',
    `agent_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT '代理id',
    `media_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT '媒体id',
    `data_id` varchar(64) NOT NULL COMMENT '创意ID',
    `plan_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT '关联的计划ID',
    `group_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT '关联的单元ID',
    `name` varchar(128) NOT NULL DEFAULT '' COMMENT '创意名称',
    `remark` text COMMENT '备注',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_platform_data_id` (`platform_type`, `data_id`),
    KEY `idx_plan_id` (`plan_id`),
    KEY `idx_group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='广告创意表';

-- 转化数据表
CREATE TABLE `platform_conversions` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `belong_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT '计划、创意、单元表主键',
    `belong_type` varchar(32) NOT NULL DEFAULT '' COMMENT 'plan/conversion/group',
    `plan_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT '关联的计划ID',
    `biz_date` varchar(32) NOT NULL DEFAULT '' COMMENT '数据汇总时间',
    `conversion_type` varchar(64) NOT NULL COMMENT '转化事件类型',
    `conversion_type_name` varchar(64) NOT NULL COMMENT '转化事件类型名称',
    `conversion_result` varchar(64) NOT NULL COMMENT '转化事件结果',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_biz_date_belong_type_belong_id` (`biz_date`, `belong_type`, `belong_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转化数据表';

-- 报表数据表
CREATE TABLE `platform_reports` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `belong_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT '计划、创意、单元表主键',
    `belong_type` varchar(32) NOT NULL DEFAULT '' COMMENT 'plan/conversion/group',
    `biz_date` varchar(32) NOT NULL DEFAULT '' COMMENT '数据汇总时间',
    `impression` bigint NOT NULL DEFAULT '0' COMMENT '展现量',
    `click` bigint NOT NULL DEFAULT '0' COMMENT '点击量',
    `cost` bigint NOT NULL DEFAULT '0' COMMENT '消费金额(分)',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_biz_date_belong_type_belong_id` (`biz_date`, `belong_type`, `belong_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报表数据表';

-- 推广成本导入任务表
CREATE TABLE `tasks` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `task_type` tinyint NOT NULL DEFAULT 1 COMMENT '任务类型：1-费用导入',
    `name` varchar(128) NOT NULL DEFAULT '' COMMENT '任务名称',
    `params` text NOT NULL COMMENT '任务参数JSON',
    `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '任务状态：pending-待处理 processing-处理中 success-成功 failed-失败',
    `error` varchar(255) DEFAULT NULL COMMENT '错误信息',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
    PRIMARY KEY (`id`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务表';