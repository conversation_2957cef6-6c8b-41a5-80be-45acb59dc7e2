-- 推广位表
CREATE TABLE IF NOT EXISTS `promotion_zones` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `plan_id` bigint unsigned NOT NULL COMMENT '投放计划ID',
  `zone_name` varchar(255) NOT NULL COMMENT '推广位名称',
  `pid` varchar(255) NOT NULL COMMENT 'PID',
  `zone_type` varchar(50) NOT NULL COMMENT '推广位类型',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_plan_id` (`plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='推广位表'; 