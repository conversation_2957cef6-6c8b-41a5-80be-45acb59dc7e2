{"name": "admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "icons": "node scripts/generate-icons.js"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.9", "echarts": "^5.6.0", "element-plus": "^2.9.4", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "pinia": "^3.0.0", "sass": "^1.84.0", "screenfull": "^6.0.2", "vue": "^3.5.13", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "sharp": "^0.33.5", "vite": "^6.1.0"}}