import sharp from 'sharp';
import fs from 'fs';
import path from 'path';

const PUBLIC_DIR = './public';

// 确保public目录存在
if (!fs.existsSync(PUBLIC_DIR)) {
  fs.mkdirSync(PUBLIC_DIR);
}

// 读取SVG文件
const svg = fs.readFileSync(path.join(PUBLIC_DIR, 'logo.svg'));

// 生成不同尺寸的PNG图标
async function generateIcons() {
  const sizes = {
    'favicon.png': 32, // 用32x32的PNG替代ICO
    'favicon-16x16.png': 16,
    'favicon-32x32.png': 32,
    'apple-touch-icon.png': 180,
    'android-chrome-192x192.png': 192,
    'android-chrome-512x512.png': 512,
    'og-image.png': [1200, 630] // 社交媒体分享图片使用特殊尺寸
  };

  for (const [filename, size] of Object.entries(sizes)) {
    const width = Array.isArray(size) ? size[0] : size;
    const height = Array.isArray(size) ? size[1] : size;

    await sharp(svg)
      .resize(width, height)
      .png()
      .toFile(path.join(PUBLIC_DIR, filename));
    
    console.log(`Generated ${filename}`);
  }
}

generateIcons().catch(console.error); 