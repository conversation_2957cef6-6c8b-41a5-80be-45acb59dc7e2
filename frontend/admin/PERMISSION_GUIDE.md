# 权限系统使用指南

## 概述

本权限系统基于RBAC（基于角色的访问控制）模型，支持菜单权限、按钮权限和数据权限的精细化控制。

## 权限级别

### 1. 菜单权限
控制用户可以访问哪些页面和菜单项。

### 2. 按钮权限
控制页面中特定按钮或功能的显示和可用性。

### 3. 数据权限
控制用户可以访问的数据范围（全部、本部门、仅自己等）。

## 使用方法

### 1. 路由权限配置

在路由配置中添加权限标识：

```javascript
{
  path: 'users',
  name: 'Users',
  component: () => import('@/views/users/index.vue'),
  meta: { 
    title: '用户管理',
    icon: 'UserFilled',
    permission: 'user.view' // 单个权限
  }
}

// 或者多个权限
{
  path: 'admin',
  name: 'Admin',
  component: () => import('@/views/admin/index.vue'),
  meta: { 
    title: '系统管理',
    icon: 'Setting',
    permissions: ['system.view', 'system.admin'] // 多个权限
  }
}
```

### 2. 权限指令使用

#### v-permission 指令

```vue
<template>
  <!-- 基础用法 -->
  <el-button v-permission="'user.create'">创建用户</el-button>
  
  <!-- 多个权限（任一） -->
  <el-button v-permission="['user.edit', 'user.update']">编辑用户</el-button>
  
  <!-- 多个权限（全部） -->
  <el-button v-permission.all="['user.view', 'user.edit']">管理用户</el-button>
  
  <!-- 无权限时禁用 -->
  <el-button v-permission.disabled="'admin.delete'">删除</el-button>
  
  <!-- 无权限时隐藏 -->
  <el-button v-permission.hidden="'super.admin'">超级管理</el-button>
</template>
```

#### 指令修饰符

- `.all`: 需要所有权限
- `.disabled`: 无权限时禁用元素
- `.hidden`: 无权限时隐藏元素（使用visibility）
- 默认：无权限时移除元素

### 3. 权限组件使用

#### PermissionWrapper 组件

```vue
<template>
  <!-- 基础用法 -->
  <PermissionWrapper permission="dashboard.view">
    <div>仪表盘内容</div>
  </PermissionWrapper>
  
  <!-- 多权限检查 -->
  <PermissionWrapper 
    :permissions="['report.view', 'report.export']"
    mode="any"
  >
    <div>报表内容</div>
  </PermissionWrapper>
  
  <!-- 全部权限 + 自定义无权限内容 -->
  <PermissionWrapper 
    :permissions="['system.admin', 'system.config']"
    mode="all"
    show-fallback
  >
    <div>系统管理内容</div>
    <template #fallback>
      <div>您需要系统管理员权限</div>
    </template>
  </PermissionWrapper>
</template>
```

#### 组件属性

- `permission`: 单个权限标识
- `permissions`: 权限数组
- `mode`: 检查模式（'any' | 'all'）
- `show-fallback`: 是否显示无权限时的占位内容
- `fallback-text`: 默认占位文本
- `wrapper-class`: 包装器CSS类名

### 4. 编程式权限检查

#### 在 Composition API 中使用

```vue
<script setup>
import { usePermission } from '@/hooks/usePermission'

const { hasPermission, hasAnyPermission, hasAllPermissions } = usePermission()

// 检查单个权限
const canCreateUser = hasPermission('user.create')

// 检查任一权限
const canManageUser = hasAnyPermission(['user.edit', 'user.delete'])

// 检查所有权限
const canFullAccess = hasAllPermissions(['admin.view', 'admin.edit'])

// 在方法中使用
const handleEdit = () => {
  if (!hasPermission('user.edit')) {
    ElMessage.error('无编辑权限')
    return
  }
  // 执行编辑操作
}
</script>
```

#### 在 Options API 中使用

```vue
<script>
export default {
  methods: {
    handleDelete() {
      if (!this.$permission.hasPermission('user.delete')) {
        this.$message.error('无删除权限')
        return
      }
      // 执行删除操作
    }
  }
}
</script>
```

### 5. 用户状态管理

#### 获取用户权限

```javascript
import { useUserStore } from '@/store/modules/user'

const userStore = useUserStore()

// 检查权限
const hasPermission = userStore.hasPermission('user.create')

// 获取权限列表
const permissions = userStore.permissions
const permissionCodes = userStore.permissionCodes

// 检查角色
const isAdmin = userStore.isAdmin
const isOperator = userStore.isOperator
```

## 权限编码规范

### 命名规则

权限编码采用模块.操作的格式：

```
{module}.{action}.{resource?}
```

### 常用权限编码

#### 基础操作
- `{module}.view` - 查看
- `{module}.create` - 创建
- `{module}.edit` - 编辑
- `{module}.delete` - 删除
- `{module}.export` - 导出

#### 示例
```
user.view          - 查看用户
user.create        - 创建用户
user.edit          - 编辑用户
user.delete        - 删除用户

system.account.view     - 查看系统账号
system.permission.edit  - 编辑系统权限

dashboard.view     - 查看仪表盘
dashboard.export   - 导出仪表盘数据

report.view        - 查看报表
report.export      - 导出报表
```

## 最佳实践

### 1. 权限粒度

- **菜单级别**: 控制大的功能模块访问
- **页面级别**: 控制具体页面访问
- **按钮级别**: 控制具体操作权限
- **字段级别**: 控制敏感数据显示

### 2. 权限设计原则

- **最小权限原则**: 用户默认没有任何权限，只授予必需的权限
- **职责分离**: 不同角色有不同的权限范围
- **权限继承**: 高级角色包含低级角色的权限

### 3. 开发建议

- 权限检查应该在组件渲染时进行，避免在数据获取后才检查
- 重要操作应该同时在前端和后端进行权限验证
- 权限编码应该与后端保持一致
- 使用权限组件而不是 v-if 来控制元素显示

### 4. 性能优化

- 权限信息在用户登录时一次性获取并缓存
- 避免在循环中进行权限检查
- 使用计算属性缓存权限检查结果

## 故障排除

### 常见问题

1. **权限检查不生效**
   - 确认用户已登录并获取到权限信息
   - 检查权限编码是否正确
   - 确认后端返回的权限数据格式

2. **菜单不显示**
   - 检查路由配置中的权限标识
   - 确认用户有对应的权限
   - 查看控制台是否有权限相关错误

3. **按钮权限异常**
   - 确认权限指令语法正确
   - 检查权限编码是否与后端一致
   - 验证用户权限列表中是否包含对应权限

### 调试方法

```javascript
// 在浏览器控制台中检查用户权限
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()

console.log('用户角色:', userStore.roles)
console.log('用户权限:', userStore.permissions)
console.log('权限编码:', userStore.permissionCodes)
```

## 更新记录

- 2024-01-XX: 初始版本
- 支持菜单权限、按钮权限
- 提供权限指令和组件
- 集成用户状态管理 