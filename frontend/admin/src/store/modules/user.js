import { defineStore } from 'pinia'
import { login, getInfo, logout } from '@/api/user'
import { getUserPermissions } from '@/api/role'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { USER_ROLE } from '@/constants'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken(),
    id: '',
    name: '',
    email: '',
    roles: [],
    permissions: [], // 用户权限列表
    permissionCodes: [], // 权限编码列表，用于快速查找
    // 工作模式相关状态
    workMode: 'traffic', // 当前工作模式：traffic-流量采买，ad-广告投放
    availableModes: [], // 用户可访问的工作模式列表
    menuItems: [], // 当前模式的菜单项
  }),
  
  getters: {
    // 是否是管理员
    isAdmin: (state) => state.roles.includes(USER_ROLE.ADMIN),
    // 是否是运营
    isOperator: (state) => state.roles.includes(USER_ROLE.OPERATOR),
    // 是否是财务
    isFinance: (state) => state.roles.includes(USER_ROLE.FINANCE),
    // 是否是媒介
    isMedia: (state) => state.roles.includes(USER_ROLE.MEDIA),
    // 是否是投手
    isPromoter: (state) => state.roles.includes(USER_ROLE.PROMOTER),
    // 是否是管理员或运营
    isAdminOrOperator: (state) => state.roles.includes(USER_ROLE.ADMIN) || state.roles.includes(USER_ROLE.OPERATOR),
    
    // 权限相关getters
    hasPermission: (state) => {
      return (permission) => {
        console.log('🔧 权限检查详细信息:', {
          检查权限: permission,
          用户角色: state.roles,
          角色数组长度: state.roles.length,
          是否管理员: state.roles.includes(USER_ROLE.ADMIN),
          用户权限码: state.permissionCodes,
          权限码数组长度: state.permissionCodes.length,
          权限码类型: typeof state.permissionCodes,
          是否为数组: Array.isArray(state.permissionCodes),
          是否包含该权限: state.permissionCodes.includes(permission),
          管理员角色常量: USER_ROLE.ADMIN,
          权限码内容预览: state.permissionCodes.slice(0, 5)
        })
        
        if (!permission) {
          console.log('🔧 权限为空，直接通过')
          return true
        }
        
        // 管理员拥有所有权限
        if (state.roles.includes(USER_ROLE.ADMIN)) {
          console.log('🔧 管理员权限通过')
          return true
        }
        
        const hasPermission = state.permissionCodes.includes(permission)
        console.log(`🔧 权限检查结果 [${permission}]:`, {
          最终结果: hasPermission,
          权限码在数组中的索引: state.permissionCodes.indexOf(permission),
          精确匹配检查: state.permissionCodes.filter(code => code === permission)
        })
        return hasPermission
      }
    },
    
    hasAnyPermission: (state) => {
      return (permissions) => {
        if (!permissions || permissions.length === 0) return true
        // 管理员拥有所有权限
        if (state.roles.includes(USER_ROLE.ADMIN)) return true
        return permissions.some(permission => state.permissionCodes.includes(permission))
      }
    },
    
    hasAllPermissions: (state) => {
      return (permissions) => {
        if (!permissions || permissions.length === 0) return true
        // 管理员拥有所有权限
        if (state.roles.includes(USER_ROLE.ADMIN)) return true
        return permissions.every(permission => state.permissionCodes.includes(permission))
      }
    }
  },
  
  actions: {
    // 登录
    async login(userInfo) {
      try {
        const { email, password } = userInfo
        const response = await login({ 
          Email: email, 
          Password: password 
        })
        
        console.log('Login response:', response) // 调试日志
        
        if (!response) {
          throw new Error('登录响应数据为空')
        }
        
        const { token, user } = response.data
        if (!token) {
          throw new Error('Token不能为空')
        }
        
        this.token = token
        if (user) {
          this.id = user.id || ''
          this.name = user.name || ''
          this.email = user.email || email
        }
        setToken(token)
        return response
      } catch (error) {
        console.error('Login error:', error) // 调试日志
        throw error
      }
    },

    // 获取用户信息
    async getInfo() {
      try {
        const response = await getInfo()
        console.log('GetInfo response:', response) // 调试日志
        
        if (!response) {
          throw new Error('获取用户信息响应数据为空')
        }
        
        const { id, name, realName, email, role, status } = response.data
        this.id = id
        this.name = realName || name // 优先使用真实姓名
        this.email = email
        this.roles = [role] // 保存用户角色
        console.log('Current roles:', this.roles) // 调试日志
        
        // 获取用户权限
        await this.getUserPermissions()
        
        // 验证权限获取结果
        console.log('🔧 用户信息获取完成后的状态验证:', {
          用户ID: this.id,
          用户角色: this.roles,
          权限数量: this.permissions.length,
          权限码数量: this.permissionCodes.length,
          是否有plan_create权限: this.permissionCodes.includes('plan:create'),
          前5个权限码: this.permissionCodes.slice(0, 5)
        })
        
        return response.data
      } catch (error) {
        console.error('GetInfo error:', error) // 调试日志
        throw error
      }
    },

    // 获取用户权限
    async getUserPermissions() {
      try {
        if (!this.id) {
          console.warn('🔧 User ID is empty, cannot fetch permissions')
          return
        }
        
        console.log('🔧 开始获取用户权限, UserID:', this.id)
        const response = await getUserPermissions(this.id)
        console.log('🔧 GetUserPermissions response:', response) // 调试日志
        
        // 详细检查响应数据结构
        console.log('🔧 响应数据详细分析:', {
          响应对象类型: typeof response,
          响应对象: response,
          是否有data字段: response?.hasOwnProperty('data'),
          data字段类型: typeof response?.data,
          data字段内容: response?.data,
          是否为数组: Array.isArray(response?.data)
        })
        
        if (response && response.data) {
          const permissionsArray = response.data || []
          console.log('🔧 权限数组处理:', {
            原始权限数组: permissionsArray,
            数组长度: permissionsArray.length,
            第一个权限对象: permissionsArray[0],
            提取权限码过程: permissionsArray.map(permission => {
              console.log('🔧 处理权限对象:', permission, '提取的code:', permission.code)
              return permission.code
            })
          })
          
          this.permissions = permissionsArray
          this.permissionCodes = permissionsArray.map(permission => permission.code)
          
          console.log('🔧 用户权限更新完成:', {
            权限数量: this.permissions.length,
            权限码列表: this.permissionCodes,
            完整权限信息: this.permissions,
            store中的权限数量: this.permissions.length,
            store中的权限码数量: this.permissionCodes.length
          })
          
          // 验证store是否真的被更新了
          setTimeout(() => {
            console.log('🔧 1秒后验证store状态:', {
              permissions长度: this.permissions.length,
              permissionCodes长度: this.permissionCodes.length,
              permissionCodes内容: this.permissionCodes,
              是否包含plan_create: this.permissionCodes.includes('plan:create')
            })
          }, 1000)
          
        } else {
          console.warn('🔧 权限响应数据为空:', response)
          this.permissions = []
          this.permissionCodes = []
        }
      } catch (error) {
        console.error('🔧 GetUserPermissions error:', error) // 调试日志
        // 权限获取失败不影响登录，只是没有权限而已
        this.permissions = []
        this.permissionCodes = []
      }
    },

    // 登出
    async logout() {
      try {
        await logout()
        this.resetToken()
      } catch (error) {
        console.error('Logout error:', error) // 调试日志
        // 即使登出失败也要清除本地状态
        this.resetToken()
        throw error
      }
    },

    // 检查是否有权限
    checkPermission(permission) {
      return this.hasPermission(permission)
    },

    // 检查是否有任一权限
    checkAnyPermission(permissions) {
      return this.hasAnyPermission(permissions)
    },

    // 检查是否有所有权限
    checkAllPermissions(permissions) {
      return this.hasAllPermissions(permissions)
    },

    // 设置工作模式
    setWorkMode(mode) {
      if (this.workMode !== mode) {
        this.workMode = mode
      }
    },

    // 设置可用模式列表
    setAvailableModes(modes) {
      this.availableModes = modes
    },

    // 设置菜单项
    setMenus(menus) {
      const newMenus = menus || []
      console.log('🔧 Store setMenus 被调用:', {
        当前菜单长度: this.menuItems?.length || 0,
        新菜单长度: newMenus.length,
        工作模式: this.workMode,
        是否为空数组清空: newMenus.length === 0
      })
      
      // 强制更新：总是创建新数组，确保引用变化
      this.menuItems = newMenus.length === 0 ? [] : [...newMenus]
      
      console.log('✅ 菜单已更新，当前菜单长度:', this.menuItems.length)
    },

    // 设置权限（用于模式切换）
    setPermissions(permissions) {
      console.log('🔧 setPermissions 被调用:', {
        调用来源: new Error().stack.split('\n')[1],
        传入的权限: permissions,
        权限数量: permissions?.length || 0,
        当前权限数量: this.permissionCodes.length,
        是否会覆盖: true
      })
      
      const newPermissions = permissions || []
      const newPermissionCodes = Array.isArray(newPermissions) 
        ? newPermissions 
        : newPermissions.map(permission => permission.code || permission)
      
      console.log('🔧 权限处理结果:', {
        新权限码: newPermissionCodes,
        新权限数量: newPermissionCodes.length,
        旧权限数量: this.permissionCodes.length,
        是否相同: JSON.stringify(this.permissionCodes) === JSON.stringify(newPermissionCodes)
      })
      
      // 检查权限是否真的发生了变化，避免不必要的更新
      if (JSON.stringify(this.permissionCodes) !== JSON.stringify(newPermissionCodes)) {
        console.log('🔧 权限确实发生变化，开始更新')
        this.permissions = newPermissions
        this.permissionCodes = newPermissionCodes
        
        console.log('🔧 权限更新完成，新状态:', {
          权限数量: this.permissions.length,
          权限码数量: this.permissionCodes.length,
          包含plan_create: this.permissionCodes.includes('plan:create')
        })
      } else {
        console.log('🔧 权限没有变化，跳过更新')
      }
    },

    // 重置token时也重置工作模式相关状态
    resetToken() {
      this.token = ''
      this.id = ''
      this.name = ''
      this.email = ''
      this.roles = []
      this.permissions = []
      this.permissionCodes = []
      this.workMode = 'traffic'
      this.availableModes = []
      this.menuItems = []
      removeToken()
    }
  }
}) 