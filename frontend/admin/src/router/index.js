import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/index.vue'
import {
  Odometer,
  UserFilled,
  Picture,
  Grid,
  DataLine,
  Operation,
  TrendCharts,
  WalletFilled,
  PictureFilled,
  Files,
  Setting,
  ShoppingBag
} from '@element-plus/icons-vue'

export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index.vue'),
    hidden: true
  },
  {
    path: '/403',
    component: () => import('@/views/error/403.vue'),
    hidden: true
  },
  {
    path: '/',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { 
          title: '仪表盘', 
          icon: 'Odometer',
          permission: 'dashboard:view'
        }
      },
      {
        path: 'agents',
        name: 'Agents',
        component: () => import('@/views/agents/index.vue'),
        meta: { 
          title: '代理管理', 
          icon: 'UserFilled',
          permission: 'agent:view'
        }
      },
      {
        path: 'media',
        name: 'Media',
        component: () => import('@/views/media/index.vue'),
        meta: { 
          title: '媒体管理', 
          icon: 'Picture',
          permission: 'media:view'
        }
      },
      {
        path: 'slots',
        name: 'Slots',
        component: () => import('@/views/slots/index.vue'),
        meta: { 
          title: '资源位管理', 
          icon: 'Grid',
          permission: 'slot:view'
        }
      },
      {
        path: 'ad-slot-plans',
        name: 'AdSlotPlans',
        component: () => import('@/views/ad-slot-plans/index.vue'),
        meta: { 
          title: '投放计划', 
          icon: 'DataLine',
          permission: 'plan:view'
        }
      },
      {
        path: 'ad-slot-plans/edit/:id?',
        name: 'AdSlotPlanEdit',
        component: () => import('@/views/ad-slot-plans/edit.vue'),
        meta: { 
          title: '编辑投放计划', 
          activeMenu: '/ad-slot-plans',
          permission: 'plan:edit'
        },
        hidden: true
      },
      {
        path: 'ad-slot-plans/view/:id',
        name: 'AdSlotPlanView',
        component: () => import('@/views/ad-slot-plans/view.vue'),
        meta: { 
          title: '投放计划详情', 
          activeMenu: '/ad-slot-plans',
          permission: 'plan:view'
        },
        hidden: true,
        beforeEnter: (to, from, next) => {
          // 检查 id 参数是否为数字
          const id = parseInt(to.params.id)
          if (isNaN(id)) {
            // 如果不是数字,重定向到列表页
            next('/ad-slot-plans')
          } else {
            next()
          }
        }
      },
      {
        path: 'delivery',
        name: 'Delivery',
        redirect: '/delivery/plan',
        meta: { 
          title: '投放管理', 
          icon: 'Operation',
          permission: 'delivery:view'
        },
        children: [
          {
            path: 'plan',
            name: 'DeliveryPlan',
            component: () => import('@/views/delivery/plan/index.vue'),
            meta: { 
              title: '计划管理',
              permission: 'delivery:plan:view'
            }
          },
          {
            path: 'command',
            name: 'DeliveryCommand',
            component: () => import('@/views/delivery/command/index.vue'),
            meta: { 
              title: '口令管理',
              permission: 'delivery:command:view'
            }
          },
          {
            path: 'cost',
            name: 'DeliveryCost',
            component: () => import('@/views/delivery/cost/index.vue'),
            meta: { 
              title: '费用管理',
              permission: 'delivery:cost:view'
            }
          },
          {
            path: 'model',
            name: 'DeliveryModel',
            component: () => import('@/views/delivery/model/index.vue'),
            meta: { 
              title: '模型管理',
              permission: 'delivery:model:view'
            }
          },
          {
            path: 'report',
            name: 'DeliveryReport',
            component: () => import('@/views/delivery/report/index.vue'),
            meta: { 
              title: '查看报表',
              permission: 'delivery:report:view'
            }
          }
        ]
      },
      {
        path: 'reports',
        name: 'Reports',
        redirect: '/reports/daily',
        meta: { 
          title: '数据报表', 
          icon: 'Files',
          permission: 'report:view'
        },
        children: [
          {
            path: 'daily',
            name: 'DailyReport',
            component: () => import('@/views/reports/daily-report.vue'),
            meta: { 
              title: '日报表',
              permission: 'report:daily:view'
            }
          },
          {
            path: 'weekly',
            name: 'WeeklyReport',
            component: () => import('@/views/reports/weekly-report.vue'),
            meta: { 
              title: '周报表',
              permission: 'report:weekly:view'
            }
          },
          {
            path: 'lighthouse',
            name: 'LighthouseData',
            component: () => import('@/views/reports/lighthouse-data.vue'),
            meta: { 
              title: '灯火数据',
              permission: 'report:lighthouse:view'
            }
          }
        ]
      },
      {
        path: 'finance',
        name: 'Finance',
        redirect: '/finance/taobao-pids',
        meta: { 
          title: '财务管理', 
          icon: 'Operation',
          permission: 'finance:view'
        },
        children: [
          {
            path: 'taobao-pids',
            name: 'TaobaoPids',
            component: () => import('@/views/finance/taobao-pids/index.vue'),
            meta: { 
              title: '淘联链接',
              permission: 'finance:taobao:view'
            }
          }
        ]
      },
      {
        path: 'creative',
        name: 'Creative',
        component: () => import('@/views/creative/list.vue'),
        meta: { 
          title: '创意管理', 
          icon: 'PictureFilled',
          permission: 'creative:view'
        }
      },
      {
        path: 'creative/edit/:id?',
        name: 'CreativeEdit',
        component: () => import('@/views/creative/index.vue'),
        meta: { 
          title: '编辑创意', 
          activeMenu: '/creative',
          permission: 'creative:edit'
        },
        hidden: true
      },
      {
        path: 'xiaohongshu',
        name: 'Xiaohongshu',
        redirect: '/xiaohongshu/content-data',
        meta: {
          title: '小红书投放',
          icon: 'ShoppingBag',
          permission: 'xiaohongshu:view'
        },
        children: [
          {
            path: 'content-data',
            name: 'XiaohongshuContentData',
            component: () => import('@/views/xiaohongshu/content-data/index.vue'),
            meta: {
              title: '小红书内容数据',
              permission: 'xiaohongshu:content:view'
            }
          },
          {
            path: 'ad-plans',
            name: 'XiaohongshuAdPlans',
            component: () => import('@/views/xiaohongshu/ad-plans/index.vue'),
            meta: {
              title: '计划列表',
              permission: 'xiaohongshu:plans:view'
            }
          },
          {
            path: 'passwords',
            name: 'XiaohongshuPasswords',
            component: () => import('@/views/xiaohongshu/passwords/index.vue'),
            meta: {
              title: '口令列表',
              permission: 'xiaohongshu:passwords:view'
            }
          }
        ]
      },
      {
        path: 'system',
        name: 'System',
        redirect: '/system/account',
        meta: {
          title: '系统管理',
          icon: 'Setting',
          permission: 'system:view'
        },
        children: [
          {
            path: 'account',
            name: 'SystemAccount',
            component: () => import('@/views/system/account/index.vue'),
            meta: { 
              title: '账号管理',
              permission: 'system:account:view'
            }
          },
          {
            path: 'lighthouse-account',
            name: 'SystemLighthouseAccount',
            component: () => import('@/views/system/lighthouse-account/index.vue'),
            meta: { 
              title: '投放账号管理',
              permission: 'system:lighthouse:view'
            }
          },
          {
            path: 'permission',
            name: 'SystemPermission',
            component: () => import('@/views/system/permission/index.vue'),
            meta: { 
              title: '权限管理',
              permission: 'system:permission:view'
            }
          },
          {
            path: 'department',
            name: 'SystemDepartment',
            component: () => import('@/views/system/department/index.vue'),
            meta: { 
              title: '部门管理',
              permission: 'system:department:view'
            }
          }
        ]
      },
      {
        path: 'plugin-dashboard/:mediaId',
        name: 'PluginDashboard',
        component: () => import('@/views/plugin/dashboard.vue'),
        meta: { 
          title: '插件数据看板', 
          activeMenu: '/media',
          hideInMenu: true,
          permission: 'plugin:dashboard:view'
        },
        hidden: true
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior: () => ({ top: 0 })
})

export default router 