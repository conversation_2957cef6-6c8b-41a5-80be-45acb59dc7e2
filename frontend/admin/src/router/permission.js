import router from './index'
import { useUserStore } from '@/store/modules/user'
import { PermissionUtils } from '@/utils/permission'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'

NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/404', '/401', '/403']

router.beforeEach(async (to, from, next) => {
  NProgress.start()
  
  const hasToken = getToken()
  
  if (hasToken) {
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else {
      const userStore = useUserStore()
      const hasRoles = userStore.roles && userStore.roles.length > 0
      
      if (hasRoles) {
        // 检查路由权限
        const permissionCheckResult = PermissionUtils.checkRoutePermissionDetail(to)
        
        // 添加详细的路由权限检查日志
        console.log('🔧 路由权限守卫检查:', {
          目标路由: to.path,
          路由名称: to.meta?.title || to.name,
          路由权限要求: to.meta?.permission || to.meta?.permissions,
          权限检查结果: permissionCheckResult,
          用户权限: userStore.permissionCodes,
          用户角色: userStore.roles,
          是否管理员: userStore.isAdmin
        })
        
        if (!permissionCheckResult.hasPermission) {
          // 无权限，跳转到403页面并传递详细权限信息
          console.error('🚫 路由权限检查失败:', {
            路径: to.path,
            需要权限: permissionCheckResult.requiredPermissions,
            缺少权限: permissionCheckResult.missingPermissions,
            用户权限: userStore.permissionCodes
          })
          
          const query = {
            routePath: to.path,
            routeName: to.meta?.title || to.name
          }
          
          // 添加权限信息
          if (permissionCheckResult.requiredPermissions.length > 0) {
            query.requiredPermission = permissionCheckResult.requiredPermissions[0]
            if (permissionCheckResult.requiredPermissions.length > 1) {
              query.requiredPermissions = JSON.stringify(permissionCheckResult.requiredPermissions)
            }
          }
          
          // 添加缺失权限信息
          if (permissionCheckResult.missingPermissions.length > 0) {
            query.missingPermissions = JSON.stringify(permissionCheckResult.missingPermissions)
          }
          
          next({ path: '/403', query })
          NProgress.done()
          return
        }
        
        next()
      } else {
        try {
          await userStore.getInfo()
          // 重新检查权限
          const permissionCheckResult = PermissionUtils.checkRoutePermissionDetail(to)
          if (!permissionCheckResult.hasPermission) {
            const query = {
              routePath: to.path,
              routeName: to.meta?.title || to.name
            }
            
            if (permissionCheckResult.requiredPermissions.length > 0) {
              query.requiredPermission = permissionCheckResult.requiredPermissions[0]
              if (permissionCheckResult.requiredPermissions.length > 1) {
                query.requiredPermissions = JSON.stringify(permissionCheckResult.requiredPermissions)
              }
            }
            
            if (permissionCheckResult.missingPermissions.length > 0) {
              query.missingPermissions = JSON.stringify(permissionCheckResult.missingPermissions)
            }
            
            next({ path: '/403', query })
            NProgress.done()
            return
          }
          
          next({ ...to, replace: true })
        } catch (error) {
          await userStore.resetToken()
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      }
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
}) 