import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/modules/user'

// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  timeout: 20000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 从 store 获取 token
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers['Authorization'] = `Bearer ${userStore.token}`
    }
    return config
  },
  error => {
    console.error(error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 如果是blob响应，直接返回整个response对象
    if (response.config.responseType === 'blob') {
      return response
    }
    
    const res = response.data

    // 判断响应是否成功
    // 兼容不同的成功码：0 或 200
    const isSuccess = res.code === 0 || res.code === 200 || response.status === 200

    // 如果响应不成功,显示错误信息
    if (!isSuccess) {
      ElMessage({
        message: res.message || '请求失败',
        type: 'error',
        duration: 30 * 1000
      })
      return Promise.reject(new Error(res.message || '请求失败'))
    }

    // 对于普通JSON接口，返回解包后的数据结构，保持原有行为
    return res
  },
  error => {
    console.error('请求错误:', error)
    const message = error.response?.data?.message || error.message || '请求失败'
    
    ElMessage({
      message,
      type: 'error',
      duration: 30 * 1000
    })
    
    // 如果是 401 错误,说明 token 失效,需要重新登录
    if (error.response?.status === 401) {
      const userStore = useUserStore()
      userStore.logout()
      window.location.reload()
    }
    
    return Promise.reject(error)
  }
)

export default service 