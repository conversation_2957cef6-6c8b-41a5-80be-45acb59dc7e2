/**
 * 通用工具函数
 */

// 从date.js导入日期格式化函数
import { formatDate as _formatDate } from './date'

/**
 * 格式化日期时间
 * @param {string|Date} date 日期
 * @param {string} format 格式 默认 YYYY-MM-DD HH:mm:ss
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  return _formatDate(date, format)
}

/**
 * 格式化金额
 * @param {number} amount 金额
 * @param {number} decimals 小数位数，默认2位
 * @param {boolean} thousands 是否千分位，默认true
 * @returns {string} 格式化后的金额字符串
 */
export function formatAmount(amount, decimals = 2, thousands = true) {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return '0.00'
  }
  
  // 转换为数字
  const num = Number(amount)
  
  // 保留指定小数位
  const fixed = num.toFixed(decimals)
  
  // 如果不需要千分位，直接返回
  if (!thousands) {
    return fixed
  }
  
  // 添加千分位分隔符
  const parts = fixed.split('.')
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  
  return parts.join('.')
}

/**
 * 格式化百分比
 * @param {number} value 数值
 * @param {number} decimals 小数位数，默认2位
 * @returns {string} 格式化后的百分比字符串
 */
export function formatPercent(value, decimals = 2) {
  if (value === null || value === undefined || isNaN(value)) {
    return '0.00%'
  }
  
  const num = Number(value)
  return (num * 100).toFixed(decimals) + '%'
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @param {number} decimals 小数位数，默认2位
 * @returns {string} 格式化后的文件大小字符串
 */
export function formatFileSize(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**
 * 格式化数字，添加千分位分隔符
 * @param {number} num 数字
 * @returns {string} 格式化后的数字字符串
 */
export function formatNumber(num) {
  if (num === null || num === undefined || isNaN(num)) {
    return '0'
  }
  
  return Number(num).toLocaleString()
}

/**
 * 格式化倍率
 * @param {number} value 数值
 * @param {number} decimals 小数位数，默认2位
 * @returns {string} 格式化后的倍率字符串
 */
export function formatMultiple(value, decimals = 2) {
  if (value === null || value === undefined || isNaN(value)) {
    return '0.00x'
  }
  
  const num = Number(value)
  return num.toFixed(decimals) + 'x'
}

/**
 * 安全解析JSON
 * @param {string} str JSON字符串
 * @param {any} defaultValue 默认值
 * @returns {any} 解析结果
 */
export function safeJsonParse(str, defaultValue = null) {
  try {
    return JSON.parse(str)
  } catch (e) {
    return defaultValue
  }
}

/**
 * 生成随机字符串
 * @param {number} length 长度，默认8位
 * @returns {string} 随机字符串
 */
export function generateRandomString(length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间，毫秒
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 时间限制，毫秒
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
  let inThrottle
  return function() {
    const args = arguments
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 深拷贝对象
 * @param {any} obj 要拷贝的对象
 * @returns {any} 深拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

// 重新导出date.js中的其他函数，方便统一导入
export {
  formatDateTime,
  formatDateOnly,
  formatTimeOnly,
  getRelativeTime,
  getTodayStart,
  getTodayEnd,
  getWeekStart,
  getMonthStart,
  getYearStart,
  isSameDay,
  getDateRangeText
} from './date' 