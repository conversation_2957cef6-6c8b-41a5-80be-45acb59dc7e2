import { useUserStore } from '@/store/modules/user'

/**
 * 权限指令
 * 用法：v-permission="'user.create'" 或 v-permission="['user.create', 'user.edit']"
 */
export const permissionDirective = {
  mounted(el, binding, vnode) {
    checkPermission(el, binding)
  },
  updated(el, binding, vnode) {
    checkPermission(el, binding)
  }
}

/**
 * 检查权限并控制元素显示/隐藏
 * @param {HTMLElement} el DOM元素
 * @param {Object} binding 指令绑定对象
 */
function checkPermission(el, binding) {
  const { value, modifiers } = binding
  const userStore = useUserStore()
  
  if (!value) {
    return // 没有权限要求，默认显示
  }
  
  let hasPermission = false
  
  if (Array.isArray(value)) {
    // 权限数组
    if (modifiers.all) {
      // 需要所有权限
      hasPermission = userStore.hasAllPermissions(value)
    } else {
      // 需要任一权限（默认）
      hasPermission = userStore.hasAnyPermission(value)
    }
  } else {
    // 单个权限
    hasPermission = userStore.hasPermission(value)
  }
  
  if (!hasPermission) {
    // 无权限时的处理方式
    if (modifiers.hidden) {
      // 使用 visibility: hidden
      el.style.visibility = 'hidden'
    } else if (modifiers.disabled) {
      // 禁用元素
      el.disabled = true
      el.classList.add('is-disabled')
    } else {
      // 默认移除元素
      el.parentNode && el.parentNode.removeChild(el)
    }
  } else {
    // 有权限时恢复元素状态
    if (modifiers.hidden) {
      el.style.visibility = 'visible'
    } else if (modifiers.disabled) {
      el.disabled = false
      el.classList.remove('is-disabled')
    }
  }
}

/**
 * 权限检查工具函数
 */
export const PermissionUtils = {
  /**
   * 检查是否有权限
   * @param {string|Array} permission 权限标识
   * @param {Object} options 选项
   * @returns {boolean}
   */
  hasPermission(permission, options = {}) {
    const userStore = useUserStore()
    
    if (!permission) return true
    
    if (Array.isArray(permission)) {
      return options.all 
        ? userStore.hasAllPermissions(permission)
        : userStore.hasAnyPermission(permission)
    } else {
      return userStore.hasPermission(permission)
    }
  },

  /**
   * 详细权限检查，返回检查结果和缺失的权限
   * @param {string|Array} permission 权限标识
   * @param {Object} options 选项
   * @returns {Object} 详细检查结果
   */
  checkPermissionDetail(permission, options = {}) {
    const userStore = useUserStore()
    
    if (!permission) {
      return { 
        hasPermission: true,
        requiredPermissions: [],
        missingPermissions: []
      }
    }
    
    const requiredPermissions = Array.isArray(permission) ? permission : [permission]
    const userPermissionCodes = userStore.permissionCodes || []
    
    // 管理员拥有所有权限
    if (userStore.isAdmin) {
      return {
        hasPermission: true,
        requiredPermissions,
        missingPermissions: []
      }
    }
    
    const missingPermissions = requiredPermissions.filter(
      perm => !userPermissionCodes.includes(perm)
    )
    
    let hasPermission = false
    if (Array.isArray(permission)) {
      hasPermission = options.all 
        ? missingPermissions.length === 0
        : missingPermissions.length < requiredPermissions.length
    } else {
      hasPermission = missingPermissions.length === 0
    }
    
    return {
      hasPermission,
      requiredPermissions,
      missingPermissions
    }
  },

  /**
   * 检查路由权限
   * @param {Object} route 路由对象
   * @returns {boolean}
   */
  checkRoutePermission(route) {
    if (!route || !route.meta) return true
    
    const { permission, permissions } = route.meta
    
    if (permission) {
      return this.hasPermission(permission)
    }
    
    if (permissions && permissions.length > 0) {
      return this.hasPermission(permissions)
    }
    
    return true
  },

  /**
   * 详细检查路由权限
   * @param {Object} route 路由对象
   * @returns {Object} 详细检查结果
   */
  checkRoutePermissionDetail(route) {
    if (!route || !route.meta) {
      return { 
        hasPermission: true,
        requiredPermissions: [],
        missingPermissions: []
      }
    }
    
    const { permission, permissions } = route.meta
    
    if (permission) {
      return this.checkPermissionDetail(permission)
    }
    
    if (permissions && permissions.length > 0) {
      return this.checkPermissionDetail(permissions)
    }
    
    return { 
      hasPermission: true,
      requiredPermissions: [],
      missingPermissions: []
    }
  },

  /**
   * 过滤有权限的菜单项
   * @param {Array} menuItems 菜单项数组
   * @returns {Array} 过滤后的菜单项
   */
  filterMenuItems(menuItems) {
    if (!Array.isArray(menuItems)) {
      return []
    }
    
    return menuItems.map(item => {
      // 创建浅拷贝避免修改原始数据
      const newItem = { ...item }
      
      // 检查当前菜单项权限
      if (!this.checkRoutePermission(newItem)) {
        return null
      }
      
      // 递归处理子菜单
      if (newItem.children && newItem.children.length > 0) {
        const filteredChildren = this.filterMenuItems(newItem.children)
        newItem.children = filteredChildren
        
        // 如果所有子菜单都没有权限，且当前菜单需要重定向，则不显示
        if (filteredChildren.length === 0 && newItem.redirect) {
          return null
        }
      }
      
      return newItem
    }).filter(item => item !== null)
  },

  /**
   * 获取用户权限编码列表
   * @returns {Array}
   */
  getPermissionCodes() {
    const userStore = useUserStore()
    return userStore.permissionCodes || []
  },

  /**
   * 获取用户权限详情列表
   * @returns {Array}
   */
  getPermissions() {
    const userStore = useUserStore()
    return userStore.permissions || []
  },

  /**
   * 生成权限不足的错误信息
   * @param {string|Array} requiredPermissions 需要的权限
   * @param {string} context 上下文信息
   * @returns {string}
   */
  generatePermissionErrorMessage(requiredPermissions, context = '') {
    const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions]
    const permissionText = permissions.length === 1 
      ? `权限 "${permissions[0]}"` 
      : `权限 "${permissions.join('", "')}"`
    
    return context 
      ? `执行 "${context}" 操作需要 ${permissionText}，请联系管理员获取相应权限。`
      : `需要 ${permissionText}，请联系管理员获取相应权限。`
  }
}

export default {
  install(app) {
    // 注册权限指令
    app.directive('permission', permissionDirective)
    
    // 将权限工具函数添加到全局属性
    app.config.globalProperties.$permission = PermissionUtils
  }
} 