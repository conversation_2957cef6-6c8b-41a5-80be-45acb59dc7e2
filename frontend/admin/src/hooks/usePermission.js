import { useUserStore } from '@/store/modules/user'
import { ROLE } from '@/constants'


export function usePermission() {
  const userStore = useUserStore()

  /**
   * 判断是否拥有指定角色
   * @param {number|number[]} roles 角色ID或角色ID数组
   * @returns {boolean}
   */
  const hasRole = (roles) => {
    const userRoles = userStore.roles || []
    if (Array.isArray(roles)) {
      return roles.some(role => userRoles.includes(role))
    }
    return userRoles.includes(roles)
  }

  /**
   * 判断是否拥有权限
   * @param {string|string[]} permissions 权限标识或权限标识数组
   * @returns {boolean}
   */
  const hasPermission = (permissions) => {
    if (!permissions) return true
    
    // 管理员拥有所有权限
    if (hasRole(ROLE.ADMIN)) {
      return true
    }

    // 将单个权限转换为数组
    const permissionList = Array.isArray(permissions) ? permissions : [permissions]

    // 使用用户store中的权限检查方法
    return userStore.hasAnyPermission(permissionList)
  }

  /**
   * 判断是否拥有任一权限
   * @param {string[]} permissions 权限标识数组
   * @returns {boolean}
   */
  const hasAnyPermission = (permissions) => {
    if (!permissions || permissions.length === 0) return true
    
    // 管理员拥有所有权限
    if (hasRole(ROLE.ADMIN)) {
      return true
    }

    return userStore.hasAnyPermission(permissions)
  }

  /**
   * 判断是否拥有所有权限
   * @param {string[]} permissions 权限标识数组
   * @returns {boolean}
   */
  const hasAllPermissions = (permissions) => {
    if (!permissions || permissions.length === 0) return true
    
    // 管理员拥有所有权限
    if (hasRole(ROLE.ADMIN)) {
      return true
    }

    return userStore.hasAllPermissions(permissions)
  }

  /**
   * 检查路由权限
   * @param {Object} route 路由对象
   * @returns {boolean}
   */
  const checkRoutePermission = (route) => {
    if (!route || !route.meta) return true
    
    const { permission, permissions } = route.meta
    
    if (permission) {
      return hasPermission(permission)
    }
    
    if (permissions && permissions.length > 0) {
      return hasAnyPermission(permissions)
    }
    
    return true
  }

  /**
   * 过滤有权限的路由
   * @param {Array} routes 路由数组
   * @returns {Array} 过滤后的路由数组
   */
  const filterRoutes = (routes) => {
    const filteredRoutes = []
    
    for (const route of routes) {
      // 检查当前路由权限
      if (checkRoutePermission(route)) {
        const filteredRoute = { ...route }
        
        // 递归处理子路由
        if (route.children && route.children.length > 0) {
          filteredRoute.children = filterRoutes(route.children)
          // 如果所有子路由都没有权限，且当前路由需要重定向，则不显示
          if (filteredRoute.children.length === 0 && route.redirect) {
            continue
          }
        }
        
        filteredRoutes.push(filteredRoute)
      }
    }
    
    return filteredRoutes
  }

  /**
   * 获取用户所有权限编码
   * @returns {Array} 权限编码数组
   */
  const getPermissionCodes = () => {
    return userStore.permissionCodes || []
  }

  /**
   * 获取用户权限详情
   * @returns {Array} 权限详情数组
   */
  const getPermissions = () => {
    return userStore.permissions || []
  }

  return {
    hasRole,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    checkRoutePermission,
    filterRoutes,
    getPermissionCodes,
    getPermissions
  }
} 