<template>
  <div 
    class="app-wrapper"
    :class="{ 
      mobile: isMobile,
      'hide-sidebar': !showSidebar
    }"
  >
    <!-- 遮罩层 -->
    <div 
      v-if="showSidebar && isMobile" 
      class="drawer-bg" 
      @click="handleClickOutside"
    />
    
    <!-- 侧边栏 -->
    <div class="sidebar-container">
      <div class="logo-container">
        <h1 class="sidebar-title">{{ title }}</h1>
      </div>
      
      <!-- 菜单过渡动画包装 -->
      <transition name="menu-fade" mode="out-in" appear>
        <el-menu
          :key="menuKey"
          :default-active="activeMenu"
          :default-openeds="defaultOpeneds"
          :router="true"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          mode="vertical"
        >
          <template v-for="route in routes" :key="route.path">
            <template v-if="!route.hidden">
              <!-- 没有子菜单的情况 -->
              <el-menu-item 
                v-if="!route.children || route.children.length === 0" 
                :index="route.path.startsWith('/') ? route.path : '/' + route.path"
              >
                <el-icon v-if="route.meta?.icon">
                  <component :is="icons[route.meta.icon]" />
                </el-icon>
                <template #title>{{ route.meta?.title }}</template>
              </el-menu-item>

              <!-- 有子菜单的情况 -->
              <el-sub-menu 
                v-else-if="route.children && route.children.length > 0" 
                :index="route.path.startsWith('/') ? route.path : '/' + route.path"
              >
                <template #title>
                  <el-icon v-if="route.meta?.icon">
                    <component :is="icons[route.meta.icon]" />
                  </el-icon>
                  <span>{{ route.meta?.title }}</span>
                </template>
                
                <template v-for="child in route.children" :key="child.path">
                  <el-menu-item 
                    v-if="!child.hidden"
                    :index="child.path.startsWith('/') ? child.path : '/' + (route.path === '/' ? '' : route.path) + '/' + child.path"
                  >
                    <el-icon v-if="child.meta?.icon">
                      <component :is="icons[child.meta.icon]" />
                    </el-icon>
                    <template #title>{{ child.meta?.title }}</template>
                  </el-menu-item>
                </template>
              </el-sub-menu>
            </template>
          </template>
        </el-menu>
      </transition>
    </div>
    
    <!-- 主容器 -->
    <div class="main-container">
      <!-- 头部 -->
      <div class="navbar">
        <!-- 汉堡菜单 -->
        <div class="hamburger-container" @click="toggleSidebar">
          <el-icon :size="20">
            <Fold v-if="showSidebar" />
            <Expand v-else />
          </el-icon>
        </div>

        <!-- 工作模式指示器 -->
        <div class="work-mode-indicator">
          <transition name="mode-change" mode="out-in" appear>
            <div 
              :key="`mode-badge-${userStore.workMode}-${menuForceUpdateKey}`" 
              class="mode-badge"
              v-if="userStore.workMode"
            >
              <el-icon class="mode-badge-icon">
                <ShoppingCart v-if="userStore.workMode === 'traffic'" />
                <TrendCharts v-else />
              </el-icon>
              <span class="mode-badge-text">
                {{ userStore.workMode === 'traffic' ? '流量采买' : '广告投放' }}
              </span>
            </div>
          </transition>
        </div>

        <div class="right-menu">
          <!-- 全屏按钮 -->
          <div class="right-menu-item hover-effect" @click="toggleFullScreen">
            <el-tooltip
              effect="dark"
              :content="isFullscreen ? '退出全屏' : '全屏'"
              placement="bottom"
            >
              <el-icon :size="20">
                <FullScreen v-if="!isFullscreen" />
                <Aim v-else />
              </el-icon>
            </el-tooltip>
          </div>

          <!-- 工作模式切换器 -->
          <div class="right-menu-item">
            <WorkModeSwitcher />
          </div>
          
          <el-dropdown class="avatar-container" trigger="click">
            <div class="avatar-wrapper">
              <el-avatar 
                :size="32"
                class="user-avatar"
                :style="{ backgroundColor: '#409EFF' }"
              >
                {{ userStore.name?.charAt(0)?.toUpperCase() || 'U' }}
              </el-avatar>
              <span class="user-name">{{ userStore.name || userStore.email }}</span>
              <el-icon class="el-icon-caret-bottom"><CaretBottom /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleLogout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <!-- 主要内容 -->
      <div class="app-main">
        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted, nextTick, watch, markRaw } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { 
  CaretBottom, 
  SwitchButton, 
  FullScreen, 
  Aim,
  Fold,
  Expand,
  Odometer,
  UserFilled,
  Picture,
  Grid,
  DataLine,
  Operation,
  TrendCharts,
  PictureFilled,
  Files,
  Setting,
  ShoppingCart
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import screenfull from 'screenfull'
import { PermissionUtils } from '@/utils/permission'
import WorkModeSwitcher from '@/components/WorkModeSwitcher.vue'

const title = 'Admin Pro'
const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const isFullscreen = ref(false)
const isMobile = ref(false)
const showSidebar = ref(true)
const menuForceUpdateKey = ref(0) // 简化为单一的强制更新 key

// 图标映射 - 使用 markRaw 防止图标组件被响应式化
const icons = {
  Odometer: markRaw(Odometer),
  UserFilled: markRaw(UserFilled),
  Picture: markRaw(Picture),
  Grid: markRaw(Grid),
  DataLine: markRaw(DataLine),
  Operation: markRaw(Operation),
  TrendCharts: markRaw(TrendCharts),
  PictureFilled: markRaw(PictureFilled),
  Files: markRaw(Files),
  Setting: markRaw(Setting)
}

// 获取菜单项 - 重新设计计算逻辑
const routes = computed(() => {
  // 添加强制更新依赖，确保响应式更新
  const _ = menuForceUpdateKey.value
  
  console.log('🔄 routes计算属性重新执行:', {
    forceUpdateKey: menuForceUpdateKey.value,
    workMode: userStore.workMode,
    menuItemsLength: userStore.menuItems?.length || 0
  })
  
  try {
    let menuItems = []
    
    // 优先使用store中的菜单项
    if (userStore.menuItems && userStore.menuItems.length > 0) {
      menuItems = userStore.menuItems
      console.log('📋 使用store中的菜单项:', menuItems.length, '个')
    } else {
      // 兜底使用路由配置中的菜单
      const rootRoute = router.options.routes.find(route => route.path === '/')
      menuItems = rootRoute ? rootRoute.children : []
      console.log('📋 使用路由配置中的菜单:', menuItems.length, '个')
    }
    
    // 创建深拷贝避免修改原始数据
    const clonedMenuItems = JSON.parse(JSON.stringify(menuItems))
    
    // 对复制的菜单进行权限过滤
    const filteredRoutes = PermissionUtils.filterMenuItems(clonedMenuItems)
    
    console.log('✅ 菜单过滤完成:', filteredRoutes.length, '个菜单项')
    
    return filteredRoutes
  } catch (error) {
    console.warn('菜单权限过滤失败:', error)
    return []
  }
})

// 菜单唯一 key - 结合多个因素确保唯一性
const menuKey = computed(() => {
  return `menu-${userStore.workMode}-${routes.value.length}-${menuForceUpdateKey.value}-${Date.now()}`
})

// 获取默认展开的菜单项
const defaultOpeneds = computed(() => {
  const openeds = []
  const routesList = routes.value || []
  
  routesList.forEach(route => {
    if (!route.hidden && route.children && route.children.length > 0) {
      const routePath = route.path.startsWith('/') ? route.path : '/' + route.path
      openeds.push(routePath)
    }
  })
  return openeds
})

const activeMenu = computed(() => route.path)

// 强制更新菜单的函数
const forceUpdateMenu = () => {
  console.log('🚀 强制更新菜单')
  menuForceUpdateKey.value++
}

// 监听工作模式变化 - 简化监听逻辑
watch(
  () => userStore.workMode,
  (newMode, oldMode) => {
    if (newMode !== oldMode) {
      console.log('👀 工作模式变化:', { 旧模式: oldMode, 新模式: newMode })
      nextTick(() => {
        forceUpdateMenu()
      })
    }
  },
  { immediate: false }
)

// 监听菜单项变化 - 使用深度监听确保能捕获到变化
watch(
  () => userStore.menuItems,
  (newMenuItems, oldMenuItems) => {
    const newLength = newMenuItems?.length || 0
    const oldLength = oldMenuItems?.length || 0
    
    console.log('👀 菜单项变化监听:', {
      新菜单长度: newLength,
      旧菜单长度: oldLength,
      工作模式: userStore.workMode
    })
    
    // 只要菜单项发生变化就立即更新
    if (newLength !== oldLength || JSON.stringify(newMenuItems) !== JSON.stringify(oldMenuItems)) {
      console.log('🚀 立即更新菜单')
      nextTick(() => {
        forceUpdateMenu()
      })
    }
  },
  { 
    immediate: false,
    deep: false // 不需要深度监听，只监听数组本身的变化
  }
)

// 检查是否是移动设备
const checkIsMobile = () => {
  const rect = document.body.getBoundingClientRect()
  isMobile.value = rect.width - 1 < 992
  showSidebar.value = !isMobile.value
}

// 切换侧边栏
const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value
}

// 点击遮罩层
const handleClickOutside = () => {
  showSidebar.value = false
}

const handleLogout = async () => {
  try {
    await userStore.logout()
    router.push('/login')
  } catch (error) {
    console.error(error)
  }
}

// 切换全屏
const toggleFullScreen = () => {
  if (!screenfull.isEnabled) {
    ElMessage({
      message: '你的浏览器不支持全屏',
      type: 'warning'
    })
    return false
  }
  screenfull.toggle()
}

// 监听全屏变化
const change = () => {
  isFullscreen.value = screenfull.isFullscreen
}

// 组件挂载时添加监听器
onMounted(() => {
  if (screenfull.isEnabled) {
    screenfull.on('change', change)
  }
  checkIsMobile()
  window.addEventListener('resize', checkIsMobile)
})

// 组件卸载时移除监听器
onUnmounted(() => {
  if (screenfull.isEnabled) {
    screenfull.off('change', change)
  }
  window.removeEventListener('resize', checkIsMobile)
})
</script>

<style lang="scss" scoped>
// 移动端断点
$mobile-width: 992px;

.app-wrapper {
  display: flex;
  height: 100vh;
  width: 100%;
  position: relative;
  
  &.mobile {
    .sidebar-container {
      transition: transform .3s;
      width: 210px;
    }
    
    &.hide-sidebar {
      .sidebar-container {
        transform: translate3d(-210px, 0, 0);
      }
      
      .main-container {
        margin-left: 0;
      }
    }
    
    .main-container {
      margin-left: 0;
      transition: margin-left .3s;
    }
    
    .right-menu {
      .user-name {
        display: none;
      }
    }
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.sidebar-container {
  width: 210px;
  height: 100%;
  background: #304156;
  position: fixed;
  left: 0;
  z-index: 1001;
  transition: width .3s;
  
  .logo-container {
    height: 50px;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #2b2f3a;
    
    .sidebar-title {
      color: #fff;
      font-weight: 600;
      line-height: 50px;
      font-size: 16px;
      margin: 0;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}

.main-container {
  flex: 1;
  min-height: 100%;
  margin-left: 210px;
  position: relative;
  transition: margin-left .3s;
  width: calc(100% - 210px);
  
  .navbar {
    height: 50px;
    overflow: hidden;
    position: relative;
    background: #fff;
    box-shadow: 0 1px 4px rgba(0,21,41,.08);
    
    .hamburger-container {
      line-height: 50px;
      height: 100%;
      float: left;
      padding: 0 15px;
      cursor: pointer;
      transition: background .3s;
      
      &:hover {
        background: rgba(0, 0, 0, .025)
      }
    }
    
    .work-mode-indicator {
      float: left;
      height: 100%;
      line-height: 50px;
      margin-left: 16px;
      display: flex;
      align-items: center;
      
      .mode-badge {
        display: flex;
        align-items: center;
        padding: 6px 12px;
        background: rgba(64, 158, 255, 0.1);
        border: 1px solid rgba(64, 158, 255, 0.3);
        border-radius: 16px;
        font-size: 14px;
        color: #409EFF;
        transition: all 0.3s ease;
        
        &:hover {
          background: rgba(64, 158, 255, 0.15);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
        }
        
        .mode-badge-icon {
          margin-right: 6px;
          font-size: 16px;
        }
        
        .mode-badge-text {
          font-weight: 500;
          white-space: nowrap;
        }
      }
    }
    
    .right-menu {
      float: right;
      height: 100%;
      line-height: 50px;
      margin-right: 20px;
      display: flex;
      align-items: center;
      
      .right-menu-item {
        padding: 0 8px;
        height: 100%;
        font-size: 18px;
        color: #5a5e66;
        vertical-align: text-bottom;
        display: flex;
        align-items: center;
        cursor: pointer;
        
        &.hover-effect {
          cursor: pointer;
          transition: background .3s;

          &:hover {
            background: rgba(0, 0, 0, .025)
          }
        }

        // 工作模式切换器特殊样式
        &:has(.work-mode-switcher) {
          padding: 0 12px;
          cursor: default;
        }
      }
      
      .avatar-container {
        .avatar-wrapper {
          display: flex;
          align-items: center;
          cursor: pointer;
          padding: 0 8px;
          height: 50px;
          transition: background .3s;
          
          &:hover {
            background: rgba(0,0,0,.025);
          }
          
          .user-avatar {
            margin-right: 8px;
          }
          
          .user-name {
            font-size: 14px;
            color: #333;
            @media screen and (max-width: $mobile-width) {
              display: none;
            }
          }

          .el-icon-caret-bottom {
            margin-left: 8px;
            font-size: 12px;
          }
        }
      }
    }
  }
  
  .app-main {
    min-height: calc(100vh - 50px);
    width: 100%;
    position: relative;
    overflow: hidden;
    padding: 20px;
    background: #f0f2f5;
    box-sizing: border-box;
    
    @media screen and (max-width: $mobile-width) {
      padding: 10px;
    }
  }
}

.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.5s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

// 菜单过渡动画 - 增强效果
.menu-fade-enter-active, .menu-fade-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.menu-fade-enter-from {
  opacity: 0;
  transform: translateX(-30px) scale(0.95);
}

.menu-fade-leave-to {
  opacity: 0;
  transform: translateX(30px) scale(1.05);
}

// 工作模式切换动画 - 增强效果
.mode-change-enter-active,
.mode-change-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.mode-change-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.9);
}

.mode-change-leave-to {
  opacity: 0;
  transform: translateY(10px) scale(1.1);
}
</style> 