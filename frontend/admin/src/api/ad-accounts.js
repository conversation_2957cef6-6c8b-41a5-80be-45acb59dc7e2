import request from '@/utils/request'

/**
 * 获取广告账号列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，默认1
 * @param {number} params.page_size - 每页数量，默认20，最大100
 * @param {string} params.account_name - 账号名称/ID联合查询
 * @param {number} params.platform - 平台筛选（1:小红书）
 * @param {number} params.authorization_status - 授权状态筛选（0:未授权,1:已授权,2:已过期,3:继承授权）
 * @param {number} params.usage_status - 使用状态筛选（1:启用,2:禁用）
 * @param {number} params.account_type - 账号类型筛选（1:主账号,2:子账号）
 * @param {number} params.parent_id - 父账号ID筛选
 * @returns {Promise} API响应
 */
export function getAdAccounts(params) {
  return request({
    url: '/api/v1/ad-accounts',
    method: 'get',
    params
  })
}

/**
 * 创建广告账号
 * @param {Object} data - 账号数据
 * @param {number} data.account_type - 账号类型（1:主账号,2:子账号）
 * @param {number} data.parent_id - 父账号ID（子账号时必填）
 * @param {number} data.platform - 所属平台（1:小红书）
 * @param {string} data.account_name - 账号名称
 * @param {string} data.platform_account_id - 平台账号ID
 * @param {string} data.token - 访问令牌
 * @param {string} data.token_expire_time - 令牌过期时间
 * @param {number} data.account_balance - 账户余额
 * @param {string} data.owner - 归属人员
 * @returns {Promise} API响应
 */
export function createAdAccount(data) {
  return request({
    url: '/api/v1/ad-accounts',
    method: 'post',
    data
  })
}

/**
 * 创建主账号（便捷接口）
 * @param {Object} data - 主账号数据
 * @param {number} data.platform - 所属平台（1:小红书）
 * @param {string} data.account_name - 账号名称
 * @param {string} data.platform_account_id - 平台账号ID
 * @param {string} data.token - 访问令牌
 * @param {string} data.token_expire_time - 令牌过期时间
 * @param {number} data.account_balance - 账户余额
 * @param {string} data.owner - 归属人员
 * @returns {Promise} API响应
 */
export function createMasterAccount(data) {
  return request({
    url: '/api/v1/ad-accounts/master',
    method: 'post',
    data
  })
}

/**
 * 创建子账号（便捷接口）
 * @param {Object} data - 子账号数据
 * @param {number} data.parent_id - 父账号ID
 * @param {number} data.platform - 所属平台（1:小红书）
 * @param {string} data.account_name - 账号名称
 * @param {string} data.platform_account_id - 平台账号ID
 * @param {string} data.token - 访问令牌
 * @param {string} data.token_expire_time - 令牌过期时间
 * @param {number} data.account_balance - 账户余额
 * @param {string} data.owner - 归属人员
 * @returns {Promise} API响应
 */
export function createSubAccount(data) {
  return request({
    url: '/api/v1/ad-accounts/sub',
    method: 'post',
    data
  })
}

/**
 * 更新广告账号
 * @param {number} id - 账号ID
 * @param {Object} data - 更新数据
 * @param {string} data.account_name - 账号名称
 * @param {string} data.platform_account_id - 平台账号ID
 * @param {string} data.token - 访问令牌
 * @param {string} data.token_expire_time - 令牌过期时间
 * @param {number} data.usage_status - 使用状态（1:启用,2:禁用）
 * @param {number} data.account_balance - 账户余额
 * @param {string} data.owner - 归属人员
 * @returns {Promise} API响应
 */
export function updateAdAccount(id, data) {
  return request({
    url: `/api/v1/ad-accounts/${id}`,
    method: 'put',
    data
  })
}

/**
 * 获取广告账号详情
 * @param {number} id - 账号ID
 * @returns {Promise} API响应
 */
export function getAdAccountDetail(id) {
  return request({
    url: `/api/v1/ad-accounts/${id}`,
    method: 'get'
  })
}

/**
 * 删除广告账号
 * @param {number} id - 账号ID
 * @returns {Promise} API响应
 */
export function deleteAdAccount(id) {
  return request({
    url: `/api/v1/ad-accounts/${id}`,
    method: 'delete'
  })
}

/**
 * 获取广告账号选项
 * @returns {Promise} API响应
 */
export function getAdAccountOptions() {
  return request({
    url: '/api/v1/ad-accounts/options',
    method: 'get'
  })
}

/**
 * 获取小红书授权链接
 * @param {Object} data - 授权参数
 * @param {string} data.state - 广告账号ID（字符串格式）
 * @returns {Promise} API响应
 */
export function getXiaohongshuAuthUrl(data) {
  return request({
    url: '/api/v1/xhs/auth/url',
    method: 'post',
    data
  })
}

/**
 * 小红书账号授权
 * @param {Object} data - 授权数据
 * @param {number} data.account_id - 广告账号ID
 * @param {string} data.auth_code - 小红书授权码
 * @returns {Promise} API响应
 */
export function xiaohongshuAuth(data) {
  return request({
    url: '/api/v1/ad-accounts/xiaohongshu/auth',
    method: 'post',
    data
  })
}

/**
 * 小红书刷新令牌
 * @param {Object} data - 刷新令牌数据
 * @param {number} data.account_id - 广告账号ID
 * @param {string} data.refresh_token - 小红书刷新令牌
 * @returns {Promise} API响应
 */
export function xiaohongshuRefreshToken(data) {
  return request({
    url: '/api/v1/ad-accounts/xiaohongshu/refresh-token',
    method: 'post',
    data
  })
}

/**
 * 平台枚举
 */
export const PLATFORMS = {
  1: '小红书'
}

/**
 * 账号类型枚举
 */
export const ACCOUNT_TYPES = {
  1: '主账号',
  2: '子账号'
}

/**
 * 授权状态枚举
 */
export const AUTHORIZATION_STATUS = {
  0: '未授权',
  1: '已授权',
  2: '已过期',
  3: '继承授权'
}

/**
 * 使用状态枚举
 */
export const USAGE_STATUS = {
  1: '启用',
  2: '禁用'
}
