import request from '@/utils/request'

// 获取模型列表
export function getModelList(params) {
    return request({
        url: '/api/v1/delivery/models/list',
        method: 'get',
        params
    })
}

// 创建模型
export function createModel(data) {
    return request({
        url: '/api/v1/delivery/models/create',
        method: 'post',
        data
    })
}

// 更新模型
export function updateModel(data) {
    return request({
        url: '/api/v1/delivery/models/update',
        method: 'put',
        data
    })
}

// 删除模型
export function deleteModel(id) {
    return request({
        url: `/api/v1/delivery/models/delete/${id}`,
        method: 'delete'
    })
}

// 导入模型
export function importModel(data) {
    return request({
        url: '/api/v1/delivery/models/import',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
} 