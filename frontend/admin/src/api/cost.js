import request from '@/utils/request'

// 创建费用记录
export function createCost(data) {
    return request({
        url: '/api/v1/delivery/reports/cost/update',
        method: 'post',
        data
    })
}

// 获取费用列表（旧版API，保持兼容）
export function getCostList(params) {
    return request({
        url: '/api/v1/delivery/reports/cost/list',
        method: 'get',
        params
    })
}

// 获取推广报表列表（新版API，基于promotion_reports表）
export function getPromotionReportList(params) {
    return request({
        url: '/api/v1/promotion-reports',
        method: 'get',
        params
    })
}

// 获取推广报表汇总数据
export function getPromotionReportSummary(params) {
    return request({
        url: '/api/v1/promotion-reports/summary',
        method: 'get',
        params
    })
}

// 根据ID获取推广报表详情
export function getPromotionReportById(id) {
    return request({
        url: `/api/v1/promotion-reports/${id}`,
        method: 'get'
    })
}

// 删除费用记录
export function deleteCost(id) {
    return request({
        url: `/api/v1/delivery/reports/cost/delete/${id}`,
        method: 'delete'
    })
}

// 更新费用记录
export function updateCost(data) {
    return request({
        url: '/api/v1/delivery/reports/cost/update',
        method: 'post',
        data
    })
}

// 导入推广成本
export function importCost(data) {
    return request({
        url: '/api/v1/delivery/reports/cost/import',
        method: 'post',
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        data
    })
}

// 获取任务列表
export function getTaskList() {
    return request({
        url: '/api/v1/delivery/reports/cost/tasks',
        method: 'get'
    })
} 