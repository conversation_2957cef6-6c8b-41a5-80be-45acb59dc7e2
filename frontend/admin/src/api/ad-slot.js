import request from '@/utils/request'

// 获取广告位列表
export function getAdSlots(params) {
  return request({
    url: '/api/v1/slots/list',
    method: 'get',
    params
  })
}

// 获取广告位详情
export function getAdSlot(id) {
  return request({
    url: `/api/v1/ad-slots/${id}`,
    method: 'get'
  })
}

// 创建广告位
export function createAdSlot(data) {
  return request({
    url: '/api/v1/ad-slots',
    method: 'post',
    data
  })
}

// 更新广告位
export function updateAdSlot(id, data) {
  return request({
    url: `/api/v1/ad-slots/${id}`,
    method: 'put',
    data
  })
}

// 删除广告位
export function deleteAdSlot(id) {
  return request({
    url: `/api/v1/ad-slots/${id}`,
    method: 'delete'
  })
}

// 获取广告位的投放计划列表
export function getAdSlotPlans(id, params) {
  return request({
    url: `/api/v1/ad-slots/${id}/plans`,
    method: 'get',
    params
  })
}

// 获取广告位的统计数据
export function getAdSlotStats(id, params) {
  return request({
    url: `/api/v1/ad-slots/${id}/stats`,
    method: 'get',
    params
  })
} 