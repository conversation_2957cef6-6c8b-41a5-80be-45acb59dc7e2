import request from '@/utils/request'

// ======================== 角色管理 ========================

/**
 * 获取角色列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getRoleList(params = {}) {
  return request({
    url: '/api/v1/roles',
    method: 'get',
    params
  })
}

/**
 * 获取角色详情
 * @param {number} id 角色ID
 * @returns {Promise}
 */
export function getRoleDetail(id) {
  return request({
    url: `/api/v1/roles/${id}`,
    method: 'get'
  })
}

/**
 * 创建角色
 * @param {Object} data 角色数据
 * @returns {Promise}
 */
export function createRole(data) {
  return request({
    url: '/api/v1/roles',
    method: 'post',
    data
  })
}

/**
 * 更新角色
 * @param {number} id 角色ID
 * @param {Object} data 角色数据
 * @returns {Promise}
 */
export function updateRole(id, data) {
  return request({
    url: `/api/v1/roles/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除角色
 * @param {number} id 角色ID
 * @returns {Promise}
 */
export function deleteRole(id) {
  return request({
    url: `/api/v1/roles/${id}`,
    method: 'delete'
  })
}

/**
 * 获取角色选项（用于下拉选择）
 * @returns {Promise}
 */
export function getRoleOptions() {
  return request({
    url: '/api/v1/roles/options',
    method: 'get'
  })
}

// ======================== 权限管理 ========================

/**
 * 获取权限列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getPermissionList(params = {}) {
  return request({
    url: '/api/v1/permissions',
    method: 'get',
    params
  })
}

/**
 * 获取权限详情
 * @param {number} id 权限ID
 * @returns {Promise}
 */
export function getPermissionDetail(id) {
  return request({
    url: `/api/v1/permissions/${id}`,
    method: 'get'
  })
}

/**
 * 创建权限
 * @param {Object} data 权限数据
 * @returns {Promise}
 */
export function createPermission(data) {
  return request({
    url: '/api/v1/permissions',
    method: 'post',
    data
  })
}

/**
 * 更新权限
 * @param {number} id 权限ID
 * @param {Object} data 权限数据
 * @returns {Promise}
 */
export function updatePermission(id, data) {
  return request({
    url: `/api/v1/permissions/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除权限
 * @param {number} id 权限ID
 * @returns {Promise}
 */
export function deletePermission(id) {
  return request({
    url: `/api/v1/permissions/${id}`,
    method: 'delete'
  })
}

/**
 * 获取权限模块列表
 * @returns {Promise}
 */
export function getPermissionModules() {
  return request({
    url: '/api/v1/permissions/modules',
    method: 'get'
  })
}

// ======================== 角色权限关联管理 ========================

/**
 * 获取角色的权限列表
 * @param {number} roleId 角色ID
 * @returns {Promise}
 */
export function getRolePermissions(roleId) {
  return request({
    url: `/api/v1/role-permissions`,
    method: 'get',
    params: { role_id: roleId }
  })
}

/**
 * 更新角色权限
 * @param {number} roleId 角色ID
 * @param {Object} data 权限数据
 * @returns {Promise}
 */
export function updateRolePermissions(roleId, data) {
  return request({
    url: `/api/v1/role-permissions/assign`,
    method: 'post',
    data: {
      role_id: roleId,
      permission_ids: data.permission_ids
    }
  })
}

/**
 * 分配权限给角色
 * @param {Object} data 权限分配数据
 * @returns {Promise}
 */
export function assignPermissions(data) {
  return request({
    url: '/api/v1/role-permissions/assign',
    method: 'post',
    data
  })
}

/**
 * 撤销角色权限
 * @param {Object} data 权限撤销数据
 * @returns {Promise}
 */
export function revokePermissions(data) {
  return request({
    url: '/api/v1/role-permissions/revoke',
    method: 'post',
    data
  })
}

// ======================== 用户权限管理 ========================

/**
 * 获取用户权限
 * @param {number} userId 用户ID
 * @returns {Promise}
 */
export function getUserPermissions(userId) {
  return request({
    url: `/api/v1/user-permissions`,
    method: 'get',
    params: { user_id: userId }
  })
}

/**
 * 检查用户权限
 * @param {number} userId 用户ID
 * @param {string} permissionCode 权限编码
 * @returns {Promise}
 */
export function checkUserPermission(userId, permissionCode) {
  return request({
    url: `/api/v1/user-permissions/check`,
    method: 'get',
    params: { 
      user_id: userId,
      permission_code: permissionCode 
    }
  })
}

/**
 * 获取权限操作日志
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getPermissionLogs(params = {}) {
  return request({
    url: '/api/v1/permission-logs',
    method: 'get',
    params
  })
} 