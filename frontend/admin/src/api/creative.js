import request from '@/utils/request'

// 获取创意列表
export function getCreativeList(params) {
    return request({
        url: '/api/v1/ad-creatives/list',
        method: 'get',
        params
    })
}

// 获取创意详情
export function getCreativeDetail(id) {
    return request({
        url: '/api/v1/ad-creatives/detail',
        method: 'get',
        params: { id }
    })
}

// 创建创意
export function createCreative(data) {
    return request({
        url: '/api/v1/ad-creatives/create',
        method: 'post',
        data
    })
}

// 更新创意
export function updateCreative(data) {
    return request({
        url: '/api/v1/ad-creatives/update',
        method: 'put',
        data
    })
}

// 删除创意
export function deleteCreative(id) {
    return request({
        url: '/api/v1/ad-creatives/delete',
        method: 'delete',
        data: { id }
    })
} 