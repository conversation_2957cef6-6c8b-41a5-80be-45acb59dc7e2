import request from '@/utils/request'

// 获取代理列表
export function getAgentList(params) {
  return request({
    url: '/api/v1/agents/list',
    method: 'get',
    params
  })
}

// 创建代理
export function createAgent(data) {
  return request({
    url: '/api/v1/agents/create',
    method: 'post',
    data
  })
}

// 更新代理
export function updateAgent(data) {
  return request({
    url: '/api/v1/agents/update',
    method: 'put',
    data
  })
}

// 删除代理
export function deleteAgent(id) {
  return request({
    url: '/api/v1/agents/delete',
    method: 'delete',
    params: { id }
  })
}

// 获取代理详情
export function getAgentDetail(id) {
  return request({
    url: '/api/v1/agents/detail',
    method: 'get',
    params: { id }
  })
}

// 审核代理
export function auditAgent(data) {
  return request({
    url: '/api/v1/agents/audit',
    method: 'put',
    data
  })
} 