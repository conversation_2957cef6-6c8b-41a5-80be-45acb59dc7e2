import request from '@/utils/request'

// 获取投放计划列表
export function list(params) {
  return request({
    url: '/api/v1/ad-slot-plans/list',
    method: 'get',
    params
  })
}

// 获取投放计划详情
export function get(id) {
  return request({
    url: '/api/v1/ad-slot-plans/get',
    method: 'get',
    params: { id }
  })
}

// 创建投放计划
export function create(data) {
  return request({
    url: '/api/v1/ad-slot-plans/create',
    method: 'post',
    data
  })
}

// 更新投放计划
export function update(data) {
  return request({
    url: '/api/v1/ad-slot-plans/update',
    method: 'post',
    data
  })
}

// 删除投放计划
export function remove(id) {
  return request({
    url: '/api/v1/ad-slot-plans/delete',
    method: 'post',
    params: { id }
  })
}

// 审核通过
export function approve(id) {
  return request({
    url: '/api/v1/ad-slot-plans/approve',
    method: 'post',
    params: { id }
  })
}

// 审核拒绝
export function reject(data) {
  return request({
    url: '/api/v1/ad-slot-plans/reject',
    method: 'post',
    data
  })
}

// 更新投放策略
export function updateDeliveryMode(data) {
  return request({
    url: '/api/v1/ad-slot-plans/update-delivery-mode',
    method: 'post',
    data
  })
}

// 生成推广链接
export function generatePromotionLink(id, product_id) {
  return request({
    url: '/api/v1/ad-slot-plans/generate-promotion-link',
    method: 'post',
    data: { id, product_id }
  })
}

// 生成短链接
export function generateShortUrl(id) {
  return request({
    url: '/api/v1/ad-slot-plans/generate-short-url',
    method: 'get',
    params: { id }
  })
}

// 更新推广链接
export function updatePromotionLink(data) {
  return request({
    url: '/api/v1/ad-slot-plans/update-promotion-link',
    method: 'post',
    data
  })
}

// 更新短链接
export function updateShortUrl(data) {
  return request({
    url: '/api/v1/ad-slot-plans/update-short-url',
    method: 'post',
    data
  })
}

// 更新融合链接
export function updateMergeLinks(data) {
  return request({
    url: '/api/v1/ad-slot-plans/update-merge-links',
    method: 'post',
    data
  })
}

export function getPlatformObjData(params) {
  return request({
    url: '/api/v1/ad-slot-plans/platform_object_data',
    method: 'get',
    params
  })
}
