import request from '@/utils/request'

// 获取媒体列表
export function getMediaList(params) {
  return request({
    url: '/api/v1/media/list',
    method: 'get',
    params
  })
}

// 创建媒体
export function createMedia(data) {
  return request({
    url: '/api/v1/media/create',
    method: 'post',
    data
  })
}

// 更新媒体
export function updateMedia(data) {
  return request({
    url: '/api/v1/media/update',
    method: 'put',
    data
  })
}

// 删除媒体
export function deleteMedia(id) {
  return request({
    url: '/api/v1/media/delete',
    method: 'delete',
    params: { id }
  })
}

// 获取媒体详情
export function getMediaDetail(id) {
  return request({
    url: '/api/v1/media/detail',
    method: 'get',
    params: { id }
  })
}

// 审核媒体
export function auditMedia(data) {
  return request({
    url: '/api/v1/media/audit',
    method: 'put',
    data
  })
}

// 获取媒体类型数量统计
export function getMediaTypeCounts() {
  return request({
    url: '/api/v1/media/type-counts',
    method: 'get'
  })
} 