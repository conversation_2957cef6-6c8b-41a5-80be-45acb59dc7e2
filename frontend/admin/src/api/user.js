import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/api/v1/auth/login',
    method: 'post',
    data
  })
}

export function getInfo() {
  return request({
    url: '/api/v1/auth/info',
    method: 'get'
  })
}

export function logout() {
  return request({
    url: '/api/v1/auth/logout',
    method: 'post'
  })
}

// 获取媒介用户列表
export function getMediaUserList() {
  return request({
    url: '/api/v1/media/users',
    method: 'get'
  })
}

/**
 * 获取用户列表
 * @param {Object} params 查询参数
 */
export function getUserList(params) {
  return request({
    url: '/api/v1/users',
    method: 'get',
    params
  })
}

/**
 * 创建用户
 * @param {Object} data 用户数据
 */
export function createUser(data) {
  return request({
    url: '/api/v1/users',
    method: 'post',
    data
  })
}

/**
 * 更新用户
 * @param {number} id 用户ID
 * @param {Object} data 用户数据
 */
export function updateUser(id, data) {
  return request({
    url: `/api/v1/users/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除用户
 * @param {number} id 用户ID
 */
export function deleteUser(id) {
  return request({
    url: `/api/v1/users/${id}`,
    method: 'delete'
  })
}

/**
 * 标记用户为离职状态
 * @param {number} id 用户ID
 */
export function setUserInactive(id) {
  return request({
    url: `/api/v1/users/${id}/inactive`,
    method: 'post'
  })
}

/**
 * 锁定用户
 * @param {number} id 用户ID
 * @param {string} lockReason 锁定原因
 */
export function lockUser(id, data) {
  return request({
    url: `/api/v1/users/${id}/lock`,
    method: 'post',
    data
  })
}

/**
 * 解锁用户
 * @param {number} id 用户ID
 */
export function unlockUser(id) {
  return request({
    url: `/api/v1/users/${id}/unlock`,
    method: 'post'
  })
}

/**
 * 重置用户密码
 * @param {number} id 用户ID
 * @param {string} newPassword 新密码
 */
export function resetUserPassword(id, data) {
  return request({
    url: `/api/v1/users/${id}/password/reset`,
    method: 'post',
    data
  })
}

/**
 * 获取用户选项（角色、部门、上级等）
 */
export function getUserOptions() {
  return request({
    url: '/api/v1/users/options',
    method: 'get'
  })
} 