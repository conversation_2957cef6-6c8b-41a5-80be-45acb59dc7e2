import request from '@/utils/request'

/**
 * 工作模式管理API
 */
export const workModeApi = {
  /**
   * 获取用户可用的工作模式
   * @returns {Promise}
   */
  getUserModes() {
    return request({
      url: '/api/v1/work-modes',
      method: 'get'
    })
  },

  /**
   * 切换工作模式
   * @param {string} mode - 工作模式：traffic 或 ad
   * @returns {Promise}
   */
  switchMode(mode) {
    return request({
      url: '/api/v1/work-modes/switch',
      method: 'post',
      data: {
        mode
      }
    })
  },

  /**
   * 检查指定模式的权限
   * @param {string} mode - 工作模式：traffic 或 ad
   * @returns {Promise}
   */
  checkModePermission(mode) {
    return request({
      url: '/api/v1/work-modes/check',
      method: 'get',
      params: {
        mode
      }
    })
  }
} 