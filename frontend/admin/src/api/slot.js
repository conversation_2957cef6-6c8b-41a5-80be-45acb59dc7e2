import request from '@/utils/request'

// 获取资源位列表
export function getSlotList(params) {
  return request({
    url: '/api/v1/slots/list',
    method: 'get',
    params
  })
}

// 创建资源位
export function createSlot(data) {
  return request({
    url: '/api/v1/slots/create',
    method: 'post',
    data
  })
}

// 更新资源位
export function updateSlot(data) {
  return request({
    url: '/api/v1/slots/update',
    method: 'put',
    data
  })
}

// 删除资源位
export function deleteSlot(id) {
  return request({
    url: '/api/v1/slots/delete',
    method: 'delete',
    params: { id }
  })
}

// 审核资源位
export function auditSlot(data) {
  return request({
    url: '/api/v1/slots/audit',
    method: 'put',
    data
  })
}

// 搜索媒体
export function searchMedia(params) {
  return request({
    url: '/api/v1/media/search',
    method: 'get',
    params
  })
} 