import request from '@/utils/request'

/**
 * 获取小红书创意报表列表
 * @param {Object} params - 查询参数
 * @param {number} params.account_id - 账号ID
 * @param {string} params.account_name - 账号名称（模糊匹配）
 * @param {string} params.campaign_name - 计划名称（模糊匹配）
 * @param {string} params.unit_name - 单元名称（模糊匹配）
 * @param {string} params.title - 标题（模糊匹配）
 * @param {string} params.pwd - 口令（模糊匹配）
 * @param {string} params.start_date - 开始日期 YYYY-MM-DD
 * @param {string} params.end_date - 结束日期 YYYY-MM-DD
 * @param {number} params.placement - 广告类型 1:信息流 2:搜索 3:开屏 4:全站智投 7:视频内流
 * @param {string} params.aggregate_type - 聚合类型 daily:分日数据 summary:汇总数据
 * @param {number} params.page - 页码，从1开始
 * @param {number} params.page_size - 每页数量，最大1000
 * @returns {Promise} API响应
 */
export function getXhsCreativeReports(params) {
  return request({
    url: '/api/v1/xhs-creative-reports',
    method: 'get',
    params
  })
}

/**
 * 获取小红书创意报表统计信息
 * @param {Object} params - 查询参数（与列表接口相同的筛选参数，除分页参数外）
 * @returns {Promise} API响应
 */
export function getXhsCreativeReportsStats(params) {
  return request({
    url: '/api/v1/xhs-creative-reports/stats',
    method: 'get',
    params
  })
}

/**
 * 导出小红书创意报表数据
 * @param {Object} params - 查询参数
 * @param {string} params.format - 导出格式：xlsx 或 csv
 * @returns {Promise} API响应
 */
export function exportXhsCreativeReports(params) {
  return request({
    url: '/api/v1/xhs-creative-reports/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取小红书创意报表详情
 * @param {number} id - 报表ID
 * @returns {Promise} API响应
 */
export function getXhsCreativeReportDetail(id) {
  return request({
    url: `/api/v1/xhs-creative-reports/${id}`,
    method: 'get'
  })
}

/**
 * 广告类型枚举
 */
export const PLACEMENT_TYPES = {
  1: '信息流',
  2: '搜索',
  3: '开屏',
  4: '全站智投',
  7: '视频内流'
}

/**
 * 优化目标枚举
 */
export const OPTIMIZE_TARGETS = {
  1: '点击量',
  2: '转化量',
  3: '曝光量',
  4: '触达量'
}

/**
 * 推广标的枚举
 */
export const PROMOTION_TARGETS = {
  1: '笔记',
  2: '直播间',
  3: '商品',
  4: '落地页'
}

/**
 * 出价方式枚举
 */
export const BIDDING_STRATEGIES = {
  1: '点击出价',
  2: '曝光出价',
  3: '转化出价'
}

/**
 * 搭建类型枚举
 */
export const BUILD_TYPES = {
  1: '手动搭建',
  2: '自动搭建'
}

/**
 * 营销诉求枚举
 */
export const MARKETING_TARGETS = {
  1: '品牌推广',
  4: '产品种草',
  9: '客资收集',
  16: '应用换端'
}

/**
 * 聚合类型枚举
 */
export const AGGREGATE_TYPES = {
  daily: '分日数据',
  summary: '汇总数据'
}
