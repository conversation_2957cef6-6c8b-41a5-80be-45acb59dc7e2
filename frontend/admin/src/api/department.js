import request from '@/utils/request'

/**
 * 获取部门列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getDepartmentList(params = {}) {
  return request({
    url: '/api/v1/departments',
    method: 'get',
    params
  })
}

/**
 * 获取部门详情
 * @param {number} id 部门ID
 * @returns {Promise}
 */
export function getDepartmentDetail(id) {
  return request({
    url: `/api/v1/departments/${id}`,
    method: 'get'
  })
}

/**
 * 创建部门
 * @param {Object} data 部门数据
 * @returns {Promise}
 */
export function createDepartment(data) {
  return request({
    url: '/api/v1/departments',
    method: 'post',
    data
  })
}

/**
 * 更新部门
 * @param {number} id 部门ID
 * @param {Object} data 部门数据
 * @returns {Promise}
 */
export function updateDepartment(id, data) {
  return request({
    url: `/api/v1/departments/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除部门
 * @param {number} id 部门ID
 * @returns {Promise}
 */
export function deleteDepartment(id) {
  return request({
    url: `/api/v1/departments/${id}`,
    method: 'delete'
  })
}

/**
 * 获取部门树形结构
 * @returns {Promise}
 */
export function getDepartmentTree() {
  return request({
    url: '/api/v1/departments/tree',
    method: 'get'
  })
}

/**
 * 获取部门选项（用于下拉选择）
 * @returns {Promise}
 */
export function getDepartmentOptions() {
  return request({
    url: '/api/v1/departments/options',
    method: 'get'
  })
}

/**
 * 获取部门成员
 * @param {number} id 部门ID
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getDepartmentMembers(id, params = {}) {
  return request({
    url: `/api/v1/departments/${id}/members`,
    method: 'get',
    params
  })
} 