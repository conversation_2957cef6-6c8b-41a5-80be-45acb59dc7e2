import request from '@/utils/request'

/**
 * 获取口令列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getCommandList(params) {
    return request({
        url: '/api/v1/password/list',
        method: 'get',
        params
    })
}

/**
 * 获取口令详情
 * @param {number} id - 口令ID
 * @returns {Promise} - 返回Promise对象
 */
export function getCommandDetail(id) {
    return request({
        url: `/api/v1/password/${id}`,
        method: 'get'
    })
}

/**
 * 创建口令
 * @param {Object} data - 口令数据
 * @returns {Promise} - 返回Promise对象
 */
export function createCommand(data) {
    return request({
        url: '/api/v1/password/create',
        method: 'post',
        data
    })
}

/**
 * 更新口令
 * @param {number} id - 口令ID
 * @param {Object} data - 口令数据
 * @returns {Promise} - 返回Promise对象
 */
export function updateCommand(id, data) {
    return request({
        url: `/api/v1/password/${id}`,
        method: 'put',
        data
    })
}

/**
 * 删除口令
 * @param {number} id - 口令ID
 * @returns {Promise} - 返回Promise对象
 */
export function deleteCommand(id) {
    return request({
        url: `/api/v1/password/${id}`,
        method: 'delete'
    })
}

/**
 * 启用/禁用口令
 * @param {number} id - 口令ID
 * @param {boolean} status - 状态
 * @returns {Promise} - 返回Promise对象
 */
export function toggleCommandStatus(id, status) {
    return request({
        url: `/api/v1/password/${id}/status`,
        method: 'put',
        data: { status }
    })
} 