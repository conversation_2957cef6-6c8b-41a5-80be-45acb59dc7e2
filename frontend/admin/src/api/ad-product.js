import request from '@/utils/request'

/**
 * 获取投放产品列表
 * @param {Object} params 查询参数
 * @param {number} [params.page] 页码
 * @param {number} [params.size] 每页数量
 * @returns {Promise<Object>} 响应结果
 */
export function getAdProducts(params) {
  return request({
    url: '/api/v1/ad-products/list',
    method: 'get',
    params
  })
}


/**
 * 获取活跃投放产品列表（用于下拉选择）
 * @returns {Promise<Object>} 响应结果
 */
export function getActiveAdProducts() {
  return request({
    url: '/api/v1/ad-products/list',
    method: 'get',
    params: {
      page: 1,
      size: 1000,
      status: 'active'
    }
  })
} 