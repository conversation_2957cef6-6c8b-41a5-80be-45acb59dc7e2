import request from '@/utils/request'

/**
 * 获取创意列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function list(params) {
    return request({
        url: '/api/v1/ad-creatives/list',
        method: 'get',
        params
    })
}

/**
 * 获取创意详情
 * @param {number|string} id 创意ID
 * @returns {Promise}
 */
export function getDetail(id) {
    return request({
        url: '/api/v1/ad-creatives/detail',
        method: 'get',
        params: { id }
    })
}

/**
 * 创建创意
 * @param {Object} data 创意数据
 * @returns {Promise}
 */
export function create(data) {
    return request({
        url: '/api/v1/ad-creatives/create',
        method: 'post',
        data
    })
}

/**
 * 更新创意
 * @param {Object} data 创意数据
 * @returns {Promise}
 */
export function update(data) {
    return request({
        url: '/api/v1/ad-creatives/update',
        method: 'put',
        data
    })
} 