import request from '@/utils/request'

/**
 * 获取计划列表
 * @param {Object} params - 查询参数
 * @param {number} params.account_id - 账号ID（来自广告账号管理API）
 * @param {string} params.account_name - 账号名称（模糊查询）
 * @param {string} params.plan_name - 计划名称（模糊查询）
 * @param {string} params.start_date - 开始日期，格式：YYYY-MM-DD
 * @param {string} params.end_date - 结束日期，格式：YYYY-MM-DD
 * @param {number} params.page - 页码，默认1
 * @param {number} params.page_size - 每页数量，默认20
 * @returns {Promise} API响应
 */
export function getAdPlans(params) {
  return request({
    url: '/api/v1/ad-plans',
    method: 'get',
    params
  })
}

/**
 * 获取计划统计信息
 * @param {Object} params - 查询参数
 * @param {number} params.account_id - 账号ID（来自广告账号管理API）
 * @param {string} params.account_name - 账号名称
 * @param {string} params.start_date - 开始日期，格式：YYYY-MM-DD
 * @param {string} params.end_date - 结束日期，格式：YYYY-MM-DD
 * @returns {Promise} API响应
 */
export function getAdPlansStats(params) {
  return request({
    url: '/api/v1/ad-plans/stats',
    method: 'get',
    params
  })
}

/**
 * 导出计划列表
 * @param {Object} data - 导出参数
 * @param {number} data.account_id - 账号ID
 * @param {string} data.account_name - 账号名称
 * @param {string} data.plan_name - 计划名称
 * @param {string} data.start_date - 开始日期，格式：YYYY-MM-DD
 * @param {string} data.end_date - 结束日期，格式：YYYY-MM-DD
 * @param {string} data.format - 导出格式：xlsx 或 csv，默认xlsx
 * @returns {Promise} API响应
 */
export function exportAdPlans(data) {
  return request({
    url: '/api/v1/ad-plans/export',
    method: 'post',
    data
  })
}

/**
 * 获取计划详情
 * @param {string} planId - 计划ID
 * @returns {Promise} API响应
 */
export function getAdPlanDetail(planId) {
  return request({
    url: `/api/v1/ad-plans/${planId}`,
    method: 'get'
  })
}

/**
 * 获取账号列表（用于计划列表筛选）
 * @param {Object} params - 查询参数
 * @param {number} params.platform - 平台筛选（1:小红书）
 * @param {number} params.usage_status - 使用状态筛选（1:启用,2:禁用）
 * @param {number} params.authorization_status - 授权状态筛选（1:已授权）
 * @returns {Promise} API响应
 */
export function getAdPlanAccounts(params = {}) {
  // 默认只查询小红书平台的启用账号
  const defaultParams = {
    platform: 1, // 小红书
    usage_status: 1, // 启用状态
    // authorization_status: 1, // 已授权
    page_size: 100, // 获取更多账号选项
    ...params
  }

  return request({
    url: '/api/v1/ad-accounts',
    method: 'get',
    params: defaultParams
  })
}

/**
 * 格式化金额显示
 * @param {number} amount - 金额
 * @returns {string} 格式化后的金额
 */
export function formatCurrency(amount) {
  if (amount === null || amount === undefined) return '0.00'
  return Number(amount).toFixed(2)
}

/**
 * 格式化点击率显示
 * @param {number} rate - 点击率
 * @returns {string} 格式化后的点击率
 */
export function formatClickRate(rate) {
  if (rate === null || rate === undefined) return '0.00%'
  return Number(rate).toFixed(2) + '%'
}

/**
 * 格式化数字显示（千分位）
 * @param {number} num - 数字
 * @returns {string} 格式化后的数字
 */
export function formatNumber(num) {
  if (num === null || num === undefined) return '0'
  return Number(num).toLocaleString()
}

/**
 * 获取日期范围的预设选项
 * @returns {Array} 日期范围选项
 */
export function getDateRangeOptions() {
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  
  const lastWeek = new Date(today)
  lastWeek.setDate(lastWeek.getDate() - 7)
  
  const lastMonth = new Date(today)
  lastMonth.setMonth(lastMonth.getMonth() - 1)
  
  return [
    {
      text: '今天',
      value: [today, today]
    },
    {
      text: '昨天',
      value: [yesterday, yesterday]
    },
    {
      text: '最近7天',
      value: [lastWeek, today]
    },
    {
      text: '最近30天',
      value: [lastMonth, today]
    }
  ]
}

/**
 * 格式化日期为API需要的格式
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串 YYYY-MM-DD
 */
export function formatDateForAPI(date) {
  if (!date) return ''
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

/**
 * 验证日期范围
 * @param {Array} dateRange - 日期范围数组 [startDate, endDate]
 * @returns {Object} 验证结果
 */
export function validateDateRange(dateRange) {
  if (!dateRange || dateRange.length !== 2) {
    return { valid: false, message: '请选择日期范围' }
  }
  
  const [startDate, endDate] = dateRange
  if (!startDate || !endDate) {
    return { valid: false, message: '请选择完整的日期范围' }
  }
  
  if (startDate > endDate) {
    return { valid: false, message: '开始日期不能大于结束日期' }
  }
  
  // 检查日期范围是否超过365天
  const diffTime = Math.abs(endDate - startDate)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  if (diffDays > 365) {
    return { valid: false, message: '日期范围不能超过365天' }
  }
  
  return { valid: true, message: '' }
}

/**
 * 默认查询参数
 */
export const DEFAULT_QUERY_PARAMS = {
  page: 1,
  page_size: 20,
  start_date: formatDateForAPI(new Date()),
  end_date: formatDateForAPI(new Date())
}

/**
 * 导出格式选项
 */
export const EXPORT_FORMAT_OPTIONS = [
  { label: 'Excel格式', value: 'xlsx' },
  { label: 'CSV格式', value: 'csv' }
]
