import request from '@/utils/request'


// 获取媒体列表
export function getMediaList(params) {
  return request({
    url: '/api/v1/dashboard/media-list',
    method: 'get',
    params
  })
}

// 获取指标数据
export function getMetrics(params) {
  return request({
    url: '/api/v1/dashboard/metrics',
    method: 'get',
    params
  })
}

// 获取趋势数据
export function getTrendData(params) {
  console.log('Calling getTrendData API with params:', params)
  return request({
    url: '/api/v1/dashboard/trend',
    method: 'get',
    params
  })
}

// 获取地域分析数据
export function getRegionData(params) {
  return request({
    url: '/api/v1/dashboard/region',
    method: 'get',
    params
  })
}

// 获取订单类型分析数据
export function getOrderTypeData(params) {
  return request({
    url: '/api/v1/dashboard/order-type',
    method: 'get',
    params
  })
}

// 获取投放计划列表
export function getPlans(params) {
  return request({
    url: '/api/v1/dashboard/plans',
    method: 'get',
    params
  })
}

// 获取产品列表
export function getProducts() {
  return request({
    url: '/api/v1/dashboard/products',
    method: 'get'
  })
}

// 获取分计划数据
export function getPlanStats(params) {
  return request({
    url: '/api/v1/dashboard/plan-stats',
    method: 'get',
    params
  })
}

// 获取动态筛选选项
export function getDynamicFilterOptions(params) {
  return request({
    url: '/api/v1/dashboard/dynamic-filter',
    method: 'get',
    params
  })
} 