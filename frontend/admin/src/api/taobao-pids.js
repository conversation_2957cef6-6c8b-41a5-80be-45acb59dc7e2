import request from '@/utils/request'

// 获取淘联链接列表
export function getTaobaoPids(params) {
  return request({
    url: '/api/v1/taobao-pids/list',
    method: 'get',
    params
  })
}

// 创建淘联链接
export function createTaobaoPid(data) {
  return request({
    url: '/api/v1/taobao-pids/create',
    method: 'post',
    data
  })
}

// 更新淘联链接
export function updateTaobaoPid(id, data) {
  return request({
    url: `/api/v1/taobao-pids/${id}`,
    method: 'put',
    data
  })
}

// 删除淘联链接
export function deleteTaobaoPid(id) {
  return request({
    url: `/api/v1/taobao-pids/${id}`,
    method: 'delete'
  })
} 