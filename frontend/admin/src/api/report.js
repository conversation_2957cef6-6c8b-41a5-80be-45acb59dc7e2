import request from '@/utils/request'

// 获取投放报表数据
export function getDeliveryReport(params) {
    return request({
        url: '/api/v1/delivery/reports/list',
        method: 'get',
        params
    })
}

// 导入成本数据
export function importCost(data) {
    return request({
        url: '/api/v1/delivery/reports/import-cost',
        method: 'post',
        data,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}

// 获取日报表汇总数据
export function getDailyReport(params) {
    return request({
        url: '/api/v1/reports/daily-report',
        method: 'get',
        params
    })
}

// 获取单日详细数据
export function getDailyDetail(params) {
    return request({
        url: '/api/v1/reports/daily-detail',
        method: 'get',
        params
    })
}

// 导出日报表数据
export function exportDailyReport(params) {
    return request({
        url: '/api/v1/reports/daily-report/export',
        method: 'get',
        params,
        responseType: 'blob'
    })
}

// ============= 周报表相关接口 =============

// 获取周报表数据
export function getWeeklyReport(params) {
    return request({
        url: '/api/v1/reports/weekly-report',
        method: 'get',
        params
    })
}

// 获取周报表详情
export function getWeeklyDetail(params) {
    return request({
        url: '/api/v1/reports/weekly-detail',
        method: 'get',
        params
    })
}

// 导出周报表
export function exportWeeklyReport(params) {
    return request({
        url: '/api/v1/reports/weekly-report/export',
        method: 'get',
        params,
        responseType: 'blob'
    })
}

