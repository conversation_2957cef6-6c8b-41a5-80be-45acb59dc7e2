<template>
  <div class="account-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>账户管理</h1>
      <p class="page-description">管理系统用户账户，包括媒介、运营、管理层等不同角色的用户</p>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="用户名">
          <el-input
            v-model="filterForm.name"
            placeholder="请输入用户名"
            clearable
            style="width: 180px"
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="角色">
          <el-select
            v-model="filterForm.role"
            placeholder="全部角色"
            clearable
            style="width: 150px"
            @change="handleSearch"
          >
            <el-option
              v-for="role in roleOptions"
              :key="role.value"
              :label="role.label"
              :value="role.value"
            >
              <el-tag 
                :type="getRoleTagType(role.value)" 
                size="small"
                style="margin-right: 8px"
              >
                {{ role.label }}
              </el-tag>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="部门">
          <el-select
            v-model="filterForm.department"
            placeholder="全部部门"
            clearable
            style="width: 150px"
            @change="handleSearch"
          >
            <el-option
              v-for="dept in departmentOptions"
              :key="dept.value"
              :label="dept.label"
              :value="dept.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select
            v-model="filterForm.status"
            placeholder="全部状态"
            clearable
            style="width: 120px"
            @change="handleSearch"
          >
            <el-option label="在职" :value="1" />
            <el-option label="离职" :value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label="锁定状态">
          <el-select
            v-model="filterForm.isLocked"
            placeholder="全部"
            clearable
            style="width: 120px"
            @change="handleSearch"
          >
            <el-option label="正常" :value="0" />
            <el-option label="已锁定" :value="1" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            查询
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            重置
          </el-button>
          <el-button 
            v-permission="'system:account:create'"
            type="primary" 
            :icon="Plus" 
            @click="handleAdd"
          >
            新增用户
          </el-button>
          <el-button 
            v-permission="'system:account:export'"
            type="success" 
            :icon="Download" 
            @click="handleExport"
          >
            导出数据
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="id" label="ID" width="80" sortable="custom" />
        
        <el-table-column prop="name" label="用户名" width="120" />
        
        <el-table-column prop="real_name" label="真实姓名" width="120" />
        
        <el-table-column label="联系方式" width="200">
          <template #default="{ row }">
            <div class="contact-info">
              <div v-if="row.email" class="email">
                <el-icon><Message /></el-icon>
                {{ row.email }}
              </div>
              <div v-if="row.phone" class="phone">
                <el-icon><Phone /></el-icon>
                {{ row.phone }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="角色部门" width="180">
          <template #default="{ row }">
            <div class="role-dept-info">
                              <el-tag :type="getRoleTagType(row.role_id)" size="small">
                {{ row.role_name || getRoleLabel(row.role_id) }}
              </el-tag>
              <div v-if="row.department_name || row.department" class="department">
                {{ row.department_name || row.department }}
              </div>
              <div v-if="row.position" class="position">
                {{ row.position }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="直属上级" width="120">
          <template #default="{ row }">
            <span v-if="row.supervisor_name">{{ row.supervisor_name }}</span>
            <span v-else class="text-muted">无</span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="120">
          <template #default="{ row }">
            <div class="status-info">
              <el-tag :type="row.status === 1 ? 'success' : 'info'" size="small">
                {{ row.status === 1 ? '在职' : '离职' }}
              </el-tag>
              <el-tag v-if="row.is_locked" type="danger" size="small">
                已锁定
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="最后登录" width="160">
          <template #default="{ row }">
            <div v-if="row.last_login_at" class="login-info">
              <div class="time">{{ formatDate(row.last_login_at) }}</div>
              <div v-if="row.last_login_ip" class="ip">{{ row.last_login_ip }}</div>
            </div>
            <span v-else class="text-muted">从未登录</span>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="160" sortable="custom">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="220" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                v-permission="'system:account:edit'"
                type="primary"
                size="small"
                :icon="Edit"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>

              <!-- 更多操作下拉菜单 -->
              <el-dropdown @command="handleCommand($event, row)">
                <el-button size="small" :icon="MoreFilled">
                  更多
                  <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item 
                      v-permission="'system:account:reset_password'"
                      command="resetPassword"
                      :icon="Key"
                    >
                      重置密码
                    </el-dropdown-item>
                    
                    <el-dropdown-item 
                      v-if="hasPermission('system:account:edit') && !row.is_locked"
                      command="lock"
                      :icon="Lock"
                    >
                      锁定账户
                    </el-dropdown-item>
                    
                    <el-dropdown-item 
                      v-if="hasPermission('system:account:edit') && row.is_locked"
                      command="unlock"
                      :icon="Unlock"
                    >
                      解锁账户
                    </el-dropdown-item>
                    
                    <el-dropdown-item 
                      v-if="hasPermission('system:account:edit') && row.status === 1"
                      command="setInactive"
                      :icon="UserFilled"
                      divided
                    >
                      标记离职
                    </el-dropdown-item>
                    
                    <el-dropdown-item 
                      v-permission="'system:account:delete'"
                      command="delete"
                      :icon="Delete"
                      divided
                    >
                      删除用户
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 新增/编辑用户对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        class="user-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入用户名"
                :disabled="isEdit"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="真实姓名" prop="real_name">
              <el-input v-model="formData.real_name" placeholder="请输入真实姓名" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="formData.email" placeholder="请输入邮箱地址" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="formData.phone" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="部门" prop="department">
              <el-select
                v-model="formData.department"
                placeholder="请选择部门"
                style="width: 100%"
                clearable
                filterable
              >
                <el-option
                  v-for="dept in departmentOptions"
                  :key="dept.value"
                  :label="dept.label"
                  :value="dept.value"
                >
                  <span style="float: left">{{ dept.label }}</span>
                  <span 
                    v-if="dept.description" 
                    style="float: right; color: #8492a6; font-size: 12px"
                  >
                    {{ dept.description }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位" prop="position">
              <el-input v-model="formData.position" placeholder="请输入职位" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
                          <el-form-item label="角色" prop="role_id">
              <el-select
                v-model="formData.role_id"
                placeholder="请选择角色"
                style="width: 100%"
                clearable
                filterable
              >
                <el-option
                  v-for="role in roleOptions"
                  :key="role.value"
                  :label="role.label"
                  :value="role.value"
                >
                  <div class="role-option">
                    <div class="role-name">
                      <el-tag 
                        :type="getRoleTagType(role.value)" 
                        size="small"
                        style="margin-right: 8px"
                      >
                        {{ role.label }}
                      </el-tag>
                    </div>
                    <div 
                      v-if="role.description" 
                      class="role-description"
                    >
                      {{ role.description }}
                    </div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="直属上级" prop="supervisor_id">
              <el-select
                v-model="formData.supervisor_id"
                placeholder="请选择直属上级"
                style="width: 100%"
                clearable
                filterable
              >
                <el-option
                  v-for="supervisor in supervisorOptions"
                  :key="supervisor.value"
                  :label="supervisor.label"
                  :value="supervisor.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="!isEdit" :gutter="20">
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="formData.password"
                type="password"
                placeholder="请输入密码"
                show-password
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="formData.confirmPassword"
                type="password"
                placeholder="请确认密码"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="isEdit" :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="formData.status">
                <el-radio :label="1">在职</el-radio>
                <el-radio :label="2">离职</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 锁定用户对话框 -->
    <el-dialog
      v-model="lockDialogVisible"
      title="锁定用户"
      width="500px"
    >
      <el-form
        ref="lockFormRef"
        :model="lockForm"
        :rules="lockFormRules"
        label-width="80px"
      >
        <el-form-item label="锁定原因" prop="lockReason">
          <el-input
            v-model="lockForm.lockReason"
            type="textarea"
            :rows="4"
            placeholder="请输入锁定原因"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="lockDialogVisible = false">取消</el-button>
          <el-button type="danger" :loading="submitLoading" @click="confirmLock">
            确定锁定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog
      v-model="resetPasswordDialogVisible"
      title="重置密码"
      width="400px"
    >
      <el-form
        ref="resetPasswordFormRef"
        :model="resetPasswordForm"
        :rules="resetPasswordFormRules"
        label-width="80px"
      >
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="resetPasswordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetPasswordDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="confirmResetPassword">
            确定重置
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Edit,
  Delete,
  MoreFilled,
  ArrowDown,
  Key,
  Lock,
  Unlock,
  Message,
  Phone,
  Download,
  UserFilled
} from '@element-plus/icons-vue'
import { formatDate } from '@/utils/date'
import { getUserList, createUser, updateUser, deleteUser, setUserInactive, lockUser, unlockUser, resetUserPassword, getUserOptions } from '@/api/user'
import { getRoleList } from '@/api/role'

import { usePermission } from '@/hooks/usePermission'
import { useUserStore } from '@/store/modules/user'

// 使用权限系统
const { hasPermission } = usePermission()
const userStore = useUserStore()

// 数据状态
const loading = ref(false)
const submitLoading = ref(false)
const tableData = ref([])

// 筛选表单
const filterForm = reactive({
  name: '',
  role: '',
  department: '',
  status: '',
  isLocked: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 对话框状态
const dialogVisible = ref(false)
const isEdit = ref(false)
const lockDialogVisible = ref(false)
const resetPasswordDialogVisible = ref(false)

// 表单数据
const formData = reactive({
  id: null,
  name: '',
  real_name: '',
  email: '',
  phone: '',
  department: '',
  position: '',
  role_id: '',
  supervisor_id: '',
  password: '',
  confirmPassword: '',
  status: 1,
  remark: ''
})

// 锁定表单
const lockForm = reactive({
  userId: null,
  lockReason: ''
})

// 重置密码表单
const resetPasswordForm = reactive({
  userId: null,
  newPassword: ''
})

// 选项数据
const roleOptions = ref([])
const departmentOptions = ref([])
const supervisorOptions = ref([])

// 角色类型映射 - 动态生成
const roleTypeMap = ref({})

// 表单引用
const formRef = ref()
const lockFormRef = ref()
const resetPasswordFormRef = ref()

// 计算属性
const dialogTitle = computed(() => {
  return isEdit.value ? '编辑用户' : '新增用户'
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  real_name: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '真实姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  role_id: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 30, message: '密码长度在 6 到 30 个字符', trigger: 'blur' },
    { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/, message: '密码必须包含大小写字母和数字', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== formData.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

const lockFormRules = {
  lockReason: [
    { required: true, message: '请输入锁定原因', trigger: 'blur' },
    { min: 1, max: 200, message: '锁定原因长度在 1 到 200 个字符', trigger: 'blur' }
  ]
}

const resetPasswordFormRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 30, message: '密码长度在 6 到 30 个字符', trigger: 'blur' }
  ]
}

// 获取角色标签类型
const getRoleTagType = (roleId) => {
  return roleTypeMap.value[roleId]?.type || 'info'
}

// 获取角色标签名称
const getRoleLabel = (roleId) => {
  return roleTypeMap.value[roleId]?.label || roleId
}

// 初始化数据
const initData = async () => {
  try {
    loading.value = true
    await Promise.all([
      fetchTableData(),
      fetchOptions()
    ])
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('页面初始化失败，请刷新重试')
  } finally {
    loading.value = false
  }
}

// 获取表格数据
const fetchTableData = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      ...filterForm
    }
    
    const response = await getUserList(params)
    tableData.value = response.data.list || []
    pagination.total = response.data.total || 0
  } catch (error) {
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 获取选项数据
const fetchOptions = async () => {
  try {
    const [userOptionsRes, roleListRes] = await Promise.all([
      getUserOptions(),
      getRoleList()
    ])
    
    // 处理用户选项
    // 转换部门数据格式 - 使用部门名称作为显示和值
    if (userOptionsRes.data.departments) {
      departmentOptions.value = userOptionsRes.data.departments.map(dept => ({
        value: dept.name,  // 使用部门名称作为值
        label: dept.name,  // 使用部门名称作为显示标签
        description: dept.description || ''
      }))
    } else {
      departmentOptions.value = []
    }
    
    supervisorOptions.value = userOptionsRes.data.supervisors || []
    
    // 处理角色选项 - 从角色API获取真实的角色ID
    if (roleListRes.data && roleListRes.data.list) {
      roleOptions.value = roleListRes.data.list.map(role => ({
        value: role.id,  // 使用角色表的真实ID
        label: role.name,
        description: role.description
      }))
      
      // 生成角色类型映射
      const typeColors = ['warning', 'success', 'danger', 'primary', 'info']
      roleListRes.data.list.forEach((role, index) => {
        roleTypeMap.value[role.id] = {
          type: typeColors[index % typeColors.length],
          label: role.name
        }
      })
    }
    
  } catch (error) {
    console.error('获取选项数据失败:', error)
    ElMessage.error('获取选项数据失败')
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchTableData()
}

// 重置
const handleReset = () => {
  Object.keys(filterForm).forEach(key => {
    filterForm[key] = ''
  })
  handleSearch()
}

// 分页处理
const handlePageChange = (page) => {
  pagination.page = page
  fetchTableData()
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.page = 1
  fetchTableData()
}

// 排序处理
const handleSortChange = ({ prop, order }) => {
  // 这里可以添加排序逻辑
  console.log('排序:', prop, order)
}

// 新增用户
const handleAdd = () => {
  isEdit.value = false
  dialogVisible.value = true
  resetForm()
}

// 编辑用户
const handleEdit = (row) => {
  isEdit.value = true
  dialogVisible.value = true
  
  // 填充表单数据
  Object.keys(formData).forEach(key => {
    if (key in row) {
      formData[key] = row[key]
    }
  })
}

// 命令处理
const handleCommand = (command, row) => {
  switch (command) {
    case 'resetPassword':
      handleResetPassword(row)
      break
    case 'lock':
      handleLock(row)
      break
    case 'unlock':
      handleUnlock(row)
      break
    case 'setInactive':
      handleSetInactive(row)
      break
    case 'delete':
      handleDelete(row)
      break
  }
}

// 重置密码
const handleResetPassword = (row) => {
  resetPasswordForm.userId = row.id
  resetPasswordForm.newPassword = ''
  resetPasswordDialogVisible.value = true
}

// 锁定用户
const handleLock = (row) => {
  lockForm.userId = row.id
  lockForm.lockReason = ''
  lockDialogVisible.value = true
}

// 解锁用户
const handleUnlock = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要解锁用户 "${row.real_name}" 吗？`,
      '确认解锁',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await unlockUser(row.id)
    ElMessage.success('解锁成功')
    fetchTableData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('解锁失败')
    }
  }
}

// 标记用户离职
const handleSetInactive = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要标记用户 "${row.real_name}" 为离职状态吗？`,
      '确认标记离职',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await setUserInactive(row.id)
    ElMessage.success('标记离职成功')
    fetchTableData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('标记离职失败')
    }
  }
}

// 删除用户
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${row.real_name}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteUser(row.id)
    ElMessage.success('删除成功')
    fetchTableData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    if (isEdit.value) {
      await updateUser(formData.id, formData)
      ElMessage.success('更新成功')
    } else {
      await createUser(formData)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    fetchTableData()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    submitLoading.value = false
  }
}

// 确认锁定
const confirmLock = async () => {
  try {
    await lockFormRef.value.validate()
    
    submitLoading.value = true
    
    await lockUser(lockForm.userId, { lock_reason: lockForm.lockReason })
    ElMessage.success('锁定成功')
    
    lockDialogVisible.value = false
    fetchTableData()
  } catch (error) {
    ElMessage.error('锁定失败')
  } finally {
    submitLoading.value = false
  }
}

// 确认重置密码
const confirmResetPassword = async () => {
  try {
    await resetPasswordFormRef.value.validate()
    
    submitLoading.value = true
    
    await resetUserPassword(resetPasswordForm.userId, { 
      new_password: resetPasswordForm.newPassword 
    })
    ElMessage.success('密码重置成功')
    
    resetPasswordDialogVisible.value = false
  } catch (error) {
    ElMessage.error('密码重置失败')
  } finally {
    submitLoading.value = false
  }
}

// 导出数据
const handleExport = async () => {
  try {
    const params = {
      ...filterForm,
      export: true
    }
    
    // 这里应该调用导出API
    // const response = await exportUserList(params)
    
    ElMessage.success('导出功能开发中...')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  Object.keys(formData).forEach(key => {
    if (key === 'status') {
      formData[key] = 1
    } else if (key === 'id') {
      formData[key] = null
    } else {
      formData[key] = ''
    }
  })
}

// 检查页面访问权限
const checkPagePermission = () => {
  if (!hasPermission('system:account:view')) {
    ElMessage.error('您没有权限访问此页面')
    // 可以跳转到403页面或其他处理
    return false
  }
  return true
}

// 生命周期
onMounted(() => {
  if (checkPagePermission()) {
    initData()
  }
})
</script>

<style lang="scss" scoped>
.account-management {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    background: white;
    padding: 24px;
    margin-bottom: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .page-description {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .filter-container {
    background: white;
    padding: 24px;
    margin-bottom: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    .filter-form {
      .el-form-item {
        margin-bottom: 16px;
      }
    }
  }

  .table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;

    .el-table {
      .contact-info {
        .email, .phone {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #606266;
          margin-bottom: 4px;

          .el-icon {
            margin-right: 4px;
            font-size: 12px;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      .role-dept-info {
        .department {
          font-size: 12px;
          color: #606266;
          margin-top: 4px;
        }

        .position {
          font-size: 12px;
          color: #909399;
          margin-top: 2px;
        }
      }

      .status-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }

      .login-info {
        .time {
          font-size: 12px;
          color: #303133;
        }

        .ip {
          font-size: 11px;
          color: #909399;
          margin-top: 2px;
        }
      }

      .text-muted {
        color: #C0C4CC;
        font-size: 12px;
      }

      .action-buttons {
        display: flex;
        gap: 8px;
      }
    }

    .pagination-container {
      padding: 20px;
      text-align: right;
      border-top: 1px solid #EBEEF5;
    }
  }

  .user-form {
    .el-form-item {
      margin-bottom: 20px;
    }

    .role-option {
      display: flex;
      flex-direction: column;
      
      .role-name {
        display: flex;
        align-items: center;
        margin-bottom: 4px;
      }
      
      .role-description {
        font-size: 12px;
        color: #8492a6;
        line-height: 1.2;
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .account-management {
    padding: 10px;

    .filter-container {
      padding: 16px;

      .filter-form {
        .el-form-item {
          width: 100%;
          margin-bottom: 12px;

          .el-input,
          .el-select {
            width: 100% !important;
          }
        }
      }
    }

    .table-container {
      .el-table {
        font-size: 12px;
      }

      .pagination-container {
        padding: 16px;
        text-align: center;
      }
    }
  }
}
</style> 