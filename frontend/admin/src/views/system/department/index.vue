<template>
  <div class="department-management-container">
    <!-- 筛选区域 -->
    <div class="filter-container">
      <div class="filter-item">
        <el-input
          v-model="listQuery.keyword"
          placeholder="部门名称/编码"
          style="width: 200px;"
          clearable
          @keyup.enter="handleFilter"
        />
      </div>
      <div class="filter-item">
        <el-select v-model="listQuery.status" placeholder="部门状态" clearable style="width: 140px">
          <el-option label="正常" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </div>
      <div class="filter-item">
        <el-button type="primary" @click="handleFilter" :loading="listLoading">
          搜索
        </el-button>
        <el-button @click="resetFilter">
          重置
        </el-button>
      </div>
      <div class="filter-item" style="margin-left: auto;">
        <el-button type="primary" icon="Plus" @click="handleCreate">
          新增部门
        </el-button>
      </div>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="listLoading"
      :data="treeData"
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      border
      style="width: 100%"
    >
      <el-table-column align="center" label="ID" width="80">
        <template #default="{ row }">
          {{ row.id }}
        </template>
      </el-table-column>
      
      <el-table-column label="部门信息" min-width="250">
        <template #default="{ row }">
          <div class="department-info">
            <div class="dept-name">
              <el-icon v-if="row.children && row.children.length > 0" style="margin-right: 5px;">
                <Folder />
              </el-icon>
              <el-icon v-else style="margin-right: 5px;">
                <Document />
              </el-icon>
              {{ row.name }}
            </div>
            <div class="dept-code">编码: {{ row.code }}</div>
            <div class="dept-description" v-if="row.description">{{ row.description }}</div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="部门负责人" width="150" align="center">
        <template #default="{ row }">
          <div v-if="row.manager_name" class="manager-info">
            <div class="manager-name">{{ row.manager_name }}</div>
            <div class="manager-id">ID: {{ row.manager_id }}</div>
          </div>
          <span v-else class="text-muted">未设置</span>
        </template>
      </el-table-column>
      
      <el-table-column label="排序" width="80" align="center">
        <template #default="{ row }">
          {{ row.sort_order }}
        </template>
      </el-table-column>
      
      <el-table-column label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '正常' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column label="创建时间" width="160">
        <template #default="{ row }">
          {{ formatDateTime(row.created_at) }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" align="center" width="200">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-dropdown trigger="click" @command="(command) => handleDropdownCommand(command, row)">
              <el-button size="small" type="text" class="more-btn">
                更多
                <el-icon class="el-icon--right">
                  <ArrowDown />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="add-child" icon="Plus">
                    添加子部门
                  </el-dropdown-item>
                  <el-dropdown-item 
                    :command="row.status === 1 ? 'disable' : 'enable'"
                    :icon="row.status === 1 ? 'CircleClose' : 'CircleCheck'"
                  >
                    {{ row.status === 1 ? '禁用' : '启用' }}
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" icon="Delete" divided>
                    删除部门
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogStatus === 'create' ? '新增部门' : '编辑部门'"
      :model-value="dialogFormVisible"
      @update:model-value="dialogFormVisible = $event"
      width="600px"
    >
      <el-form
        ref="dataFormRef"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="部门名称" prop="name">
              <el-input v-model="temp.name" placeholder="请输入部门名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门编码" prop="code">
              <el-input v-model="temp.code" placeholder="请输入部门编码" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="上级部门" prop="parent_id">
              <el-tree-select
                v-model="temp.parent_id"
                :data="departmentTreeOptions"
                :render-after-expand="false"
                placeholder="请选择上级部门"
                check-strictly
                clearable
                style="width: 100%"
                :props="{ 
                  label: 'name', 
                  value: 'id',
                  children: 'children'
                }"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门负责人" prop="manager_id">
              <el-select 
                v-model="temp.manager_id" 
                placeholder="请选择部门负责人" 
                style="width: 100%"
                filterable
                clearable
              >
                <el-option 
                  v-for="user in managerOptions" 
                  :key="user.value" 
                  :label="user.label" 
                  :value="user.value" 
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排序" prop="sort_order">
              <el-input-number 
                v-model="temp.sort_order" 
                :min="0" 
                :max="9999"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="temp.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="正常" :value="1" />
                <el-option label="禁用" :value="0" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="部门描述">
          <el-input 
            v-model="temp.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入部门描述（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown, Folder, Document, Plus } from '@element-plus/icons-vue'
import { 
  getDepartmentList, 
  createDepartment, 
  updateDepartment, 
  deleteDepartment,
  getDepartmentOptions 
} from '@/api/department'
import { getUserOptions } from '@/api/user'

const listLoading = ref(false)
const list = ref([])
const dialogFormVisible = ref(false)
const dialogStatus = ref('')
const dataFormRef = ref()

const listQuery = reactive({
  keyword: '',
  status: ''
})

const temp = reactive({
  id: undefined,
  name: '',
  code: '',
  parent_id: null,
  manager_id: null,
  description: '',
  sort_order: 0,
  status: 1
})

// 用户选项（作为部门负责人）
const managerOptions = ref([])

// 部门树形数据
const treeData = computed(() => {
  return buildTree(list.value)
})

// 部门树形选择器数据（排除自己和子部门）
const departmentTreeOptions = computed(() => {
  if (dialogStatus.value === 'edit') {
    return buildTree(list.value.filter(dept => 
      dept.id !== temp.id && !isChildDepartment(dept.id, temp.id)
    ))
  }
  return buildTree(list.value)
})

const rules = {
  name: [{ required: true, message: '请输入部门名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入部门编码', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

onMounted(() => {
  getList()
  getManagerOptions()
})

// 构建树形结构
const buildTree = (departments) => {
  const tree = []
  const map = {}
  
  // 创建映射
  departments.forEach(dept => {
    map[dept.id] = { ...dept, children: [] }
  })
  
  // 构建树形结构
  departments.forEach(dept => {
    if (dept.parent_id && map[dept.parent_id]) {
      map[dept.parent_id].children.push(map[dept.id])
    } else {
      tree.push(map[dept.id])
    }
  })
  
  return tree
}

// 检查是否为子部门
const isChildDepartment = (childId, parentId) => {
  const findParent = (deptId) => {
    const dept = list.value.find(d => d.id === deptId)
    if (!dept) return false
    if (dept.parent_id === parentId) return true
    if (dept.parent_id) return findParent(dept.parent_id)
    return false
  }
  return findParent(childId)
}

const getList = async () => {
  try {
    listLoading.value = true
    const params = {
      keyword: listQuery.keyword,
      status: listQuery.status || undefined
    }
    
    const response = await getDepartmentList(params)
    list.value = response.data.list || []
  } catch (error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  } finally {
    listLoading.value = false
  }
}

const getManagerOptions = async () => {
  try {
    const response = await getUserOptions()
    managerOptions.value = response.data.supervisors || []
  } catch (error) {
    console.error('获取负责人选项失败:', error)
  }
}

const handleFilter = () => {
  getList()
}

const resetFilter = () => {
  listQuery.keyword = ''
  listQuery.status = ''
  getList()
}

const resetTemp = () => {
  temp.id = undefined
  temp.name = ''
  temp.code = ''
  temp.parent_id = null
  temp.manager_id = null
  temp.description = ''
  temp.sort_order = 0
  temp.status = 1
}

const handleCreate = () => {
  resetTemp()
  dialogStatus.value = 'create'
  dialogFormVisible.value = true
}

const handleEdit = (row) => {
  temp.id = row.id
  temp.name = row.name
  temp.code = row.code
  temp.parent_id = row.parent_id
  temp.manager_id = row.manager_id
  temp.description = row.description
  temp.sort_order = row.sort_order
  temp.status = row.status
  dialogStatus.value = 'edit'
  dialogFormVisible.value = true
}

const createData = async () => {
  try {
    await dataFormRef.value.validate()
    
    const data = {
      name: temp.name,
      code: temp.code,
      parent_id: temp.parent_id,
      manager_id: temp.manager_id,
      description: temp.description,
      sort_order: temp.sort_order,
      status: temp.status
    }
    
    await createDepartment(data)
    ElMessage.success('创建成功')
    dialogFormVisible.value = false
    getList()
  } catch (error) {
    console.error('创建部门失败:', error)
    ElMessage.error('创建部门失败')
  }
}

const updateData = async () => {
  try {
    await dataFormRef.value.validate()
    
    const data = {
      name: temp.name,
      code: temp.code,
      parent_id: temp.parent_id,
      manager_id: temp.manager_id,
      description: temp.description,
      sort_order: temp.sort_order,
      status: temp.status
    }
    
    await updateDepartment(temp.id, data)
    ElMessage.success('更新成功')
    dialogFormVisible.value = false
    getList()
  } catch (error) {
    console.error('更新部门失败:', error)
    ElMessage.error('更新部门失败')
  }
}

const handleToggleStatus = async (row) => {
  const action = row.status === 1 ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(`确定要${action}部门"${row.name}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const data = {
      name: row.name,
      code: row.code,
      parent_id: row.parent_id,
      manager_id: row.manager_id,
      description: row.description,
      sort_order: row.sort_order,
      status: row.status === 1 ? 0 : 1
    }
    
    await updateDepartment(row.id, data)
    ElMessage.success(`${action}成功`)
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}部门失败:`, error)
      ElMessage.error(`${action}部门失败`)
    }
  }
}

const handleDelete = async (row) => {
  if (row.children && row.children.length > 0) {
    ElMessage.warning('该部门下还有子部门，无法删除')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确定要删除部门"${row.name}"吗？此操作不可恢复！`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteDepartment(row.id)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除部门失败:', error)
      ElMessage.error('删除部门失败')
    }
  }
}

const handleAddChild = (row) => {
  resetTemp()
  temp.parent_id = row.id
  dialogStatus.value = 'create'
  dialogFormVisible.value = true
}

const formatDateTime = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
}

const handleDropdownCommand = (command, row) => {
  switch (command) {
    case 'add-child':
      handleAddChild(row)
      break
    case 'enable':
    case 'disable':
      handleToggleStatus(row)
      break
    case 'delete':
      handleDelete(row)
      break
  }
}
</script>

<style lang="scss" scoped>
.department-management-container {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  
  .filter-container {
    padding: 20px;
    margin-bottom: 0;
    border-bottom: 1px solid #ebeef5;
    background: #fafafa;
    border-radius: 4px 4px 0 0;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    
    .filter-item {
      margin-right: 10px;
      margin-bottom: 0;
      
      &:last-child {
        margin-right: 0;
      }
    }
  }

  .department-info {
    .dept-name {
      font-weight: 500;
      color: #303133;
      margin-bottom: 4px;
      display: flex;
      align-items: center;
    }
    .dept-code {
      font-size: 12px;
      color: #909399;
      margin-bottom: 2px;
    }
    .dept-description {
      font-size: 12px;
      color: #606266;
    }
  }

  .manager-info {
    .manager-name {
      font-weight: 500;
      color: #303133;
      margin-bottom: 2px;
    }
    .manager-id {
      font-size: 12px;
      color: #909399;
    }
  }

  .text-muted {
    color: #909399;
  }

  .action-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    
    .more-btn {
      color: #606266;
      &:hover {
        color: #409eff;
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }

  :deep(.el-table) {
    .el-table__header {
      background: #f5f7fa;
      
      th {
        background: #f5f7fa !important;
        color: #909399;
        font-weight: 500;
      }
    }

    .el-table__row {
      &:hover {
        background: #f5f7fa;
      }
    }
  }
}
</style> 