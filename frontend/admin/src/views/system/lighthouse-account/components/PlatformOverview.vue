<template>
  <div class="platform-overview">
    <!-- 测试显示 -->
    <div style="padding: 20px; background: #f0f0f0; margin-bottom: 20px;">
      <h3>统计概览组件已加载</h3>
      <p>Loading状态: {{ loading }}</p>
      <p>总体统计数据长度: {{ totalStats.length }}</p>
      <p>平台统计数据长度: {{ platformStats.length }}</p>
    </div>
    
    <!-- 总体统计卡片 -->
    <div class="total-stats-section">
      <h3 class="section-title">
        <el-icon><DataAnalysis /></el-icon>
        总体统计
      </h3>
      <div class="stats-cards">
        <el-card class="stat-card" v-for="stat in totalStats" :key="stat.key">
          <div class="stat-content">
            <div 
              class="stat-icon" 
              :style="{ 
                '--icon-color-start': stat.color,
                '--icon-color-end': stat.colorEnd || stat.color
              }"
            >
              <el-icon><component :is="stat.icon" /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
              <div class="stat-change" :class="stat.changeClass">
                <el-icon v-if="stat.trend === 'up'"><TrendCharts /></el-icon>
                <el-icon v-else-if="stat.trend === 'down'"><Bottom /></el-icon>
                <span>{{ stat.change }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

        <!-- 平台分布统计 -->
    <div class="platform-stats-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>平台账号分布</span>
          </div>
        </template>
        
        <el-table :data="platformStats" stripe>
          <el-table-column prop="platform" label="平台" width="100">
            <template #default="scope">
              <div class="platform-cell">
                <el-icon :style="{ color: scope.row.color }">
                  <component :is="scope.row.icon" />
                </el-icon>
                <span>{{ scope.row.platform }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="totalAccounts" label="总账号" align="center" />
          <el-table-column prop="activeAccounts" label="启用" align="center">
            <template #default="scope">
              <span class="text-success">{{ scope.row.activeAccounts }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="totalBalance" label="总余额" align="right">
            <template #default="scope">
              ¥{{ formatAmount(scope.row.totalBalance) }}
            </template>
          </el-table-column>
          <el-table-column prop="todayCost" label="今日消耗" align="right">
            <template #default="scope">
              ¥{{ formatAmount(scope.row.todayCost) }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 代理商维度统计 -->
    <div class="agent-stats-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <h3 class="section-title">
              <el-icon><OfficeBuilding /></el-icon>
              代理商维度统计
            </h3>
            <div class="header-actions">
              <el-input
                v-model="agentSearchKeyword"
                placeholder="搜索代理商"
                prefix-icon="Search"
                size="small"
                style="width: 200px; margin-right: 12px"
                clearable
              />
              <el-button size="small" @click="handleRefreshAgentStats">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </div>
        </template>

        <el-table 
          :data="filteredAgentStats" 
          stripe 
          border 
          v-loading="agentLoading"
          :default-sort="{ prop: 'totalBalance', order: 'descending' }"
        >
          <el-table-column prop="agentName" label="代理商名称" min-width="150" show-overflow-tooltip />
          <el-table-column prop="agentCode" label="代理商编号" width="120" />
          <el-table-column prop="platformCount" label="覆盖平台" width="100" align="center">
            <template #default="scope">
              <el-tag size="small">{{ scope.row.platformCount }}个</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="totalAccounts" label="管理账号" width="100" align="center" />
          <el-table-column prop="activeAccounts" label="启用账号" width="100" align="center">
            <template #default="scope">
              <span class="text-success">{{ scope.row.activeAccounts }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="totalBalance" label="总余额" width="140" align="right" sortable>
            <template #default="scope">
              <span :class="getBalanceClass(scope.row.totalBalance)">
                ¥{{ formatAmount(scope.row.totalBalance) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="todayCost" label="今日消耗" width="120" align="right" sortable>
            <template #default="scope">
              ¥{{ formatAmount(scope.row.todayCost) }}
            </template>
          </el-table-column>
          <el-table-column prop="weekCost" label="本周消耗" width="120" align="right" sortable>
            <template #default="scope">
              ¥{{ formatAmount(scope.row.weekCost) }}
            </template>
          </el-table-column>
          <el-table-column prop="monthCost" label="本月消耗" width="120" align="right" sortable>
            <template #default="scope">
              ¥{{ formatAmount(scope.row.monthCost) }}
            </template>
          </el-table-column>
          <el-table-column prop="lastActiveTime" label="最后活跃" width="160">
            <template #default="scope">
              {{ formatDate(scope.row.lastActiveTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right" align="center">
            <template #default="scope">
              <el-button size="small" type="primary" @click="handleViewAgentDetail(scope.row)">
                详情
              </el-button>
              <el-button size="small" type="success" @click="handleViewAgentAccounts(scope.row)">
                查看账号
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="agentPagination.page"
            v-model:page-size="agentPagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="agentPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleAgentSizeChange"
            @current-change="handleAgentCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 分账号维度统计 -->
    <div class="account-stats-section">
      <el-card>
        <template #header>
          <div class="card-header">
            <h3 class="section-title">
              <el-icon><User /></el-icon>
              分账号维度统计
            </h3>
            <div class="header-actions">
              <el-select
                v-model="accountFilterPlatform"
                placeholder="筛选平台"
                size="small"
                style="width: 120px; margin-right: 12px"
                clearable
                @change="handleAccountFilter"
              >
                <el-option label="全部" value="" />
                <el-option label="灯火" value="lighthouse" />
                <el-option label="小红书" value="xiaohongshu" />
              </el-select>
              <el-select
                v-model="accountFilterAgent"
                placeholder="筛选代理商"
                size="small"
                style="width: 150px; margin-right: 12px"
                clearable
                @change="handleAccountFilter"
              >
                <el-option label="全部" value="" />
                <el-option 
                  v-for="agent in agentOptions" 
                  :key="agent.value" 
                  :label="agent.label" 
                  :value="agent.value" 
                />
              </el-select>
              <el-input
                v-model="accountSearchKeyword"
                placeholder="搜索账号名称"
                prefix-icon="Search"
                size="small"
                style="width: 200px; margin-right: 12px"
                clearable
                @input="handleAccountFilter"
              />
              <el-button size="small" @click="handleRefreshAccountStats">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
              <el-button size="small" type="primary" @click="handleExportAccountStats">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </div>
          </div>
        </template>

        <el-table 
          :data="filteredAccountStats" 
          stripe 
          border 
          v-loading="accountLoading"
          :default-sort="{ prop: 'balance', order: 'descending' }"
        >
          <el-table-column prop="accountName" label="账号名称" min-width="180" show-overflow-tooltip />
          <el-table-column prop="platform" label="平台" width="100" align="center">
            <template #default="scope">
              <div class="platform-cell">
                <el-icon :style="{ color: getPlatformColor(scope.row.platform) }">
                  <component :is="getPlatformIcon(scope.row.platform)" />
                </el-icon>
                <span>{{ scope.row.platform }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="agentName" label="所属代理商" width="150" show-overflow-tooltip />
          <el-table-column prop="accountId" label="账号ID" width="120" show-overflow-tooltip />
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="authStatus" label="授权状态" width="100" align="center">
            <template #default="scope">
              <el-tag :type="getAuthStatusType(scope.row.authStatus)" size="small">
                {{ getAuthStatusText(scope.row.authStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="balance" label="余额" width="120" align="right" sortable>
            <template #default="scope">
              <span :class="getBalanceClass(scope.row.balance)">
                ¥{{ formatAmount(scope.row.balance) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="todayCost" label="今日消耗" width="120" align="right" sortable>
            <template #default="scope">
              ¥{{ formatAmount(scope.row.todayCost) }}
            </template>
          </el-table-column>
          <el-table-column prop="weekCost" label="本周消耗" width="120" align="right" sortable>
            <template #default="scope">
              ¥{{ formatAmount(scope.row.weekCost) }}
            </template>
          </el-table-column>
          <el-table-column prop="monthCost" label="本月消耗" width="120" align="right" sortable>
            <template #default="scope">
              ¥{{ formatAmount(scope.row.monthCost) }}
            </template>
          </el-table-column>
          <el-table-column prop="lastActiveTime" label="最后活跃" width="160">
            <template #default="scope">
              {{ formatDate(scope.row.lastActiveTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="ownerName" label="负责人" width="100" show-overflow-tooltip />
          <el-table-column label="操作" width="200" fixed="right" align="center">
            <template #default="scope">
              <el-button size="small" type="primary" @click="handleViewAccountDetail(scope.row)">
                详情
              </el-button>
              <el-button size="small" type="success" @click="handleSyncAccount(scope.row)">
                同步
              </el-button>
              <el-dropdown @command="(command) => handleAccountAction(command, scope.row)">
                <el-button size="small" type="text">
                  更多<el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="toggle-status">
                      {{ scope.row.status === 'active' ? '禁用' : '启用' }}
                    </el-dropdown-item>
                    <el-dropdown-item command="view-history" divided>查看历史</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="accountPagination.page"
            v-model:page-size="accountPagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="accountPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleAccountSizeChange"
            @current-change="handleAccountCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  DataAnalysis, OfficeBuilding, User, VideoCamera, TrendCharts, Bottom,
  Refresh, Download, Search, ArrowDown, Monitor, CircleCheck, Money
} from '@element-plus/icons-vue'
import { formatDate, formatAmount } from '@/utils/index'

// 响应式数据
const loading = ref(false)
const agentLoading = ref(false)
const accountLoading = ref(false)

// 搜索和筛选
const agentSearchKeyword = ref('')
const accountSearchKeyword = ref('')
const accountFilterPlatform = ref('')
const accountFilterAgent = ref('')

// 分页数据
const agentPagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

const accountPagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 总体统计数据
const totalStats = ref([
  {
    key: 'totalAccounts',
    label: '总账号数',
    value: '156',
    icon: 'User',
    color: '#409eff',
    colorEnd: '#66b3ff',
    change: '+12.5%',
    trend: 'up',
    changeClass: 'text-success'
  },
  {
    key: 'activeAccounts',
    label: '启用账号',
    value: '142',
    icon: 'CircleCheck',
    color: '#67c23a',
    colorEnd: '#85d461',
    change: '+8.3%',
    trend: 'up',
    changeClass: 'text-success'
  },
  {
    key: 'totalBalance',
    label: '总余额(万)',
    value: '2,847.6',
    icon: 'Money',
    color: '#e6a23c',
    colorEnd: '#edb75f',
    change: '-3.2%',
    trend: 'down',
    changeClass: 'text-danger'
  },
  {
    key: 'todayCost',
    label: '今日消耗(万)',
    value: '186.4',
    icon: 'TrendCharts',
    color: '#f56c6c',
    colorEnd: '#f78989',
    change: '+15.7%',
    trend: 'up',
    changeClass: 'text-success'
  }
])

// 平台统计数据
const platformStats = ref([
  {
    platform: '灯火',
    icon: 'Monitor',
    color: '#f39c12',
    totalAccounts: 89,
    activeAccounts: 82,
    totalBalance: 1567800,
    todayCost: 98650
  },
  {
    platform: '小红书',
    icon: 'VideoCamera',
    color: '#ff2442',
    totalAccounts: 67,
    activeAccounts: 60,
    totalBalance: 1279400,
    todayCost: 67890
  }
])

// 代理商统计数据
const agentStats = ref([
  {
    id: 1,
    agentName: '北京广告代理有限公司',
    agentCode: 'BJ001',
    platformCount: 2,
    totalAccounts: 45,
    activeAccounts: 42,
    totalBalance: 892650,
    todayCost: 45600,
    weekCost: 285400,
    monthCost: 1156780,
    lastActiveTime: '2024-01-15 14:30:00'
  },
  {
    id: 2,
    agentName: '上海营销推广公司',
    agentCode: 'SH002',
    platformCount: 2,
    totalAccounts: 38,
    activeAccounts: 35,
    totalBalance: 756890,
    todayCost: 38900,
    weekCost: 234500,
    monthCost: 945600,
    lastActiveTime: '2024-01-15 13:45:00'
  },
  {
    id: 3,
    agentName: '深圳互动媒体',
    agentCode: 'SZ003',
    platformCount: 1,
    totalAccounts: 28,
    activeAccounts: 26,
    totalBalance: 567440,
    todayCost: 29800,
    weekCost: 189600,
    monthCost: 678900,
    lastActiveTime: '2024-01-15 12:20:00'
  },
  {
    id: 4,
    agentName: '广州创意工坊',
    agentCode: 'GZ004',
    platformCount: 2,
    totalAccounts: 31,
    activeAccounts: 28,
    totalBalance: 445600,
    todayCost: 22100,
    weekCost: 145600,
    monthCost: 567800,
    lastActiveTime: '2024-01-15 11:10:00'
  },
  {
    id: 5,
    agentName: '杭州数字营销',
    agentCode: 'HZ005',
    platformCount: 1,
    totalAccounts: 14,
    activeAccounts: 11,
    totalBalance: 184210,
    todayCost: 9890,
    weekCost: 67800,
    monthCost: 234500,
    lastActiveTime: '2024-01-15 10:50:00'
  }
])

// 账号统计数据
const accountStats = ref([
  {
    id: 1,
    accountName: '灯火主推广账号A',
    platform: '灯火',
    agentName: '北京广告代理有限公司',
    accountId: 'lh_main_001',
    status: 'active',
    authStatus: 'authorized',
    balance: 156780,
    todayCost: 8650,
    weekCost: 45600,
    monthCost: 178900,
    lastActiveTime: '2024-01-15 14:30:00',
    ownerName: '张三'
  },
  {
    id: 2,
    accountName: '小红书推广账号B1',
    platform: '小红书',
    agentName: '上海营销推广公司',
    accountId: 'xhs_main_002',
    status: 'active',
    authStatus: 'authorized',
    balance: 89650,
    todayCost: 4560,
    weekCost: 28900,
    monthCost: 123450,
    lastActiveTime: '2024-01-15 13:45:00',
    ownerName: '李四'
  },
  {
    id: 3,
    accountName: '灯火测试账号C',
    platform: '灯火',
    agentName: '深圳互动媒体',
    accountId: 'lh_test_003',
    status: 'disabled',
    authStatus: 'expired',
    balance: 12340,
    todayCost: 0,
    weekCost: 1200,
    monthCost: 5600,
    lastActiveTime: '2024-01-12 09:20:00',
    ownerName: '王五'
  },
  {
    id: 4,
    accountName: '小红书备用账号D',
    platform: '小红书',
    agentName: '广州创意工坊',
    accountId: 'xhs_backup_004',
    status: 'active',
    authStatus: 'unauthorized',
    balance: 67890,
    todayCost: 3450,
    weekCost: 23400,
    monthCost: 98760,
    lastActiveTime: '2024-01-15 11:10:00',
    ownerName: '赵六'
  },
  {
    id: 5,
    accountName: '灯火高消耗账号E',
    platform: '灯火',
    agentName: '杭州数字营销',
    accountId: 'lh_high_005',
    status: 'active',
    authStatus: 'authorized',
    balance: 234560,
    todayCost: 15600,
    weekCost: 89400,
    monthCost: 356700,
    lastActiveTime: '2024-01-15 10:50:00',
    ownerName: '钱七'
  }
])

// 计算属性
const filteredAgentStats = computed(() => {
  let filtered = agentStats.value
  
  if (agentSearchKeyword.value) {
    filtered = filtered.filter(item => 
      item.agentName.toLowerCase().includes(agentSearchKeyword.value.toLowerCase()) ||
      item.agentCode.toLowerCase().includes(agentSearchKeyword.value.toLowerCase())
    )
  }
  
  return filtered
})

const filteredAccountStats = computed(() => {
  let filtered = accountStats.value
  
  if (accountFilterPlatform.value) {
    filtered = filtered.filter(item => item.platform === accountFilterPlatform.value)
  }
  
  if (accountFilterAgent.value) {
    filtered = filtered.filter(item => item.agentName === accountFilterAgent.value)
  }
  
  if (accountSearchKeyword.value) {
    filtered = filtered.filter(item => 
      item.accountName.toLowerCase().includes(accountSearchKeyword.value.toLowerCase()) ||
      item.accountId.toLowerCase().includes(accountSearchKeyword.value.toLowerCase())
    )
  }
  
  return filtered
})

const agentOptions = computed(() => {
  const agents = [...new Set(accountStats.value.map(item => item.agentName))]
  return agents.map(agent => ({
    label: agent,
    value: agent
  }))
})



// 生命周期
onMounted(async () => {
  try {
    await loadAllData()
  } catch (error) {
    console.error('组件初始化失败:', error)
    ElMessage.error('页面初始化失败，请刷新重试')
  }
})

onUnmounted(() => {
  // 这里可以放置其他清理逻辑
})

// 方法
const loadAllData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadAgentStats(),
      loadAccountStats()
    ])
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadAgentStats = async () => {
  agentLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    agentPagination.total = agentStats.value.length
  } catch (error) {
    ElMessage.error('加载代理商数据失败')
  } finally {
    agentLoading.value = false
  }
}

const loadAccountStats = async () => {
  accountLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    accountPagination.total = accountStats.value.length
  } catch (error) {
    ElMessage.error('加载账号数据失败')
  } finally {
    accountLoading.value = false
  }
}

// 筛选处理
const handleAccountFilter = () => {
  // 重新计算分页
  accountPagination.page = 1
  accountPagination.total = filteredAccountStats.value.length
}

// 分页处理
const handleAgentSizeChange = (size) => {
  agentPagination.pageSize = size
  agentPagination.page = 1
}

const handleAgentCurrentChange = (page) => {
  agentPagination.page = page
}

const handleAccountSizeChange = (size) => {
  accountPagination.pageSize = size
  accountPagination.page = 1
}

const handleAccountCurrentChange = (page) => {
  accountPagination.page = page
}

// 刷新处理
const handleRefreshAgentStats = () => {
  agentSearchKeyword.value = ''
  loadAgentStats()
}

const handleRefreshAccountStats = () => {
  accountSearchKeyword.value = ''
  accountFilterPlatform.value = ''
  accountFilterAgent.value = ''
  loadAccountStats()
}

// 操作处理
const handleViewAgentDetail = (agent) => {
  ElMessage.info(`查看代理商详情: ${agent.agentName}`)
}

const handleViewAgentAccounts = (agent) => {
  ElMessage.info(`查看代理商账号: ${agent.agentName}`)
}

const handleViewAccountDetail = (account) => {
  ElMessage.info(`查看账号详情: ${account.accountName}`)
}

const handleSyncAccount = async (account) => {
  try {
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    account.lastActiveTime = new Date().toISOString()
    ElMessage.success('账号数据同步成功')
  } catch (error) {
    ElMessage.error('同步失败')
  } finally {
    loading.value = false
  }
}

const handleAccountAction = (command, account) => {
  switch (command) {
    case 'edit':
      ElMessage.info(`编辑账号: ${account.accountName}`)
      break
    case 'toggle-status':
      account.status = account.status === 'active' ? 'disabled' : 'active'
      ElMessage.success(`账号状态已更新`)
      break
    case 'view-history':
      ElMessage.info(`查看历史: ${account.accountName}`)
      break
  }
}

const handleExportAccountStats = () => {
  ElMessage.success('账号统计数据导出成功')
}

// 工具方法
const getBalanceClass = (balance) => {
  if (balance < 10000) return 'text-danger'
  if (balance < 50000) return 'text-warning'
  return 'text-success'
}

const getStatusType = (status) => {
  const statusMap = {
    'active': 'success',
    'disabled': 'danger',
    'pending': 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'active': '启用',
    'disabled': '禁用',
    'pending': '待审核'
  }
  return statusMap[status] || '未知'
}

const getAuthStatusType = (status) => {
  const statusMap = {
    'authorized': 'success',
    'unauthorized': 'danger',
    'expired': 'warning'
  }
  return statusMap[status] || 'info'
}

const getAuthStatusText = (status) => {
  const statusMap = {
    'authorized': '已授权',
    'unauthorized': '未授权',
    'expired': '已过期'
  }
  return statusMap[status] || '未知'
}

const getPlatformColor = (platform) => {
  const colorMap = {
    '灯火': '#f39c12',
    '小红书': '#ff2442'
  }
  return colorMap[platform] || '#409eff'
}

const getPlatformIcon = (platform) => {
  const iconMap = {
    '灯火': 'Monitor',
    '小红书': 'VideoCamera'
  }
  return iconMap[platform] || 'Postcard'
}






</script>

<style lang="scss" scoped>
.platform-overview {
  padding: 20px;
  background: #f5f7fa;
}

// 总体统计部分
.total-stats-section {
  margin-bottom: 24px;

  .section-title {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;

    .el-icon {
      color: #409eff;
    }
  }

  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;

    .stat-card {
      border: none;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      }

      .stat-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .stat-icon {
          width: 64px;
          height: 64px;
          border-radius: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 28px;
          background: linear-gradient(135deg, var(--icon-color-start), var(--icon-color-end));
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          position: relative;
          
          &::before {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: inherit;
            background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0));
            pointer-events: none;
          }
        }

        .stat-info {
          flex: 1;

          .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            line-height: 1;
            margin-bottom: 4px;
          }

          .stat-label {
            font-size: 14px;
            color: #8492a6;
            margin-bottom: 8px;
          }

          .stat-change {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;

            &.text-success {
              color: #67c23a;
            }

            &.text-danger {
              color: #f56c6c;
            }
          }
        }
      }
    }
  }
}

// 平台统计部分
.platform-stats-section {
  margin-bottom: 24px;
}

// 卡片头部
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .section-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

// 平台单元格
.platform-cell {
  display: flex;
  align-items: center;
  gap: 8px;

  .el-icon {
    font-size: 18px;
    padding: 4px;
    border-radius: 6px;
    background-color: currentColor;
    color: white;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
  
  span {
    font-weight: 500;
  }
}

// 分页容器
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

// 文本颜色类
.text-success {
  color: #67c23a;
}

.text-warning {
  color: #e6a23c;
}

.text-danger {
  color: #f56c6c;
}

// 响应式设计
@media (max-width: 1200px) {
  .stats-cards {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .platform-overview {
    padding: 10px;
  }

  .stats-cards {
    grid-template-columns: 1fr;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .header-actions {
      width: 100%;
      flex-wrap: wrap;
    }
  }
}
</style> 