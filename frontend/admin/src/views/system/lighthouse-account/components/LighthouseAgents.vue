<template>
  <div class="lighthouse-agents-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="请输入代理商名称或PID"
        style="width: 200px"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-select
        v-model="listQuery.status"
        placeholder="状态"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="有效" :value="1" />
        <el-option label="失效" :value="-1" />
      </el-select>
      <el-select
        v-model="listQuery.api_type"
        placeholder="API类型"
        clearable
        style="width: 140px"
        class="filter-item"
      >
        <el-option label="直连" :value="1" />
        <el-option label="代理商封装" :value="2" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="Search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="Plus"
        @click="handleCreate"
      >
        新增代理商
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="80">
        <template #default="{ row }">
          {{ row.id }}
        </template>
      </el-table-column>
      <el-table-column label="代理商名称">
        <template #default="{ row }">
          {{ row.agent_name }}
        </template>
      </el-table-column>
      <el-table-column label="代理商PID" width="150">
        <template #default="{ row }">
          {{ row.agent_pid }}
        </template>
      </el-table-column>
      <el-table-column label="代理商Token" width="150">
        <template #default="{ row }">
          <span v-if="row.agent_token">
            {{ row.agent_token.substring(0, 16) }}...
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="API类型" width="120" align="center">
        <template #default="{ row }">
          <el-tag :type="row.api_type === 1 ? 'success' : 'warning'">
            {{ row.api_type === 1 ? '直连' : '代理商封装' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="余额" width="120" align="center">
        <template #default="{ row }">
          <span :class="{ 'text-danger': row.balance <= 0 }">
            ¥{{ parseFloat(row.balance).toFixed(2) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '有效' : '失效' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="info" size="small" @click="handleCheckBalance(row)">
              查询余额
            </el-button>
            <el-dropdown trigger="click" @command="(command) => handleDropdownCommand(command, row)">
              <el-button size="small" type="text" class="more-btn">
                更多
                <el-icon class="el-icon--right">
                  <ArrowDown />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item 
                    :command="row.status === 1 ? 'disable' : 'enable'"
                    :icon="row.status === 1 ? 'CircleClose' : 'CircleCheck'"
                  >
                    {{ row.status === 1 ? '停用代理商' : '启用代理商' }}
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" icon="Delete" divided>
                    删除代理商
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <Pagination
      :total="total"
      :page="listQuery.page"
      :limit="listQuery.limit"
      @pagination="handlePagination"
    />

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogStatus === 'create' ? '新增代理商' : '编辑代理商'"
      :model-value="dialogFormVisible"
      @update:model-value="dialogFormVisible = $event"
      width="600px"
    >
      <el-form
        ref="dataFormRef"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="120px"
      >
        <el-form-item label="代理商名称" prop="agent_name">
          <el-input v-model="temp.agent_name" placeholder="请输入代理商名称" />
        </el-form-item>
        <el-form-item label="代理商PID" prop="agent_pid">
          <el-input v-model="temp.agent_pid" placeholder="请输入代理商PID" />
        </el-form-item>
        <el-form-item label="代理商Token" prop="agent_token">
          <el-input
            v-model="temp.agent_token"
            type="textarea"
            :rows="3"
            placeholder="请输入代理商Token"
          />
        </el-form-item>
        <el-form-item label="API类型" prop="api_type">
          <el-select v-model="temp.api_type" placeholder="请选择API类型" style="width: 100%">
            <el-option label="直连" :value="1" />
            <el-option label="代理商封装" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="代理商余额" prop="balance">
          <el-input-number
            v-model="temp.balance"
            :precision="2"
            :step="100"
            :min="0"
            placeholder="请输入代理商余额"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="temp.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="有效" :value="1" />
            <el-option label="失效" :value="-1" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="temp.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 余额查询对话框 -->
    <el-dialog
      title="余额查询结果"
      :model-value="balanceDialogVisible"
      @update:model-value="balanceDialogVisible = $event"
      width="400px"
    >
      <div class="balance-info">
        <p><strong>代理商名称:</strong> {{ balanceInfo.agent_name }}</p>
        <p><strong>代理商PID:</strong> {{ balanceInfo.agent_pid }}</p>
        <p><strong>当前余额:</strong> 
          <span class="balance-amount" :class="{ 'text-danger': balanceInfo.balance <= 0 }">
            ¥{{ parseFloat(balanceInfo.balance || 0).toFixed(2) }}
          </span>
        </p>
        <p><strong>查询时间:</strong> {{ balanceInfo.query_time }}</p>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="balanceDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import Pagination from './Pagination.vue'

const listLoading = ref(false)
const list = ref([])
const total = ref(0)
const dialogFormVisible = ref(false)
const balanceDialogVisible = ref(false)
const dialogStatus = ref('')
const dataFormRef = ref()

const listQuery = reactive({
  page: 1,
  limit: 20,
  keyword: '',
  status: '',
  api_type: ''
})

const temp = reactive({
  id: undefined,
  agent_name: '',
  agent_pid: '',
  agent_token: '',
  api_type: 1,
  balance: 0,
  status: 1,
  remark: ''
})

const balanceInfo = reactive({
  agent_name: '',
  agent_pid: '',
  balance: 0,
  query_time: ''
})

const rules = {
  agent_name: [{ required: true, message: '代理商名称是必填的', trigger: 'blur' }],
  agent_pid: [{ required: true, message: '代理商PID是必填的', trigger: 'blur' }],
  agent_token: [{ required: true, message: '代理商Token是必填的', trigger: 'blur' }],
  api_type: [{ required: true, message: 'API类型是必填的', trigger: 'change' }],
  balance: [{ required: true, message: '代理商余额是必填的', trigger: 'blur' }]
}

onMounted(() => {
  getList()
})

const getList = () => {
  listLoading.value = true
  // TODO: 调用API获取代理商列表
  // 模拟数据
  setTimeout(() => {
    list.value = [
      {
        id: 1,
        agent_name: '测试代理商1',
        agent_pid: 'PID_TEST_001',
        agent_token: 'token_1234567890abcdef',
        api_type: 1,
        balance: 1000.00,
        status: 1,
        remark: '测试用代理商账号',
        created_at: '2023-12-01 10:00:00',
        updated_at: '2023-12-01 10:00:00'
      },
      {
        id: 2,
        agent_name: '测试代理商2',
        agent_pid: 'PID_TEST_002',
        agent_token: 'token_abcdef1234567890',
        api_type: 2,
        balance: 2500.50,
        status: 1,
        remark: '代理商封装API类型',
        created_at: '2023-12-02 10:00:00',
        updated_at: '2023-12-02 10:00:00'
      },
      {
        id: 3,
        agent_name: '停用代理商',
        agent_pid: 'PID_TEST_003',
        agent_token: 'token_disabled_test',
        api_type: 1,
        balance: 0.00,
        status: -1,
        remark: '已停用的代理商账号',
        created_at: '2023-12-03 10:00:00',
        updated_at: '2023-12-03 10:00:00'
      }
    ]
    total.value = 3
    listLoading.value = false
  }, 1000)
}

const handleFilter = () => {
  listQuery.page = 1
  getList()
}

const resetTemp = () => {
  temp.id = undefined
  temp.agent_name = ''
  temp.agent_pid = ''
  temp.agent_token = ''
  temp.api_type = 1
  temp.balance = 0
  temp.status = 1
  temp.remark = ''
}

const handleCreate = () => {
  resetTemp()
  dialogStatus.value = 'create'
  dialogFormVisible.value = true
}

const handleEdit = (row) => {
  Object.assign(temp, row)
  dialogStatus.value = 'update'
  dialogFormVisible.value = true
}

const createData = () => {
  dataFormRef.value.validate((valid) => {
    if (valid) {
      // TODO: 调用API创建代理商
      ElMessage.success('创建成功')
      dialogFormVisible.value = false
      getList()
    }
  })
}

const updateData = () => {
  dataFormRef.value.validate((valid) => {
    if (valid) {
      // TODO: 调用API更新代理商
      ElMessage.success('更新成功')
      dialogFormVisible.value = false
      getList()
    }
  })
}

const handleCheckBalance = (row) => {
  // TODO: 调用API查询代理商余额
  Object.assign(balanceInfo, {
    agent_name: row.agent_name,
    agent_pid: row.agent_pid,
    balance: row.balance,
    query_time: new Date().toLocaleString()
  })
  balanceDialogVisible.value = true
}

const handleToggleStatus = (row) => {
  const action = row.status === 1 ? '停用' : '启用'
  ElMessageBox.confirm(`确定要${action}该代理商吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // TODO: 调用API切换状态
    row.status = row.status === 1 ? -1 : 1
    ElMessage.success(`${action}成功`)
  })
}

const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该代理商吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // TODO: 调用API删除代理商
    ElMessage.success('删除成功')
    getList()
  })
}

const formatDate = (dateString) => {
  return dateString
}

const handlePagination = ({ page, limit }) => {
  listQuery.page = page
  listQuery.limit = limit
  getList()
}

const handleDropdownCommand = (command, row) => {
  switch (command) {
    case 'enable':
    case 'disable':
      handleToggleStatus(row)
      break
    case 'delete':
      handleDelete(row)
      break
  }
}
</script>

<style lang="scss" scoped>
.lighthouse-agents-container {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  
  .filter-container {
    padding: 20px;
    margin-bottom: 0;
    border-bottom: 1px solid #ebeef5;
    background: #fafafa;
    border-radius: 4px 4px 0 0;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    
    .filter-item {
      margin-right: 10px;
      margin-bottom: 0;
      
      &:last-child {
        margin-right: 0;
      }
    }

    .el-input {
      min-width: 200px;
    }

    .el-select {
      min-width: 120px;
    }

    .el-button {
      height: 32px;
      padding: 8px 15px;
      font-size: 14px;
      border-radius: 4px;
      
      &.el-button--primary {
        background: #409eff;
        border-color: #409eff;
        
        &:hover {
          background: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }

  .el-table {
    margin: 0;
    border-radius: 0;
    
    .el-table__header-wrapper {
      background: #f5f7fa;
    }

    .el-table__header th {
      background: #f5f7fa;
      color: #606266;
      font-weight: 500;
      border-bottom: 1px solid #ebeef5;
    }

    .el-table__row {
      &:hover > td {
        background-color: #f5f7fa !important;
      }
    }

    .el-table__body td {
      border-bottom: 1px solid #ebeef5;
    }

    .action-buttons {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      
      .el-button {
        padding: 4px 8px;
        font-size: 12px;
        border-radius: 3px;
        
        &.el-button--small {
          padding: 4px 8px;
          font-size: 12px;
        }
        
        &.more-btn {
          color: #606266;
          padding: 4px 6px;
          
          &:hover {
            color: #409eff;
            background: rgba(64, 158, 255, 0.1);
          }
          
          .el-icon {
            font-size: 12px;
            margin-left: 2px;
          }
        }
      }
    }
  }

  .text-danger {
    color: #f56c6c;
    font-weight: 500;
  }

  .el-tag {
    border-radius: 4px;
    font-size: 12px;
    
    &.el-tag--success {
      background-color: #f0f9ff;
      border-color: #b3d8ff;
      color: #409eff;
    }
    
    &.el-tag--warning {
      background-color: #fdf6ec;
      border-color: #f5dab1;
      color: #e6a23c;
    }
    
    &.el-tag--danger {
      background-color: #fef0f0;
      border-color: #fbc4c4;
      color: #f56c6c;
    }
  }

  // 对话框样式优化
  :deep(.el-dialog) {
    border-radius: 6px;
    
    .el-dialog__header {
      padding: 20px 24px 10px;
      border-bottom: 1px solid #ebeef5;
      
      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }
    
    .el-dialog__body {
      padding: 24px;
      
      .el-form {
        .el-form-item {
          margin-bottom: 22px;
          
          .el-form-item__label {
            color: #606266;
            font-weight: 500;
            line-height: 32px;
          }
          
          .el-input, .el-select, .el-input-number {
            width: 100%;
            
            .el-input__inner {
              border-radius: 4px;
              transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
              
              &:focus {
                border-color: #409eff;
              }
            }
          }
          
          .el-textarea {
            .el-textarea__inner {
              border-radius: 4px;
              transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
              
              &:focus {
                border-color: #409eff;
              }
            }
          }
        }
      }
    }
    
    .el-dialog__footer {
      padding: 10px 24px 20px;
      text-align: right;
      border-top: 1px solid #ebeef5;
      
      .el-button {
        padding: 8px 20px;
        border-radius: 4px;
        font-size: 14px;
        
        &.el-button--primary {
          background: #409eff;
          border-color: #409eff;
          
          &:hover {
            background: #66b1ff;
            border-color: #66b1ff;
          }
        }
      }
    }
  }

  .balance-info {
    padding: 20px;
    background: #f9f9f9;
    border-radius: 6px;
    border: 1px solid #ebeef5;
    
    p {
      margin-bottom: 16px;
      line-height: 1.6;
      color: #606266;
      font-size: 14px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      strong {
        color: #303133;
        font-weight: 600;
        margin-right: 8px;
        min-width: 80px;
        display: inline-block;
      }
    }

    .balance-amount {
      font-size: 20px;
      font-weight: 700;
      color: #67c23a;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

      &.text-danger {
        color: #f56c6c;
      }
    }
  }

  // 分页样式优化
  :deep(.pagination-wrapper) {
    padding: 20px;
    background: #fafafa;
    border-top: 1px solid #ebeef5;
    border-radius: 0 0 4px 4px;
    
    .el-pagination {
      .el-pagination__total {
        color: #606266;
        font-size: 14px;
      }
      
      .el-pager li {
        border-radius: 4px;
        margin: 0 2px;
        
        &.active {
          background: #409eff;
          color: #fff;
        }
      }
      
      .el-pagination__jump {
        color: #606266;
        font-size: 14px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .lighthouse-agents-container {
    .filter-container {
      padding: 15px;
      flex-direction: column;
      align-items: stretch;
      
      .filter-item {
        width: 100%;
        margin-right: 0 !important;
        margin-bottom: 10px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .el-input, .el-select {
          width: 100% !important;
        }
        
        .el-button {
          width: 100%;
          justify-content: center;
        }
      }
    }
    
    .el-table {
      font-size: 12px;
      
      .action-buttons {
        flex-direction: column;
        gap: 4px;
        
        .el-button {
          padding: 2px 6px;
          font-size: 11px;
          width: auto;
          min-width: 60px;
        }
        
        .el-dropdown {
          width: 100%;
          
          .more-btn {
            width: 100%;
            justify-content: center;
          }
        }
      }
    }
  }
}
</style> 