<template>
  <div v-show="total > 0" class="pagination-wrapper">
    <el-pagination
      :current-page="page"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="limit"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
const props = defineProps({
  total: {
    type: Number,
    required: true
  },
  page: {
    type: Number,
    default: 1
  },
  limit: {
    type: Number,
    default: 20
  }
})

const emit = defineEmits(['pagination'])

const handleSizeChange = (val) => {
  emit('pagination', { page: props.page, limit: val })
}

const handleCurrentChange = (val) => {
  emit('pagination', { page: val, limit: props.limit })
}
</script>

<style lang="scss" scoped>
.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}
</style> 