# 投放账号管理树形结构实现

## 功能概述

本次更新将投放账号管理的小红书账号组件改造为树形结构显示，主账号可以展开显示对应的子账号，实现了类似您提供的参考页面的交互效果。

## 实现特性

### 1. 树形数据结构

#### 数据组织方式
- **主账号**: 作为树的根节点显示
- **子账号**: 作为主账号的children显示
- **层级关系**: 通过parent_id建立父子关系

#### 数据转换逻辑
```javascript
// 构建树形数据结构
const allAccounts = (data.list || []).map(item => ({
  // 数据映射...
}))

// 分离主账号和子账号
const mainAccounts = allAccounts.filter(item => item.account_type === 'main')
const subAccounts = allAccounts.filter(item => item.account_type === 'sub')

// 构建树形结构：将子账号作为主账号的children
tableData.value = mainAccounts.map(mainAccount => {
  const children = subAccounts.filter(subAccount => 
    subAccount.parent_account_id === mainAccount.id
  )
  
  return {
    ...mainAccount,
    children: children.length > 0 ? children : undefined,
    hasChildren: children.length > 0
  }
})
```

### 2. 表格树形配置

#### Element Plus树形表格配置
```vue
<el-table
  :data="tableData"
  :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
  row-key="id"
  :default-expand-all="false"
  :expand-row-keys="expandedRowKeys"
  @expand-change="handleExpandChange"
>
```

#### 展开状态管理
- **expandedRowKeys**: 记录当前展开的主账号ID
- **handleExpandChange**: 处理展开/收起事件
- **default-expand-all**: 默认不展开所有节点

### 3. 视觉层级区分

#### 主账号显示
- **展开图标**: 有子账号时显示展开图标
- **主账号标签**: 蓝色"主账号"标签
- **子账号数量**: 显示"(X个子账号)"提示
- **字体样式**: 较大字体，深色文字

#### 子账号显示
- **缩进效果**: 左侧20px缩进
- **子账号标签**: 绿色"子账号"标签
- **字体样式**: 较小字体，浅色文字
- **层级标识**: 视觉上明确的层级关系

### 4. 操作按钮差异化

#### 主账号操作
- **授权按钮**: 支持小红书OAuth授权
- **刷新Token**: 支持Token刷新
- **编辑功能**: 支持基本信息编辑
- **更多操作**: 同步数据、撤销授权等

#### 子账号操作
- **编辑功能**: 支持基本信息编辑
- **继承授权**: 显示"继承主账号授权"标签
- **状态管理**: 支持启用/禁用切换
- **简化操作**: 不显示授权相关操作

### 5. 交互体验优化

#### 展开/收起功能
- **点击展开**: 点击主账号行可展开/收起子账号
- **状态记忆**: 记录用户的展开状态
- **平滑动画**: Element Plus内置的展开动画

#### 视觉反馈
- **层级缩进**: 子账号明显的左侧缩进
- **颜色区分**: 不同的标签颜色和文字颜色
- **图标提示**: 展开图标和子账号数量提示

## 技术实现细节

### 1. 数据结构设计

```javascript
// 主账号数据结构
{
  id: 1,
  account_type: 'main',
  account_name: '主账号名称',
  children: [
    {
      id: 2,
      account_type: 'sub',
      parent_account_id: 1,
      account_name: '子账号名称',
      // 其他字段...
    }
  ],
  hasChildren: true,
  // 其他字段...
}
```

### 2. 样式实现

```scss
.account-name-cell {
  display: flex;
  align-items: center;
  
  &.sub-account {
    padding-left: 20px; // 子账号缩进
  }
  
  .account-name {
    &.main-account {
      color: #303133;
      font-size: 14px;
      font-weight: 500;
    }
    
    &.sub-account {
      color: #606266;
      font-size: 13px;
    }
  }
  
  .sub-count {
    margin-left: 8px;
    color: #909399;
    font-size: 12px;
  }
}
```

### 3. 展开状态管理

```javascript
// 展开状态变化处理
const handleExpandChange = (row, expandedRows) => {
  const isExpanded = expandedRows.some(expandedRow => expandedRow.id === row.id)
  if (isExpanded) {
    // 展开：添加到展开列表
    if (!expandedRowKeys.value.includes(row.id)) {
      expandedRowKeys.value.push(row.id)
    }
  } else {
    // 收起：从展开列表移除
    const index = expandedRowKeys.value.indexOf(row.id)
    if (index > -1) {
      expandedRowKeys.value.splice(index, 1)
    }
  }
}
```

## 使用说明

### 1. 查看账号层级
- 主账号默认收起状态显示
- 点击主账号行可展开查看子账号
- 子账号显示在主账号下方，有明显的缩进

### 2. 操作差异
- **主账号**: 可进行授权、刷新Token等操作
- **子账号**: 继承主账号授权，主要进行基本信息管理

### 3. 视觉识别
- **主账号**: 蓝色标签，较大字体，显示子账号数量
- **子账号**: 绿色标签，较小字体，左侧缩进

## 优势特点

1. **清晰的层级关系**: 直观显示主账号和子账号的从属关系
2. **节省空间**: 默认收起状态，按需展开查看
3. **操作区分**: 根据账号类型显示相应的操作按钮
4. **用户体验**: 符合用户对树形结构的操作习惯
5. **数据组织**: 逻辑清晰的数据结构，便于管理和维护

## 兼容性说明

- 基于Element Plus的树形表格组件实现
- 保持了原有的API接口调用逻辑
- 向下兼容现有的数据结构
- 支持所有原有的功能操作
