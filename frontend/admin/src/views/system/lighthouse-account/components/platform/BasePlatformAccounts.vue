<template>
  <div class="platform-accounts-container">
    <!-- 功能栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" icon="Plus" @click="handleAdd">
          添加{{ platformName }}账号
        </el-button>
        <el-button icon="Refresh" @click="handleRefresh">刷新</el-button>
        <el-button 
          type="success" 
          icon="Download" 
          @click="handleExport"
          :loading="exportLoading"
        >
          导出数据
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索账号名称、ID"
          prefix-icon="Search"
          style="width: 250px"
          @input="handleSearch"
          clearable
        />
        <el-select
          v-model="statusFilter"
          placeholder="状态筛选"
          style="width: 120px; margin-left: 12px"
          @change="handleSearch"
          clearable
        >
          <el-option label="启用" value="active" />
          <el-option label="禁用" value="disabled" />
          <el-option label="待审核" value="pending" />
        </el-select>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      :data="tableData"
      style="width: 100%"
      v-loading="loading"
      :default-sort="{ prop: 'created_at', order: 'descending' }"
      stripe
      border
    >
      <!-- 基础信息 -->
      <el-table-column
        prop="account_name"
        label="账号名称"
        min-width="150"
        show-overflow-tooltip
      />
      
      <el-table-column
        prop="platform_account_id"
        label="平台账号ID"
        min-width="120"
        show-overflow-tooltip
      />

      <el-table-column
        prop="platform_name"
        label="平台"
        width="100"
        align="center"
      >
        <template #default="scope">
          <el-tag :type="getPlatformTagType(scope.row.platform_name)" size="small">
            {{ scope.row.platform_name }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 账号状态 -->
      <el-table-column
        prop="status"
        label="状态"
        width="100"
        align="center"
      >
        <template #default="scope">
          <el-tag
            :type="getStatusType(scope.row.status)"
            size="small"
          >
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 余额信息 -->
      <el-table-column
        prop="balance"
        label="账户余额"
        width="120"
        align="right"
      >
        <template #default="scope">
          <span :class="{ 'text-warning': scope.row.balance < 1000 }">
            ¥{{ formatAmount(scope.row.balance) }}
          </span>
        </template>
      </el-table-column>

      <!-- 归属信息 -->
      <el-table-column
        prop="owner_name"
        label="归属人员"
        width="100"
        show-overflow-tooltip
      />

      <!-- 时间信息 -->
      <el-table-column
        prop="last_active_time"
        label="最后活跃"
        width="160"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ formatDate(scope.row.last_active_time) }}
        </template>
      </el-table-column>

      <el-table-column
        prop="created_at"
        label="创建时间"
        width="160"
        sortable
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ formatDate(scope.row.created_at) }}
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column
        label="操作"
        width="200"
        fixed="right"
        align="center"
      >
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="handleViewDetail(scope.row)"
          >
            详情
          </el-button>
          <el-dropdown
            @command="(command) => handleDropdownCommand(command, scope.row)"
            size="small"
          >
            <el-button size="small" type="text">
              更多<el-icon><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item 
                  :command="{ action: 'toggle-status', row: scope.row }"
                >
                  {{ scope.row.status === 'active' ? '禁用' : '启用' }}
                </el-dropdown-item>
                <el-dropdown-item 
                  :command="{ action: 'sync', row: scope.row }"
                >
                  同步数据
                </el-dropdown-item>
                <el-dropdown-item 
                  :command="{ action: 'delete', row: scope.row }"
                  divided
                >
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="账号名称" prop="account_name">
          <el-input
            v-model="formData.account_name"
            placeholder="请输入账号名称"
          />
        </el-form-item>

        <el-form-item label="平台账号ID" prop="platform_account_id">
          <el-input
            v-model="formData.platform_account_id"
            placeholder="请输入平台账号ID"
          />
        </el-form-item>

        <el-form-item label="访问令牌" prop="access_token">
          <el-input
            v-model="formData.access_token"
            type="textarea"
            :rows="3"
            placeholder="请输入访问令牌"
          />
        </el-form-item>

        <el-form-item label="归属人员" prop="owner_id">
          <el-select
            v-model="formData.owner_id"
            placeholder="请选择归属人员"
            style="width: 100%"
          >
            <el-option
              v-for="user in userOptions"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="disabled">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="handleDialogClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog
      title="账号详情"
      v-model="detailDialogVisible"
      width="800px"
    >
      <div class="detail-content" v-if="currentDetail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="账号名称">
            {{ currentDetail.account_name }}
          </el-descriptions-item>
          <el-descriptions-item label="平台账号ID">
            {{ currentDetail.platform_account_id }}
          </el-descriptions-item>
          <el-descriptions-item label="平台">
            <el-tag :type="getPlatformTagType(currentDetail.platform_name)">
              {{ currentDetail.platform_name }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentDetail.status)">
              {{ getStatusText(currentDetail.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="账户余额">
            ¥{{ formatAmount(currentDetail.balance) }}
          </el-descriptions-item>
          <el-descriptions-item label="归属人员">
            {{ currentDetail.owner_name }}
          </el-descriptions-item>
          <el-descriptions-item label="最后活跃">
            {{ formatDate(currentDetail.last_active_time) }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(currentDetail.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ currentDetail.remark || '无' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'

// Props定义
const props = defineProps({
  platformName: {
    type: String,
    required: true
  },
  platformCode: {
    type: String,
    required: true
  },
  apiPrefix: {
    type: String,
    required: true
  }
})

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const submitLoading = ref(false)
const tableData = ref([])
const searchKeyword = ref('')
const statusFilter = ref('')

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 对话框
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const isEdit = ref(false)
const currentDetail = ref(null)

// 表单
const formRef = ref()
const formData = reactive({
  account_name: '',
  platform_account_id: '',
  access_token: '',
  owner_id: '',
  status: 'active',
  remark: ''
})

// 用户选项
const userOptions = ref([])

// 计算属性
const dialogTitle = computed(() => 
  isEdit.value ? `编辑${props.platformName}账号` : `添加${props.platformName}账号`
)

// 表单验证规则
const formRules = {
  account_name: [
    { required: true, message: '请输入账号名称', trigger: 'blur' }
  ],
  platform_account_id: [
    { required: true, message: '请输入平台账号ID', trigger: 'blur' }
  ],
  access_token: [
    { required: true, message: '请输入访问令牌', trigger: 'blur' }
  ],
  owner_id: [
    { required: true, message: '请选择归属人员', trigger: 'change' }
  ]
}

// 方法定义
const fetchData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    const mockData = generateMockData()
    tableData.value = mockData.list
    pagination.total = mockData.total
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const generateMockData = () => {
  const list = []
  for (let i = 1; i <= pagination.size; i++) {
    list.push({
      id: i + (pagination.page - 1) * pagination.size,
      account_name: `${props.platformName}账号${i}`,
      platform_account_id: `${props.platformCode}_${1000 + i}`,
      platform_name: props.platformName,
      status: i % 3 === 0 ? 'disabled' : 'active',
      balance: Math.floor(Math.random() * 50000) + 1000,
      owner_name: `用户${i}`,
      last_active_time: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
      created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      remark: i % 2 === 0 ? `这是${props.platformName}账号${i}的备注` : ''
    })
  }
  return {
    list,
    total: 100
  }
}

// 事件处理
const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  isEdit.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

const handleViewDetail = (row) => {
  currentDetail.value = row
  detailDialogVisible.value = true
}

const handleRefresh = () => {
  fetchData()
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleExport = async () => {
  exportLoading.value = true
  try {
    // 模拟导出
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const handleDropdownCommand = async ({ action, row }) => {
  switch (action) {
    case 'toggle-status':
      await handleToggleStatus(row)
      break
    case 'sync':
      await handleSync(row)
      break
    case 'delete':
      await handleDelete(row)
      break
  }
}

const handleToggleStatus = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要${row.status === 'active' ? '禁用' : '启用'}该账号吗？`,
      '确认操作',
      { type: 'warning' }
    )
    
    // 模拟API调用
    row.status = row.status === 'active' ? 'disabled' : 'active'
    ElMessage.success('操作成功')
  } catch {
    // 用户取消
  }
}

const handleSync = async (row) => {
  try {
    ElMessage.info('开始同步数据...')
    // 模拟同步
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('同步完成')
  } catch (error) {
    ElMessage.error('同步失败')
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该账号吗？此操作不可恢复！',
      '确认删除',
      { type: 'warning' }
    )
    
    // 模拟删除
    tableData.value = tableData.value.filter(item => item.id !== row.id)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(isEdit.value ? '更新成功' : '添加成功')
    dialogVisible.value = false
    fetchData()
  } catch (error) {
    if (error !== false) { // 不是验证失败
      ElMessage.error('操作失败')
    }
  } finally {
    submitLoading.value = false
  }
}

const handleDialogClose = () => {
  dialogVisible.value = false
  resetForm()
}

const handleSizeChange = (size) => {
  pagination.size = size
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchData()
}

const resetForm = () => {
  Object.assign(formData, {
    account_name: '',
    platform_account_id: '',
    access_token: '',
    owner_id: '',
    status: 'active',
    remark: ''
  })
  formRef.value?.clearValidate()
}

// 工具方法
const formatAmount = (amount) => {
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}

const getStatusType = (status) => {
  const typeMap = {
    active: 'success',
    disabled: 'danger',
    pending: 'warning'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    active: '启用',
    disabled: '禁用',
    pending: '待审核'
  }
  return textMap[status] || status
}

const getPlatformTagType = (platform) => {
  const typeMap = {
    '灯火': 'warning',
    '小红书': 'danger',
    'B站': 'primary',
    '抖音': 'danger',
    '头条': 'warning',
    '快手': 'warning'
  }
  return typeMap[platform] || 'info'
}

// 生命周期
onMounted(() => {
  fetchData()
  // 模拟获取用户选项
  userOptions.value = [
    { id: 1, name: '张三' },
    { id: 2, name: '李四' },
    { id: 3, name: '王五' }
  ]
})
</script>

<style lang="scss" scoped>
.platform-accounts-container {
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    .toolbar-left {
      display: flex;
      gap: 12px;
    }

    .toolbar-right {
      display: flex;
      align-items: center;
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .detail-content {
    padding: 16px 0;
  }

  .text-warning {
    color: #e6a23c;
    font-weight: 600;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .platform-accounts-container {
    .toolbar {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .toolbar-left {
        justify-content: center;
      }

      .toolbar-right {
        justify-content: center;
        flex-wrap: wrap;
        gap: 12px;
      }
    }
  }
}
</style> 