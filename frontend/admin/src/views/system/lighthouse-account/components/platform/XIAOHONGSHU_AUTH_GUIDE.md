# 小红书账号授权功能说明

## 功能概述

小红书账号授权功能实现了完整的OAuth2.0授权流程，支持获取授权链接、处理授权回调、刷新Token等核心功能。

## 功能特性

### 1. 开始授权流程
- **触发方式**: 点击主账号的"授权"按钮
- **获取授权链接**: 调用API获取小红书授权URL
- **自动跳转**: 跳转到小红书开放平台进行授权
- **回调处理**: 授权完成后自动返回当前页面

### 2. 授权回调处理
- **参数检测**: 自动检测URL中的`auth_code`和`state`参数
- **自动授权**: 检测到参数后自动调用授权API
- **状态更新**: 授权成功后更新账号状态
- **URL清理**: 处理完成后清除URL参数

### 3. Token刷新功能
- **触发方式**: 点击已授权账号的"刷新Token"按钮
- **自动获取**: 使用账号中存储的refresh_token
- **状态更新**: 刷新成功后更新Token有效期

## 技术实现

### 1. API接口

#### 获取授权链接
```javascript
POST /api/v1/ad-accounts/xiaohongshu/auth-url
{
  "account_id": 123,
  "redirect_uri": "http://localhost:3001/system/lighthouse-account"
}
```

#### 账号授权
```javascript
POST /api/v1/ad-accounts/xiaohongshu/auth
{
  "account_id": 123,
  "auth_code": "授权码"
}
```

#### 刷新Token
```javascript
POST /api/v1/ad-accounts/xiaohongshu/refresh-token
{
  "account_id": 123,
  "refresh_token": "刷新令牌"
}
```

### 2. 前端实现

#### 开始授权
```javascript
const handleStartAuth = async () => {
  // 获取当前页面URL作为回调地址
  const redirectUri = window.location.origin + window.location.pathname
  
  // 获取授权链接
  const response = await getXiaohongshuAuthUrl({
    account_id: authAccount.value.id,
    redirect_uri: redirectUri
  })
  
  // 跳转到授权页面
  window.location.href = response.data.auth_url
}
```

#### 处理授权回调
```javascript
const handleAuthCallback = async () => {
  const urlParams = new URLSearchParams(window.location.search)
  const authCode = urlParams.get('auth_code')
  const state = urlParams.get('state')
  
  if (authCode && state) {
    // state就是account_id
    const accountId = parseInt(state)
    
    // 调用授权API
    await xiaohongshuAuth({
      account_id: accountId,
      auth_code: authCode
    })
    
    // 清除URL参数
    const newUrl = window.location.pathname
    window.history.replaceState({}, document.title, newUrl)
  }
}
```

#### 刷新Token
```javascript
const handleRefreshToken = async (row) => {
  // 检查是否有refresh_token
  if (!row.refresh_token) {
    ElMessage.error('该账号没有刷新Token，请重新授权')
    return
  }
  
  // 调用刷新Token接口
  await xiaohongshuRefreshToken({
    account_id: row.id,
    refresh_token: row.refresh_token
  })
}
```

## 授权流程

### 1. 完整授权流程
```
1. 用户点击"授权"按钮
   ↓
2. 前端调用获取授权链接API
   ↓
3. 跳转到小红书开放平台
   ↓
4. 用户在小红书平台完成授权
   ↓
5. 小红书平台回调到指定URL（带auth_code和state参数）
   ↓
6. 前端检测到回调参数，自动调用授权API
   ↓
7. 后端使用auth_code获取access_token和refresh_token
   ↓
8. 前端显示授权成功，更新账号状态
```

### 2. Token刷新流程
```
1. 用户点击"刷新Token"按钮
   ↓
2. 前端检查账号是否有refresh_token
   ↓
3. 调用刷新Token API
   ↓
4. 后端使用refresh_token获取新的access_token
   ↓
5. 前端显示刷新成功，更新Token有效期
```

## 参数说明

### 1. 授权链接参数
- **account_id**: 广告账号ID，用作state参数
- **redirect_uri**: 授权完成后的回调地址

### 2. 回调参数
- **auth_code**: 小红书返回的授权码
- **state**: 对应的account_id

### 3. 授权响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "advertiser_name": "广告主名称",
    "access_token_expires_in": 7200,
    "refresh_token_expires_in": 2592000
  }
}
```

## 用户体验

### 1. 授权过程
- **一键授权**: 点击按钮即可开始授权流程
- **自动跳转**: 无需手动复制链接
- **自动处理**: 授权完成后自动处理回调
- **状态反馈**: 实时显示授权状态和结果

### 2. 错误处理
- **友好提示**: 各种错误情况都有明确提示
- **自动恢复**: 授权失败后可重新尝试
- **状态保护**: 避免重复授权操作

### 3. 安全性
- **参数验证**: 严格验证回调参数
- **URL清理**: 处理完成后清除敏感参数
- **状态校验**: 使用state参数防止CSRF攻击

## 注意事项

### 1. 回调地址配置
- 回调地址必须与小红书开放平台配置一致
- 支持本地开发和生产环境不同域名
- 确保回调地址可正常访问

### 2. Token管理
- access_token有效期通常为2小时
- refresh_token有效期通常为30天
- 需要在Token过期前及时刷新

### 3. 错误处理
- 授权码只能使用一次
- 过期的refresh_token需要重新授权
- 网络异常时提供重试机制

## 开发调试

### 1. 本地测试
```bash
# 确保开发服务器运行在正确端口
npm run dev

# 访问账号管理页面
http://localhost:3001/system/lighthouse-account
```

### 2. 调试技巧
- 使用浏览器开发者工具查看网络请求
- 检查URL参数是否正确传递
- 查看控制台日志了解错误详情

### 3. 常见问题
- **授权链接获取失败**: 检查API接口是否正常
- **回调参数缺失**: 检查小红书平台配置
- **Token刷新失败**: 检查refresh_token是否有效

## 扩展功能

### 1. 批量授权
- 支持选择多个账号批量授权
- 提供授权进度显示
- 支持失败重试

### 2. 授权状态监控
- 定期检查Token有效期
- 自动提醒即将过期的Token
- 支持自动刷新Token

### 3. 授权历史记录
- 记录授权操作历史
- 提供授权日志查询
- 支持授权统计分析

## 总结

小红书账号授权功能实现了完整的OAuth2.0授权流程，提供了便捷的用户体验和可靠的安全保障。通过自动化的授权回调处理和Token管理，大大简化了用户的操作流程，提高了系统的易用性和稳定性。
