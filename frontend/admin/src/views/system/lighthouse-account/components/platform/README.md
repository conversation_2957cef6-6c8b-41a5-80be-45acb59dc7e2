# 系统管理-投放账号管理功能更新

## 更新概述

本次更新将系统管理下的小红书投放账号管理功能与API文档中定义的真实接口进行了集成，替换了原有的模拟数据，实现了完整的账号管理功能。

## 更新内容

### 1. API接口集成

#### 新增API文件
- **文件路径**: `frontend/admin/src/api/ad-accounts.js`
- **功能**: 包含所有投放账号管理相关的API接口

#### 主要接口
- `getAdAccounts()` - 获取广告账号列表
- `createMasterAccount()` - 创建主账号
- `createSubAccount()` - 创建子账号
- `updateAdAccount()` - 更新账号信息
- `deleteAdAccount()` - 删除账号
- `getAdAccountOptions()` - 获取账号选项
- `xiaohongshuAuth()` - 小红书账号授权
- `xiaohongshuRefreshToken()` - 刷新Token

### 2. 数据结构映射

#### API数据转换
将API返回的数据结构转换为组件所需的格式：

```javascript
// API返回格式 -> 组件使用格式
{
  account_type: 1,           // -> 'main'
  authorization_status: 1,   // -> 'authorized'
  usage_status: 1,          // -> 'active'
  platform_account_id: '',  // -> platform_account_id
  account_balance: 0,        // -> balance
  owner: '',                // -> owner_name
  // ...其他字段映射
}
```

#### 状态枚举映射
- **授权状态**: 0:未授权, 1:已授权, 2:已过期, 3:继承授权
- **使用状态**: 1:启用, 2:禁用
- **账号类型**: 1:主账号, 2:子账号

### 3. 功能实现

#### 数据加载功能
- 使用真实API接口获取账号列表
- 支持分页、筛选、搜索功能
- 自动处理主账号和子账号的层级关系

#### 账号创建功能
- 支持创建主账号和子账号
- 表单验证和错误处理
- 子账号必须关联到有效的主账号

#### 账号编辑功能
- 支持编辑账号基本信息
- 支持状态切换（启用/禁用）
- 实时数据更新

#### 授权功能
- 集成小红书OAuth授权流程
- 支持授权码输入和验证
- 授权状态实时更新

#### Token管理功能
- 支持Token刷新
- Token过期提醒
- 自动状态更新

#### 删除功能
- 支持账号删除
- 删除确认机制
- 级联删除处理

### 4. 用户体验优化

#### 状态显示
- 授权状态标签颜色区分
- Token过期时间警告
- 账号余额显示

#### 操作反馈
- 加载状态指示
- 操作成功/失败提示
- 详细错误信息显示

#### 界面优化
- 响应式设计
- 操作按钮状态管理
- 表格数据排序

## 技术实现

### 1. 状态转换辅助方法

```javascript
// 授权状态转换
const getAuthStatusKey = (authStatus) => {
  const statusMap = {
    0: 'unauthorized',
    1: 'authorized', 
    2: 'expired',
    3: 'inherited'
  }
  return statusMap[authStatus] || 'unauthorized'
}

// 使用状态转换
const getUsageStatusValue = (statusKey) => {
  const statusMap = {
    'active': 1,
    'disabled': 2
  }
  return statusMap[statusKey]
}
```

### 2. 错误处理机制

```javascript
try {
  const response = await getAdAccounts(params)
  // 处理成功响应
} catch (error) {
  console.error('获取投放账号列表失败:', error)
  ElMessage.error('加载数据失败: ' + 
    (error.response?.data?.message || error.message || '未知错误'))
}
```

### 3. 数据验证

- 前端表单验证
- API响应验证
- 数据类型转换

## 使用说明

### 1. 访问路径
- 系统管理 -> 投放账号管理 -> 小红书

### 2. 主要功能
1. **查看账号列表**: 支持分页、筛选、搜索
2. **添加主账号**: 创建独立的主账号
3. **添加子账号**: 创建关联到主账号的子账号
4. **编辑账号**: 修改账号基本信息
5. **授权管理**: 进行小红书OAuth授权
6. **Token管理**: 刷新过期的Token
7. **状态管理**: 启用/禁用账号
8. **删除账号**: 删除不需要的账号

### 3. 注意事项
1. 子账号必须关联到有效的主账号
2. 删除主账号前需要先删除所有子账号
3. 授权需要从小红书开放平台获取授权码
4. Token过期前建议主动刷新

## 文件结构

```
frontend/admin/src/
├── api/
│   └── ad-accounts.js                 # 投放账号管理API接口
├── views/system/lighthouse-account/
│   ├── index.vue                      # 主页面
│   └── components/platform/
│       ├── XiaohongshuAccounts.vue    # 小红书账号管理组件
│       └── README.md                  # 本文档
```

## 更新日志

- **2024-01-XX**: 完成API接口集成
- **2024-01-XX**: 实现数据结构映射
- **2024-01-XX**: 完成授权功能集成
- **2024-01-XX**: 实现编辑和删除功能
- **2024-01-XX**: 完成Token管理功能
- **2024-01-XX**: 测试和优化完成
