<template>
  <div class="xiaohongshu-accounts-container">
    <!-- 功能栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" icon="Plus" @click="handleAddMainAccount">
          添加主账号
        </el-button>
        <el-button type="success" icon="Plus" @click="handleAddSubAccount">
          添加子账号
        </el-button>
        <el-button icon="Refresh" @click="handleRefresh">刷新</el-button>
        <el-button 
          type="success" 
          icon="Download" 
          @click="handleExport"
          :loading="exportLoading"
        >
          导出数据
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索账号名称、ID"
          prefix-icon="Search"
          style="width: 250px"
          @input="handleSearch"
          clearable
        />
        <el-select
          v-model="statusFilter"
          placeholder="状态筛选"
          style="width: 120px; margin-left: 12px"
          @change="handleSearch"
          clearable
        >
          <el-option label="已授权" value="authorized" />
          <el-option label="未授权" value="unauthorized" />
          <el-option label="授权过期" value="expired" />
          <el-option label="继承授权" value="inherited" />
          <el-option label="启用" value="active" />
          <el-option label="禁用" value="disabled" />
        </el-select>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      :data="tableData"
      style="width: 100%"
      v-loading="loading"
      stripe
      border
      :tree-props="{ children: 'children' }"
      row-key="id"
      :default-expand-all="false"
      :expand-row-keys="expandedRowKeys"
      @expand-change="handleExpandChange"
    >
      <!-- 账号名称 -->
      <el-table-column
        prop="account_name"
        label="账号名称"
        min-width="180"
        show-overflow-tooltip
      >
        <template #default="scope">
          <div class="account-name-cell" :class="{ 'sub-account': scope.row.account_type === 'sub' }">
            <!-- 主账号显示 -->
            <template v-if="scope.row.account_type === 'main'">
              <el-icon class="expand-icon" v-if="scope.row.hasChildren">
                <CaretRight />
              </el-icon>
              <el-tag
                type="primary"
                size="small"
                style="margin-right: 8px"
              >
                主账号
              </el-tag>
              <span class="account-name main-account">{{ scope.row.account_name }}</span>
              <span v-if="scope.row.children && scope.row.children.length > 0" class="sub-count">
                ({{ scope.row.children.length }}个子账号)
              </span>
            </template>

            <!-- 子账号显示 -->
            <template v-else>
              <span class="sub-account-indent"></span>
              <el-tag
                type="success"
                size="small"
                style="margin-right: 8px"
              >
                子账号
              </el-tag>
              <span class="account-name sub-account">{{ scope.row.account_name }}</span>
            </template>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column
        prop="platform_account_id"
        label="小红书账号ID"
        min-width="140"
        show-overflow-tooltip
      />

      <!-- 授权状态 -->
      <el-table-column
        prop="auth_status"
        label="授权状态"
        width="120"
        align="center"
      >
        <template #default="scope">
          <el-tag
            :type="getAuthStatusType(scope.row.auth_status)"
            size="small"
          >
            {{ getAuthStatusText(scope.row.auth_status) }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- Token过期时间 -->
      <el-table-column
        prop="token_expires_at"
        label="Token过期时间"
        width="160"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span v-if="scope.row.token_expires_at" :class="getExpirationClass(scope.row.token_expires_at)">
            {{ formatDate(scope.row.token_expires_at) }}
          </span>
          <span v-else class="text-muted">--</span>
        </template>
      </el-table-column>

      <!-- 账号状态 -->
      <el-table-column
        prop="status"
        label="使用状态"
        width="100"
        align="center"
      >
        <template #default="scope">
          <el-tag
            :type="getStatusType(scope.row.status)"
            size="small"
          >
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 余额信息 -->
      <el-table-column
        prop="balance"
        label="账户余额"
        width="120"
        align="right"
      >
        <template #default="scope">
          <span :class="{ 'text-warning': scope.row.balance < 1000 }">
            ¥{{ formatAmount(scope.row.balance) }}
          </span>
        </template>
      </el-table-column>

      <!-- 归属信息 -->
      <el-table-column
        prop="owner_name"
        label="归属人员"
        width="100"
        show-overflow-tooltip
      />

      <!-- 最后同步时间 -->
      <el-table-column
        prop="last_sync_time"
        label="最后同步"
        width="160"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ formatDate(scope.row.last_sync_time) }}
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column
        label="操作"
        width="240"
        fixed="right"
        align="center"
      >
        <template #default="scope">
          <!-- 主账号操作 -->
          <template v-if="scope.row.account_type === 'main'">
            <el-button
              v-if="scope.row.auth_status !== 'authorized'"
              size="small"
              type="success"
              @click="handleAuthorize(scope.row)"
            >
              授权
            </el-button>
            <el-button
              v-if="scope.row.auth_status === 'authorized'"
              size="small"
              type="warning"
              @click="handleRefreshToken(scope.row)"
            >
              刷新Token
            </el-button>
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
          </template>

          <!-- 子账号操作 -->
          <template v-else>
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-tag size="small" type="info" style="margin-left: 8px;">
              继承主账号授权
            </el-tag>
          </template>

          <!-- 更多操作下拉菜单 -->
          <el-dropdown
            @command="(command) => handleDropdownCommand(command, scope.row)"
            size="small"
            style="margin-left: 8px;"
          >
            <el-button size="small" type="text">
              更多<el-icon><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  :command="{ action: 'view-detail', row: scope.row }"
                >
                  查看详情
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="scope.row.account_type === 'main' && scope.row.auth_status === 'authorized'"
                  :command="{ action: 'sync', row: scope.row }"
                >
                  同步数据
                </el-dropdown-item>
                <el-dropdown-item
                  :command="{ action: 'toggle-status', row: scope.row }"
                >
                  {{ scope.row.status === 'active' ? '禁用' : '启用' }}
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="scope.row.account_type === 'main'"
                  :command="{ action: 'revoke-auth', row: scope.row }"
                >
                  撤销授权
                </el-dropdown-item>
                <el-dropdown-item
                  :command="{ action: 'delete', row: scope.row }"
                  divided
                >
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="账号类型" prop="account_type">
          <el-radio-group v-model="formData.account_type" :disabled="isEdit">
            <el-radio label="main">主账号</el-radio>
            <el-radio label="sub">子账号</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item 
          v-if="formData.account_type === 'sub'" 
          label="关联主账号" 
          prop="parent_account_id"
        >
          <el-select
            v-model="formData.parent_account_id"
            placeholder="选择关联的主账号"
            style="width: 100%"
          >
            <el-option
              v-for="mainAccount in mainAccounts"
              :key="mainAccount.value"
              :label="mainAccount.label"
              :value="mainAccount.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="账号名称" prop="account_name">
          <el-input v-model="formData.account_name" placeholder="请输入账号名称" />
        </el-form-item>

        <el-form-item label="小红书账号ID" prop="platform_account_id">
          <el-input v-model="formData.platform_account_id" placeholder="请输入小红书账号ID" />
        </el-form-item>

        <el-form-item label="归属人员" prop="owner_name">
          <el-input v-model="formData.owner_name" placeholder="请输入归属人员" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 账号详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="账号详情"
      width="800px"
    >
      <div v-if="currentAccount" class="account-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="账号名称">
            {{ currentAccount.account_name }}
          </el-descriptions-item>
          <el-descriptions-item label="账号类型">
            <el-tag :type="currentAccount.account_type === 'main' ? 'primary' : 'info'">
              {{ currentAccount.account_type === 'main' ? '主账号' : '子账号' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="小红书账号ID">
            {{ currentAccount.platform_account_id }}
          </el-descriptions-item>
          <el-descriptions-item label="授权状态">
            <el-tag :type="getAuthStatusType(currentAccount.auth_status)">
              {{ getAuthStatusText(currentAccount.auth_status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="Access Token" v-if="currentAccount.access_token">
            <el-input 
              :value="currentAccount.access_token" 
              type="password" 
              readonly 
              style="width: 300px"
            />
          </el-descriptions-item>
          <el-descriptions-item label="Token过期时间" v-if="currentAccount.token_expires_at">
            <span :class="getExpirationClass(currentAccount.token_expires_at)">
              {{ formatDate(currentAccount.token_expires_at) }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="账户余额">
            ¥{{ formatAmount(currentAccount.balance) }}
          </el-descriptions-item>
          <el-descriptions-item label="使用状态">
            <el-tag :type="getStatusType(currentAccount.status)">
              {{ getStatusText(currentAccount.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="归属人员">
            {{ currentAccount.owner_name || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="最后同步时间">
            {{ formatDate(currentAccount.last_sync_time) }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(currentAccount.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDate(currentAccount.updated_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" span="2">
            {{ currentAccount.remark || '--' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 授权对话框 -->
    <el-dialog
      v-model="authDialogVisible"
      title="小红书账号授权"
      width="500px"
    >
      <div class="auth-content">
        <el-alert
          title="授权说明"
          type="info"
          :closable="false"
          style="margin-bottom: 20px"
        >
          <p>1. 点击下方授权按钮将跳转到小红书授权页面</p>
          <p>2. 完成授权后系统将自动获取并保存 Access Token</p>
          <p>3. Token 有效期为30天，到期前请及时刷新</p>
        </el-alert>

        <div class="auth-info" v-if="authAccount">
          <p><strong>账号名称：</strong>{{ authAccount.account_name }}</p>
          <p><strong>小红书ID：</strong>{{ authAccount.platform_account_id }}</p>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="authDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleStartAuth" :loading="authLoading">
            开始授权
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown, CaretRight } from '@element-plus/icons-vue'
import { formatDate, formatAmount } from '@/utils/index'
import {
  getAdAccounts,
  createMasterAccount,
  createSubAccount,
  updateAdAccount,
  deleteAdAccount,
  getAdAccountOptions,
  xiaohongshuAuth,
  xiaohongshuRefreshToken,
  AUTHORIZATION_STATUS,
  USAGE_STATUS,
  ACCOUNT_TYPES
} from '@/api/ad-accounts'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const submitLoading = ref(false)
const authLoading = ref(false)
const tableData = ref([])
const searchKeyword = ref('')
const statusFilter = ref('')
const expandedRowKeys = ref([]) // 展开的行keys

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 对话框状态
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const authDialogVisible = ref(false)
const isEdit = ref(false)
const currentAccount = ref(null)
const authAccount = ref(null)

// 表单数据
const formRef = ref(null)
const formData = reactive({
  account_type: 'main',
  parent_account_id: '',
  account_name: '',
  platform_account_id: '',
  owner_name: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  account_type: [{ required: true, message: '请选择账号类型', trigger: 'change' }],
  parent_account_id: [
    { 
      required: true, 
      message: '请选择关联的主账号', 
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (formData.account_type === 'sub' && !value) {
          callback(new Error('子账号必须关联一个主账号'))
        } else {
          callback()
        }
      }
    }
  ],
  account_name: [{ required: true, message: '请输入账号名称', trigger: 'blur' }],
  platform_account_id: [{ required: true, message: '请输入小红书账号ID', trigger: 'blur' }],
  owner_name: [{ required: true, message: '请输入归属人员', trigger: 'blur' }]
}

// 计算属性
const dialogTitle = computed(() => {
  return isEdit.value ? '编辑小红书账号' : '添加小红书账号'
})

const mainAccounts = ref([])

// 加载主账号选项
const loadMainAccounts = async () => {
  try {
    const response = await getAdAccountOptions()
    mainAccounts.value = response.data.parent_accounts || []
  } catch (error) {
    console.error('加载主账号选项失败:', error)
  }
}

// 生命周期
onMounted(() => {
  loadTableData()
  loadMainAccounts()
})

// 方法
const loadTableData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      platform: 1, // 小红书平台
      account_name: searchKeyword.value || undefined,
      authorization_status: getAuthStatusValue(statusFilter.value) || undefined,
      usage_status: getUsageStatusValue(statusFilter.value) || undefined
    }

    // 使用真实API调用
    const response = await getAdAccounts(params)
    const data = response.data

    // 处理API返回的层级数据结构，转换为Element Plus树形表格需要的格式
    tableData.value = (data.list || []).map(item => {
      // 转换主账号数据
      const mainAccount = {
        id: String(item.id), // 确保ID是字符串类型
        account_type: item.account_type === 1 ? 'main' : 'sub',
        account_name: item.account_name,
        platform_account_id: item.platform_account_id,
        auth_status: getAuthStatusKey(item.authorization_status),
        access_token: item.token,
        refresh_token: item.refresh_token,
        token_expires_at: item.token_expire_time,
        status: item.usage_status === 1 ? 'active' : 'disabled',
        balance: item.account_balance || 0,
        owner_name: item.owner,
        last_sync_time: item.last_sync,
        created_at: item.created_at,
        updated_at: item.updated_at,
        remark: item.remark || '',
        parent_account_id: item.parent_id,
        parent_account_name: item.parent_account_name
      }

      // 处理子账号数据
      if (item.sub_accounts && item.sub_accounts.length > 0) {
        mainAccount.children = item.sub_accounts.map(subItem => ({
          id: String(subItem.id), // 确保子账号ID也是字符串类型
          account_type: 'sub',
          account_name: subItem.account_name,
          platform_account_id: subItem.platform_account_id,
          auth_status: getAuthStatusKey(subItem.authorization_status),
          access_token: subItem.token,
          refresh_token: subItem.refresh_token,
          token_expires_at: subItem.token_expire_time,
          status: subItem.usage_status === 1 ? 'active' : 'disabled',
          balance: subItem.account_balance || 0,
          owner_name: subItem.owner,
          last_sync_time: subItem.last_sync,
          created_at: subItem.created_at,
          updated_at: subItem.updated_at,
          remark: subItem.remark || '',
          parent_account_id: subItem.parent_id,
          parent_account_name: subItem.parent_account_name
        }))
      } else {
        mainAccount.children = []
      }

      return mainAccount
    })

    pagination.total = data.total || 0

    if (tableData.value.length === 0) {
      ElMessage.info('暂无数据')
    }
  } catch (error) {
    console.error('获取投放账号列表失败:', error)
    ElMessage.error('加载数据失败: ' + (error.response?.data?.message || error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 实现搜索逻辑
  loadTableData()
}

const handleRefresh = () => {
  searchKeyword.value = ''
  statusFilter.value = ''
  pagination.page = 1
  loadTableData()
}

const handleAddMainAccount = () => {
  isEdit.value = false
  Object.assign(formData, {
    account_type: 'main',
    parent_account_id: '',
    account_name: '',
    platform_account_id: '',
    owner_name: '',
    remark: ''
  })
  dialogVisible.value = true
}

const handleAddSubAccount = async () => {
  // 确保主账号选项已加载
  if (mainAccounts.value.length === 0) {
    await loadMainAccounts()
  }

  if (mainAccounts.value.length === 0) {
    ElMessage.warning('请先添加主账号')
    return
  }

  isEdit.value = false
  Object.assign(formData, {
    account_type: 'sub',
    parent_account_id: '',
    account_name: '',
    platform_account_id: '',
    owner_name: '',
    remark: ''
  })
  dialogVisible.value = true
}

const handleEdit = (row) => {
  isEdit.value = true
  Object.assign(formData, {
    id: row.id,
    account_type: row.account_type,
    parent_account_id: row.parent_account_id || '',
    account_name: row.account_name,
    platform_account_id: row.platform_account_id,
    owner_name: row.owner_name,
    remark: row.remark || ''
  })
  dialogVisible.value = true
}

const handleViewDetail = (row) => {
  currentAccount.value = row
  detailDialogVisible.value = true
}

const handleAuthorize = (row) => {
  authAccount.value = row
  authDialogVisible.value = true
}

const handleStartAuth = async () => {
  authLoading.value = true
  try {
    // 这里应该先跳转到小红书授权页面获取授权码
    // 为了演示，我们假设用户已经获得了授权码
    const authCode = await getAuthCodeFromUser()

    if (!authCode) {
      ElMessage.warning('请先获取授权码')
      return
    }

    // 调用小红书授权接口
    const response = await xiaohongshuAuth({
      account_id: authAccount.value.id,
      auth_code: authCode
    })

    ElMessage.success('授权成功')

    // 显示授权结果信息
    if (response.data) {
      const { advertiser_name, access_token_expires_in } = response.data
      ElMessage({
        message: `授权成功！广告主：${advertiser_name}，Token有效期：${access_token_expires_in}秒`,
        type: 'success',
        duration: 5000
      })
    }

    authDialogVisible.value = false
    // 重新加载数据以获取最新状态
    loadTableData()
  } catch (error) {
    console.error('授权失败:', error)
    ElMessage.error('授权失败: ' + (error.response?.data?.message || error.message || '未知错误'))
  } finally {
    authLoading.value = false
  }
}

// 获取用户输入的授权码（简化版本，实际应该通过弹窗或其他方式获取）
const getAuthCodeFromUser = async () => {
  try {
    const { value } = await ElMessageBox.prompt('请输入从小红书开放平台获取的授权码', '授权码', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputErrorMessage: '授权码不能为空'
    })
    return value
  } catch {
    return null
  }
}

const handleRefreshToken = async (row) => {
  try {
    await ElMessageBox.confirm('确定要刷新Token吗？', '确认操作', {
      type: 'warning'
    })

    loading.value = true

    // 获取刷新Token（简化版本，实际应该从账号信息中获取）
    const refreshToken = await getRefreshTokenFromUser(row)

    if (!refreshToken) {
      ElMessage.warning('请提供有效的刷新Token')
      return
    }

    // 调用刷新Token接口
    const response = await xiaohongshuRefreshToken({
      account_id: row.id,
      refresh_token: refreshToken
    })

    ElMessage.success('Token刷新成功')

    // 显示刷新结果信息
    if (response.data) {
      const { access_token_expires_in } = response.data
      ElMessage({
        message: `Token刷新成功！新Token有效期：${access_token_expires_in}秒`,
        type: 'success',
        duration: 5000
      })
    }

    // 重新加载数据以获取最新状态
    loadTableData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Token刷新失败:', error)
      ElMessage.error('Token刷新失败: ' + (error.response?.data?.message || error.message || '未知错误'))
    }
  } finally {
    loading.value = false
  }
}

// 获取刷新Token（简化版本，实际应该从账号信息中获取）
const getRefreshTokenFromUser = async (row) => {
  try {
    const { value } = await ElMessageBox.prompt(
      `请输入账号 "${row.account_name}" 的刷新Token`,
      '刷新Token',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '刷新Token不能为空',
        inputValue: row.refresh_token || ''
      }
    )
    return value
  } catch {
    return null
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    submitLoading.value = true

    const submitData = {
      platform: 1, // 小红书平台
      account_name: formData.account_name,
      platform_account_id: formData.platform_account_id,
      owner: formData.owner_name,
      account_balance: 0, // 初始余额为0
      token: '', // 初始token为空，需要授权后获取
      token_expire_time: '',
      usage_status: 1 // 默认启用
    }

    if (isEdit.value) {
      // 编辑模式
      await updateAdAccount(formData.id, submitData)
      ElMessage.success('编辑成功')
    } else {
      // 新增模式
      if (formData.account_type === 'main') {
        // 创建主账号
        await createMasterAccount(submitData)
      } else {
        // 创建子账号
        submitData.parent_id = formData.parent_account_id
        await createSubAccount(submitData)
      }
      ElMessage.success('添加成功')
    }

    dialogVisible.value = false
    loadTableData()
  } catch (error) {
    if (error !== false) { // 不是表单验证失败
      console.error('提交失败:', error)
      ElMessage.error((isEdit.value ? '编辑失败: ' : '添加失败: ') +
        (error.response?.data?.message || error.message || '未知错误'))
    }
  } finally {
    submitLoading.value = false
  }
}

const handleDialogClose = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

const handleDropdownCommand = async (command) => {
  const { action, row } = command
  
  switch (action) {
    case 'view-detail':
      handleViewDetail(row)
      break
    case 'sync':
      await handleSyncData(row)
      break
    case 'toggle-status':
      await handleToggleStatus(row)
      break
    case 'revoke-auth':
      await handleRevokeAuth(row)
      break
    case 'delete':
      await handleDelete(row)
      break
  }
}

const handleSyncData = async (row) => {
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    row.last_sync_time = new Date().toISOString()
    ElMessage.success('数据同步成功')
  } catch (error) {
    ElMessage.error('数据同步失败')
  } finally {
    loading.value = false
  }
}

const handleToggleStatus = async (row) => {
  try {
    const action = row.status === 'active' ? '禁用' : '启用'
    const newStatus = row.status === 'active' ? 2 : 1

    await ElMessageBox.confirm(`确定要${action}该账号吗？`, '确认操作', {
      type: 'warning'
    })

    loading.value = true

    await updateAdAccount(row.id, {
      account_name: row.account_name,
      platform_account_id: row.platform_account_id,
      usage_status: newStatus,
      account_balance: row.balance,
      owner: row.owner_name
    })

    ElMessage.success(`${action}成功`)
    loadTableData() // 重新加载数据
  } catch (error) {
    if (error !== 'cancel') {
      console.error('状态切换失败:', error)
      ElMessage.error('操作失败: ' + (error.response?.data?.message || error.message || '未知错误'))
    }
  } finally {
    loading.value = false
  }
}

const handleRevokeAuth = async (row) => {
  try {
    await ElMessageBox.confirm('确定要撤销授权吗？撤销后需要重新授权才能使用。', '确认操作', {
      type: 'warning'
    })
    
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    row.auth_status = 'unauthorized'
    row.access_token = ''
    row.refresh_token = ''
    row.token_expires_at = ''
    
    ElMessage.success('授权撤销成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('撤销授权失败')
    }
  } finally {
    loading.value = false
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该账号吗？删除后无法恢复。', '确认删除', {
      type: 'error'
    })

    loading.value = true
    await deleteAdAccount(row.id)

    ElMessage.success('删除成功')
    loadTableData() // 重新加载数据
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败: ' + (error.response?.data?.message || error.message || '未知错误'))
    }
  } finally {
    loading.value = false
  }
}

const handleExport = async () => {
  exportLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.page = 1
  loadTableData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadTableData()
}

// 处理表格行展开/收起
const handleExpandChange = (row, expandedRows) => {
  const isExpanded = expandedRows.some(expandedRow => expandedRow.id === row.id)
  if (isExpanded) {
    // 展开：添加到展开列表
    if (!expandedRowKeys.value.includes(row.id)) {
      expandedRowKeys.value.push(row.id)
    }
  } else {
    // 收起：从展开列表移除
    const index = expandedRowKeys.value.indexOf(row.id)
    if (index > -1) {
      expandedRowKeys.value.splice(index, 1)
    }
  }
}

// 状态转换辅助方法
const getAuthStatusKey = (authStatus) => {
  const statusMap = {
    0: 'unauthorized',  // 未授权
    1: 'authorized',    // 已授权
    2: 'expired',       // 已过期
    3: 'inherited'      // 继承授权
  }
  return statusMap[authStatus] || 'unauthorized'
}

const getAuthStatusValue = (statusKey) => {
  const statusMap = {
    'unauthorized': 0,
    'authorized': 1,
    'expired': 2,
    'inherited': 3
  }
  return statusMap[statusKey]
}

const getUsageStatusValue = (statusKey) => {
  const statusMap = {
    'active': 1,
    'disabled': 2
  }
  return statusMap[statusKey]
}

// 状态相关方法
const getAuthStatusType = (status) => {
  const statusMap = {
    'authorized': 'success',
    'unauthorized': 'danger',
    'expired': 'warning',
    'inherited': 'info'
  }
  return statusMap[status] || 'info'
}

const getAuthStatusText = (status) => {
  const statusMap = {
    'authorized': '已授权',
    'unauthorized': '未授权',
    'expired': '已过期',
    'inherited': '继承授权'
  }
  return statusMap[status] || '未知'
}

const getStatusType = (status) => {
  const statusMap = {
    'active': 'success',
    'disabled': 'danger',
    'pending': 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'active': '启用',
    'disabled': '禁用',
    'pending': '待审核'
  }
  return statusMap[status] || '未知'
}

const getExpirationClass = (expiresAt) => {
  const expireDate = new Date(expiresAt)
  const now = new Date()
  const diffDays = (expireDate - now) / (1000 * 60 * 60 * 24)
  
  if (diffDays < 0) {
    return 'text-danger' // 已过期
  } else if (diffDays < 7) {
    return 'text-warning' // 7天内过期
  } else {
    return 'text-success' // 正常
  }
}
</script>

<style scoped>
.xiaohongshu-accounts-container {
  padding: 20px;
  background: #f5f7fa;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.account-name-cell {
  display: flex;
  align-items: center;

  &.sub-account {
    padding-left: 20px;
  }

  .expand-icon {
    margin-right: 4px;
    color: #909399;
    font-size: 12px;
  }

  .account-name {
    font-weight: 500;

    &.main-account {
      color: #303133;
      font-size: 14px;
    }

    &.sub-account {
      color: #606266;
      font-size: 13px;
    }
  }

  .sub-count {
    margin-left: 8px;
    color: #909399;
    font-size: 12px;
  }

  .sub-account-indent {
    width: 20px;
    height: 1px;
  }
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.account-detail {
  padding: 20px 0;
}

.auth-content {
  padding: 10px 0;
}

.auth-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.auth-info p {
  margin: 8px 0;
  color: #606266;
}

.text-warning {
  color: #e6a23c;
}

.text-danger {
  color: #f56c6c;
}

.text-success {
  color: #67c23a;
}

.text-muted {
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-table) {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.el-table__header) {
  background-color: #fafafa;
}

:deep(.el-table .el-table__cell) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #303133;
}
</style> 