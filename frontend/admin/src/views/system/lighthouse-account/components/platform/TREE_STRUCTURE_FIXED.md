# 投放账号管理树形结构问题修复

## 问题描述

在实现投放账号管理的树形结构时，遇到了子账号不显示的问题。尽管API返回了正确的数据结构，但Element Plus的树形表格没有正确显示子账号。

## 问题原因分析

经过调试发现，主要问题在于：

1. **数据结构转换不正确**: API返回的数据结构与Element Plus树形表格要求的格式不完全匹配
2. **ID类型问题**: Element Plus的row-key要求ID为字符串类型，但API返回的是数字类型
3. **children属性处理**: 需要确保children属性始终存在，即使是空数组

## 解决方案

### 1. 正确的数据结构转换

```javascript
// 处理API返回的层级数据结构，转换为Element Plus树形表格需要的格式
tableData.value = (data.list || []).map(item => {
  // 转换主账号数据
  const mainAccount = {
    id: String(item.id), // 确保ID是字符串类型
    account_type: item.account_type === 1 ? 'main' : 'sub',
    account_name: item.account_name,
    platform_account_id: item.platform_account_id,
    auth_status: getAuthStatusKey(item.authorization_status),
    // ... 其他字段映射
  }
  
  // 处理子账号数据
  if (item.sub_accounts && item.sub_accounts.length > 0) {
    mainAccount.children = item.sub_accounts.map(subItem => ({
      id: String(subItem.id), // 确保子账号ID也是字符串类型
      account_type: 'sub',
      account_name: subItem.account_name,
      // ... 其他字段映射
    }))
  } else {
    mainAccount.children = [] // 确保children属性存在
  }
  
  return mainAccount
})
```

### 2. Element Plus表格配置

```vue
<el-table
  :data="tableData"
  :tree-props="{ children: 'children' }"
  row-key="id"
  :default-expand-all="false"
  :expand-row-keys="expandedRowKeys"
  @expand-change="handleExpandChange"
>
```

### 3. 关键配置要点

- **row-key="id"**: 必须设置，且ID必须是字符串类型
- **tree-props**: 指定children字段名
- **children属性**: 必须存在，空时设为空数组而不是undefined
- **ID唯一性**: 确保所有节点的ID在整个树中唯一

## 修复后的效果

### 1. 树形结构显示
- 主账号作为根节点显示
- 子账号作为主账号的子节点显示
- 支持展开/收起功能

### 2. 视觉区分
- 主账号：蓝色标签，较大字体
- 子账号：绿色标签，较小字体，左侧缩进

### 3. 操作差异化
- 主账号：支持授权、刷新Token等完整操作
- 子账号：显示"继承主账号授权"，简化操作

## 调试技巧

### 1. 数据结构验证
```javascript
// 添加调试信息验证数据结构
console.log('表格数据:', tableData.value)
tableData.value.forEach((item, index) => {
  console.log(`主账号${index}:`, {
    id: item.id,
    name: item.account_name,
    childrenCount: item.children?.length || 0,
    children: item.children?.map(child => ({
      id: child.id,
      name: child.account_name
    }))
  })
})
```

### 2. 简化测试
```javascript
// 创建简单的测试数据验证树形结构
tableData.value = [
  {
    id: '1',
    account_name: '主账号',
    children: [
      {
        id: '2',
        account_name: '子账号'
      }
    ]
  }
]
```

## 常见问题

### 1. 子账号不显示
- **原因**: ID类型不匹配或children属性为undefined
- **解决**: 确保ID为字符串，children为数组

### 2. 展开功能不工作
- **原因**: 缺少row-key或expand-change事件处理
- **解决**: 正确配置row-key和事件处理

### 3. 数据更新后树形结构丢失
- **原因**: 数据重新赋值时没有保持正确的结构
- **解决**: 确保每次数据更新都正确转换格式

## 最佳实践

1. **数据转换**: 在API数据和组件数据之间建立清晰的转换层
2. **类型一致**: 确保ID类型在整个应用中保持一致
3. **结构验证**: 在开发阶段添加数据结构验证
4. **渐进测试**: 从简单的静态数据开始，逐步增加复杂性
5. **文档参考**: 严格按照Element Plus官方文档的要求配置

## 总结

树形表格的实现关键在于：
1. 正确的数据结构转换
2. 合适的Element Plus配置
3. 字符串类型的ID
4. 完整的children属性

通过以上修复，投放账号管理现在能够正确显示主账号和子账号的树形结构，实现了预期的交互效果。
