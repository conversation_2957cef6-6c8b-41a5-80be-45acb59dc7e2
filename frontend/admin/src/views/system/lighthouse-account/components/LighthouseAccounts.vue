<template>
  <div class="lighthouse-accounts-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="请输入账号名称、邮箱或APPID"
        style="width: 240px"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-select
        v-model="listQuery.agent_id"
        placeholder="代理商"
        clearable
        style="width: 150px"
        class="filter-item"
      >
        <el-option 
          v-for="agent in agentList" 
          :key="agent.id" 
          :label="agent.agent_name" 
          :value="agent.id" 
        />
      </el-select>
      <el-select
        v-model="listQuery.operator_id"
        placeholder="负责投手"
        clearable
        style="width: 150px"
        class="filter-item"
      >
        <el-option 
          v-for="operator in operatorList" 
          :key="operator.id" 
          :label="operator.username" 
          :value="operator.id" 
        />
      </el-select>
      <el-select
        v-model="listQuery.status"
        placeholder="状态"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="启用" :value="1" />
        <el-option label="禁用" :value="0" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="Search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="Plus"
        @click="handleCreate"
      >
        新增账号
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="80">
        <template #default="{ row }">
          {{ row.id }}
        </template>
      </el-table-column>
      <el-table-column label="账号名称" min-width="120">
        <template #default="{ row }">
          <div class="account-info">
            <div class="account-name">{{ row.account_name }}</div>
            <div class="account-email">{{ row.account_email }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="主体/商家标记" min-width="150">
        <template #default="{ row }">
          <div class="subject-info">
            <div class="subject">{{ row.subject }}</div>
            <div class="merchant-mark" v-if="row.merchant_mark">{{ row.merchant_mark }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="APPID" width="120">
        <template #default="{ row }">
          <el-tooltip :content="row.app_id" placement="top">
            <span class="app-id">{{ row.app_id.substring(0, 8) }}...</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="余额" width="100" align="center">
        <template #default="{ row }">
          <span :class="{ 'text-danger': row.balance <= 0 }">
            ¥{{ parseFloat(row.balance).toFixed(2) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="消耗比例" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getRatioType(row.consumption_ratio)">
            {{ row.consumption_ratio }}x
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="代理商" width="120">
        <template #default="{ row }">
          <span v-if="row.agent_name">{{ row.agent_name }}</span>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>
      <el-table-column label="负责投手" width="100">
        <template #default="{ row }">
          <span v-if="row.operator_name">{{ row.operator_name }}</span>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="最后同步" width="120">
        <template #default="{ row }">
          <div class="sync-time">
            {{ row.last_sync_time ? formatDateTime(row.last_sync_time) : '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="info" size="small" @click="handleSync(row)">
              同步数据
            </el-button>
            <el-dropdown trigger="click" @command="(command) => handleDropdownCommand(command, row)">
              <el-button size="small" type="text" class="more-btn">
                更多
                <el-icon class="el-icon--right">
                  <ArrowDown />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item 
                    :command="row.status === 1 ? 'disable' : 'enable'"
                    :icon="row.status === 1 ? 'CircleClose' : 'CircleCheck'"
                  >
                    {{ row.status === 1 ? '禁用账号' : '启用账号' }}
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" icon="Delete" divided>
                    删除账号
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <Pagination
      :total="total"
      :page="listQuery.page"
      :limit="listQuery.limit"
      @pagination="handlePagination"
    />

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogStatus === 'create' ? '新增灯火账号' : '编辑灯火账号'"
      :model-value="dialogFormVisible"
      @update:model-value="dialogFormVisible = $event"
      width="600px"
    >
      <el-form
        ref="dataFormRef"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="账号名称" prop="account_name">
              <el-input v-model="temp.account_name" placeholder="请输入账号名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号邮箱" prop="account_email">
              <el-input v-model="temp.account_email" placeholder="请输入账号邮箱" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="主体" prop="subject">
          <el-input v-model="temp.subject" placeholder="请输入主体名称" />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商家标记">
              <el-input v-model="temp.merchant_mark" placeholder="请输入商家标记（可选）" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账号APPID" prop="app_id">
              <el-input v-model="temp.app_id" placeholder="请输入账号APPID" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="账号余额" prop="balance">
              <el-input-number
                v-model="temp.balance"
                :precision="2"
                :step="100"
                :min="0"
                placeholder="请输入账号余额"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="消耗比例" prop="consumption_ratio">
              <el-input-number
                v-model="temp.consumption_ratio"
                :precision="2"
                :step="0.1"
                :min="0.1"
                :max="10"
                placeholder="请输入消耗比例"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="账号公钥">
          <el-input
            v-model="temp.public_key"
            type="textarea"
            :rows="4"
            placeholder="请输入账号公钥（可选）"
          />
        </el-form-item>
        
        <el-form-item label="账号私钥">
          <el-input
            v-model="temp.private_key"
            type="textarea"
            :rows="4"
            placeholder="请输入账号私钥（可选）"
            show-password
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="代理商">
              <el-select v-model="temp.agent_id" placeholder="请选择代理商" style="width: 100%">
                <el-option label="无" :value="null" />
                <el-option 
                  v-for="agent in agentList" 
                  :key="agent.id" 
                  :label="agent.agent_name" 
                  :value="agent.id" 
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责投手">
              <el-select v-model="temp.operator_id" placeholder="请选择负责投手" style="width: 100%">
                <el-option label="无" :value="null" />
                <el-option 
                  v-for="operator in operatorList" 
                  :key="operator.id" 
                  :label="operator.username" 
                  :value="operator.id" 
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="temp.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="启用" :value="1" />
                <el-option label="禁用" :value="0" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="备注">
          <el-input
            v-model="temp.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import Pagination from './Pagination.vue'

const listLoading = ref(false)
const list = ref([])
const total = ref(0)
const dialogFormVisible = ref(false)
const dialogStatus = ref('')
const dataFormRef = ref()

const agentList = ref([])
const operatorList = ref([])

const listQuery = reactive({
  page: 1,
  limit: 20,
  keyword: '',
  agent_id: '',
  operator_id: '',
  status: ''
})

const temp = reactive({
  id: undefined,
  account_name: '',
  account_email: '',
  subject: '',
  balance: 0,
  merchant_mark: '',
  app_id: '',
  public_key: '',
  private_key: '',
  consumption_ratio: 1.00,
  operator_id: null,
  agent_id: null,
  status: 1,
  remark: ''
})

const rules = {
  account_name: [{ required: true, message: '请输入账号名称', trigger: 'blur' }],
  account_email: [
    { required: true, message: '请输入账号邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  subject: [{ required: true, message: '请输入主体名称', trigger: 'blur' }],
  app_id: [{ required: true, message: '请输入账号APPID', trigger: 'blur' }],
  balance: [{ required: true, message: '请输入账号余额', trigger: 'blur' }],
  consumption_ratio: [{ required: true, message: '请输入消耗比例', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

onMounted(() => {
  getList()
  getAgentList()
  getOperatorList()
})

const getList = () => {
  listLoading.value = true
  // TODO: 调用API获取灯火账号列表
  // 模拟数据
  setTimeout(() => {
    list.value = [
      {
        id: 1,
        account_name: '灯火测试账号1',
        account_email: '<EMAIL>',
        subject: '北京某某科技有限公司',
        balance: 5000.00,
        merchant_mark: 'TECH_001',
        app_id: 'APP_LH_001_1234567890',
        public_key: 'MIIBIjANBgkqhkiG9w0BAQEF...',
        private_key: 'MIIEvQIBADANBgkqhkiG9w0BAQ...',
        consumption_ratio: 1.20,
        operator_id: 1,
        operator_name: '张三',
        agent_id: 1,
        agent_name: '测试代理商1',
        status: 1,
        last_sync_time: '2023-12-01 15:30:00',
        created_at: '2023-12-01 10:00:00'
      },
      {
        id: 2,
        account_name: '灯火测试账号2',
        account_email: '<EMAIL>',
        subject: '上海某某广告有限公司',
        balance: 8500.50,
        merchant_mark: 'AD_002',
        app_id: 'APP_LH_002_0987654321',
        public_key: 'MIIBIjANBgkqhkiG9w0BAQEF...',
        private_key: 'MIIEvQIBADANBgkqhkiG9w0BAQ...',
        consumption_ratio: 1.10,
        operator_id: 2,
        operator_name: '李四',
        agent_id: 2,
        agent_name: '测试代理商2',
        status: 1,
        last_sync_time: '2023-12-02 16:00:00',
        created_at: '2023-12-02 10:00:00'
      },
      {
        id: 3,
        account_name: '停用的账号',
        account_email: '<EMAIL>',
        subject: '深圳某某传媒有限公司',
        balance: 0.00,
        merchant_mark: 'MEDIA_003',
        app_id: 'APP_LH_003_5555555555',
        public_key: '',
        private_key: '',
        consumption_ratio: 1.00,
        operator_id: null,
        operator_name: null,
        agent_id: 1,
        agent_name: '测试代理商1',
        status: 0,
        last_sync_time: null,
        created_at: '2023-12-03 10:00:00'
      }
    ]
    total.value = 3
    listLoading.value = false
  }, 1000)
}

const getAgentList = () => {
  // TODO: 调用API获取代理商列表
  // 模拟数据
  agentList.value = [
    { id: 1, agent_name: '测试代理商1' },
    { id: 2, agent_name: '测试代理商2' }
  ]
}

const getOperatorList = () => {
  // TODO: 调用API获取投手列表
  // 模拟数据
  operatorList.value = [
    { id: 1, username: '张三' },
    { id: 2, username: '李四' },
    { id: 3, username: '王五' }
  ]
}

const handleFilter = () => {
  listQuery.page = 1
  getList()
}

const resetTemp = () => {
  temp.id = undefined
  temp.account_name = ''
  temp.account_email = ''
  temp.subject = ''
  temp.balance = 0
  temp.merchant_mark = ''
  temp.app_id = ''
  temp.public_key = ''
  temp.private_key = ''
  temp.consumption_ratio = 1.00
  temp.operator_id = null
  temp.agent_id = null
  temp.status = 1
  temp.remark = ''
}

const handleCreate = () => {
  resetTemp()
  dialogStatus.value = 'create'
  dialogFormVisible.value = true
}

const handleEdit = (row) => {
  Object.assign(temp, row)
  dialogStatus.value = 'update'
  dialogFormVisible.value = true
}

const createData = () => {
  dataFormRef.value.validate((valid) => {
    if (valid) {
      // TODO: 调用API创建灯火账号
      ElMessage.success('创建成功')
      dialogFormVisible.value = false
      getList()
    }
  })
}

const updateData = () => {
  dataFormRef.value.validate((valid) => {
    if (valid) {
      // TODO: 调用API更新灯火账号
      ElMessage.success('更新成功')
      dialogFormVisible.value = false
      getList()
    }
  })
}

const handleSync = (row) => {
  ElMessageBox.confirm('确定要同步该账号的数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    // TODO: 调用API同步数据
    ElMessage.success('同步成功')
    // 更新最后同步时间
    row.last_sync_time = new Date().toLocaleString()
  })
}

const handleToggleStatus = (row) => {
  const action = row.status === 1 ? '禁用' : '启用'
  ElMessageBox.confirm(`确定要${action}该账号吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // TODO: 调用API切换状态
    row.status = row.status === 1 ? 0 : 1
    ElMessage.success(`${action}成功`)
  })
}

const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该灯火账号吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // TODO: 调用API删除灯火账号
    ElMessage.success('删除成功')
    getList()
  })
}

const formatDate = (dateString) => {
  return dateString
}

const handlePagination = ({ page, limit }) => {
  listQuery.page = page
  listQuery.limit = limit
  getList()
}

const getRatioType = (ratio) => {
  if (ratio >= 1.5) return 'danger'
  if (ratio >= 1.2) return 'warning'
  return 'success'
}

const formatDateTime = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
}

const handleDropdownCommand = (command, row) => {
  switch (command) {
    case 'enable':
    case 'disable':
      handleToggleStatus(row)
      break
    case 'delete':
      handleDelete(row)
      break
  }
}
</script>

<style lang="scss" scoped>
.lighthouse-accounts-container {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  
  .filter-container {
    padding: 20px;
    margin-bottom: 0;
    border-bottom: 1px solid #ebeef5;
    background: #fafafa;
    border-radius: 4px 4px 0 0;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    
    .filter-item {
      margin-right: 10px;
      margin-bottom: 0;
      
      &:last-child {
        margin-right: 0;
      }
    }

    .el-input {
      min-width: 200px;
    }

    .el-select {
      min-width: 120px;
    }

    .el-button {
      height: 32px;
      padding: 8px 15px;
      font-size: 14px;
      border-radius: 4px;
      
      &.el-button--primary {
        background: #409eff;
        border-color: #409eff;
        
        &:hover {
          background: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }

  .el-table {
    margin: 0;
    border-radius: 0;
    
    .el-table__header-wrapper {
      background: #f5f7fa;
    }

    .el-table__header th {
      background: #f5f7fa;
      color: #606266;
      font-weight: 500;
      border-bottom: 1px solid #ebeef5;
    }

    .el-table__row {
      &:hover > td {
        background-color: #f5f7fa !important;
      }
    }

    .el-table__body td {
      border-bottom: 1px solid #ebeef5;
    }

    .action-buttons {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      
      .el-button {
        padding: 4px 8px;
        font-size: 12px;
        border-radius: 3px;
        
        &.el-button--small {
          padding: 4px 8px;
          font-size: 12px;
        }
        
        &.more-btn {
          color: #606266;
          padding: 4px 6px;
          
          &:hover {
            color: #409eff;
            background: rgba(64, 158, 255, 0.1);
          }
          
          .el-icon {
            font-size: 12px;
            margin-left: 2px;
          }
        }
      }
    }
  }

  .el-tag {
    border-radius: 4px;
    font-size: 12px;
    
    &.el-tag--success {
      background-color: #f0f9ff;
      border-color: #b3d8ff;
      color: #409eff;
    }
    
    &.el-tag--danger {
      background-color: #fef0f0;
      border-color: #fbc4c4;
      color: #f56c6c;
    }
  }

  // 对话框样式优化
  :deep(.el-dialog) {
    border-radius: 6px;
    
    .el-dialog__header {
      padding: 20px 24px 10px;
      border-bottom: 1px solid #ebeef5;
      
      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }
    
    .el-dialog__body {
      padding: 24px;
      
      .el-form {
        .el-form-item {
          margin-bottom: 22px;
          
          .el-form-item__label {
            color: #606266;
            font-weight: 500;
            line-height: 32px;
          }
          
          .el-input, .el-select {
            width: 100%;
            
            .el-input__inner {
              border-radius: 4px;
              transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
              
              &:focus {
                border-color: #409eff;
              }
            }
          }
          
          .el-textarea {
            .el-textarea__inner {
              border-radius: 4px;
              transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
              
              &:focus {
                border-color: #409eff;
              }
            }
          }
        }
      }
    }
    
    .el-dialog__footer {
      padding: 10px 24px 20px;
      text-align: right;
      border-top: 1px solid #ebeef5;
      
      .el-button {
        padding: 8px 20px;
        border-radius: 4px;
        font-size: 14px;
        
        &.el-button--primary {
          background: #409eff;
          border-color: #409eff;
          
          &:hover {
            background: #66b1ff;
            border-color: #66b1ff;
          }
        }
      }
    }
  }

  // 分页样式优化
  :deep(.pagination-wrapper) {
    padding: 20px;
    background: #fafafa;
    border-top: 1px solid #ebeef5;
    border-radius: 0 0 4px 4px;
    
    .el-pagination {
      .el-pagination__total {
        color: #606266;
        font-size: 14px;
      }
      
      .el-pager li {
        border-radius: 4px;
        margin: 0 2px;
        
        &.active {
          background: #409eff;
          color: #fff;
        }
      }
      
      .el-pagination__jump {
        color: #606266;
        font-size: 14px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .lighthouse-accounts-container {
    .filter-container {
      padding: 15px;
      flex-direction: column;
      align-items: stretch;
      
      .filter-item {
        width: 100%;
        margin-right: 0 !important;
        margin-bottom: 10px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .el-input, .el-select {
          width: 100% !important;
        }
        
        .el-button {
          width: 100%;
          justify-content: center;
        }
      }
    }
    
    .el-table {
      font-size: 12px;
      
      .action-buttons {
        flex-direction: column;
        gap: 4px;
        
        .el-button {
          padding: 2px 6px;
          font-size: 11px;
          width: auto;
          min-width: 60px;
        }
        
        .el-dropdown {
          width: 100%;
          
          .more-btn {
            width: 100%;
            justify-content: center;
          }
        }
      }
    }
  }

  .account-info {
    .account-name {
      font-weight: 500;
      color: #303133;
    }
    .account-email {
      font-size: 12px;
      color: #909399;
      margin-top: 2px;
    }
  }

  .subject-info {
    .subject {
      font-size: 14px;
      color: #303133;
      margin-bottom: 2px;
    }
    .merchant-mark {
      font-size: 12px;
      color: #409eff;
      background: #ecf5ff;
      padding: 1px 6px;
      border-radius: 2px;
      display: inline-block;
    }
  }

  .app-id {
    font-family: 'Courier New', Courier, monospace;
    font-size: 12px;
    color: #606266;
    cursor: pointer;
    
    &:hover {
      color: #409eff;
    }
  }

  .text-danger {
    color: #f56c6c;
  }

  .text-muted {
    color: #909399;
  }

  .sync-time {
    font-size: 12px;
    color: #606266;
  }
}
</style> 