<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon><Monitor /></el-icon>
        投放账号管理
      </h2>
      <p class="page-description">管理各大平台的投放账号和代理商信息</p>
    </div>

    <!-- 主要Tab切换 -->
    <el-tabs v-model="activeMainTab" @tab-click="handleMainTabClick" class="main-tabs">
      <!-- 账号管理 -->
      <el-tab-pane name="accounts">
        <template #label>
          <span class="tab-label">
            <el-icon><User /></el-icon>
            账号管理
          </span>
        </template>
        
        <!-- 平台子Tab -->
        <el-tabs v-model="activePlatformTab" @tab-click="handlePlatformTabClick" class="platform-tabs">
          <!-- 灯火平台 -->
          <el-tab-pane name="lighthouse">
            <template #label>
              <span class="platform-tab-label lighthouse">
                <el-icon><Lightning /></el-icon>
                灯火
              </span>
            </template>
            <LighthouseAccounts />
          </el-tab-pane>

          <!-- 小红书平台 -->
          <el-tab-pane name="xiaohongshu">
            <template #label>
              <span class="platform-tab-label xiaohongshu">
                <el-icon><Star /></el-icon>
                小红书
              </span>
            </template>
            <XiaohongshuAccounts />
          </el-tab-pane>

          <!-- B站平台 - 暂时隐藏 -->
          <!-- <el-tab-pane name="bilibili">
            <template #label>
              <span class="platform-tab-label bilibili">
                <el-icon><VideoPlay /></el-icon>
                B站
              </span>
            </template>
            <BilibiliAccounts />
          </el-tab-pane> -->

          <!-- 抖音平台 - 暂时隐藏 -->
          <!-- <el-tab-pane name="douyin">
            <template #label>
              <span class="platform-tab-label douyin">
                <el-icon><MagicStick /></el-icon>
                抖音
              </span>
            </template>
            <DouyinAccounts />
          </el-tab-pane> -->

          <!-- 今日头条 - 暂时隐藏 -->
          <!-- <el-tab-pane name="toutiao">
            <template #label>
              <span class="platform-tab-label toutiao">
                <el-icon><Reading /></el-icon>
                头条
              </span>
            </template>
            <ToutiaoAccounts />
          </el-tab-pane> -->

          <!-- 快手 - 暂时隐藏 -->
          <!-- <el-tab-pane name="kuaishou">
            <template #label>
              <span class="platform-tab-label kuaishou">
                <el-icon><VideoCamera /></el-icon>
                快手
              </span>
            </template>
            <KuaishouAccounts />
          </el-tab-pane> -->
        </el-tabs>
      </el-tab-pane>

      <!-- 代理商管理 -->
      <el-tab-pane name="agents">
        <template #label>
          <span class="tab-label">
            <el-icon><OfficeBuilding /></el-icon>
            代理商管理
          </span>
        </template>
        <LighthouseAgents />
      </el-tab-pane>

      <!-- 统计概览 -->
      <el-tab-pane name="overview">
        <template #label>
          <span class="tab-label">
            <el-icon><DataAnalysis /></el-icon>
            统计概览
          </span>
        </template>
        <PlatformOverview />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { 
  Monitor, User, Lightning, Star, OfficeBuilding, DataAnalysis
  // VideoPlay, MagicStick, Reading, VideoCamera // 暂时隐藏的平台图标
} from '@element-plus/icons-vue'

// 现有组件
import LighthouseAccounts from './components/LighthouseAccounts.vue'
import LighthouseAgents from './components/LighthouseAgents.vue'

// 新增平台组件（暂时用通用组件，后续可以按需定制）
import { defineAsyncComponent } from 'vue'

const XiaohongshuAccounts = defineAsyncComponent(() => 
  import('./components/platform/XiaohongshuAccounts.vue')
)

// 暂时隐藏的平台组件
// const BilibiliAccounts = defineAsyncComponent(() => 
//   import('./components/platform/BilibiliAccounts.vue')
// )
// const DouyinAccounts = defineAsyncComponent(() => 
//   import('./components/platform/DouyinAccounts.vue')
// )
// const ToutiaoAccounts = defineAsyncComponent(() => 
//   import('./components/platform/ToutiaoAccounts.vue')
// )
// const KuaishouAccounts = defineAsyncComponent(() => 
//   import('./components/platform/KuaishouAccounts.vue')
// )
const PlatformOverview = defineAsyncComponent(() => 
  import('./components/PlatformOverview.vue')
)

// Tab状态管理
const activeMainTab = ref('accounts')
const activePlatformTab = ref('lighthouse')

// Tab点击处理
const handleMainTabClick = (tab) => {
  activeMainTab.value = tab.name
}

const handlePlatformTabClick = (tab) => {
  activePlatformTab.value = tab.name
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 84px);
}

// 页面头部
.page-header {
  margin-bottom: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.06);

  .page-title {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 12px;

    .el-icon {
      font-size: 26px;
      color: #409eff;
    }
  }

  .page-description {
    margin: 0;
    font-size: 14px;
    color: #606266;
    line-height: 1.5;
  }
}

// 主要Tab样式
.main-tabs {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.06);
  overflow: hidden;

  :deep(.el-tabs__header) {
    margin: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: none;
    padding: 0 24px;

    .el-tabs__nav-wrap::after {
      display: none;
    }

    .el-tabs__nav {
      border: none;

      .el-tabs__item {
        border: none;
        padding: 0 32px;
        height: 56px;
        line-height: 56px;
        font-size: 15px;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.8);
        transition: all 0.3s;
        border-radius: 0;

        &:hover {
          color: #fff;
          background: rgba(255, 255, 255, 0.1);
        }

        &.is-active {
          color: #fff;
          background: rgba(255, 255, 255, 0.15);
          border-bottom: 3px solid #fff;
          font-weight: 600;
        }

        .tab-label {
          display: flex;
          align-items: center;
          gap: 8px;

          .el-icon {
            font-size: 16px;
          }
        }
      }
    }

    .el-tabs__active-bar {
      display: none;
    }
  }

  :deep(.el-tabs__content) {
    padding: 0;
  }
}

// 平台Tab样式
.platform-tabs {
  :deep(.el-tabs__header) {
    margin: 0;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 0 24px;

    .el-tabs__nav-wrap::after {
      display: none;
    }

    .el-tabs__nav {
      border: none;

      .el-tabs__item {
        border: none;
        padding: 0 24px;
        height: 48px;
        line-height: 48px;
        font-size: 14px;
        font-weight: 500;
        color: #606266;
        transition: all 0.3s;
        border-radius: 6px 6px 0 0;
        margin-right: 8px;

        &:hover {
          background: rgba(64, 158, 255, 0.05);
        }

        &.is-active {
          background: #fff;
          color: #409eff;
          border-bottom: 2px solid #409eff;
          font-weight: 600;
        }

        .platform-tab-label {
          display: flex;
          align-items: center;
          gap: 6px;

          .el-icon {
            font-size: 14px;
          }

          // 平台特色颜色
          &.lighthouse .el-icon { color: #f39c12; }
          &.xiaohongshu .el-icon { color: #ff2442; }
          // 暂时隐藏的平台样式
          // &.bilibili .el-icon { color: #00a1d6; }
          // &.douyin .el-icon { color: #fe2c55; }
          // &.toutiao .el-icon { color: #ff6600; }
          // &.kuaishou .el-icon { color: #ff6600; }
        }
      }
    }

    .el-tabs__active-bar {
      display: none;
    }
  }

  :deep(.el-tabs__content) {
    padding: 24px;
    background: #fff;
    border-radius: 0 0 8px 8px;
  }
}

// 响应式优化
@media (max-width: 1200px) {
  .platform-tabs :deep(.el-tabs__nav .el-tabs__item) {
    padding: 0 16px;
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .page-header {
    padding: 16px;
    margin-bottom: 16px;

    .page-title {
      font-size: 20px;
    }
  }

  .main-tabs :deep(.el-tabs__header .el-tabs__nav .el-tabs__item) {
    padding: 0 20px;
    font-size: 14px;
  }

  .platform-tabs {
    :deep(.el-tabs__header) {
      padding: 0 16px;

      .el-tabs__nav .el-tabs__item {
        padding: 0 12px;
        font-size: 12px;
        margin-right: 4px;
      }
    }

    :deep(.el-tabs__content) {
      padding: 16px;
    }
  }
}
</style>