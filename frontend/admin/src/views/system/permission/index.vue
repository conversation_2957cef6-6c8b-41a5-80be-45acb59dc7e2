<template>
  <div class="permission-management-container">
    <div class="header-container">
      <h2>权限管理</h2>
      <p class="header-desc">配置不同角色的权限，实现精细化访问控制</p>
    </div>

    <el-card>
      <template #header>
        <div class="card-header">
          <span>角色权限配置</span>
          <div class="header-actions">
            <el-button type="success" @click="handleAddPermission">
              <el-icon><Plus /></el-icon>
              新增权限
            </el-button>
            <el-button type="primary" @click="handleSaveAll" :loading="saving">
              <el-icon><Check /></el-icon>
              保存所有更改
            </el-button>
            <el-button @click="handleReset">
              <el-icon><RefreshRight /></el-icon>
              重置为默认
            </el-button>
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </div>
      </template>

      <div class="role-tabs-container" v-loading="loading">
        <el-tabs v-model="activeRole" @tab-click="handleRoleChange">
          <el-tab-pane 
            v-for="role in roles" 
            :key="role.id" 
            :label="role.name" 
            :name="String(role.id)"
          >
            <div class="role-info">
              <div class="role-basic">
                <el-tag :type="getRoleTagType(role.id)" size="large">
                  {{ role.name }}
                </el-tag>
                <span class="role-desc">{{ role.description }}</span>
                <span class="permission-count">
                  已配置 {{ getRolePermissionCount(role.id) }} 项权限
                </span>
                <el-tag v-if="role.is_system" type="warning" size="small">
                  系统角色
                </el-tag>
              </div>
              
              <div class="permission-legend">
                <span class="legend-title">权限类型说明：</span>
                <div class="legend-items">
                  <div class="legend-item">
                    <el-tag type="primary" size="small">菜单</el-tag>
                    <span>页面访问权限</span>
                  </div>
                  <div class="legend-item">
                    <el-tag type="success" size="small">按钮</el-tag>
                    <span>操作功能权限</span>
                  </div>
                  <div class="legend-item">
                    <el-tag type="warning" size="small">数据</el-tag>
                    <span>数据范围权限</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="permissions-container">
              <div 
                v-for="module in permissionModules" 
                :key="module.module" 
                class="module-section"
              >
                <div class="module-header">
                  <div class="module-title">
                    <h3>{{ module.module_name }}</h3>
                    <div class="module-stats">
                      <el-tag size="small" type="primary">菜单: {{ getModulePermissionCount(module, 'menu') }}</el-tag>
                      <el-tag size="small" type="success">按钮: {{ getModulePermissionCount(module, 'action') }}</el-tag>
                      <el-tag size="small" type="warning">数据: {{ getModulePermissionCount(module, 'data') }}</el-tag>
                    </div>
                  </div>
                  <div class="module-actions">
                    <el-button 
                      size="small" 
                      @click="handleSelectAllModule(role.id, module.module, true)"
                      :disabled="role.is_system === 1"
                    >
                      全选
                    </el-button>
                    <el-button 
                      size="small" 
                      @click="handleSelectAllModule(role.id, module.module, false)"
                      :disabled="role.is_system === 1"
                    >
                      取消全选
                    </el-button>
                  </div>
                </div>
                
                <div class="permissions-grid">
                  <div 
                    v-for="permission in module.permissions" 
                    :key="permission.id" 
                    class="permission-item"
                    :class="`permission-type-${getPermissionType(permission)}`"
                  >
                    <el-checkbox
                      :model-value="isPermissionChecked(role.id, permission.id)"
                      @change="handlePermissionChange(role.id, permission.id, $event)"
                      :disabled="role.is_system === 1"
                    >
                      <div class="permission-info">
                        <div class="permission-header">
                          <div class="permission-name">{{ permission.name }}</div>
                          <div class="permission-tags">
                            <el-tag 
                              :type="getPermissionTypeTag(getPermissionType(permission)).type" 
                              size="small"
                              class="permission-type-tag"
                            >
                              {{ getPermissionTypeTag(getPermissionType(permission)).text }}
                            </el-tag>
                            <el-tag 
                              v-if="permission.data_scope"
                              :type="getDataScopeType(permission.data_scope)" 
                              size="small"
                              class="permission-scope-tag"
                            >
                              {{ getDataScopeText(permission.data_scope) }}
                            </el-tag>
                          </div>
                          <div class="permission-actions">
                            <el-button 
                              size="small" 
                              type="primary" 
                              link 
                              @click.stop="handleEditPermission(permission)"
                            >
                              编辑
                            </el-button>
                            <el-button 
                              size="small" 
                              type="danger" 
                              link 
                              @click.stop="handleDeletePermission(permission)"
                            >
                              删除
                            </el-button>
                          </div>
                        </div>
                        <div class="permission-desc">{{ permission.description }}</div>
                        <div class="permission-code">{{ permission.code }}</div>
                      </div>
                    </el-checkbox>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>

    <!-- 权限变更日志 -->
    <el-card class="logs-card">
      <template #header>
        <div class="card-header">
          <span>权限变更日志</span>
          <el-button size="small" @click="getPermissionLogs">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>
      
      <el-table :data="permissionLogs" v-loading="logsLoading">
        <el-table-column label="操作时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作人">
          <template #default="{ row }">
            {{ row.operator_name || '系统' }}
          </template>
        </el-table-column>
        <el-table-column label="角色" width="100">
          <template #default="{ row }">
            <el-tag :type="getRoleTagType(row.role_id)" size="small">
              {{ getRoleName(row.role_id) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.action === 'grant' ? 'success' : 'danger'" size="small">
              {{ row.action === 'grant' ? '授权' : '撤销' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="权限">
          <template #default="{ row }">
            <div>
              <div class="permission-name">{{ row.permission_name || '未知权限' }}</div>
              <div class="permission-code">{{ row.permission_code || '' }}</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="logs-pagination">
        <el-pagination
          v-model:current-page="logsQuery.page"
          v-model:page-size="logsQuery.page_size"
          :total="logsTotal"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="getPermissionLogs"
          @current-change="getPermissionLogs"
        />
      </div>
    </el-card>

    <!-- 权限编辑对话框 -->
    <el-dialog 
      v-model="permissionDialogVisible" 
      :title="permissionDialogTitle"
      width="600px"
      @close="resetPermissionForm"
    >
      <el-form 
        ref="permissionFormRef" 
        :model="permissionForm" 
        :rules="permissionRules" 
        label-width="100px"
      >
        <el-form-item label="权限名称" prop="name">
          <el-input v-model="permissionForm.name" placeholder="请输入权限名称" />
        </el-form-item>
        
        <el-form-item label="权限编码" prop="code">
          <el-input v-model="permissionForm.code" placeholder="请输入权限编码，如：user:view" />
        </el-form-item>
        
        <el-form-item label="所属模块" prop="module">
          <el-select v-model="permissionForm.module" placeholder="请选择模块">
            <el-option 
              v-for="(name, key) in moduleOptions" 
              :key="key" 
              :label="name" 
              :value="key"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="权限类型" prop="type">
          <el-radio-group v-model="permissionForm.type">
            <el-radio label="function">功能权限</el-radio>
            <el-radio label="data">数据权限</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item 
          label="数据范围" 
          prop="data_scope"
          v-if="permissionForm.type === 'data'"
        >
          <el-select v-model="permissionForm.data_scope" placeholder="请选择数据范围">
            <el-option label="全部数据" value="all" />
            <el-option label="本部门数据" value="dept" />
            <el-option label="仅自己的数据" value="self" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="权限描述" prop="description">
          <el-input 
            v-model="permissionForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入权限描述"
          />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="permissionForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="permissionDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSavePermission" :loading="permissionSaving">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, RefreshRight, Refresh, Plus } from '@element-plus/icons-vue'
import { 
  getRoleList, 
  getPermissionList, 
  getRolePermissions, 
  assignPermissions,
  getPermissionLogs as getLogsApi,
  createPermission,
  updatePermission,
  deletePermission
} from '@/api/role'

const activeRole = ref('1')
const loading = ref(false)
const saving = ref(false)
const logsLoading = ref(false)

// 角色数据
const roles = ref([])

// 权限模块数据
const permissionModules = ref([])

// 所有权限数据
const allPermissions = ref([])

// 角色权限映射 { roleId: [permissionId1, permissionId2, ...] }
const rolePermissions = reactive({})

// 原始权限配置（用于重置）
const originalRolePermissions = reactive({})

// 权限日志
const permissionLogs = ref([])
const logsTotal = ref(0)
const logsQuery = reactive({
  page: 1,
  page_size: 20
})

// 权限编辑对话框
const permissionDialogVisible = ref(false)
const permissionDialogTitle = ref('新增权限')
const permissionFormRef = ref(null)
const permissionForm = ref({
  name: '',
  code: '',
  module: '',
  type: 'function',
  description: '',
  data_scope: '',
  status: 1
})
const permissionRules = ref({
  name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入权限编码', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_:]*$/, message: '权限编码格式不正确', trigger: 'blur' }
  ],
  module: [
    { required: true, message: '请选择所属模块', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择权限类型', trigger: 'change' }
  ]
})
const moduleOptions = ref({
  'user': '用户管理',
  'department': '部门管理', 
  'role': '角色管理',
  'permission': '权限管理',
  'system': '系统管理',
  'dashboard': '仪表盘',
  'agent': '代理管理',
  'media': '媒体管理',
  'slot': '资源位管理',
  'plan': '投放计划',
  'delivery': '投放管理',
  'report': '数据报表',
  'finance': '财务管理',
  'creative': '创意管理',
  'plugin': '插件管理'
})
const permissionSaving = ref(false)

onMounted(() => {
  loadData()
})

// 加载所有数据
const loadData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadRoles(),
      loadPermissions(),
    ])
    await loadRolePermissions()
    await getPermissionLogs()
  } catch (error) {
    ElMessage.error('加载数据失败：' + error.message)
  } finally {
    loading.value = false
  }
}

// 加载角色列表
const loadRoles = async () => {
  try {
    const response = await getRoleList({ page_size: 100 })
    console.log('角色列表响应:', response)
    // 检查响应数据结构 - 后端返回code: 0表示成功
    if (response && response.code === 0 && response.data) {
      roles.value = response.data.list || response.data || []
      // 设置默认选中第一个角色
      if (roles.value.length > 0) {
        activeRole.value = String(roles.value[0].id)
      }
    }
  } catch (error) {
    console.error('加载角色列表失败:', error)
    throw error
  }
}

// 加载权限列表
const loadPermissions = async () => {
  try {
    const response = await getPermissionList({ page_size: 1000 })
    console.log('权限列表响应:', response)
    // 检查响应数据结构 - 后端返回code: 0表示成功
    if (response && response.code === 0 && response.data) {
      allPermissions.value = response.data.list || response.data || []
      
      // 按模块分组权限
      const moduleMap = {}
      allPermissions.value.forEach(permission => {
        if (!moduleMap[permission.module]) {
          moduleMap[permission.module] = {
            module: permission.module,
            module_name: getModuleName(permission.module),
            permissions: []
          }
        }
        moduleMap[permission.module].permissions.push(permission)
      })
      
      permissionModules.value = Object.values(moduleMap)
    }
  } catch (error) {
    console.error('加载权限列表失败:', error)
    throw error
  }
}

// 加载角色权限配置
const loadRolePermissions = async () => {
  try {
    for (const role of roles.value) {
      const response = await getRolePermissions(role.id)
      console.log(`角色${role.name}权限响应:`, response)
      // 检查响应数据结构 - 后端返回code: 0表示成功
      if (response && response.code === 0 && response.data) {
        const permissions = response.data.permissions || response.data || []
        const permissionIds = permissions.map(p => p.id)
        rolePermissions[role.id] = permissionIds
        originalRolePermissions[role.id] = [...permissionIds]
      }
    }
  } catch (error) {
    console.error('加载角色权限失败:', error)
    throw error
  }
}

// 获取模块名称
const getModuleName = (module) => {
  const moduleNames = {
    'user': '用户管理',
    'department': '部门管理', 
    'role': '角色管理',
    'permission': '权限管理',
    'system': '系统管理',
    'dashboard': '仪表盘',
    'agent': '代理管理',
    'media': '媒体管理',
    'slot': '资源位管理',
    'plan': '投放计划',
    'delivery': '投放管理',
    'report': '数据报表',
    'finance': '财务管理',
    'creative': '创意管理',
    'plugin': '插件管理'
  }
  return moduleNames[module] || module
}

// 获取权限类型
const getPermissionType = (permission) => {
  // 优先使用新的type字段
  if (permission.type) {
    // 将后端的type字段映射到前端的显示类型
    if (permission.type === 'data') {
      return 'data'
    } else if (permission.type === 'function') {
      // 对于功能权限，根据权限编码进一步区分菜单和按钮
      const code = permission.code
      if (code.includes(':create') || code.includes(':edit') || 
          code.includes(':delete') || code.includes(':audit') ||
          code.includes(':import') || code.includes(':batch') ||
          code.includes(':assign') || code.includes(':reset') ||
          code.includes(':config') || code.includes(':install') ||
          code.includes(':uninstall') || code.includes(':generate')) {
        return 'action'
      } else {
        return 'menu'
      }
    }
  }
  
  // 兼容旧数据：基于权限编码推断类型
  const code = permission.code
  
  // 数据权限：包含data关键字或明确设置了数据范围的权限
  if (code.includes(':data:') || 
      (permission.data_scope && permission.data_scope !== '')) {
    return 'data'
  }
  
  // 按钮权限：操作类权限
  if (code.includes(':create') || code.includes(':edit') || 
      code.includes(':delete') || code.includes(':audit') ||
      code.includes(':import') || code.includes(':batch') ||
      code.includes(':assign') || code.includes(':reset') ||
      code.includes(':config') || code.includes(':install') ||
      code.includes(':uninstall') || code.includes(':generate')) {
    return 'action'
  }
  
  // 菜单权限：查看类权限
  if (code.includes(':view') || code.includes(':manage')) {
    return 'menu'
  }
  
  return 'menu' // 默认为菜单权限
}

// 获取权限类型标签
const getPermissionTypeTag = (type) => {
  const typeMap = {
    'menu': { text: '菜单', type: 'primary' },
    'action': { text: '按钮', type: 'success' },
    'data': { text: '数据', type: 'warning' }
  }
  return typeMap[type] || { text: '未知', type: 'info' }
}

// 获取模块中指定类型权限的数量
const getModulePermissionCount = (module, type) => {
  return module.permissions.filter(permission => getPermissionType(permission) === type).length
}

// 检查权限是否被选中
const isPermissionChecked = (roleId, permissionId) => {
  return rolePermissions[roleId]?.includes(permissionId) || false
}

// 获取角色权限数量
const getRolePermissionCount = (roleId) => {
  return rolePermissions[roleId]?.length || 0
}

// 获取角色标签类型
const getRoleTagType = (roleId) => {
  const types = { 1: 'primary', 2: 'success', 3: 'warning', 4: 'info', 5: 'danger' }
  return types[roleId] || 'default'
}

// 获取角色名称
const getRoleName = (roleId) => {
  const role = roles.value.find(r => r.id === roleId)
  return role ? role.name : '未知'
}

// 权限变更处理
const handlePermissionChange = (roleId, permissionId, checked) => {
  if (!rolePermissions[roleId]) {
    rolePermissions[roleId] = []
  }
  
  const permissions = rolePermissions[roleId]
  
  if (checked) {
    if (!permissions.includes(permissionId)) {
      permissions.push(permissionId)
    }
  } else {
    const index = permissions.indexOf(permissionId)
    if (index > -1) {
      permissions.splice(index, 1)
    }
  }
}

// 模块全选/取消全选
const handleSelectAllModule = (roleId, moduleName, selectAll) => {
  const module = permissionModules.value.find(m => m.module === moduleName)
  if (!module) return
  
  if (!rolePermissions[roleId]) {
    rolePermissions[roleId] = []
  }
  
  const rolePerms = rolePermissions[roleId]
  
  module.permissions.forEach(permission => {
    const index = rolePerms.indexOf(permission.id)
    
    if (selectAll) {
      if (index === -1) {
        rolePerms.push(permission.id)
      }
    } else {
      if (index > -1) {
        rolePerms.splice(index, 1)
      }
    }
  })
}

// 角色切换
const handleRoleChange = (tab) => {
  activeRole.value = tab.name
}

// 保存所有更改
const handleSaveAll = async () => {
  saving.value = true
  
  try {
    // 找出有变更的角色
    const changedRoles = []
    for (const role of roles.value) {
      const currentPermissions = rolePermissions[role.id] || []
      const originalPermissions = originalRolePermissions[role.id] || []
      
      // 比较权限是否有变化
      const hasChanged = currentPermissions.length !== originalPermissions.length ||
        !currentPermissions.every(id => originalPermissions.includes(id))
      
      if (hasChanged) {
        changedRoles.push({
          roleId: role.id,
          roleName: role.name,
          permissionIds: currentPermissions
        })
      }
    }
    
    if (changedRoles.length === 0) {
      ElMessage.info('没有权限变更需要保存')
      return
    }
    
    // 逐个保存角色权限
    for (const roleData of changedRoles) {
      await assignPermissions({
        role_id: roleData.roleId,
        permission_ids: roleData.permissionIds
      })
    }
    
    ElMessage.success(`成功保存 ${changedRoles.length} 个角色的权限配置`)
    
    // 更新原始配置
    for (const role of roles.value) {
      originalRolePermissions[role.id] = [...(rolePermissions[role.id] || [])]
    }
    
    // 刷新日志
    await getPermissionLogs()
    
  } catch (error) {
    ElMessage.error('保存失败：' + error.message)
  } finally {
    saving.value = false
  }
}

// 重置为默认配置
const handleReset = () => {
  ElMessageBox.confirm('确定要重置为默认权限配置吗？这将撤销所有未保存的更改！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 重置为原始配置
    for (const role of roles.value) {
      rolePermissions[role.id] = [...(originalRolePermissions[role.id] || [])]
    }
    ElMessage.success('已重置为默认配置')
  })
}

// 刷新数据
const handleRefresh = () => {
  loadData()
}

// 获取权限变更日志
const getPermissionLogs = async () => {
  logsLoading.value = true
  
  try {
    const response = await getLogsApi(logsQuery)
    console.log('权限日志响应:', response)
    // 检查响应数据结构 - 后端返回code: 0表示成功
    if (response && response.code === 0 && response.data) {
      permissionLogs.value = response.data.list || response.data || []
      logsTotal.value = response.data.total || 0
    }
  } catch (error) {
    console.error('获取权限日志失败:', error)
    // 使用模拟数据作为后备
    permissionLogs.value = []
    logsTotal.value = 0
  } finally {
    logsLoading.value = false
  }
}

// 格式化时间
const formatDateTime = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
}

// 获取数据范围文本
const getDataScopeText = (scope) => {
  const scopeMap = {
    'all': '全部数据',
    'dept': '本部门',
    'self': '仅自己'
  }
  return scopeMap[scope] || '未定义'
}

// 获取数据范围标签类型
const getDataScopeType = (scope) => {
  const typeMap = {
    'all': 'danger',
    'dept': 'warning', 
    'self': 'info'
  }
  return typeMap[scope] || ''
}

// 添加权限编辑对话框
const handleAddPermission = () => {
  permissionDialogVisible.value = true
  permissionDialogTitle.value = '新增权限'
  resetPermissionForm()
}

// 编辑权限
const handleEditPermission = (permission) => {
  permissionDialogVisible.value = true
  permissionDialogTitle.value = '编辑权限'
  permissionForm.value = { ...permission }
}

// 删除权限
const handleDeletePermission = async (permission) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除权限"${permission.name}"吗？删除后将从所有角色中移除该权限！`, 
      '删除权限', 
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await deletePermission(permission.id)
    if (response && response.code === 0) {
      ElMessage.success('权限删除成功')
      await loadData() // 重新加载数据
    } else {
      ElMessage.error(response?.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败：' + error.message)
    }
  }
}

// 保存权限
const handleSavePermission = async () => {
  if (!permissionFormRef.value) return
  
  try {
    await permissionFormRef.value.validate()
  } catch (error) {
    return
  }
  
  permissionSaving.value = true
  
  try {
    const formData = { ...permissionForm.value }
    
    // 如果是功能权限，清空data_scope
    if (formData.type === 'function') {
      formData.data_scope = ''
    }
    
    let response
    if (formData.id) {
      // 更新权限
      response = await updatePermission(formData.id, formData)
    } else {
      // 创建权限
      response = await createPermission(formData)
    }
    
    if (response && response.code === 0) {
      ElMessage.success(formData.id ? '权限更新成功' : '权限创建成功')
      permissionDialogVisible.value = false
      await loadData() // 重新加载数据
    } else {
      ElMessage.error(response?.message || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败：' + error.message)
  } finally {
    permissionSaving.value = false
  }
}

// 重置权限表单
const resetPermissionForm = () => {
  permissionForm.value = {
    name: '',
    code: '',
    module: '',
    type: 'function',
    description: '',
    data_scope: '',
    status: 1
  }
  if (permissionFormRef.value) {
    permissionFormRef.value.clearValidate()
  }
}
</script>

<style lang="scss" scoped>
.permission-management-container {
  padding: 20px;
  
  .header-container {
    margin-bottom: 20px;
    
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
    }
    
    .header-desc {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-actions {
      display: flex;
      gap: 12px;
    }
  }
  
  .role-tabs-container {
    .role-info {
      margin-bottom: 24px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
      
      .role-basic {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
        
        .role-desc {
          color: #606266;
          font-size: 14px;
        }
        
        .permission-count {
          color: #409eff;
          font-size: 12px;
          margin-left: auto;
        }
      }
      
      .permission-legend {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .legend-title {
          font-size: 12px;
          color: #909399;
          font-weight: 500;
        }
        
        .legend-items {
          display: flex;
          gap: 16px;
          
          .legend-item {
            display: flex;
            align-items: center;
            gap: 4px;
            
            span {
              font-size: 11px;
              color: #606266;
            }
            
            .el-tag {
              font-size: 10px;
              height: 18px;
              line-height: 16px;
              padding: 0 4px;
            }
          }
        }
      }
    }
    
    .permissions-container {
      .module-section {
        margin-bottom: 32px;
        
        .module-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          padding-bottom: 8px;
          border-bottom: 1px solid #ebeef5;
          
          .module-title {
            display: flex;
            align-items: center;
            gap: 12px;
            
            h3 {
              margin: 0;
              color: #303133;
              font-size: 16px;
            }
            
            .module-stats {
              display: flex;
              gap: 6px;
              
              .el-tag {
                font-size: 11px;
                height: 20px;
                line-height: 18px;
                padding: 0 6px;
              }
            }
          }
          
          .module-actions {
            display: flex;
            gap: 8px;
          }
        }
        
        .permissions-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
          gap: 16px;
          
          .permission-item {
            padding: 12px;
            border: 1px solid #ebeef5;
            border-radius: 6px;
            transition: all 0.3s;
            position: relative;
            
            &:hover {
              border-color: #c6e2ff;
              background: #fafcff;
              
              .permission-actions {
                opacity: 1;
              }
            }
            
            // 不同权限类型的样式
            &.permission-type-menu {
              border-left: 4px solid #409eff;
              background: linear-gradient(135deg, #f0f7ff 0%, #ffffff 100%);
            }
            
            &.permission-type-action {
              border-left: 4px solid #67c23a;
              background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);
            }
            
            &.permission-type-data {
              border-left: 4px solid #e6a23c;
              background: linear-gradient(135deg, #fdf6ec 0%, #ffffff 100%);
            }
            
            :deep(.el-checkbox) {
              width: 100%;
              
              .el-checkbox__label {
                width: 100%;
                padding-left: 8px;
              }
            }
            
            .permission-info {
              .permission-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 6px;
                
                .permission-name {
                  font-weight: 500;
                  color: #303133;
                  flex: 1;
                  margin-right: 8px;
                }
                
                .permission-tags {
                  display: flex;
                  gap: 4px;
                  flex-shrink: 0;
                  
                  .permission-type-tag {
                    font-size: 10px;
                    height: 18px;
                    line-height: 16px;
                    padding: 0 4px;
                    font-weight: 500;
                  }
                  
                  .permission-scope-tag {
                    font-size: 10px;
                    height: 18px;
                    line-height: 16px;
                    padding: 0 4px;
                  }
                }
                
                .permission-actions {
                  display: flex;
                  gap: 4px;
                  opacity: 0;
                  transition: opacity 0.2s;
                  margin-left: 8px;
                  
                  .el-button {
                    padding: 2px 4px;
                    font-size: 11px;
                    height: auto;
                    min-height: auto;
                  }
                }
              }
              
              .permission-desc {
                font-size: 12px;
                color: #909399;
                margin-bottom: 4px;
                line-height: 1.4;
              }
              
              .permission-code {
                font-size: 11px;
                color: #c0c4cc;
                font-family: 'Courier New', Courier, monospace;
              }
            }
          }
        }
      }
    }
  }
  
  .logs-card {
    margin-top: 24px;
    
    .permission-name {
      font-weight: 500;
      color: #303133;
      margin-bottom: 2px;
    }
    
    .permission-code {
      font-size: 12px;
      color: #909399;
      font-family: 'Courier New', Courier, monospace;
    }
    
    .logs-pagination {
      margin-top: 16px;
      text-align: right;
    }
  }
}

:deep(.el-tabs__header) {
  margin-bottom: 24px;
}

:deep(.el-tabs__item) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.el-checkbox__label) {
  color: #303133;
  font-weight: normal;
}

:deep(.el-table) {
  .el-table__header {
    background: #f5f7fa;
    
    th {
      background: #f5f7fa !important;
      color: #909399;
      font-weight: 500;
    }
  }
  
  .el-table__row {
    &:hover {
      background: #f5f7fa;
    }
  }
}
</style> 