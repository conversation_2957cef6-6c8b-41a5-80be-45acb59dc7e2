<template>
  <div class="plugin-dashboard">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button 
          link 
          @click="goBack"
          class="back-btn"
        >
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <div class="header-info">
          <h1 class="page-title">{{ mediaName }} - 插件数据看板</h1>
          <p class="page-desc">查看插件接入的资源位、创意信息及数据表现</p>
        </div>
      </div>
      <div class="header-right">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateChange"
          class="date-picker"
        />
        <el-button 
          type="primary" 
          @click="refreshData"
          :loading="loading"
        >
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>


    <!-- 资源位信息 -->
    <el-card class="section-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <el-icon class="header-icon"><Grid /></el-icon>
            <span>接入插件的资源位</span>
          </div>
          <el-button type="primary" size="small" @click="handleAddSlot">
            <el-icon><Plus /></el-icon>
            添加资源位
          </el-button>
        </div>
      </template>
      
      <el-table 
        :data="slotList" 
        v-loading="loading"
        stripe
        class="data-table"
      >
        <el-table-column prop="slotId" label="资源位ID" width="120" />
        <el-table-column prop="slotName" label="资源位名称" min-width="200" />
        <el-table-column prop="slotType" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getSlotTypeColor(row.slotType)">
              {{ getSlotTypeText(row.slotType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="position" label="位置" width="150" />
        <el-table-column prop="size" label="尺寸" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="160" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              link 
              @click="handleEditSlot(row)"
            >
              编辑
            </el-button>
            <el-button 
              type="success" 
              size="small" 
              link 
              @click="handleViewSlotData(row)"
            >
              数据
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创意管理 -->
    <el-card class="section-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <el-icon class="header-icon"><PictureFilled /></el-icon>
            <span>创意管理</span>
          </div>
          <el-button type="primary" size="small" @click="handleAddCreative">
            <el-icon><Plus /></el-icon>
            添加创意
          </el-button>
        </div>
      </template>
      
      <el-table 
        :data="creativeList" 
        v-loading="loading"
        stripe
        class="data-table"
      >
        <el-table-column prop="creativeId" label="创意ID" width="120" />
        <el-table-column prop="creativeName" label="创意名称" min-width="200" />
        <el-table-column prop="creativeType" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getCreativeTypeColor(row.creativeType)">
              {{ getCreativeTypeText(row.creativeType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="slotName" label="所属资源位" width="150" />
        <el-table-column label="预览" width="100">
          <template #default="{ row }">
            <el-image
              v-if="row.previewUrl"
              :src="row.previewUrl"
              fit="cover"
              style="width: 60px; height: 40px; border-radius: 4px;"
              preview-teleported
              :preview-src-list="[row.previewUrl]"
            />
            <span v-else class="text-gray">无预览</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '投放中' : '已暂停' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              link 
              @click="handleEditCreative(row)"
            >
              编辑
            </el-button>
            <el-button 
              type="success" 
              size="small" 
              link 
              @click="handleViewCreativeData(row)"
            >
              查看数据
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 数据分析 -->
    <el-card class="section-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <div class="header-title">
            <el-icon class="header-icon"><TrendCharts /></el-icon>
            <span>数据分析</span>
          </div>
          <div class="header-actions">
            <div class="filter-controls">
              <el-select 
                v-model="selectedSlotId" 
                placeholder="全部资源位" 
                clearable
                @change="handleSlotFilter"
                style="width: 180px;"
                size="small"
              >
                <el-option label="全部资源位" value="" />
                <el-option 
                  v-for="slot in slotList" 
                  :key="slot.id" 
                  :label="slot.slotName" 
                  :value="slot.id" 
                />
              </el-select>
              
              <el-select 
                v-model="selectedCreativeId" 
                placeholder="全部创意" 
                clearable
                @change="handleCreativeFilter"
                style="width: 180px;"
                size="small"
              >
                <el-option label="全部创意" value="" />
                <el-option 
                  v-for="creative in creativeList" 
                  :key="creative.id" 
                  :label="creative.creativeName" 
                  :value="creative.id" 
                />
              </el-select>
              
              <el-select 
                v-model="selectedPlan" 
                placeholder="全部广告计划" 
                clearable
                @change="handlePlanFilter"
                style="width: 200px;"
                size="small"
              >
                <el-option label="全部广告计划" value="" />
                <el-option 
                  v-for="plan in planList" 
                  :key="plan.value" 
                  :label="plan.label" 
                  :value="plan.value" 
                />
              </el-select>
            </div>
            
            <el-button type="success" size="small" @click="exportData">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table 
        :data="dailyDataList" 
        v-loading="loading"
        stripe
        class="data-table"
        show-summary
        :summary-method="getDailySummaries"
        fit
        table-layout="auto"
        row-key="id"
      >
        <el-table-column type="expand" width="50">
          <template #default="{ row }">
            <div class="expand-content">
              <div class="expand-title">{{ row.date }} - {{ row.advertiser }} 的计划明细</div>
              <el-table 
                :data="row.plans" 
                size="small"
                stripe
                class="inner-table"
              >
                <el-table-column prop="planName" label="计划名称" min-width="160" />
                <el-table-column prop="exposurePV" label="曝光PV" min-width="90" align="right">
                  <template #default="{ row: plan }">
                    {{ formatNumber(plan.exposurePV) }}
                  </template>
                </el-table-column>
                <el-table-column prop="exposureUV" label="曝光UV" min-width="90" align="right">
                  <template #default="{ row: plan }">
                    {{ formatNumber(plan.exposureUV) }}
                  </template>
                </el-table-column>
                <el-table-column prop="clickPV" label="点击PV" min-width="90" align="right">
                  <template #default="{ row: plan }">
                    {{ formatNumber(plan.clickPV) }}
                  </template>
                </el-table-column>
                <el-table-column prop="clickUV" label="点击UV" min-width="90" align="right">
                  <template #default="{ row: plan }">
                    {{ formatNumber(plan.clickUV) }}
                  </template>
                </el-table-column>
                <el-table-column prop="clickRate" label="点击率" min-width="80" align="right">
                  <template #default="{ row: plan }">
                    {{ plan.exposurePV ? ((plan.clickPV / plan.exposurePV) * 100).toFixed(2) : '0.00' }}%
                  </template>
                </el-table-column>
                <el-table-column prop="conversionPV" label="转化PV" min-width="90" align="right">
                  <template #default="{ row: plan }">
                    {{ formatNumber(plan.conversionPV) }}
                  </template>
                </el-table-column>
                <el-table-column prop="conversionUV" label="转化UV" min-width="90" align="right">
                  <template #default="{ row: plan }">
                    {{ formatNumber(plan.conversionUV) }}
                  </template>
                </el-table-column>
                <el-table-column prop="conversionRate" label="转化率" min-width="80" align="right">
                  <template #default="{ row: plan }">
                    {{ plan.clickPV ? ((plan.conversionPV / plan.clickPV) * 100).toFixed(2) : '0.00' }}%
                  </template>
                </el-table-column>
                <el-table-column prop="orderPV" label="订单PV" min-width="90" align="right">
                  <template #default="{ row: plan }">
                    {{ formatNumber(plan.orderPV) }}
                  </template>
                </el-table-column>
                <el-table-column prop="orderUV" label="订单UV" min-width="90" align="right">
                  <template #default="{ row: plan }">
                    {{ formatNumber(plan.orderUV) }}
                  </template>
                </el-table-column>
                <el-table-column prop="revenue" label="收益(¥)" min-width="100" align="right">
                  <template #default="{ row: plan }">
                    {{ formatNumber(plan.revenue) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="日期" min-width="100" fixed="left" />
        <el-table-column prop="advertiser" label="广告主" min-width="120">
          <template #default="{ row }">
            <div class="advertiser-info">
              <el-icon class="advertiser-icon"><User /></el-icon>
              <span class="advertiser-name">{{ row.advertiser }}</span>
              <el-tag size="small" type="info">{{ row.planCount }}个计划</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="exposurePV" label="曝光PV" min-width="90" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.exposurePV) }}
          </template>
        </el-table-column>
        <el-table-column prop="exposureUV" label="曝光UV" min-width="90" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.exposureUV) }}
          </template>
        </el-table-column>
        <el-table-column prop="clickPV" label="点击PV" min-width="90" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.clickPV) }}
          </template>
        </el-table-column>
        <el-table-column prop="clickUV" label="点击UV" min-width="90" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.clickUV) }}
          </template>
        </el-table-column>
        <el-table-column prop="clickRate" label="点击率" min-width="80" align="right">
          <template #default="{ row }">
            {{ row.exposurePV ? ((row.clickPV / row.exposurePV) * 100).toFixed(2) : '0.00' }}%
          </template>
        </el-table-column>
        <el-table-column prop="conversionPV" label="转化PV" min-width="90" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.conversionPV) }}
          </template>
        </el-table-column>
        <el-table-column prop="conversionUV" label="转化UV" min-width="90" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.conversionUV) }}
          </template>
        </el-table-column>
        <el-table-column prop="conversionRate" label="转化率" min-width="80" align="right">
          <template #default="{ row }">
            {{ row.clickPV ? ((row.conversionPV / row.clickPV) * 100).toFixed(2) : '0.00' }}%
          </template>
        </el-table-column>
        <el-table-column prop="orderPV" label="订单PV" min-width="90" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.orderPV) }}
          </template>
        </el-table-column>
        <el-table-column prop="orderUV" label="订单UV" min-width="90" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.orderUV) }}
          </template>
        </el-table-column>
        <el-table-column prop="revenue" label="收益(¥)" min-width="100" align="right">
          <template #default="{ row }">
            {{ formatNumber(row.revenue) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  Refresh,
  TrendCharts,
  Plus,
  Download,
  Grid,
  PictureFilled,
  User
} from '@element-plus/icons-vue'

// 路由相关
const route = useRoute()
const router = useRouter()

// 页面参数
const mediaId = route.params.mediaId
const mediaName = ref(route.query.mediaName || '')

// 日期范围
const dateRange = ref([
  new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7天前
  new Date() // 今天
])

// 分日数据
const dailyDataList = ref([])
const originalDailyDataList = ref([]) // 保存原始数据用于筛选

// 资源位列表
const slotList = ref([])

// 创意列表
const creativeList = ref([])

// 筛选条件
const selectedSlotId = ref('')
const selectedCreativeId = ref('')
const selectedPlan = ref('')

// 加载状态
const loading = ref(false)

// 计划列表（用于筛选）
const planList = ref([])

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 刷新数据
const refreshData = () => {
  loadData()
}

// 日期范围变化
const handleDateChange = () => {
  loadData()
}

// 筛选处理函数
const handleSlotFilter = () => {
  applyFilters()
}

const handleCreativeFilter = () => {
  applyFilters()
}

const handlePlanFilter = () => {
  applyFilters()
}

// 应用筛选条件
const applyFilters = () => {
  let filteredData = [...originalDailyDataList.value]
  
  // 按广告计划筛选
  if (selectedPlan.value) {
    filteredData = filteredData.filter(item => {
      // 筛选包含该计划的数据行
      return item.plans && item.plans.some(plan => plan.planName === selectedPlan.value)
    })
  }
  
  // 按资源位筛选（这里需要根据实际业务逻辑，暂时模拟）
  if (selectedSlotId.value) {
    // 实际应该根据资源位ID筛选相关的数据
    // 这里简单模拟：如果选择了资源位，则只显示部分数据
    filteredData = filteredData.filter((item, index) => index % 2 === 0)
  }
  
  // 按创意筛选（这里需要根据实际业务逻辑，暂时模拟）
  if (selectedCreativeId.value) {
    // 实际应该根据创意ID筛选相关的数据
    // 这里简单模拟：如果选择了创意，则只显示部分数据
    filteredData = filteredData.filter((item, index) => index % 3 === 0)
  }
  
  dailyDataList.value = filteredData
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadSlotData(),
      loadCreativeData(),
      loadDailyData()
    ])
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}







// 加载资源位数据
const loadSlotData = async () => {
  // 模拟数据 - 实际应该调用API
  slotList.value = [
    {
      id: 1,
      slotId: 'P001',
      slotName: '首页弹窗广告位',
      slotType: 'popup',
      position: '首页中心',
      size: '300x250',
      status: 'active',
      createdAt: '2024-01-15 10:30:00'
    },
    {
      id: 2,
      slotId: 'P002',
      slotName: '文章底部横幅',
      slotType: 'banner',
      position: '文章底部',
      size: '728x90',
      status: 'active',
      createdAt: '2024-01-16 14:20:00'
    }
  ]
}

// 加载创意数据
const loadCreativeData = async () => {
  // 模拟数据 - 实际应该调用API
  creativeList.value = [
    {
      id: 1,
      creativeId: 'C001',
      creativeName: '春节购物节活动创意',
      creativeType: 'image',
      slotName: '首页弹窗广告位',
      previewUrl: 'https://via.placeholder.com/300x250?text=Creative+1',
      status: 'active',
      createdAt: '2024-01-15 11:00:00'
    },
    {
      id: 2,
      creativeId: 'C002',
      creativeName: '新年优惠券推广',
      creativeType: 'video',
      slotName: '文章底部横幅',
      previewUrl: 'https://via.placeholder.com/728x90?text=Creative+2',
      status: 'active',
      createdAt: '2024-01-16 15:00:00'
    }
  ]
}

// 加载分日数据
const loadDailyData = async () => {
  // 模拟数据 - 实际应该调用API获取按日期-广告主维度的数据
  const data = [
    {
      id: '20240604_eleme',
      date: '2024-06-04',
      advertiser: '饿了么',
      planCount: 2,
      exposurePV: 44192,
      exposureUV: 40357,
      clickPV: 8571,
      clickUV: 7946,
      conversionPV: 1568,
      conversionUV: 1446,
      orderPV: 2794,
      orderUV: 2522,
      revenue: 1201.1,
      plans: [
        {
          planName: '饿了么-外卖推广计划',
          exposurePV: 25230,
          exposureUV: 23234,
          clickPV: 4650,
          clickUV: 4292,
          conversionPV: 890,
          conversionUV: 823,
          orderPV: 1560,
          orderUV: 1423,
          revenue: 680.30
        },
        {
          planName: '饿了么-新用户拉新计划',
          exposurePV: 18962,
          exposureUV: 17123,
          clickPV: 3921,
          clickUV: 3654,
          conversionPV: 678,
          conversionUV: 623,
          orderPV: 1234,
          orderUV: 1099,
          revenue: 520.80
        }
      ]
    },
    {
      id: '20240604_meituan',
      date: '2024-06-04',
      advertiser: '美团',
      planCount: 1,
      exposurePV: 23420,
      exposureUV: 21543,
      clickPV: 4520,
      clickUV: 4123,
      conversionPV: 890,
      conversionUV: 823,
      orderPV: 1560,
      orderUV: 1434,
      revenue: 680.30,
      plans: [
        {
          planName: '美团-生鲜配送计划',
          exposurePV: 23420,
          exposureUV: 21543,
          clickPV: 4520,
          clickUV: 4123,
          conversionPV: 890,
          conversionUV: 823,
          orderPV: 1560,
          orderUV: 1434,
          revenue: 680.30
        }
      ]
    },
    {
      id: '20240604_jd',
      date: '2024-06-04',
      advertiser: '京东',
      planCount: 1,
      exposurePV: 15432,
      exposureUV: 14321,
      clickPV: 2890,
      clickUV: 2654,
      conversionPV: 456,
      conversionUV: 423,
      orderPV: 890,
      orderUV: 823,
      revenue: 380.20,
      plans: [
        {
          planName: '京东-3C数码推广',
          exposurePV: 15432,
          exposureUV: 14321,
          clickPV: 2890,
          clickUV: 2654,
          conversionPV: 456,
          conversionUV: 423,
          orderPV: 890,
          orderUV: 823,
          revenue: 380.20
        }
      ]
    },
    {
      id: '20240605_eleme',
      date: '2024-06-05',
      advertiser: '饿了么',
      planCount: 2,
      exposurePV: 38962,
      exposureUV: 35677,
      clickPV: 7842,
      clickUV: 7123,
      conversionPV: 1234,
      conversionUV: 1089,
      orderPV: 2567,
      orderUV: 2234,
      revenue: 1056.8,
      plans: [
        {
          planName: '饿了么-外卖推广计划',
          exposurePV: 22450,
          exposureUV: 20123,
          clickPV: 4321,
          clickUV: 3987,
          conversionPV: 723,
          conversionUV: 656,
          orderPV: 1456,
          orderUV: 1287,
          revenue: 612.40
        },
        {
          planName: '饿了么-新用户拉新计划',
          exposurePV: 16512,
          exposureUV: 15554,
          clickPV: 3521,
          clickUV: 3136,
          conversionPV: 511,
          conversionUV: 433,
          orderPV: 1111,
          orderUV: 947,
          revenue: 444.40
        }
      ]
    },
    {
      id: '20240605_meituan',
      date: '2024-06-05',
      advertiser: '美团',
      planCount: 1,
      exposurePV: 19876,
      exposureUV: 18234,
      clickPV: 3987,
      clickUV: 3654,
      conversionPV: 723,
      conversionUV: 656,
      orderPV: 1234,
      orderUV: 1089,
      revenue: 567.80,
      plans: [
        {
          planName: '美团-生鲜配送计划',
          exposurePV: 19876,
          exposureUV: 18234,
          clickPV: 3987,
          clickUV: 3654,
          conversionPV: 723,
          conversionUV: 656,
          orderPV: 1234,
          orderUV: 1089,
          revenue: 567.80
        }
      ]
    },
    {
      id: '20240606_jd',
      date: '2024-06-06',
      advertiser: '京东',
      planCount: 1,
      exposurePV: 18432,
      exposureUV: 16789,
      clickPV: 3456,
      clickUV: 3123,
      conversionPV: 612,
      conversionUV: 567,
      orderPV: 1123,
      orderUV: 989,
      revenue: 456.90,
      plans: [
        {
          planName: '京东-3C数码推广',
          exposurePV: 18432,
          exposureUV: 16789,
          clickPV: 3456,
          clickUV: 3123,
          conversionPV: 612,
          conversionUV: 567,
          orderPV: 1123,
          orderUV: 989,
          revenue: 456.90
        }
      ]
    }
  ]
  
  // 保存原始数据
  originalDailyDataList.value = data
  dailyDataList.value = [...data]
  
  // 生成计划列表（用于筛选下拉框）
  const allPlans = new Set()
  data.forEach(item => {
    if (item.plans) {
      item.plans.forEach(plan => {
        allPlans.add(plan.planName)
      })
    }
  })
  
  planList.value = Array.from(allPlans).map(planName => ({
    label: planName,
    value: planName
  }))
  
  applyFilters()
}

// 格式化数字
const formatNumber = (num) => {
  if (!num) return '0'
  return new Intl.NumberFormat('zh-CN').format(num)
}

// 获取资源位类型颜色
const getSlotTypeColor = (type) => {
  const colorMap = {
    popup: 'success',
    banner: 'primary',
    video: 'warning',
    native: 'info'
  }
  return colorMap[type] || 'default'
}

// 获取资源位类型文本
const getSlotTypeText = (type) => {
  const textMap = {
    popup: '弹窗',
    banner: '横幅',
    video: '视频',
    native: '原生'
  }
  return textMap[type] || type
}

// 获取创意类型颜色
const getCreativeTypeColor = (type) => {
  const colorMap = {
    image: 'primary',
    video: 'success',
    html: 'warning'
  }
  return colorMap[type] || 'default'
}

// 获取创意类型文本
const getCreativeTypeText = (type) => {
  const textMap = {
    image: '图片',
    video: '视频',
    html: 'HTML'
  }
  return textMap[type] || type
}

// 分日数据汇总行计算
const getDailySummaries = (param) => {
  const { columns, data } = param
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = ''
      return
    }
    if (index === 1) {
      sums[index] = '汇总'
      return
    }
    if (index === 2) {
      sums[index] = '-'
      return
    }
    
    // 数值类型字段求和
    const numFields = ['exposurePV', 'exposureUV', 'clickPV', 'clickUV', 'conversionPV', 'conversionUV', 'orderPV', 'orderUV', 'revenue']
    if (numFields.includes(column.property)) {
      const values = data.map(item => Number(item[column.property]))
      const sum = values.reduce((prev, curr) => {
        const value = Number(curr)
        if (!isNaN(value)) {
          return prev + value
        } else {
          return prev
        }
      }, 0)
      sums[index] = formatNumber(sum)
    } else {
      sums[index] = '-'
    }
  })
  return sums
}

// 汇总计算
const getSummaries = (param) => {
  const { columns, data } = param
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '汇总'
      return
    }
    if (index === 1) {
      sums[index] = '-'
      return
    }
    
    // 数值类型字段求和
    const numFields = ['exposurePV', 'exposureUV', 'clickPV', 'clickUV', 'clickConfirmPV', 'clickConfirmUV', 'orderPV', 'orderUV']
    if (numFields.includes(column.property)) {
      const values = data.map(item => Number(item[column.property]))
      const sum = values.reduce((prev, curr) => {
        const value = Number(curr)
        if (!isNaN(value)) {
          return prev + value
        } else {
          return prev
        }
      }, 0)
      sums[index] = formatNumber(sum)
    } else {
      sums[index] = '-'
    }
  })
  return sums
}

// 事件处理函数
const handleAddSlot = () => {
  ElMessage.info('添加资源位功能开发中...')
}

const handleEditSlot = (slot) => {
  ElMessage.info(`编辑资源位: ${slot.slotName}`)
}

const handleViewSlotData = (slot) => {
  ElMessage.info(`查看资源位数据: ${slot.slotName}`)
}

const handleAddCreative = () => {
  ElMessage.info('添加创意功能开发中...')
}

const handleEditCreative = (creative) => {
  ElMessage.info(`编辑创意: ${creative.creativeName}`)
}

const handleViewCreativeData = (creative) => {
  ElMessage.info(`查看创意数据: ${creative.creativeName}`)
}

const exportData = () => {
  ElMessage.success('导出功能开发中...')
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.plugin-dashboard {
  padding: 24px;
  background: #f8fafc;
  min-height: calc(100vh - 50px);

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;

    .header-left {
      display: flex;
      align-items: flex-start;
      gap: 16px;

      .back-btn {
        color: #64748b;
        font-size: 16px;
        margin-top: 4px;
        padding: 6px;
        border-radius: 6px;
        transition: all 0.2s ease;

        &:hover {
          color: #3b82f6;
          background: #f1f5f9;
        }
      }

      .header-info {
        .page-title {
          font-size: 24px;
          font-weight: 600;
          color: #1e293b;
          margin: 0 0 8px 0;
        }

        .page-desc {
          font-size: 14px;
          color: #64748b;
          margin: 0;
          line-height: 1.5;
        }
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 16px;

      .date-picker {
        min-width: 300px;
      }
    }
  }

  .section-card {
    margin-bottom: 24px;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    :deep(.el-card__header) {
      background: #f8fafc;
      border-bottom: 1px solid #e2e8f0;
      padding: 20px 24px;
    }

    :deep(.el-card__body) {
      padding: 0;
      width: 100%;
      overflow-x: auto;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #1e293b;

      .header-title {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 16px;

        .header-icon {
          font-size: 18px;
          color: #3b82f6;
          background: #eff6ff;
          padding: 6px;
          border-radius: 6px;
        }
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 12px;

        .filter-controls {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .el-select {
            :deep(.el-input__wrapper) {
              border-radius: 6px;
              border: 1px solid #e2e8f0;
              box-shadow: none;
              
              &:hover {
                border-color: #3b82f6;
              }
              
              &.is-focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
              }
            }
          }
        }
      }
    }

    .data-table {
      margin: 0;
      width: 100%;

      :deep(.el-table) {
        width: 100%;
        
        .el-table__header-wrapper {
          width: 100%;
          
          th {
            background: #f8fafc;
            color: #374151;
            font-weight: 600;
            font-size: 14px;
            padding: 12px 8px;
            border-bottom: 1px solid #e2e8f0;
            text-align: center;
          }
        }

        .el-table__body-wrapper {
          width: 100%;
          
          tbody {
            tr {
              &:hover {
                background: #f8fafc;
              }

              td {
                padding: 12px 8px;
                border-bottom: 1px solid #f1f5f9;
                font-size: 14px;
                
                &:first-child {
                  font-weight: 500;
                  color: #1e293b;
                }
              }
            }
          }
        }

        .el-table__footer-wrapper {
          width: 100%;
          
          .el-table__footer {
            background: #f1f5f9;
            width: 100%;
            
            tr {
              td {
                color: #1e293b;
                font-weight: 600;
                font-size: 14px;
                padding: 12px 8px;
                border: none;
                
                &:first-child {
                  background: #f1f5f9;
                  color: #059669;
                }
              }
            }
          }
        }
      }

      // 广告主信息样式
      .advertiser-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .advertiser-icon {
          color: #3b82f6;
          font-size: 16px;
        }

        .advertiser-name {
          font-weight: 600;
          color: #1e293b;
        }
      }

      // 展开内容样式
      .expand-content {
        padding: 16px 24px;
        background: #fafbfc;

        .expand-title {
          font-size: 16px;
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 16px;
          padding-bottom: 8px;
          border-bottom: 2px solid #e2e8f0;
        }

        .inner-table {
          :deep(.el-table) {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

            .el-table__header-wrapper {
              th {
                background: #f8fafc;
                font-weight: 600;
                font-size: 13px;
                color: #475569;
                border-bottom: 1px solid #e2e8f0;
              }
            }

            .el-table__body-wrapper {
              tbody {
                tr {
                  &:hover {
                    background: #f8fafc;
                  }

                  td {
                    font-size: 13px;
                    padding: 10px 8px;
                    
                    &:first-child {
                      font-weight: 500;
                      color: #1e293b;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    .data-filter {
      margin: 16px 24px 0 24px;
      padding: 16px;
      background: #f8fafc;
      border-radius: 8px;
      display: flex;
      align-items: center;
      gap: 12px;
      border: 1px solid #e2e8f0;

      &::before {
        content: "筛选：";
        color: #64748b;
        font-weight: 500;
        font-size: 14px;
      }
    }
  }

  .text-gray {
    color: #9ca3af;
    font-size: 12px;
  }
}

@media screen and (max-width: 768px) {
  .plugin-dashboard {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 20px;
      align-items: stretch;
      padding: 24px;

      .header-left {
        .header-info {
          .page-title {
            font-size: 24px;
          }
          
          .page-desc {
            font-size: 14px;
          }
        }
      }

      .header-right {
        justify-content: flex-start;

        .date-picker {
          min-width: auto;
          width: 100%;
        }
      }
    }

    .section-card {
      margin-bottom: 20px;

      :deep(.el-card__header) {
        padding: 16px 20px;
      }

      .card-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;

        .header-title {
          font-size: 15px;
        }

        .header-actions {
          width: 100%;
          flex-direction: column;
          gap: 12px;
          align-items: stretch;

          .filter-controls {
            width: 100%;
            flex-direction: column;
            gap: 8px;

            .el-select {
              width: 100% !important;
            }
          }
        }
      }

      .data-filter {
        margin: 12px 16px 0 16px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        padding: 12px 16px;

        &::before {
          content: "筛选";
        }

        .el-select {
          width: 100% !important;
        }
      }

      .data-table {
        :deep(.el-table) {
          .el-table__header-wrapper {
            th {
              padding: 10px 8px;
              font-size: 13px;
            }
          }

          .el-table__body-wrapper {
            tbody tr td {
              padding: 10px 8px;
              font-size: 13px;
            }
          }
        }

        .expand-content {
          padding: 12px 16px;

          .expand-title {
            font-size: 14px;
            margin-bottom: 12px;
          }

          .inner-table {
            :deep(.el-table) {
              .el-table__header-wrapper {
                th {
                  padding: 8px 6px;
                  font-size: 12px;
                }
              }

              .el-table__body-wrapper {
                tbody tr td {
                  padding: 8px 6px;
                  font-size: 12px;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style> 