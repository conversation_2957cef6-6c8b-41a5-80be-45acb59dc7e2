<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card class="filter-container" shadow="never">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="代理名称">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入代理名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="代理类型">
          <el-select 
            v-model="queryParams.type" 
            placeholder="请选择类型" 
            clearable
            style="min-width: 200px"
          >
            <el-option
              v-for="item in agentTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select 
            v-model="queryParams.audit_status" 
            placeholder="请选择状态" 
            clearable
            style="min-width: 200px"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属媒介" v-if="!userStore.isMedia">
          <el-select 
            v-model="queryParams.media_id" 
            placeholder="请选择媒介" 
            clearable
            style="min-width: 200px"
          >
            <el-option
              v-for="item in mediaUsers"
              :key="item.id"
              :label="item.realName || item.username"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 代理分类展示区域 -->
    <el-card class="agent-tabs-container" shadow="never">
      <el-tabs 
        v-model="activeTab" 
        type="card" 
        class="agent-tabs"
        @tab-change="handleTabChange"
      >
        <el-tab-pane label="投放代理" name="delivery">
          <template #label>
            <span class="tab-label">
              <el-icon><Promotion /></el-icon>
              投放代理
              <el-badge 
                v-if="tabCounts.delivery > 0" 
                :value="tabCounts.delivery" 
                class="tab-badge"
              />
            </span>
          </template>
          
          <!-- 投放代理操作区 -->
          <div class="tab-operate-container">
            <PermissionWrapper permission="agent:create">
              <el-button 
                type="primary" 
                @click="handleAdd('delivery')"
                :icon="Plus"
              >
                新增投放代理
              </el-button>
            </PermissionWrapper>
            <div class="tab-info">
              <span class="info-text">
                <el-icon><InfoFilled /></el-icon>
                投放代理用于广告投放平台对接，支持灯火等投放平台
              </span>
            </div>
          </div>

          <!-- 投放代理表格 -->
          <div class="tab-table-container">
            <el-table
              v-loading="loading"
              :data="deliveryTableData"
              style="width: 100%"
              border
              class="delivery-table"
            >
              <el-table-column prop="code" label="代理编号" min-width="120" />
              <el-table-column prop="name" label="代理名称" min-width="140" />
              <el-table-column label="投放平台" width="120">
                <template #default="scope">
                  <el-tag 
                    color="#e6f7ff" 
                    style="color: #1890ff"
                  >
                    {{ getPlatformText(scope.row.platform_config?.platform) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="audit_status" label="审核状态" width="100">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.audit_status)">
                    {{ getStatusText(scope.row.audit_status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="company_name" label="公司名称" min-width="120" show-overflow-tooltip />
              <el-table-column prop="contact_name" label="联系人" width="100" />
              <el-table-column prop="contact_phone" label="联系电话" width="120" />
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="scope">
                  <PermissionWrapper permission="agent:audit">
                    <el-button
                      v-if="scope.row.audit_status === 'pending'"
                      type="success"
                      size="small"
                      @click="handleAudit(scope.row)"
                    >
                      审核
                    </el-button>
                  </PermissionWrapper>
                  <PermissionWrapper permission="agent:edit">
                    <el-button
                      type="primary"
                      size="small"
                      @click="handleUpdate(scope.row)"
                    >
                      编辑
                    </el-button>
                  </PermissionWrapper>
                  <PermissionWrapper permission="agent:delete">
                    <el-button
                      type="danger"
                      size="small"
                      @click="handleDelete(scope.row)"
                    >
                      删除
                    </el-button>
                  </PermissionWrapper>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <el-tab-pane label="流量采买代理" name="traffic">
          <template #label>
            <span class="tab-label">
              <el-icon><TrendCharts /></el-icon>
              流量采买代理
              <el-badge 
                v-if="tabCounts.traffic > 0" 
                :value="tabCounts.traffic" 
                class="tab-badge"
              />
            </span>
          </template>
          
          <!-- 流量采买代理操作区 -->
          <div class="tab-operate-container">
            <PermissionWrapper permission="agent:create">
              <el-button 
                type="primary" 
                @click="handleAdd('traffic')"
                :icon="Plus"
              >
                新增流量采买代理
              </el-button>
            </PermissionWrapper>
            <div class="tab-info">
              <span class="info-text">
                <el-icon><InfoFilled /></el-icon>
                流量采买代理用于流量渠道管理，对接各类流量供应商
              </span>
            </div>
          </div>

          <!-- 流量采买代理表格 -->
          <div class="tab-table-container">
            <el-table
              v-loading="loading"
              :data="trafficTableData"
              style="width: 100%"
              border
              class="traffic-table"
            >
              <el-table-column prop="code" label="代理编号" min-width="120" />
              <el-table-column prop="name" label="代理名称" min-width="140" />
              <el-table-column label="代理类型" width="120">
                <template #default="scope">
                  <el-tag 
                    color="#f6ffed" 
                    style="color: #52c41a"
                  >
                    {{ getTypeText(scope.row.type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="audit_status" label="审核状态" width="100">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.audit_status)">
                    {{ getStatusText(scope.row.audit_status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="company_name" label="公司名称" min-width="120" show-overflow-tooltip />
              <el-table-column prop="contact_name" label="联系人" width="100" />
              <el-table-column prop="contact_phone" label="联系电话" width="120" />
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="scope">
                  <PermissionWrapper permission="agent:audit">
                    <el-button
                      v-if="scope.row.audit_status === 'pending'"
                      type="success"
                      size="small"
                      @click="handleAudit(scope.row)"
                    >
                      审核
                    </el-button>
                  </PermissionWrapper>
                  <PermissionWrapper permission="agent:edit">
                    <el-button
                      type="primary"
                      size="small"
                      @click="handleUpdate(scope.row)"
                    >
                      编辑
                    </el-button>
                  </PermissionWrapper>
                  <PermissionWrapper permission="agent:delete">
                    <el-button
                      type="danger"
                      size="small"
                      @click="handleDelete(scope.row)"
                    >
                      删除
                    </el-button>
                  </PermissionWrapper>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.page_size"
          :page-sizes="[10, 20, 30, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="800px"
      @close="handleDialogClose"
      class="agent-dialog"
    >
      <div class="dialog-header">
        <div class="dialog-type-indicator">
          <el-icon v-if="dialogForm.type === 'delivery'" class="type-icon delivery-icon">
            <Promotion />
          </el-icon>
          <el-icon v-else class="type-icon traffic-icon">
            <TrendCharts />
          </el-icon>
          <span class="type-text">
            {{ dialogForm.type === 'delivery' ? '投放代理配置' : '流量采买代理配置' }}
          </span>
        </div>
      </div>

      <el-form
        ref="dialogFormRef"
        :model="dialogForm"
        :rules="dialogRules"
        label-width="120px"
        class="agent-form"
      >
        <!-- 基础信息 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon><User /></el-icon>
            <span>基础信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="代理名称" prop="name">
                <el-input 
                  v-model="dialogForm.name" 
                  placeholder="请输入代理名称"
                  :prefix-icon="EditPen"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="代理类型" prop="type">
                <el-select 
                  v-model="dialogForm.type" 
                  placeholder="请选择类型"
                  style="width: 100%"
                  @change="handleTypeChange"
                  :disabled="!!dialogForm.id"
                >
                  <el-option
                    v-for="item in agentTypes"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                    <div class="option-item">
                      <el-icon v-if="item.value === 'delivery'"><Promotion /></el-icon>
                      <el-icon v-else><TrendCharts /></el-icon>
                      <span>{{ item.label }}</span>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 平台配置 - 仅投放代理显示 -->
        <div class="form-section" v-if="dialogForm.type === 'delivery'">
          <div class="section-title">
            <el-icon><Setting /></el-icon>
            <span>平台配置</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="投放平台" prop="platform_config.platform">
                <el-select 
                  v-model="dialogForm.platform_config.platform" 
                  placeholder="请选择投放平台"
                  style="width: 100%"
                  @change="handlePlatformChange"
                >
                  <el-option
                    label="灯火广告平台"
                    value="denghuoplus"
                  >
                    <div class="platform-option">
                      <div class="platform-info">
                        <span class="platform-name">灯火广告平台</span>
                        <span class="platform-desc">专业的广告投放管理平台</span>
                      </div>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <!-- 灯火平台配置 -->
          <template v-if="dialogForm.platform_config.platform === 'denghuoplus'">
            <div class="platform-config-section">
              <div class="config-title">
                <el-tag type="info" size="small">灯火平台</el-tag>
                <span>API配置信息</span>
              </div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="API Token" prop="platform_config.info.token">
                    <el-input 
                      v-model="dialogForm.platform_config.info.token" 
                      placeholder="请输入API Token"
                      type="password"
                      show-password
                      :prefix-icon="Key"
                    />
                    <div class="field-tip">用于平台API认证的Token</div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="项目PID" prop="platform_config.info.pid">
                    <el-input 
                      v-model="dialogForm.platform_config.info.pid" 
                      placeholder="请输入项目PID"
                      :prefix-icon="DocumentCopy"
                    />
                    <div class="field-tip">投放项目的唯一标识</div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </template>
        </div>

        <!-- 业务配置 - 仅流量采买显示 -->
        <div class="form-section" v-if="dialogForm.type === 'traffic'">
          <div class="section-title">
            <el-icon><DataLine /></el-icon>
            <span>业务配置</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="流量类型" prop="traffic_type">
                <el-select 
                  v-model="dialogForm.traffic_type" 
                  placeholder="请选择流量类型"
                  style="width: 100%"
                  multiple
                >
                  <el-option label="搜索流量" value="search" />
                  <el-option label="信息流" value="feed" />
                  <el-option label="视频流量" value="video" />
                  <el-option label="社交流量" value="social" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结算方式" prop="settlement_type">
                <el-select 
                  v-model="dialogForm.settlement_type" 
                  placeholder="请选择结算方式"
                  style="width: 100%"
                >
                  <el-option label="CPC (按点击)" value="cpc" />
                  <el-option label="CPM (按展示)" value="cpm" />
                  <el-option label="CPA (按转化)" value="cpa" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 扩展信息切换 -->
        <div class="form-section">
          <div class="expand-toggle" @click="showExtendedInfo = !showExtendedInfo">
            <el-icon class="toggle-icon" :class="{ 'expanded': showExtendedInfo }">
              <ArrowDown />
            </el-icon>
            <span class="toggle-text">
              {{ showExtendedInfo ? '收起' : '展开' }}扩展信息
            </span>
            <span class="toggle-desc">（公司信息、联系方式等）</span>
          </div>
        </div>

        <!-- 扩展信息区域 -->
        <div class="extended-info" v-show="showExtendedInfo">
          <!-- 公司信息 -->
          <div class="form-section">
            <div class="section-title">
              <el-icon><OfficeBuilding /></el-icon>
              <span>公司信息</span>
              <el-tag size="small" type="info" class="optional-tag">选填</el-tag>
            </div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="公司名称">
                  <el-input 
                    v-model="dialogForm.company_name" 
                    placeholder="请输入公司全称"
                    :prefix-icon="OfficeBuilding"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="统一社会信用代码">
                  <el-input 
                    v-model="dialogForm.credit_code" 
                    placeholder="请输入统一社会信用代码"
                    :prefix-icon="CreditCard"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="公司地址">
                  <el-input 
                    v-model="dialogForm.company_address" 
                    placeholder="请输入详细的公司地址"
                    :prefix-icon="LocationInformation"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 联系信息 -->
          <div class="form-section">
            <div class="section-title">
              <el-icon><Phone /></el-icon>
              <span>联系信息</span>
              <el-tag size="small" type="info" class="optional-tag">选填</el-tag>
            </div>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="联系人">
                  <el-input 
                    v-model="dialogForm.contact_name" 
                    placeholder="请输入联系人姓名"
                    :prefix-icon="User"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系电话">
                  <el-input 
                    v-model="dialogForm.contact_phone" 
                    placeholder="请输入联系电话"
                    :prefix-icon="Phone"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="邮箱地址">
                  <el-input 
                    v-model="dialogForm.contact_email" 
                    placeholder="请输入邮箱地址"
                    :prefix-icon="Message"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 其他信息 -->
        <div class="form-section">
          <div class="section-title">
            <el-icon><DocumentAdd /></el-icon>
            <span>其他信息</span>
          </div>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注说明">
                <el-input
                  v-model="dialogForm.remarks"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入备注信息，如特殊要求、合作条件等"
                  show-word-limit
                  maxlength="500"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer-custom">
          <el-button @click="dialogVisible = false" size="large">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
          <el-button type="primary" @click="handleDialogConfirm" size="large">
            <el-icon><Check /></el-icon>
            {{ dialogForm.id ? '保存修改' : '立即创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog
      title="审核代理"
      v-model="auditDialogVisible"
      width="500px"
      @close="handleAuditDialogClose"
    >
      <el-form
        ref="auditFormRef"
        :model="auditForm"
        :rules="auditRules"
        label-width="100px"
      >
        <el-form-item label="审核结果" prop="audit_status">
          <el-radio-group v-model="auditForm.audit_status">
            <el-radio label="approved">通过</el-radio>
            <el-radio label="rejected">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="拒绝原因" v-if="auditForm.audit_status === 'rejected'" prop="reject_reason">
          <el-input
            v-model="auditForm.reject_reason"
            type="textarea"
            placeholder="请输入拒绝原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="auditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAuditConfirm">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Promotion, TrendCharts, InfoFilled, User, EditPen, Setting, Key, 
  DocumentCopy, DataLine, OfficeBuilding, CreditCard, LocationInformation, 
  Phone, Message, DocumentAdd, Close, Check, ArrowDown 
} from '@element-plus/icons-vue'
import { useUserStore } from '@/store/modules/user'
import {
  getAgentList,
  createAgent,
  updateAgent,
  deleteAgent,
  auditAgent
} from '@/api/agent'
import { getMediaUserList } from '@/api/user'
import { AGENT_TYPES, AGENT_TYPE_MAP, AUDIT_STATUS, AUDIT_STATUS_MAP } from '@/constants/agent'
import PermissionWrapper from '@/components/PermissionWrapper.vue'

// 注册组件
const components = {
  PermissionWrapper
}

const userStore = useUserStore()

// 代理类型选项
const agentTypes = AGENT_TYPES

// 状态选项
const statusOptions = AUDIT_STATUS

// 查询参数
const queryParams = reactive({
  name: '',
  type: '',
  audit_status: '',
  media_id: '',
  page: 1,
  page_size: 10
})

// Tab相关状态
const activeTab = ref('delivery')
const tabCounts = reactive({
  delivery: 0,
  traffic: 0
})

// 表格数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)

// 分离的表格数据
const deliveryTableData = computed(() => {
  return tableData.value.filter(item => item.type === 'delivery')
})

const trafficTableData = computed(() => {
  return tableData.value.filter(item => item.type === 'traffic')
})

// 媒介用户列表
const mediaUsers = ref([])

// 平台类型选项
const platformTypes = [
  { value: 'denghuoplus', label: '灯火' },
  { value: 'xiaohongshu', label: '小红书' }
]

// 获取列表数据
const getList = async () => {
  try {
    loading.value = true
    const params = {
      name: queryParams.name,
      type: activeTab.value, // 根据当前tab筛选类型
      audit_status: queryParams.audit_status,
      media_id: queryParams.media_id,
      page: queryParams.page,
      page_size: queryParams.page_size
    }
    
    const res = await getAgentList(params)
    if (res.code === 0) {
      tableData.value = res.data.list
      total.value = res.data.total
    } else {
      ElMessage.error(res.message || '获取列表失败')
    }
  } catch (error) {
    console.error('获取代理列表失败:', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

// 更新tab计数
const updateTabCounts = async () => {
  try {
    // 获取投放代理数量
    const deliveryRes = await getAgentList({
      type: 'delivery',
      page: 1,
      page_size: 1
    })
    if (deliveryRes.code === 0) {
      tabCounts.delivery = deliveryRes.data.total
    }
    
    // 获取流量采买代理数量
    const trafficRes = await getAgentList({
      type: 'traffic',
      page: 1,
      page_size: 1
    })
    if (trafficRes.code === 0) {
      tabCounts.traffic = trafficRes.data.total
    }
  } catch (error) {
    console.error('获取计数失败:', error)
  }
}

// 查询操作
const handleQuery = () => {
  queryParams.page = 1
  getList()
}

// 重置查询
const resetQuery = () => {
  queryParams.name = ''
  queryParams.type = ''
  queryParams.audit_status = ''
  queryParams.media_id = ''
  queryParams.page = 1
  handleQuery()
}

// 分页操作
const handleSizeChange = (val) => {
  queryParams.page_size = val
  getList()
}

const handleCurrentChange = (val) => {
  queryParams.page = val
  getList()
}

// 类型显示
const getTypeText = (type) => {
  return AGENT_TYPE_MAP[type] || type
}

// 状态显示
const getStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  return AUDIT_STATUS_MAP[status] || status
}

// Tab切换处理
const handleTabChange = (tabName) => {
  activeTab.value = tabName
  queryParams.page = 1 // 重置页码
  getList()
}

// 获取平台文本
const getPlatformText = (platform) => {
  const platformMap = {
    'denghuoplus': '灯火',
    'xiaohongshu': '小红书'
  }
  return platformMap[platform] || platform || '未配置'
}

// 格式化余额
const formatBalance = (balance) => {
  return `¥${(balance / 100).toFixed(2)}`
}

// 表单校验规则
const dialogRules = reactive({
  name: [
    { required: true, message: '请输入代理名称', trigger: 'blur' },
    { min: 2, max: 50, message: '代理名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [{ required: true, message: '请选择代理类型', trigger: 'change' }],
  company_name: [
    { min: 2, max: 100, message: '公司名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  contact_name: [
    { min: 2, max: 20, message: '联系人姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  contact_phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  contact_email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  credit_code: [
    { pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/, message: '请输入正确的统一社会信用代码', trigger: 'blur' }
  ],
  'platform_config.platform': [{ 
    required: true, 
    message: '请选择投放平台', 
    trigger: 'change',
    validator: (rule, value, callback) => {
      if (dialogForm.type === 'delivery') {
        if (!value) {
          callback(new Error('请选择投放平台'))
          return
        }
      }
      callback()
    }
  }],
  'platform_config.info.token': [{ 
    required: true, 
    message: '请输入API Token', 
    trigger: 'blur',
    validator: (rule, value, callback) => {
      if (dialogForm.type === 'delivery' && dialogForm.platform_config.platform === 'denghuoplus') {
        if (!value) {
          callback(new Error('请输入API Token'))
          return
        }
        if (value.length < 10) {
          callback(new Error('Token长度不能少于10位'))
          return
        }
      }
      callback()
    }
  }],
  'platform_config.info.pid': [{ 
    required: true, 
    message: '请输入项目PID', 
    trigger: 'blur',
    validator: (rule, value, callback) => {
      if (dialogForm.type === 'delivery' && dialogForm.platform_config.platform === 'denghuoplus') {
        if (!value) {
          callback(new Error('请输入项目PID'))
          return
        }
        if (!/^\d+$/.test(value)) {
          callback(new Error('PID必须为数字'))
          return
        }
      }
      callback()
    }
  }]
})

// 新增/编辑对话框
const dialogVisible = ref(false)
const dialogTitle = ref('')
const dialogFormRef = ref(null)
const showExtendedInfo = ref(false)
const dialogForm = reactive({
  id: undefined,
  name: '',
  type: '',
  company_name: '',
  company_address: '',
  credit_code: '',
  contact_name: '',
  contact_phone: '',
  contact_email: '',
  remarks: '',
  traffic_type: [],
  settlement_type: '',
  platform_config: {
    platform: '',
    info: {}
  }
})

const handleAdd = (type = activeTab.value) => {
  dialogTitle.value = type === 'delivery' ? '新增投放代理' : '新增流量采买代理'
  dialogVisible.value = true
  Object.assign(dialogForm, {
    id: undefined,
    name: '',
    type: type,
    company_name: '',
    company_address: '',
    credit_code: '',
    contact_name: '',
    contact_phone: '',
    contact_email: '',
    remarks: '',
    traffic_type: [],
    settlement_type: '',
    platform_config: type === 'delivery' ? {
      platform: 'denghuoplus',
      info: { token: '', pid: '' }
    } : {
      platform: '',
      info: {}
    }
  })
}

const handleUpdate = (row) => {
  dialogTitle.value = '编辑代理'
  Object.assign(dialogForm, {
    id: row.id,
    name: row.name,
    type: row.type,
    company_name: row.company_name || '',
    company_address: row.company_address || '',
    credit_code: row.credit_code || '',
    contact_name: row.contact_name || '',
    contact_phone: row.contact_phone || '',
    contact_email: row.contact_email || '',
    remarks: row.remarks || '',
    traffic_type: row.traffic_type || [],
    settlement_type: row.settlement_type || '',
    platform_config: row.type === 'delivery' ? 
      (row.platform_config || { platform: 'denghuoplus', info: { token: '', pid: '' } }) : 
      { platform: '', info: {} }
  })
  dialogVisible.value = true
}

const handleDialogClose = () => {
  dialogFormRef.value?.resetFields()
  showExtendedInfo.value = false // 重置扩展信息显示状态
  Object.assign(dialogForm, {
    id: undefined,
    name: '',
    type: '',
    company_name: '',
    company_address: '',
    credit_code: '',
    contact_name: '',
    contact_phone: '',
    contact_email: '',
    remarks: '',
    traffic_type: [],
    settlement_type: '',
    platform_config: {
      platform: '',
      info: {}
    }
  })
}

const handleDialogConfirm = async () => {
  if (!dialogFormRef.value) return
  await dialogFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const params = { ...dialogForm }
        // 只在投放类型时验证平台配置
        if (params.type === 'delivery') {
          if (!params.platform_config || !params.platform_config.platform) {
            ElMessage.error('请选择平台类型')
            return
          }
          if (!params.platform_config.info || !params.platform_config.info.token || !params.platform_config.info.pid) {
            ElMessage.error('请完善平台配置信息')
            return
          }
        }
        
        if (dialogForm.id) {
          await updateAgent(params)
          ElMessage.success('修改成功')
        } else {
          await createAgent(params)
          ElMessage.success('新增成功')
        }
        dialogVisible.value = false
        updateTabCounts() // 更新计数
        handleQuery()
      } catch (error) {
        console.error(error)
        ElMessage.error(error.message || '操作失败')
      }
    }
  })
}

// 删除操作
const handleDelete = (row) => {
  ElMessageBox.confirm(
    '确认要删除该代理吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteAgent(row.id)
      ElMessage.success('删除成功')
      updateTabCounts() // 更新计数
      getList()
    } catch (error) {
      console.error('删除失败:', error)
    }
  })
}

// 审核对话框
const auditDialogVisible = ref(false)
const auditFormRef = ref(null)
const auditForm = reactive({
  id: undefined,
  audit_status: 'approved',
  reject_reason: ''
})

const auditRules = {
  audit_status: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
  reject_reason: [{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]
}

const handleAudit = (row) => {
  auditDialogVisible.value = true
  auditForm.id = row.id
  auditForm.audit_status = 'approved'
  auditForm.reject_reason = ''
}

const handleAuditDialogClose = () => {
  auditFormRef.value?.resetFields()
}

const handleAuditConfirm = () => {
  auditFormRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        await auditAgent(auditForm)
        ElMessage.success('审核成功')
        auditDialogVisible.value = false
        updateTabCounts() // 更新计数
        getList()
      } catch (error) {
        console.error('审核失败:', error)
      }
    }
  })
}

// 获取媒介用户列表
const getMediaUsers = async () => {
  try {
    const res = await getMediaUserList()
    mediaUsers.value = res.data.list
  } catch (error) {
    console.error('获取媒介用户列表失败:', error)
  }
}

// 处理代理类型变更
const handleTypeChange = (value) => {
  if (value === 'delivery') {
    // 初始化平台配置
    dialogForm.platform_config = {
      platform: 'denghuoplus',
      info: {
        token: '',
        pid: ''
      }
    }
  } else {
    // 清空平台配置
    dialogForm.platform_config = {
      platform: '',
      info: {}
    }
  }
  // 重置表单验证
  dialogFormRef.value?.clearValidate(['platform_config.platform', 'platform_config.info.token', 'platform_config.info.pid'])
}

// 处理平台类型变更
const handlePlatformChange = () => {
  // 重置信息但保持结构
  dialogForm.platform_config.info = {
    token: '',
    pid: ''
  }
  // 重置表单验证
  dialogFormRef.value?.clearValidate(['platform_config.info.token', 'platform_config.info.pid'])
}

// 初始化
onMounted(() => {
  updateTabCounts() // 先更新计数
  getList()
  if (!userStore.isMedia) {
    getMediaUsers()
  }
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  
  .filter-container {
    margin-bottom: 20px;
  }
  
  .agent-tabs-container {
    margin-bottom: 20px;
    
    .agent-tabs {
      :deep(.el-tabs__header) {
        margin-bottom: 0;
        .el-tabs__nav-wrap {
          background: #f8f9fa;
          border-radius: 8px 8px 0 0;
          padding: 8px;
        }
        .el-tabs__item {
          border: none;
          border-radius: 6px;
          margin-right: 8px;
          transition: all 0.3s ease;
          
          &.is-active {
            background: #ffffff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
          
          &:hover {
            background: #f0f0f0;
          }
        }
      }
      
      :deep(.el-tabs__content) {
        padding: 0;
      }
    }
    
    .tab-label {
      display: flex;
      align-items: center;
      gap: 6px;
      font-weight: 500;
      
      .el-icon {
        font-size: 16px;
      }
      
      .tab-badge {
        margin-left: 4px;
        :deep(.el-badge__content) {
          background: #1890ff;
          border: none;
          font-size: 12px;
        }
      }
    }
    
    .tab-operate-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 0 16px;
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 20px;
      
      .tab-info {
        .info-text {
          display: flex;
          align-items: center;
          gap: 6px;
          color: #666;
          font-size: 14px;
          
          .el-icon {
            color: #1890ff;
          }
        }
      }
    }
    
    .tab-table-container {
      margin-top: 16px;
      
      .delivery-table {
        :deep(.el-table__header) {
          background: #f8f9ff;
          
          th {
            background: #f8f9ff !important;
            color: #1890ff;
            font-weight: 600;
          }
        }
      }
      
      .traffic-table {
        :deep(.el-table__header) {
          background: #f6ffed;
          
          th {
            background: #f6ffed !important;
            color: #52c41a;
            font-weight: 600;
          }
        }
      }
    }
    
    .pagination-container {
      display: flex;
      justify-content: center;
      margin: 20px 0;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
    }
  }
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}

// Tab特定样式
:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  border: none;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  
  &.is-active {
    border-color: #1890ff;
    background: #ffffff;
    color: #1890ff;
  }
  
  &:not(.is-active):hover {
    background: #f5f7fa;
  }
}

// 表格行悬停效果
:deep(.el-table__row:hover > td) {
  background-color: #f8f9ff !important;
}

// 对话框样式
.agent-dialog {
  :deep(.el-dialog__header) {
    padding: 0;
    border-bottom: 1px solid #f0f0f0;
  }
  
  :deep(.el-dialog__body) {
    padding: 0;
    max-height: 70vh;
    overflow-y: auto;
  }
  
  .dialog-header {
    padding: 20px 20px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    
    .dialog-type-indicator {
      display: flex;
      align-items: center;
      gap: 12px;
      color: white;
      
      .type-icon {
        font-size: 24px;
        padding: 8px;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.2);
        
        &.delivery-icon {
          background: rgba(24, 144, 255, 0.3);
        }
        
        &.traffic-icon {
          background: rgba(82, 196, 26, 0.3);
        }
      }
      
      .type-text {
        font-size: 18px;
        font-weight: 600;
      }
    }
  }
  
  .agent-form {
    padding: 20px;
    
    .form-section {
      margin-bottom: 32px;
      
      &:last-child {
        margin-bottom: 16px;
      }
      
      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 20px;
        padding-bottom: 8px;
        border-bottom: 2px solid #f0f0f0;
        color: #333;
        font-weight: 600;
        font-size: 16px;
        
        .el-icon {
          color: #1890ff;
          font-size: 18px;
        }
        
        .optional-tag {
          margin-left: auto;
          opacity: 0.7;
        }
      }
      
      .expand-toggle {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        background: #f8f9fa;
        border: 1px dashed #d9d9d9;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        user-select: none;
        
        &:hover {
          background: #e6f7ff;
          border-color: #1890ff;
          
          .toggle-text {
            color: #1890ff;
          }
        }
        
        .toggle-icon {
          font-size: 16px;
          color: #666;
          transition: transform 0.3s ease;
          
          &.expanded {
            transform: rotate(180deg);
          }
        }
        
        .toggle-text {
          font-weight: 500;
          color: #333;
          transition: color 0.3s ease;
        }
        
        .toggle-desc {
          font-size: 12px;
          color: #999;
          margin-left: 4px;
        }
      }
      
      .platform-config-section {
        margin-top: 16px;
        padding: 16px;
        background: #f8f9ff;
        border-radius: 8px;
        border: 1px solid #e6f7ff;
        
        .config-title {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 16px;
          color: #1890ff;
          font-weight: 500;
        }
        
        .field-tip {
          font-size: 12px;
          color: #999;
          margin-top: 4px;
        }
      }
      
      .option-item {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .el-icon {
          color: #1890ff;
        }
      }
      
      .platform-option {
        .platform-info {
          .platform-name {
            font-weight: 500;
            color: #333;
          }
          
          .platform-desc {
            font-size: 12px;
            color: #999;
            margin-left: 8px;
          }
        }
      }
    }
    
    .extended-info {
      transition: all 0.3s ease-in-out;
      
      .form-section {
        background: #fafafa;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #e8e8e8;
        
        .section-title {
          border-bottom-color: #e0e0e0;
          margin-bottom: 16px;
          padding-bottom: 6px;
        }
      }
    }
    
    :deep(.el-form-item) {
      margin-bottom: 18px;
      
      .el-form-item__label {
        color: #333;
        font-weight: 500;
      }
      
      .el-input__inner {
        border-radius: 6px;
        transition: all 0.3s ease;
        
        &:focus {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        }
      }
      
      .el-select {
        width: 100%;
        
        .el-input__inner {
          cursor: pointer;
        }
      }
      
      .el-textarea__inner {
        border-radius: 6px;
        resize: vertical;
        
        &:focus {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        }
      }
    }
  }
  
  .dialog-footer-custom {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
    
    .el-button {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 10px 20px;
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// 响应式布局
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
    
    .tab-operate-container {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
      
      .tab-info {
        width: 100%;
        
        .info-text {
          font-size: 13px;
        }
      }
    }
  }
  
  .agent-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 5vh auto;
    }
    
    .agent-form {
      padding: 16px;
      
      .form-section {
        margin-bottom: 24px;
        
        .section-title {
          font-size: 14px;
        }
      }
      
      :deep(.el-col) {
        &.el-col-12, &.el-col-8 {
          width: 100% !important;
        }
      }
    }
  }
}
</style> 