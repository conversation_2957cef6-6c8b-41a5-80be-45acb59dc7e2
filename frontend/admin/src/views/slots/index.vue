<template>
  <div class="app-container">
    <!-- 资源位类型Tab区域 -->
    <el-card class="tab-container" shadow="never">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="slot-tabs">
        <el-tab-pane 
          v-for="item in tabOptions" 
          :key="item.value"
          :label="item.label" 
          :name="item.value"
        >
          <template #label>
            <span class="tab-label">
              <i :class="item.icon"></i>
              {{ item.label }}
            </span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 搜索区域 -->
    <el-card class="filter-container" shadow="never">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="资源位名称">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入资源位名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select v-model="queryParams.audit_status" placeholder="请选择状态" clearable style="width: 200px">
            <el-option
              v-for="item in auditStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属媒介" v-if="!userStore.isMedia">
          <el-select v-model="queryParams.user_id" placeholder="请选择媒介" clearable style="width: 200px">
            <el-option
              v-for="item in mediaUserOptions"
              :key="item.id"
              :label="item.realName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属媒体" v-if="!userStore.isMedia">
          <el-select v-model="queryParams.media_id" placeholder="请选择媒体" clearable style="width: 200px" filterable>
            <el-option
              v-for="item in mediaOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <i class="el-icon-search"></i>
            查询
          </el-button>
          <el-button @click="resetQuery">
            <i class="el-icon-refresh"></i>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="operate-container" shadow="never">
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus"></i>
        新增资源位
      </el-button>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-container" shadow="never">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        stripe
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="资源位名称" min-width="120" />
        <el-table-column prop="type" label="资源位类型" width="140">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.type)" size="small">
              <i :class="getTypeIcon(scope.row.type)"></i>
              {{ scope.row.type_text }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="media_name" label="所属媒体" width="120" />
        <el-table-column prop="user_real_name" label="媒介" width="100" />
        <el-table-column prop="audit_status" label="审核状态" width="100">
          <template #default="scope">
            <el-tag :type="getAuditStatusType(scope.row.audit_status)">
              {{ getAuditStatusText(scope.row.audit_status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="120" />
        <el-table-column prop="created_at" label="创建时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              v-if="scope.row.audit_status === 'pending'"
              type="success"
              size="small"
              @click="handleAudit(scope.row)"
            >
              审核
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="handleUpdate(scope.row)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.page_size"
          :page-sizes="[10, 20, 30, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      @close="handleDialogClose"
    >
      <el-form
        ref="dialogFormRef"
        :model="dialogForm"
        :rules="dialogRules"
        label-width="100px"
      >
        <el-form-item label="所属媒体" prop="media_id">
          <el-select v-model="dialogForm.media_id" placeholder="请选择媒体" style="width: 100%">
            <el-option
              v-for="item in mediaOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="资源位名称" prop="name">
          <el-input v-model="dialogForm.name" placeholder="请输入资源位名称" />
        </el-form-item>
        <el-form-item label="资源位类型" prop="type">
          <el-select v-model="dialogForm.type" placeholder="请选择类型" style="width: 100%">
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <span style="float: left">
                <i :class="getTypeIcon(item.value)"></i>
                {{ item.label }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="dialogForm.remark"
            type="textarea"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleDialogConfirm">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog
      title="审核资源位"
      v-model="auditDialogVisible"
      width="500px"
      @close="handleAuditDialogClose"
    >
      <el-form
        ref="auditFormRef"
        :model="auditForm"
        :rules="auditRules"
        label-width="100px"
      >
        <el-form-item label="审核结果" prop="audit_status">
          <el-radio-group v-model="auditForm.audit_status">
            <el-radio label="approved">通过</el-radio>
            <el-radio label="rejected">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="拒绝原因" prop="reject_reason" v-if="auditForm.audit_status === 'rejected'">
          <el-input
            v-model="auditForm.reject_reason"
            type="textarea"
            placeholder="请输入拒绝原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="auditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAuditConfirm">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { getSlotList, createSlot, updateSlot, auditSlot } from '@/api/slot'
import { getMediaList } from '@/api/media'
import { getMediaUserList } from '@/api/user'

const userStore = useUserStore()

// 资源位类型选项
const typeOptions = [
  { label: '微信H5', value: 'wechat_h5', icon: 'el-icon-mobile-phone' },
  { label: '微信小程序', value: 'wechat_mp', icon: 'el-icon-files' },
  { label: '微信插件', value: 'wechat_plugin', icon: 'el-icon-cpu' },
  { label: '支付宝小程序', value: 'alipay_mp', icon: 'el-icon-files' },
  { label: '支付宝H5', value: 'alipay_h5', icon: 'el-icon-mobile-phone' },
  { label: '其他', value: 'other', icon: 'el-icon-more' }
]

// Tab选项（包含全部选项）
const tabOptions = computed(() => [
  { label: '全部', value: '', icon: 'el-icon-menu' },
  ...typeOptions
])

// 当前激活的tab
const activeTab = ref('')

// 审核状态选项
const auditStatusOptions = [
  { label: '待审核', value: 'pending' },
  { label: '已通过', value: 'approved' },
  { label: '已拒绝', value: 'rejected' }
]

// 查询参数
const queryParams = reactive({
  name: '',
  type: '',
  audit_status: '',
  media_id: '',
  user_id: '',
  page: 1,
  page_size: 10
})

// 表格数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)

// 媒体选项
const mediaOptions = ref([])

// 媒介用户选项
const mediaUserOptions = ref([])



// 获取类型图标
const getTypeIcon = (type) => {
  const typeObj = typeOptions.find(opt => opt.value === type)
  return typeObj?.icon || 'el-icon-more'
}

// 获取类型标签样式
const getTypeTagType = (type) => {
  const typeMap = {
    'wechat_h5': 'success',
    'wechat_mp': 'primary',
    'wechat_plugin': 'warning',
    'alipay_mp': 'info',
    'alipay_h5': 'success',
    'other': ''
  }
  return typeMap[type] || ''
}

// Tab点击事件
const handleTabClick = (tab) => {
  queryParams.type = tab.props.name
  queryParams.page = 1
  getList()
}

// 获取媒体列表
const getMedias = async () => {
  try {
    const res = await getMediaList({
      page: 1,
      size: 1000,
      audit_status: 'approved'
    })
    if(res.data && res.data.list) {
      mediaOptions.value = res.data.list
      console.log('媒体列表:', mediaOptions.value)
    } else {
      console.error('获取媒体列表数据格式错误:', res)
    }
  } catch (error) {
    console.error('获取媒体列表失败:', error)
    ElMessage.error('获取媒体列表失败')
  }
}

// 获取媒介用户列表
const getMediaUsers = async () => {
  try {
    const res = await getMediaUserList()
    mediaUserOptions.value = res.data.list
  } catch (error) {
    console.error('获取媒介列表失败:', error)
  }
}

// 获取列表数据
const getList = async () => {
  try {
    loading.value = true
    const res = await getSlotList(queryParams)
    tableData.value = res.data.list
    total.value = res.data.total
  } catch (error) {
    console.error('获取资源位列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 查询操作
const handleQuery = () => {
  queryParams.page = 1
  getList()
}

// 重置查询
const resetQuery = () => {
  queryParams.name = ''
  queryParams.audit_status = ''
  queryParams.media_id = ''
  queryParams.user_id = ''
  activeTab.value = ''
  queryParams.type = ''
  handleQuery()
}

// 分页操作
const handleSizeChange = (val) => {
  queryParams.page_size = val
  getList()
}

const handleCurrentChange = (val) => {
  queryParams.page = val
  getList()
}

// 类型显示
const getTypeText = (type) => {
  return typeOptions.find(opt => opt.value === type)?.label || type
}

// 审核状态显示
const getAuditStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger'
  }
  return statusMap[status] || 'info'
}

const getAuditStatusText = (status) => {
  return auditStatusOptions.find(opt => opt.value === status)?.label || status
}

// 表单校验规则
const dialogRules = {
  media_id: [{ required: true, message: '请选择所属媒体', trigger: 'change' }],
  name: [{ required: true, message: '请输入资源位名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择资源位类型', trigger: 'change' }]
}

// 新增/编辑对话框
const dialogVisible = ref(false)
const dialogTitle = ref('')
const dialogFormRef = ref(null)
const dialogForm = reactive({
  id: undefined,
  media_id: '',
  name: '',
  type: '',
  remark: ''
})

const handleAdd = () => {
  dialogTitle.value = '新增资源位'
  dialogVisible.value = true
  dialogForm.id = undefined
  dialogForm.media_id = ''
  dialogForm.name = ''
  dialogForm.type = ''
  dialogForm.remark = ''
}

const handleUpdate = (row) => {
  dialogTitle.value = '编辑资源位'
  dialogForm.id = row.id
  dialogForm.media_id = row.media_id
  dialogForm.name = row.name
  dialogForm.type = row.type
  dialogForm.remark = row.remark
  dialogVisible.value = true
}

const handleDialogClose = () => {
  dialogFormRef.value?.resetFields()
}

const handleDialogConfirm = () => {
  dialogFormRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogForm.id) {
          await updateSlot(dialogForm)
          ElMessage.success('更新成功')
        } else {
          await createSlot(dialogForm)
          ElMessage.success('创建成功')
        }
        dialogVisible.value = false
        getList()
      } catch (error) {
        console.error('保存失败:', error)
      }
    }
  })
}

// 审核对话框
const auditDialogVisible = ref(false)
const auditFormRef = ref(null)
const auditForm = reactive({
  id: undefined,
  audit_status: 'approved',
  reject_reason: ''
})

const auditRules = {
  audit_status: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
  reject_reason: [{
    required: true,
    message: '请输入拒绝原因',
    trigger: 'blur',
    validator: (rule, value, callback) => {
      if (auditForm.audit_status === 'rejected' && !value) {
        callback(new Error('请输入拒绝原因'))
      } else {
        callback()
      }
    }
  }]
}

const handleAudit = (row) => {
  auditDialogVisible.value = true
  auditForm.id = row.id
  auditForm.audit_status = 'approved'
  auditForm.reject_reason = ''
}

const handleAuditDialogClose = () => {
  auditFormRef.value?.resetFields()
}

const handleAuditConfirm = () => {
  auditFormRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        await auditSlot(auditForm)
        ElMessage.success('审核成功')
        auditDialogVisible.value = false
        getList()
      } catch (error) {
        console.error('审核失败:', error)
      }
    }
  })
}

// 初始化
onMounted(() => {
  getList()
  getMedias()
  if (!userStore.isMedia) {
    getMediaUsers()
  }
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  
  .tab-container {
    margin-bottom: 20px;
    
    .slot-tabs {
      :deep(.el-tabs__header) {
        margin: 0;
        border-bottom: 2px solid #e4e7ed;
      }
      
      :deep(.el-tabs__nav-wrap::after) {
        display: none;
      }
      
      :deep(.el-tabs__item) {
        padding: 0 20px;
        height: 50px;
        line-height: 50px;
        font-size: 14px;
        font-weight: 500;
        color: #606266;
        border-radius: 8px 8px 0 0;
        margin-right: 8px;
        transition: all 0.3s ease;
        
        &:hover {
          color: #409eff;
          background: #f0f9ff;
        }
        
        &.is-active {
          color: #409eff;
          background: #ffffff;
          border: 1px solid #e4e7ed;
          border-bottom: 1px solid #ffffff;
          margin-bottom: -1px;
          box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        }
      }
      
      .tab-label {
        display: flex;
        align-items: center;
        gap: 6px;
        
        i {
          font-size: 16px;
        }
        
        
      }
    }
  }
  
  .filter-container {
    margin-bottom: 20px;
    
    .demo-form-inline {
      .el-form-item {
        margin-bottom: 16px;
      }
      
      .el-button {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }
  
  .operate-container {
    margin-bottom: 20px;
    
    .el-button {
      display: flex;
      align-items: center;
      gap: 6px;
      font-weight: 500;
    }
  }
  
  .table-container {
    margin-bottom: 20px;
    
    :deep(.el-table) {
      .el-table__header {
        th {
          background: #fafafa;
          color: #333;
          font-weight: 600;
        }
      }
      
      .el-table__row {
        &:hover > td {
          background: #f5f7fa;
        }
      }
      
      .el-tag {
        font-weight: 500;
        
        i {
          margin-right: 4px;
        }
      }
    }
  }
  
  .pagination-container {
    display: flex;
    justify-content: center;
    margin: 20px 0;
  }
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
    
    .slot-tabs {
      :deep(.el-tabs__item) {
        padding: 0 12px;
        margin-right: 4px;
        font-size: 13px;
        
        .tab-label {
          gap: 4px;
          
          i {
            font-size: 14px;
          }
        }
      }
    }
    
    .demo-form-inline {
      .el-form-item {
        width: 100%;
        margin-right: 0;
      }
    }
  }
}
</style> 