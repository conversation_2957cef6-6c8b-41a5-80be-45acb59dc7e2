<template>
  <div class="app-container">
    <!-- 筛选区域 -->
    <el-card class="filter-container" shadow="hover">
      <div class="filter-header">
        <div class="filter-form">
          <el-form :model="queryParams" :inline="!isMobile" class="search-form">
            <div class="form-grid">
              <el-form-item label="媒体名称" class="form-item">
                <el-input
                  v-model="queryParams.name"
                  placeholder="请输入媒体名称"
                  clearable
                  @keyup.enter="handleQuery"
                  class="search-input"
                />
              </el-form-item>
              <el-form-item label="媒介" v-if="!isMediaRole" class="form-item">
                <el-select v-model="queryParams.user_id" placeholder="请选择媒介" clearable class="search-select">
                  <el-option 
                    v-for="item in mediaUserOptions" 
                    :key="item.id" 
                    :label="item.realName" 
                    :value="item.id" 
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="审核状态" class="form-item">
                <el-select v-model="queryParams.audit_status" placeholder="请选择状态" clearable class="search-select">
                  <el-option 
                    v-for="item in auditStatusOptions" 
                    :key="item.value" 
                    :label="item.label" 
                    :value="item.value" 
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="媒体类型" v-if="activeTab === 'traffic'" class="form-item">
                <el-select v-model="queryParams.media_type" placeholder="请选择媒体类型" clearable class="search-select">
                  <el-option 
                    v-for="item in mediaTypeOptions" 
                    :key="item.value" 
                    :label="item.label" 
                    :value="item.value" 
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="form-actions">
              <el-button type="primary" @click="handleQuery" class="search-btn">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
              <el-button @click="resetQuery" class="reset-btn">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </div>
          </el-form>
          
          <!-- 操作按钮 -->
          <div class="action-buttons">
            <el-button type="primary" @click="handleAdd" class="add-btn">
              <el-icon><Plus /></el-icon>
              新增媒体
            </el-button>
          </div>
        </div>
      </div>

      <!-- 合作类型Tab -->
      <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="cooperation-tabs">
        <el-tab-pane label="流量采买" name="traffic" />
        <el-tab-pane label="CPS合作" name="cps" />
        <el-tab-pane label="灯火投放" name="dh" />
        <el-tab-pane label="其他投放" name="other_delivery" />
      </el-tabs>
    </el-card>

    <!-- PC端表格视图 -->
    <el-card v-if="!isMobile" class="table-container" shadow="hover">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="媒体名称" min-width="120" />
        <el-table-column prop="userRealName" label="媒介" width="100" />
        <el-table-column prop="types" label="媒体类型" width="150">
          <template #default="scope">
            {{ getTypeText(scope.row.types) }}
          </template>
        </el-table-column>
        <el-table-column prop="industry" label="所属行业" width="100">
          <template #default="scope">
            {{ getIndustryText(scope.row.industry) }}
          </template>
        </el-table-column>
        <el-table-column prop="cooperation_type" label="合作类型" width="100">
          <template #default="scope">
            {{ getCooperationTypeText(scope.row.cooperation_type) }}
          </template>
        </el-table-column>
        <el-table-column prop="audit_status" label="审核状态" width="100">
          <template #default="scope">
            <el-tag :type="getAuditStatusType(scope.row.audit_status)">
              {{ getAuditStatusText(scope.row.audit_status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="cooperation_status" label="合作状态" width="100">
          <template #default="scope">
            <el-tag :type="getCooperationStatusType(scope.row.cooperation_status)">
              {{ getCooperationStatusText(scope.row.cooperation_status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180" />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button
              v-if="scope.row.audit_status === 'pending'"
              type="success"
              size="small"
              @click="handleAudit(scope.row)"
            >
              审核
            </el-button>

            <el-button
              v-if="isPluginMedia(scope.row)"
              type="info"
              size="small"
              @click="handleViewPluginData(scope.row)"
            >
              插件数据
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="handleUpdate(scope.row)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- PC端分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.page_size"
          :page-sizes="[10, 20, 30, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 移动端卡片视图 -->
    <div v-else class="mobile-list">
      <el-empty v-if="tableData.length === 0" description="暂无数据" />
      <div v-else class="card-list">
        <el-card 
          v-for="item in tableData" 
          :key="item.id"
          class="media-card"
          shadow="hover"
        >
          <div class="card-header">
            <span class="media-name">{{ item.name }}</span>
            <el-tag :type="getAuditStatusType(item.audit_status)" size="small">
              {{ getAuditStatusText(item.audit_status) }}
            </el-tag>
          </div>
          
          <div class="card-content">
            <div class="info-item">
              <span class="label">媒介:</span>
              <span class="value">{{ item.userRealName }}</span>
            </div>
            <div class="info-item">
              <span class="label">媒体类型:</span>
              <span class="value">{{ getTypeText(item.types) }}</span>
            </div>
            <div class="info-item">
              <span class="label">所属行业:</span>
              <span class="value">{{ getIndustryText(item.industry) }}</span>
            </div>
            <div class="info-item">
              <span class="label">合作类型:</span>
              <span class="value">{{ getCooperationTypeText(item.cooperation_type) }}</span>
            </div>
            <div class="info-item">
              <span class="label">合作状态:</span>
              <el-tag :type="getCooperationStatusType(item.cooperation_status)" size="small">
                {{ getCooperationStatusText(item.cooperation_status) }}
              </el-tag>
            </div>
            <div class="info-item">
              <span class="label">创建时间:</span>
              <span class="value">{{ item.created_at }}</span>
            </div>
          </div>

          <div class="card-footer">
            <el-button
              v-if="item.audit_status === 'pending'"
              type="success"
              size="small"
              @click="handleAudit(item)"
            >
              审核
            </el-button>

            <el-button
              v-if="isPluginMedia(item)"
              type="info"
              size="small"
              @click="handleViewPluginData(item)"
            >
              插件数据
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="handleUpdate(item)"
            >
              编辑
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 移动端加载更多 -->
      <div v-if="tableData.length > 0" class="load-more">
        <el-button 
          v-if="hasMore"
          :loading="loading"
          type="primary" 
          link 
          @click="loadMore"
        >
          加载更多
        </el-button>
        <span v-else class="no-more">没有更多了</span>
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="900px"
      @close="handleDialogClose"
      class="media-dialog"
    >
      <el-form
        ref="dialogFormRef"
        :model="dialogForm"
        :rules="dialogRules"
        label-width="140px"
        class="media-form"
      >
        <!-- 基础信息区域 -->
        <div class="form-section">
          <div class="section-title">基础信息</div>
          <div class="form-row">
            <el-form-item label="媒体名称" prop="name" class="form-item-full">
              <el-input v-model="dialogForm.name" placeholder="请输入媒体名称" class="form-input" />
            </el-form-item>
          </div>
          <div class="form-row">
            <el-form-item label="媒体类型" prop="types" class="form-item-half">
              <el-select v-model="dialogForm.types" placeholder="请选择类型" multiple class="form-select">
                <el-option 
                  v-for="item in mediaTypeOptions" 
                  :key="item.value" 
                  :label="item.label" 
                  :value="item.value" 
                />
              </el-select>
            </el-form-item>
            <el-form-item label="所属行业" prop="industry" class="form-item-half">
              <el-select v-model="dialogForm.industry" placeholder="请选择行业" class="form-select">
                <el-option 
                  v-for="item in industryOptions" 
                  :key="item.value" 
                  :label="item.label" 
                  :value="item.value" 
                />
              </el-select>
            </el-form-item>
          </div>
          <div class="form-row" v-if="dialogForm.industry === 'other'">
            <el-form-item label="自定义行业" prop="custom_industry" class="form-item-full">
              <el-input v-model="dialogForm.custom_industry" placeholder="请输入自定义行业" class="form-input" />
            </el-form-item>
          </div>
          <div class="form-row">
            <el-form-item label="合作类型" prop="cooperation_type" class="form-item-half">
              <el-select v-model="dialogForm.cooperation_type" placeholder="请选择合作类型" class="form-select">
                <el-option 
                  v-for="item in cooperationTypeOptions" 
                  :key="item.value" 
                  :label="item.label" 
                  :value="item.value" 
                />
              </el-select>
            </el-form-item>
            <el-form-item prop="return_rate" class="form-item-half">
              <template #label>
                <el-tooltip class="item" effect="dark" content="计算方式：100返30，100/130*100 = 76.92" placement="top">
                  <span>消耗比例</span>
                </el-tooltip>
              </template>
              <el-input-number
                v-model="dialogForm.return_rate"
                :min="0"
                :precision="2"
                :step="1"
                class="form-number"
              />
            </el-form-item>
          </div>
          <div class="form-row">
            <el-form-item label="代理" prop="ad_agent_id" class="form-item-full">
              <el-select v-model="dialogForm.ad_agent_id" placeholder="请选择代理" clearable class="form-select" filterable>
                <el-option 
                  v-for="item in agentOptions" 
                  :key="item.id" 
                  :label="item.name" 
                  :value="item.id" 
                />
              </el-select>
            </el-form-item>
          </div>
        </div>


        <!-- 平台配置 -->
        <div class="form-section" v-if="dialogForm.cooperation_type === 'dh'">
          <div class="section-title">平台配置</div>
          <div class="form-row">
            <el-form-item label="平台类型" prop="platform_config.platform" class="form-item-full">
              <el-select v-model="dialogForm.platform_config.platform" placeholder="请选择平台类型" disabled class="form-select">
                <el-option label="灯火" value="denghuoplus" />
              </el-select>
            </el-form-item>
          </div>
          <div class="form-row">
            <el-form-item 
              label="项目ID" 
              prop="platform_config.info.pid"
              class="form-item-half"
            >
              <el-input 
                v-model="dialogForm.platform_config.info.pid" 
                placeholder="请输入灯火平台的项目ID"
                class="form-input"
              >
                <template #append>
                  <el-tooltip content="灯火平台的项目唯一标识" placement="top">
                    <el-icon><InfoFilled /></el-icon>
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item 
              label="API Token" 
              prop="platform_config.info.token"
              class="form-item-half"
            >
              <el-input 
                v-model="dialogForm.platform_config.info.token" 
                placeholder="请输入API Token"
                class="form-input"
              >
                <template #append>
                  <el-tooltip content="API访问令牌" placement="top">
                    <el-icon><InfoFilled /></el-icon>
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <!-- CPS配置 -->
        <div class="form-section" v-if="dialogForm.cooperation_type === 'cps'">
          <div class="section-title">CPS配置</div>
          <el-collapse class="cps-collapse">
            <el-collapse-item title="账号密码设置" name="1">
              <div class="form-row">
                <el-form-item label="账号" prop="account" class="form-item-full">
                  <el-input v-model="dialogForm.account" placeholder="请输入账号" class="form-input" />
                </el-form-item>
              </div>
              <div class="form-row">
                <el-form-item label="密码" prop="password" class="form-item-half">
                  <el-input 
                    v-model="dialogForm.password" 
                    type="password" 
                    placeholder="请输入密码" 
                    show-password
                    class="form-input"
                  />
                </el-form-item>
                <el-form-item label="确认密码" prop="confirm_password" class="form-item-half">
                  <el-input 
                    v-model="dialogForm.confirm_password" 
                    type="password" 
                    placeholder="请再次输入密码" 
                    show-password
                    class="form-input"
                  />
                </el-form-item>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>

        <!-- 展开更多信息按钮 -->
        <div class="form-section">
          <el-button 
            link 
            @click="showMoreInfo = !showMoreInfo"
            class="expand-btn"
          >
            <el-icon><ArrowDown v-if="!showMoreInfo" /><ArrowUp v-else /></el-icon>
            {{ showMoreInfo ? '收起' : '展开' }}更多信息（可选）
          </el-button>
        </div>

        <!-- 可选信息区域 -->
        <template v-if="showMoreInfo">
          <!-- 媒体信息 -->
          <div class="form-section">
            <div class="section-title">媒体信息</div>
            <div class="form-row">
              <el-form-item label="日活" prop="daily_activity" class="form-item-half">
                <el-input v-model="dialogForm.daily_activity" placeholder="请输入日活" class="form-input" />
              </el-form-item>
              <el-form-item label="交易量" prop="transaction_volume" class="form-item-half">
                <el-input v-model="dialogForm.transaction_volume" placeholder="请输入交易量" class="form-input" />
              </el-form-item>
            </div>
          </div>

          <!-- 联系信息 -->
          <div class="form-section">
            <div class="section-title">联系信息</div>
            <div class="form-row">
              <el-form-item label="公司名称" prop="company_name" class="form-item-full">
                <el-input v-model="dialogForm.company_name" placeholder="请输入公司名称" class="form-input" />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="公司地址" prop="company_address" class="form-item-full">
                <el-input v-model="dialogForm.company_address" placeholder="请输入公司地址" class="form-input" />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="联系人" prop="contact_name" class="form-item-half">
                <el-input v-model="dialogForm.contact_name" placeholder="请输入联系人" class="form-input" />
              </el-form-item>
              <el-form-item label="联系电话" prop="contact_phone" class="form-item-half">
                <el-input v-model="dialogForm.contact_phone" placeholder="请输入联系电话" class="form-input" />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="备注" prop="remark" class="form-item-full">
                <el-input
                  v-model="dialogForm.remark"
                  type="textarea"
                  placeholder="请输入备注"
                  :rows="2"
                  class="form-textarea"
                />
              </el-form-item>
            </div>
          </div>
        </template>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleDialogConfirm">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog
      title="审核媒体"
      v-model="auditDialogVisible"
      width="500px"
      @close="handleAuditDialogClose"
    >
      <el-form
        ref="auditFormRef"
        :model="auditForm"
        :rules="auditRules"
        label-width="80px"
      >
        <el-form-item label="审核结果" prop="audit_status">
          <el-radio-group v-model="auditForm.audit_status">
            <el-radio label="approved">通过</el-radio>
            <el-radio label="rejected">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="拒绝原因" prop="reject_reason" v-if="auditForm.audit_status === 'rejected'">
          <el-input
            v-model="auditForm.reject_reason"
            type="textarea"
            placeholder="请输入拒绝原因"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="auditForm.remark"
            type="textarea"
            placeholder="请输入备注信息"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="auditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAuditConfirm">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowRight, InfoFilled, Search, Refresh, Plus, ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import {
  getMediaList,
  createMedia,
  updateMedia,
  deleteMedia,
  auditMedia
} from '@/api/media'
import { getAgentList } from '@/api/agent'
import { useUserStore } from '@/store/modules/user'
import { getMediaUserList } from '@/api/user'
import { useRouter } from 'vue-router'

// Router
const router = useRouter()

// Tab相关
const activeTab = ref('traffic')

// 查询参数
const queryParams = reactive({
  name: '',
  user_id: '',
  audit_status: '',
  cooperation_type: 'traffic', // 默认流量采买
  media_type: '', // 媒体类型筛选
  page: 1,
  page_size: 10
})

// 用户store
const userStore = useUserStore()
const isMediaRole = computed(() => userStore.isMedia)

// 表格数据
const loading = ref(false)
const tableData = ref([])
const total = ref(0)

// 媒介用户选项
const mediaUserOptions = ref([])

// 代理选项
const agentOptions = ref([])

// 获取媒介用户列表
const getMediaUsers = async () => {
  try {
    const res = await getMediaUserList()
    mediaUserOptions.value = res.data.list
  } catch (error) {
    console.error('获取媒介列表失败:', error)
  }
}

// 获取代理列表
const getAgents = async () => {
  try {
    const res = await getAgentList({
      page: 1,
      page_size: 1000,
      audit_status: 'approved' // 只获取已审核通过的代理
    })
    agentOptions.value = res.data.list || []
  } catch (error) {
    console.error('获取代理列表失败:', error)
    ElMessage.error('获取代理列表失败')
  }
}

// 获取列表数据
const getList = async () => {
  try {
    loading.value = true
    
    // 构建请求参数
    const params = { ...queryParams }
    
    // 如果有媒体类型筛选，转换为数组格式（后端期望types是数组）
    if (params.media_type) {
      params.types = [params.media_type]
      delete params.media_type // 删除前端的media_type参数
    }
    
    const res = await getMediaList(params)
    tableData.value = res.data.list
    total.value = res.data.total
  } catch (error) {
    console.error('获取媒体列表失败:', error)
  } finally {
    loading.value = false
  }
}

// Tab切换处理
const handleTabChange = (tab) => {
  queryParams.cooperation_type = tab
  queryParams.page = 1
  // 切换Tab时清空媒体类型筛选（媒体类型筛选只在流量采买时显示）
  if (tab !== 'traffic') {
    queryParams.media_type = ''
  }
  getList()
}

// 查询操作
const handleQuery = () => {
  queryParams.page = 1
  getList()
}

// 重置查询
const resetQuery = () => {
  queryParams.name = ''
  queryParams.user_id = ''
  queryParams.audit_status = ''
  queryParams.media_type = ''
  handleQuery()
}

// 分页操作
const handleSizeChange = (val) => {
  queryParams.page_size = val
  getList()
}

const handleCurrentChange = (val) => {
  queryParams.page = val
  getList()
}

// 类型显示
const getTypeText = (types) => {
  if (!types || !types.length) return ''
  return types.map(type => mediaTypeOptions.find(opt => opt.value === type)?.label || type).join('、')
}

// 状态显示
const getStatusType = (status) => {
  const statusMap = {
    0: 'info',
    1: 'success',
    2: 'danger'
  }
  return statusMap[status]
}

const getStatusText = (status) => {
  const statusMap = {
    0: '待审核',
    1: '正常',
    2: '禁用'
  }
  return statusMap[status]
}

// 表单默认值
const defaultForm = {
  name: '',
  types: [],
  industry: '',
  custom_industry: '',
  cooperation_type: '',
  return_rate: 100,
  account: '',
  password: '',
  confirm_password: '',
  daily_activity: '',
  transaction_volume: '',
  region_codes: [], // 添加地区编码列表
  company_name: '',
  company_address: '',
  contact_name: '',
  contact_phone: '',
  remark: '',
  ad_agent_id: '',
  platform_config: {
    platform: 'denghuoplus',
    info: {
      pid: '',
      token: ''
    }
  }
}

// 表单数据
const dialogForm = reactive({...defaultForm})

// 表单验证规则
const dialogRules = reactive({
  name: [{ required: true, message: '请输入媒体名称', trigger: 'blur' }],
  types: [{ required: true, message: '请选择媒体类型', trigger: 'change' }],
  industry: [{ required: true, message: '请选择所属行业', trigger: 'change' }],
  cooperation_type: [{ required: true, message: '请选择合作类型', trigger: 'change' }],
  ad_agent_id: [{ required: false, message: '请选择代理', trigger: 'change' }],
  'platform_config.platform': [
    { 
      required: false, 
      message: '请选择平台类型', 
      trigger: 'change'
    }
  ],
  'platform_config.info.pid': [
    {
      required: true,
      message: '请输入项目ID',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (dialogForm.cooperation_type === 'dh' && !value) {
          callback(new Error('请输入项目ID'))
        } else {
          callback()
        }
      }
    }
  ],
  'platform_config.info.token': [
    {
      required: true,
      message: '请输入API Token',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (dialogForm.cooperation_type === 'dh' && !value) {
          callback(new Error('请输入API Token'))
        } else {
          callback()
        }
      }
    }
  ]
})

// 监听合作类型变化
watch(() => dialogForm.cooperation_type, (newType) => {
  if (newType === 'dh' && !dialogForm.platform_config?.info) {
    // 只在没有现有配置时才设置默认值
    dialogForm.platform_config = {
      platform: 'denghuoplus',
      info: {
        pid: '',
        token: ''
      }
    }
  } else if (newType !== 'dh') {
    // 清空平台配置
    dialogForm.platform_config = {
      platform: '',
      info: {
        pid: '',
        token: ''
      }
    }
  }
})

// 处理表单提交
const handleDialogConfirm = async () => {
  if (!dialogFormRef.value) return
  
  await dialogFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true
        const submitData = { ...dialogForm }
        
        // 处理代理ID，如果为空字符串则设置为null
        if (!submitData.ad_agent_id) {
          submitData.ad_agent_id = null
        }
        
        // 处理平台配置
        if (submitData.cooperation_type === 'dh') {
          if (!submitData.platform_config) {
            submitData.platform_config = {
              platform: 'denghuoplus',
              info: {
                pid: '',
                token: ''
              }
            }
          }
        } else {
          submitData.platform_config = null
        }
        
        if (dialogType.value === 'add') {
          await createMedia(submitData)
          ElMessage.success('创建成功')
        } else {
          await updateMedia(submitData)
          ElMessage.success('更新成功')
        }
        
        dialogVisible.value = false
        getList()
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error(error.message || '操作失败')
      } finally {
        loading.value = false
      }
    }
  })
}

// 重置表单
const resetDialogForm = () => {
  Object.assign(dialogForm, {
    id: '',
    name: '',
    types: [],
    industry: '',
    custom_industry: '',
    cooperation_type: '',
    return_rate: 100,
    account: '',
    password: '',
    confirm_password: '',
    daily_activity: '',
    transaction_volume: '',
    region_codes: [], // 添加地区编码列表
    company_name: '',
    company_address: '',
    contact_name: '',
    contact_phone: '',
    remark: '',
    ad_agent_id: '',
    platform_config: {
      platform: 'denghuoplus',
      info: {
        pid: '',
        token: ''
      }
    }
  })
}

// 新增/编辑对话框
const dialogVisible = ref(false)
const dialogTitle = ref('')
const dialogFormRef = ref()
const dialogType = ref('add')
const showMoreInfo = ref(false)

const handleAdd = () => {
  dialogTitle.value = '新增媒体'
  dialogType.value = 'add'
  resetDialogForm()
  showMoreInfo.value = false
  dialogVisible.value = true
}

const handleUpdate = (row) => {
  dialogTitle.value = '编辑媒体'
  dialogType.value = 'edit'
  const formData = JSON.parse(JSON.stringify(row))
  
  // 处理代理ID，如果为0则设置为空
  if (formData.ad_agent_id === 0) {
    formData.ad_agent_id = ''
  }
  
  // 确保平台配置字段存在并且正确初始化
  if (formData.cooperation_type === 'dh') {
    formData.platform_config = formData.platform_config || {
      platform: 'denghuoplus',
      info: {
        pid: '',
        token: ''
      }
    }
  }
  
  // 检查是否有可选信息，如果有则展开
  const hasOptionalInfo = formData.daily_activity || formData.transaction_volume || 
                          formData.company_name || formData.company_address || 
                          formData.contact_name || formData.contact_phone || 
                          formData.remark
  showMoreInfo.value = !!hasOptionalInfo
  
  // 重置表单后再填充数据
  resetDialogForm()
  Object.assign(dialogForm, formData)
  dialogVisible.value = true
}

const handleDialogClose = () => {
  dialogFormRef.value?.resetFields()
  resetDialogForm()
  showMoreInfo.value = false
}

// 审核对话框
const auditDialogVisible = ref(false)
const auditForm = ref({})
const auditFormRef = ref()

const auditRules = {
  audit_status: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  reject_reason: [
    { required: true, message: '请输入拒绝原因', trigger: 'change' }
  ]
}

const handleAudit = (row) => {
  auditForm.value = {
    id: row.id,
    audit_status: 'pending',
    reject_reason: '',
    remark: ''
  }
  auditDialogVisible.value = true
}

const handleAuditDialogClose = () => {
  auditFormRef.value?.resetFields()
}

const handleAuditConfirm = () => {
  auditFormRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        await auditMedia(auditForm.value)
        ElMessage.success('审核成功')
        auditDialogVisible.value = false
        getList()
      } catch (error) {
        console.error('审核失败:', error)
      }
    }
  })
}

// 枚举选项
const mediaTypeOptions = [
  { label: '微信', value: 'wechat' },
  { label: '微信插件', value: 'wechat_plugin' },
  { label: '支付宝', value: 'alipay' },
  { label: '抖音', value: 'douyin' },
  { label: '小红书', value: 'xiaohongshu' },
  { label: 'B站', value: 'bilibili' },
  { label: '快手', value: 'kuaishou' },
  { label: '其他', value: 'other' }
]

const industryOptions = [
  { label: '支付', value: 'payment' },
  { label: '快递', value: 'express' },
  { label: '开票', value: 'invoice' },
  { label: '停车', value: 'parking' },
  { label: '充电', value: 'charging' },
  { label: '设备', value: 'device' },
  { label: '投放', value: 'delivery' },
  { label: '其他', value: 'other' }
]

const cooperationTypeOptions = [
  { label: 'CPS合作', value: 'cps' },
  { label: '流量采买', value: 'traffic' },
  { label: '灯火投放', value: 'dh' },
  { label: '其他投放', value: 'other_delivery' }
]

const auditStatusOptions = [
  { label: '待审核', value: 'pending' },
  { label: '已通过', value: 'approved' },
  { label: '已拒绝', value: 'rejected' }
]

const cooperationStatusOptions = [
  { label: '未开始', value: 'not_started' },
  { label: '合作中', value: 'active' },
  { label: '已终止', value: 'terminated' }
]

// 获取枚举文本
const getIndustryText = (industry) => {
  return industryOptions.find(opt => opt.value === industry)?.label || industry
}

const getCooperationTypeText = (type) => {
  return cooperationTypeOptions.find(opt => opt.value === type)?.label || type
}

const getAuditStatusType = (status) => {
  const statusMap = {
    pending: 'info',
    approved: 'success',
    rejected: 'danger'
  }
  return statusMap[status]
}

const getAuditStatusText = (status) => {
  return auditStatusOptions.find(opt => opt.value === status)?.label || status
}

const getCooperationStatusType = (status) => {
  const statusMap = {
    not_started: 'info',
    active: 'success',
    terminated: 'danger'
  }
  return statusMap[status]
}

const getCooperationStatusText = (status) => {
  return cooperationStatusOptions.find(opt => opt.value === status)?.label || status
}

// 判断是否为插件媒体
const isPluginMedia = (media) => {
  if (!media.types) return false
  
  let types = []
  
  // 处理不同的数据格式
  if (Array.isArray(media.types)) {
    types = media.types
  } else if (typeof media.types === 'string') {
    try {
      // 尝试解析为JSON数组
      types = JSON.parse(media.types)
    } catch (e) {
      // 如果不是JSON格式，可能是逗号分隔的字符串
      types = media.types.split(',').map(t => t.trim())
    }
  }
  
  // 检查是否包含微信插件类型
  return types.includes('wechat_plugin')
}

// 查看插件数据
const handleViewPluginData = (media) => {
  console.log('准备跳转到插件数据看板:', media.id)
  console.log('媒体数据:', media)
  
  // 跳转到插件数据看板页面
  router.push({
    name: 'PluginDashboard',
    params: { mediaId: media.id },
    query: { mediaName: media.name }
  }).catch(error => {
    console.error('路由跳转失败:', error)
    ElMessage.error('页面跳转失败')
  })
}

// 添加移动端相关逻辑
const isMobile = computed(() => {
  return window.innerWidth <= 768
})

const hasMore = computed(() => {
  return tableData.value.length < total.value
})

const loadMore = () => {
  queryParams.page++
  getList()
}

// 监听窗口大小变化
window.addEventListener('resize', () => {
  isMobile.value = window.innerWidth <= 768
})

// 初始化
onMounted(async () => {
  if (!isMediaRole.value) {
    await getMediaUsers()
  }
  await getAgents()
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background: #f8fafc;
  min-height: calc(100vh - 50px);
  
  .filter-container {
    margin-bottom: 20px;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .filter-header {
      padding: 24px;
      
      .filter-form {
        .search-form {
          .form-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 20px;

            .form-item {
              flex: 1;
              min-width: 240px;
              margin-bottom: 0;

              :deep(.el-form-item__label) {
                font-weight: 500;
                color: #374151;
                font-size: 14px;
              }

              .search-input {
                border-radius: 8px;
                
                :deep(.el-input__wrapper) {
                  border-radius: 8px;
                  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
                  transition: all 0.2s;
                  
                  &:hover {
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                  }
                }
              }

              .search-select {
                width: 100%;
                
                :deep(.el-select__wrapper) {
                  border-radius: 8px;
                  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
                  transition: all 0.2s;
                  
                  &:hover {
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                  }
                }
              }
            }

            @media screen and (max-width: 768px) {
              flex-direction: column;
              gap: 16px;

              .form-item {
                min-width: 100%;
              }
            }
          }

          .form-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-start;

            .search-btn, .reset-btn {
              border-radius: 8px;
              font-weight: 500;
              padding: 10px 20px;
              display: flex;
              align-items: center;
              gap: 6px;
              transition: all 0.2s;
              
              .el-icon {
                font-size: 16px;
              }
            }

            .search-btn {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              border: none;
              box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
              
              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
              }
            }

            .reset-btn {
              background: #f8fafc;
              color: #64748b;
              border: 1px solid #e2e8f0;
              
              &:hover {
                background: #f1f5f9;
                border-color: #cbd5e1;
                transform: translateY(-1px);
              }
            }

            @media screen and (max-width: 768px) {
              flex-direction: column;
              
              .search-btn, .reset-btn {
                width: 100%;
                justify-content: center;
              }
            }
          }
        }

        .action-buttons {
          margin-top: 20px;
          display: flex;
          justify-content: flex-end;

          .add-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border: none;
            border-radius: 8px;
            font-weight: 500;
            padding: 12px 24px;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
            transition: all 0.2s;
            
            .el-icon {
              font-size: 16px;
            }

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
            }
          }

          @media screen and (max-width: 768px) {
            justify-content: center;
            margin-top: 16px;
            
            .add-btn {
              width: 100%;
              justify-content: center;
            }
          }
        }

        @media screen and (max-width: 768px) {
          .action-buttons {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
          }
        }
      }
    }

    .cooperation-tabs {
      border-top: 1px solid #e2e8f0;
      
      :deep(.el-tabs__header) {
        margin: 0;
        background: #f8fafc;
        padding: 0 24px;
        border-radius: 0 0 12px 12px;
      }

      :deep(.el-tabs__nav-wrap) {
        &::after {
          display: none;
        }
      }

      :deep(.el-tabs__item) {
        font-weight: 500;
        color: #64748b;
        padding: 0 20px;
        height: 48px;
        line-height: 48px;
        transition: all 0.2s;
        
        &.is-active {
          color: #3b82f6;
          background: rgba(59, 130, 246, 0.05);
          border-radius: 8px 8px 0 0;
        }
        
        &:hover:not(.is-active) {
          color: #475569;
          background: rgba(0, 0, 0, 0.02);
          border-radius: 8px 8px 0 0;
        }
      }

      :deep(.el-tabs__active-bar) {
        background: #3b82f6;
        height: 3px;
        border-radius: 2px;
      }
    }
  }
  
  .table-container {
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    :deep(.el-table) {
      border-radius: 12px;
      overflow: hidden;
      
      .el-table__header {
        background: #f8fafc;
        
        th {
          background: #f8fafc;
          color: #374151;
          font-weight: 600;
          border-bottom: 2px solid #e2e8f0;
        }
      }

      .el-table__body {
        tr {
          transition: all 0.2s;
          
          &:hover {
            background: #f8fafc;
          }
        }
        
        td {
          border-bottom: 1px solid #f1f5f9;
        }
      }
    }

    .pagination-container {
      display: flex;
      justify-content: center;
      margin: 20px 0;
      padding: 20px;
      border-top: 1px solid #f1f5f9;
      
      :deep(.el-pagination) {
        .el-pager li {
          border-radius: 6px;
          margin: 0 2px;
          
          &.is-active {
            background: #3b82f6;
            color: #ffffff;
          }
        }
        
        .btn-prev, .btn-next {
          border-radius: 6px;
        }
      }
    }
  }

  .mobile-list {
    .card-list {
      .media-card {
        margin-bottom: 16px;

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          .media-name {
            font-size: 16px;
            font-weight: bold;
          }
        }

        .card-content {
          .info-item {
            display: flex;
            margin-bottom: 12px;

            .label {
              color: #606266;
              font-size: 14px;
              margin-right: 8px;
            }

            .value {
              flex: 1;
            }
          }
        }

        .card-footer {
          display: flex;
          justify-content: flex-end;
          margin-top: 16px;
          padding-top: 16px;
          border-top: 1px solid #EBEEF5;

          .el-button {
            margin-left: 12px;
          }
        }
      }
    }

    .load-more {
      text-align: center;
      margin: 20px 0;

      .no-more {
        color: #909399;
        font-size: 14px;
      }
    }
  }
}

// 对话框样式
.media-dialog {
  :deep(.el-dialog) {
    border-radius: 16px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 95vw;
  }

  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    padding: 20px 24px;
    border-radius: 16px 16px 0 0;
    margin: 0;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }
    
    .el-dialog__headerbtn {
      top: 20px;
      right: 24px;
      
      .el-dialog__close {
        color: #ffffff;
        font-size: 18px;
        
        &:hover {
          color: #f1f5f9;
        }
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 0;
    max-height: 65vh;
    overflow-y: auto;
    
    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 3px;
      
      &:hover {
        background: #94a3b8;
      }
    }
  }

  :deep(.el-dialog__footer) {
    padding: 20px 24px;
    background: #f8fafc;
    border-radius: 0 0 16px 16px;
    border-top: 1px solid #e2e8f0;
    text-align: right;
    
    .el-button {
      border-radius: 8px;
      font-weight: 500;
      padding: 10px 20px;
      margin-left: 12px;
      font-size: 14px;
      
      &.el-button--primary {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        border: none;
        box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
        }
      }
      
      &.el-button--default {
        background: #ffffff;
        color: #64748b;
        border: 1px solid #e2e8f0;
        
        &:hover {
          background: #f8fafc;
          border-color: #cbd5e1;
          transform: translateY(-1px);
        }
      }
    }
  }

  // 移动端适配
  @media screen and (max-width: 768px) {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 0;
    }
    
    :deep(.el-dialog__header) {
      padding: 20px 24px;
      
      .el-dialog__title {
        font-size: 18px;
      }
      
      .el-dialog__headerbtn {
        top: 20px;
        right: 24px;
        
        .el-dialog__close {
          font-size: 18px;
        }
      }
    }
    
    :deep(.el-dialog__footer) {
      padding: 20px 24px;
      
      .el-button {
        padding: 10px 20px;
        font-size: 14px;
        margin-left: 12px;
      }
    }
  }
}

.media-form {
  padding: 24px;
  
  .form-section {
    margin-bottom: 28px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #374151;
      margin-bottom: 18px;
      padding-bottom: 8px;
      border-bottom: 2px solid #e2e8f0;
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 40px;
        height: 2px;
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        border-radius: 1px;
      }
    }
    
    .form-row {
      display: flex;
      gap: 20px;
      margin-bottom: 18px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .form-item-full {
        flex: 1;
      }
      
      .form-item-half {
        flex: 1;
        min-width: 0;
      }
      
      :deep(.el-form-item) {
        margin-bottom: 0;
        
        .el-form-item__label {
          font-weight: 500;
          color: #374151;
          font-size: 14px;
          line-height: 1.5;
        }
        
        .el-form-item__error {
          font-size: 12px;
          color: #ef4444;
          padding-top: 4px;
        }
      }
      
      @media screen and (max-width: 1024px) {
        flex-direction: column;
        gap: 16px;
        
        .form-item-half {
          flex: none;
        }
      }
    }
  }
  
  .form-input {
    border-radius: 8px;
    
    :deep(.el-input__wrapper) {
      border-radius: 8px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      transition: all 0.2s;
      
      &:hover {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      
      &.is-focus {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        border-color: #3b82f6;
      }
    }
  }
  
  .form-select {
    width: 100%;
    
    :deep(.el-select__wrapper) {
      border-radius: 8px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      transition: all 0.2s;
      
      &:hover {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      
      &.is-focus {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        border-color: #3b82f6;
      }
    }
  }
  
  .form-number {
    width: 100%;
    
    :deep(.el-input-number) {
      width: 100%;
      
      .el-input__wrapper {
        border-radius: 8px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        transition: all 0.2s;
        
        &:hover {
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        &.is-focus {
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          border-color: #3b82f6;
        }
      }
    }
  }
  
  .form-textarea {
    :deep(.el-textarea__inner) {
      border-radius: 8px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      transition: all 0.2s;
      resize: vertical;
      
      &:hover {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      
      &:focus {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        border-color: #3b82f6;
      }
    }
  }
  
  .cps-collapse {
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    background: #f8fafc;
    
    :deep(.el-collapse-item) {
      .el-collapse-item__header {
        background: transparent;
        font-weight: 500;
        color: #374151;
        border: none;
        padding: 16px 20px;
        
        &:hover {
          background: rgba(59, 130, 246, 0.05);
        }
      }
      
      .el-collapse-item__content {
        padding: 0 20px 20px;
        border: none;
        background: #ffffff;
        border-radius: 0 0 8px 8px;
      }
    }
  }

  // 展开按钮样式
  .expand-btn {
    width: 100%;
    padding: 14px 0;
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
    border: 2px dashed #e2e8f0;
    border-radius: 8px;
    background: #f8fafc;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin: 0;
    
    &:hover {
      color: #3b82f6;
      border-color: #3b82f6;
      background: rgba(59, 130, 246, 0.05);
      transform: translateY(-1px);
    }
    
    .el-icon {
      font-size: 16px;
      transition: transform 0.2s;
    }
    
    &:hover .el-icon {
      transform: scale(1.1);
    }
  }
}

.w-full {
  width: 100%;
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}

.platform-config {
  :deep(.el-form-item__content) {
    overflow: visible;
  }
  
  .platform-config-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      border-color: #409EFF;
    }
    
    .platform-config-text {
      flex: 1;
      
      span {
        margin-right: 16px;
        
        &:last-child {
          margin-right: 0;
        }
      }
      
      .placeholder {
        color: #909399;
      }
    }
    
    .platform-config-arrow {
      color: #909399;
      transition: transform 0.3s;
    }
  }
}
</style> 