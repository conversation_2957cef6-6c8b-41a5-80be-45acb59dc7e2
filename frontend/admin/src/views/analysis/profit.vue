<template>
  <div class="app-container">
    <!-- 筛选区域 -->
    <el-card class="filter-container" shadow="hover">
      <el-form :model="queryParams" class="filter-form" v-loading="loading">
        <!-- 时间范围行 -->
        <el-row :gutter="16" class="time-range-row">
          <el-col :span="24">
            <el-form-item label="时间范围" class="time-range-item">
              <div class="time-range-content">
                <el-radio-group v-model="queryParams.timeRange" class="filter-radio-group">
                  <el-radio-button label="实时">实时</el-radio-button>
                  <el-radio-button label="昨日">昨日</el-radio-button>
                  <el-radio-button label="近7天">近7天</el-radio-button>
                  <el-radio-button label="近15天">近15天</el-radio-button>
                  <el-radio-button label="近30天">近30天</el-radio-button>
                  <el-radio-button label="自定义">自定义</el-radio-button>
                </el-radio-group>
                
                <el-date-picker
                  v-if="queryParams.timeRange === '自定义'"
                  v-model="queryParams.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :disabled-date="disabledDate"
                  @change="handleDateRangeChange"
                  class="date-picker"
                />
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 类型筛选行 -->
        <el-row :gutter="16" class="type-filter-row">
          <el-col :span="24">
            <el-form-item label="投放类型" class="type-filter-item">
              <div class="type-filter-content">
                <el-radio-group v-model="queryParams.type" class="filter-radio-group">
                  <el-radio-button label="1">普通投放</el-radio-button>
                  <el-radio-button label="3">灯火</el-radio-button>
                </el-radio-group>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 其他筛选条件行 -->
        <el-row :gutter="16" class="filter-row">
          <el-col :xs="24" :sm="24" :md="8" :lg="6" :xl="6" v-if="!isMediaRole">
            <el-form-item label="媒介" class="filter-item">
              <el-select 
                v-model="queryParams.media" 
                placeholder="请选择媒介" 
                clearable
                class="filter-select"
                @change="handleMediaChange"
              >
                <el-option 
                  v-for="item in filterOptions.userList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="24" :md="8" :lg="6" :xl="6">
            <el-form-item label="媒体账号" class="filter-item">
              <el-select 
                v-model="queryParams.mediaAccount" 
                placeholder="请选择媒体账号" 
                clearable
                class="filter-select"
                :disabled="!queryParams.media && filterOptions.showMediaList"
                @change="handleMediaAccountChange"
              >
                <el-option 
                  v-for="item in currentMediaAccounts"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="24" :md="8" :lg="6" :xl="6">
            <el-form-item label="投放计划" class="filter-item">
              <el-select 
                v-model="queryParams.plan" 
                placeholder="请选择投放计划" 
                clearable
                class="filter-select"
                :disabled="!queryParams.mediaAccount"
              >
                <el-option 
                  v-for="item in currentPlans"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 按钮行 -->
        <el-row>
          <el-col :span="24" class="filter-buttons">
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="handleRefresh">刷新</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 图表区域 -->
    <el-card class="chart-container" shadow="hover">
      <div class="chart-wrapper">
        <div ref="chartRef" class="chart"></div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, onUnmounted, watch } from 'vue'
import { useUserStore } from '@/store/modules/user'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'
import { getUserList, getMediaList, getPlans, getProducts } from '@/api/dashboard'
import { getProfitData } from '@/api/analysis'

// 用户store
const userStore = useUserStore()
const isMediaRole = computed(() => userStore.isMedia)

// 加载状态
const loading = ref(false)

// 格式化日期
const formatDate = (date) => {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 筛选选项数据
const filterOptions = ref({
  showMediaList: false,
  mediaList: [], // 媒体列表
  userList: [] // 媒介(用户)列表
})

// 查询参数
const queryParams = reactive({
  category: 'alipay', // 默认支付宝
  timeRange: '实时',
  dateRange: null,
  startDate: formatDate(new Date()),
  endDate: formatDate(new Date()),
  type: '1',
  media: '',
  mediaAccount: '',
  plan: ''
})

// 当前选中媒介的账号列表
const currentMediaAccounts = ref([])

// 当前选中账号的计划列表 
const currentPlans = ref([])

// 图表实例
let chart = null
const chartRef = ref(null)

// 获取筛选选项数据
const loadFilterOptions = async () => {
  try {
    loading.value = true
    console.log('开始加载筛选选项...')
    
    // 从 store 获取用户信息
    const userStore = useUserStore()
    const isMediaUser = userStore.isMedia
    
    filterOptions.value = {
      showMediaList: !isMediaUser, // 直接根据用户角色判断
      mediaList: [],
      userList: [] // 初始化用户列表
    }

    // 如果是媒介用户,直接设置当前用户
    if (isMediaUser) {
      queryParams.media = userStore.id.toString()
      await loadCurrentUserMediaAccounts()
    } else {
      // 不是媒介用户才加载媒介列表
      await loadUserList()
      // 清空媒介相关的选择
      queryParams.media = ''
      queryParams.mediaAccount = ''
      currentMediaAccounts.value = []
    }
    
    console.log('处理后的 filterOptions:', filterOptions.value)
  } catch (err) {
    console.error('获取筛选选项失败:', err)
    ElMessage.error('获取筛选选项失败')
  } finally {
    loading.value = false
  }
}

// 获取当前用户的媒体账号
const loadCurrentUserMediaAccounts = async () => {
  try {
    // 从 store 获取用户信息
    const userStore = useUserStore()
    const isMediaUser = userStore.isMedia
    
    if (isMediaUser) {
      // 如果是媒介用户,直接使用当前用户信息
      currentMediaAccounts.value = [{
        id: userStore.id,
        name: userStore.name
      }]
      
      // 如果有账号，默认选中第一个
      if (currentMediaAccounts.value.length > 0) {
        queryParams.mediaAccount = currentMediaAccounts.value[0].id
        // 加载选中账号的计划列表
        await handleMediaAccountChange(queryParams.mediaAccount)
      }
    } else {
      // 不是媒介用户才调用接口获取媒体列表
      const res = await getMediaList({
        type: queryParams.type
      })
      if (res.code !== 0 || !Array.isArray(res.data?.list)) {
        throw new Error(res.message || '获取媒体列表失败')
      }
      
      // 设置媒体账号列表
      currentMediaAccounts.value = res.data.list.map(media => ({
        id: media.id,
        name: media.name
      }))
      
      // 如果有账号，默认选中第一个
      if (currentMediaAccounts.value.length > 0) {
        queryParams.mediaAccount = currentMediaAccounts.value[0].id
        // 加载选中账号的计划列表
        await handleMediaAccountChange(queryParams.mediaAccount)
      }
    }
  } catch (err) {
    console.error('获取当前用户媒体账号失败:', err)
    ElMessage.error('获取当前用户媒体账号失败')
    currentMediaAccounts.value = []
  }
}

// 获取媒介(用户)列表
const loadUserList = async () => {
  try {
    // 调用获取用户列表接口,role=1表示媒介角色
    const res = await getUserList({ role: 1 })
    if (res.code === 0 && Array.isArray(res.data?.list)) {
      filterOptions.value.userList = res.data.list.map(user => ({
        id: user.id,
        name: user.realName || user.username
      }))
    }
  } catch (err) {
    console.error('获取媒介列表失败:', err)
    ElMessage.error('获取媒介列表失败')
  }
}

// 获取媒体账号列表
const loadMediaAccounts = async (userId) => {
  try {
    console.log('开始加载媒体账号,userId:', userId)
    
    // 获取媒体列表数据
    const res = await getMediaList({
      category: 'alipay',
      user_id: userId,
      type: queryParams.type
    })
    if (res.code !== 0 || !Array.isArray(res.data?.list)) {
      throw new Error(res.message || '获取媒体列表失败')
    }
    
    // 设置媒体账号列表
    currentMediaAccounts.value = res.data.list.map(media => ({
      id: media.id,
      name: media.name
    }))
    
    console.log('设置后的媒体账号列表:', currentMediaAccounts.value)
  } catch (err) {
    console.error('获取媒体账号列表失败:', err)
    ElMessage.error('获取媒体账号列表失败')
    currentMediaAccounts.value = []
  }
}

// 监听媒介变化
watch(() => queryParams.media, async (newVal) => {
  console.log('选中的媒介变化:', newVal)
  // 清空相关选择
  queryParams.mediaAccount = ''
  queryParams.plan = ''
  currentMediaAccounts.value = []
  currentPlans.value = []
  
  if (!newVal) {
    return
  }
  
  // 获取选中媒介的媒体账号列表
  await loadMediaAccounts(newVal)
})

// 处理媒体账号变化
const handleMediaAccountChange = async (accountId) => {
  queryParams.plan = ''
  if (!accountId) {
    currentPlans.value = []
    return
  }

  try {
    // 获取选中账号的计划列表
    const res = await getPlans({ 
      category: 'alipay',
      media_account_id: accountId 
    })
    if (res.code === 0 && Array.isArray(res.data?.list)) {
      currentPlans.value = res.data.list
    } else {
      currentPlans.value = []
    }
  } catch (err) {
    console.error('获取计划列表失败:', err)
    ElMessage.error('获取计划列表失败')
    currentPlans.value = []
  }
}

// 监听时间范围变化
watch(() => queryParams.timeRange, (newVal) => {
  // 根据选择的时间范围设置日期
  const now = new Date()
  const today = formatDate(now)
  
  switch(newVal) {
    case '实时':
      queryParams.startDate = today
      queryParams.endDate = today
      break
    case '昨日':
      const yesterday = new Date(now)
      yesterday.setDate(yesterday.getDate() - 1)
      const yesterdayStr = formatDate(yesterday)
      queryParams.startDate = yesterdayStr
      queryParams.endDate = yesterdayStr
      break
    case '近7天':
      const sevenDaysAgo = new Date(now)
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
      const yesterdayDate = new Date(now)
      yesterdayDate.setDate(yesterdayDate.getDate() - 1)
      queryParams.startDate = formatDate(sevenDaysAgo)
      queryParams.endDate = formatDate(yesterdayDate)
      break
    case '近15天':
      const fifteenDaysAgo = new Date(now)
      fifteenDaysAgo.setDate(fifteenDaysAgo.getDate() - 15)
      const yesterdayFor15 = new Date(now)
      yesterdayFor15.setDate(yesterdayFor15.getDate() - 1)
      queryParams.startDate = formatDate(fifteenDaysAgo)
      queryParams.endDate = formatDate(yesterdayFor15)
      break
    case '近30天':
      const thirtyDaysAgo = new Date(now)
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      const yesterdayFor30 = new Date(now)
      yesterdayFor30.setDate(yesterdayFor30.getDate() - 1)
      queryParams.startDate = formatDate(thirtyDaysAgo)
      queryParams.endDate = formatDate(yesterdayFor30)
      break
    case '自定义':
      if (queryParams.dateRange) {
        queryParams.startDate = formatDate(queryParams.dateRange[0])
        queryParams.endDate = formatDate(queryParams.dateRange[1])
      } else {
        queryParams.startDate = ''
        queryParams.endDate = ''
      }
      break
  }

  // 只有在非自定义时间范围时才自动更新数据
  if (newVal !== '自定义') {
    handleQuery()
  }
})

// 监听日期范围选择器变化
watch(() => queryParams.dateRange, (newVal) => {
  if (newVal && queryParams.timeRange === '自定义') {
    queryParams.startDate = formatDate(newVal[0])
    queryParams.endDate = formatDate(newVal[1])
    handleQuery()
  }
})

// 禁用日期选择范围
const disabledDate = (date) => {
  if (!queryParams.dateRange) {
    return false
  }
  const [startDate, endDate] = queryParams.dateRange
  const ninetyDaysFromStart = new Date(startDate)
  ninetyDaysFromStart.setDate(ninetyDaysFromStart.getDate() + 90)
  
  if (startDate && !endDate) {
    return date.getTime() < startDate.getTime() || date.getTime() > ninetyDaysFromStart.getTime()
  }
  
  return false
}

// 处理日期范围变化
const handleDateRangeChange = (val) => {
  if (val) {
    const [startDate, endDate] = val
    const diffDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24))
    
    if (diffDays > 90) {
      ElMessage.warning('时间跨度不能超过90天')
      queryParams.dateRange = null
      return
    }
  }
}

// Mock数据生成函数
const generateMockData = () => {
  const startDate = dayjs(queryParams.startDate)
  const endDate = dayjs(queryParams.endDate)
  const days = endDate.diff(startDate, 'day') + 1
  
  const data = []
  for (let i = 0; i < days; i++) {
    const date = startDate.add(i, 'day').format('YYYY-MM-DD')
    data.push({
      date,
      elemeCommission: Math.random() * 1000 + 500,
      feizhuCommission: Math.random() * 800 + 300,
      cost: Math.random() * 1200 + 400
    })
  }
  return data
}

// 初始化图表
const initChart = (data) => {
  const option = {
    title: {
      text: '收益分析',
      left: 'center',
      top: '10px',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333'
      },
      formatter: (params) => {
        const eleme = params.find(p => p.seriesName === '饿了么佣金') || { value: 0 }
        const feizhu = params.find(p => p.seriesName === '飞猪佣金') || { value: 0 }
        const cost = params.find(p => p.seriesName === '成本') || { value: 0 }
        const totalCommission = (eleme.value || 0) + (feizhu.value || 0)
        
        return `<div style="font-weight: bold">${params[0].axisValue}</div>
                饿了么佣金: ¥${(eleme.value || 0).toFixed(2)}<br/>
                飞猪佣金: ¥${(feizhu.value || 0).toFixed(2)}<br/>
                总佣金: ¥${totalCommission.toFixed(2)}<br/>
                成本: ¥${(cost.value || 0).toFixed(2)}<br/>
                利润: ¥${(totalCommission - (cost.value || 0)).toFixed(2)}`
      }
    },
    legend: {
      data: ['饿了么佣金', '飞猪佣金', '成本'],
      bottom: '0%',
      itemWidth: 15,
      itemHeight: 10,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.date),
      axisLine: {
        lineStyle: {
          color: '#ccc'
        }
      },
      axisTick: {
        alignWithLabel: true
      },
      axisLabel: {
        fontSize: 12,
        color: '#666'
      }
    },
    yAxis: {
      type: 'value',
      name: '金额(元)',
      nameTextStyle: {
        fontSize: 12,
        color: '#666'
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#ccc'
        }
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#eee'
        }
      },
      axisLabel: {
        fontSize: 12,
        color: '#666',
        formatter: (value) => {
          return value.toFixed(2)
        }
      }
    },
    series: [
      {
        name: '饿了么佣金',
        type: 'bar',
        stack: 'commission',
        barWidth: '40%',
        emphasis: {
          focus: 'series',
          blurScope: 'coordinateSystem'
        },
        data: data.map(item => item.elm_pre_commission),
        itemStyle: {
          color: '#40A9FF',
          borderRadius: [4, 4, 0, 0]
        },
        label: {
          show: true,
          position: (params) => {
            // 当值较小时显示在外部
            return params.value > 1000 ? 'inside' : 'top'
          },
          formatter: (params) => {
            return params.value.toFixed(2)
          },
          fontSize: 11,
          color: (params) => {
            return params.value > 1000 ? '#fff' : '#666'
          }
        }
      },
      {
        name: '飞猪佣金',
        type: 'bar',
        stack: 'commission',
        barWidth: '40%',
        emphasis: {
          focus: 'series',
          blurScope: 'coordinateSystem'
        },
        data: data.map(item => item.fliggy_pre_commission),
        itemStyle: {
          color: '#FFA940',
          borderRadius: [4, 4, 0, 0]
        },
        label: {
          show: true,
          position: (params) => {
            return params.value > 1000 ? 'inside' : 'top'
          },
          formatter: (params) => {
            return params.value.toFixed(2)
          },
          fontSize: 11,
          color: (params) => {
            return params.value > 1000 ? '#fff' : '#666'
          }
        }
      },
      {
        name: '成本',
        type: 'bar',
        barWidth: '40%',
        emphasis: {
          focus: 'series',
          blurScope: 'coordinateSystem'
        },
        data: data.map(item => item.cost),
        itemStyle: {
          color: '#FF4D4F',
          borderRadius: [4, 4, 4, 4]
        },
        label: {
          show: true,
          position: 'top',
          formatter: (params) => {
            return params.value.toFixed(2)
          },
          fontSize: 11,
          color: '#666',
          distance: 4
        }
      }
    ]
  }
  
  chart = echarts.init(chartRef.value)
  chart.setOption(option)
}

// 更新图表
const updateChart = async () => {
  try {
    loading.value = true
    const res = await getProfitData({
      category: queryParams.category,
      start_date: queryParams.startDate,
      end_date: queryParams.endDate,
      type: queryParams.type,
      user_id: queryParams.media,
      media_id: queryParams.mediaAccount,
      plan_id: queryParams.plan
    })
    if (res.code === 0 && Array.isArray(res.data?.list)) {
      initChart(res.data.list)
    } else {
      ElMessage.error('获取数据失败')
    }
  } catch (err) {
    console.error('获取数据失败:', err)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 查询操作
const handleQuery = () => {
  updateChart()
}

// 重置查询
const handleReset = () => {
  queryParams.category = 'alipay' // 重置为支付宝
  queryParams.timeRange = '实时'
  queryParams.dateRange = null
  queryParams.startDate = formatDate(new Date())
  queryParams.endDate = formatDate(new Date())
  queryParams.type = '1'
  queryParams.media = ''
  queryParams.mediaAccount = ''
  queryParams.plan = ''
  handleQuery()
}

// 刷新数据
const handleRefresh = () => {
  handleQuery()
}

// 处理媒介变化
const handleMediaChange = async (newVal) => {
  console.log('选中的媒介变化:', newVal)
  // 清空相关选择
  queryParams.mediaAccount = ''
  queryParams.plan = ''
  currentMediaAccounts.value = []
  currentPlans.value = []
  
  if (!newVal) {
    return
  }
  
  // 获取选中媒介的媒体账号列表
  await loadMediaAccounts(newVal)
}

// 响应式处理
const isMobile = computed(() => {
  return window.innerWidth <= 768
})

const handleResize = () => {
  if (chart) {
    chart.resize()
  }
}

// 监听窗口大小变化
window.addEventListener('resize', handleResize)

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (chart) {
    chart.dispose()
  }
})

// 初始化
onMounted(async () => {
  await loadFilterOptions()
  handleQuery()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  
  .filter-container {
    margin-bottom: 20px;

    .filter-form {
      .time-range-row,
      .type-filter-row {
        margin-bottom: 16px;
        
        .time-range-item,
        .type-filter-item {
          margin-bottom: 0;
          
          :deep(.el-form-item__content) {
            flex-wrap: nowrap;
          }
          
          .time-range-content,
          .type-filter-content {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
          }
        }
      }
      
      .filter-row {
        margin-bottom: 16px;
      }
      
      :deep(.el-form-item) {
        margin-bottom: 16px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .el-form-item__label {
          padding-right: 12px;
          min-width: 70px;
        }
        
        .el-form-item__content {
          flex: 1;
          min-width: 0;
        }
      }
      
      .filter-select {
        width: 100%;
      }
      
      .filter-radio-group {
        :deep(.el-radio-button__inner) {
          padding: 8px 15px;
        }
      }
      
      .date-picker {
        width: 260px;
      }
      
      .filter-buttons {
        display: flex;
        justify-content: center;
        gap: 16px;
        margin-top: 8px;
      }
    }
  }

  .chart-container {
    .chart-wrapper {
      .chart {
        width: 100%;
        height: 500px;
      }
    }
  }
}

// 移动端适配
@media screen and (max-width: 768px) {
  .app-container {
    padding: 8px;
    
    .filter-container {
      .filter-form {
        .time-range-row {
          .time-range-content {
            flex-direction: column;
            align-items: stretch;
            gap: 8px;
          }
          
          .filter-radio-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            
            :deep(.el-radio-button) {
              margin-right: 0;
              
              .el-radio-button__inner {
                border-radius: 4px;
                border: 1px solid #DCDFE6;
              }
              
              &:first-child .el-radio-button__inner {
                border-radius: 4px;
              }
              
              &:last-child .el-radio-button__inner {
                border-radius: 4px;
              }
            }
          }
          
          .date-picker {
            width: 100%;
          }
        }
        
        .filter-row {
          margin-bottom: 8px;
          
          .filter-item {
            margin-bottom: 12px;
          }
        }
        
        :deep(.el-form-item) {
          margin-bottom: 12px;
          
          .el-form-item__label {
            min-width: 60px;
          }
        }
        
        .filter-buttons {
          flex-direction: column;
          gap: 8px;
          
          .el-button {
            width: 100%;
          }
        }
      }
    }
  }
}

.w-full {
  width: 100%;
}
</style> 