<template>
  <div class="ad-slot-plan-view">
    <!-- 基本信息部分 -->
    <el-card class="mb-4">
      <template #header>
        <div class="card-header">
          <el-button link @click="handleBack">
            <el-icon><arrow-left /></el-icon>
            返回
          </el-button>
          <span class="title">基本信息</span>
          <el-button type="primary" link v-if="detail?.is_editable" @click="handleEdit">编辑</el-button>
        </div>
      </template>
      <el-descriptions :column="isMobile ? 1 : 3" border size="small">
        <el-descriptions-item label="计划编号" :span="1">
          {{ detail?.code }}
        </el-descriptions-item>
        <el-descriptions-item label="计划类型" :span="1">
          <el-tag :type="detail?.type === PLAN_TYPE.TEST ? 'warning' : 'success'" size="small">
            {{ PLAN_TYPE_MAP[detail?.type] }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="投放产品" :span="1">
          <el-tag type="info">{{ detail?.ad_product?.name }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="广告位" :span="1">
          <div class="flex items-center" style="gap: 16px;">
            <span>{{ detail?.ad_slot?.name }}</span>
            <el-tag size="small" type="info">{{ AD_TYPE_MAP[detail?.ad_slot?.type] }}</el-tag>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="所属媒体" :span="1">
          {{ detail?.ad_slot?.mediaName }}
        </el-descriptions-item>
        <el-descriptions-item label="投放时间" :span="1">
          <div>开始：{{ formatDateTime(detail?.start_date) }}</div>
          <div v-if="detail?.is_end_date_enabled" class="mt-1">
            结束：{{ formatDateTime(detail?.end_date) }}
          </div>
          <div v-else class="text-gray-500 mt-1">未启用结束时间</div>
        </el-descriptions-item>
        <el-descriptions-item label="审核状态" :span="1">
          <el-tag :type="getStatusTagType(detail?.audit_status)">
            {{ AUDIT_STATUS_MAP[detail?.audit_status] }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="投放状态" :span="1">
          <el-tag :type="getStatusTagType(detail?.delivery_status)">
            {{ DELIVERY_STATUS_MAP[detail?.delivery_status] }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="3">
          {{ detail?.remark || '-' }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 推广信息部分 -->
    <el-card v-if="showPromotionInfo" class="mb-4">
      <template #header>
        <div class="card-header">
          <span>推广信息</span>
          <el-button 
            v-if="isMergeProduct" 
            type="primary" 
            link 
            @click="handleGenerateMergeLink"
          >
            生成融合链接
          </el-button>
        </div>
      </template>
      <el-descriptions :column="1" border size="small">
        <el-descriptions-item label="推广链接">
          <div class="link-cell">
            <template v-if="detail?.promotion_link">
              <div class="link-content">
                <el-icon><link /></el-icon>
                <el-link type="primary" :href="detail?.promotion_link" target="_blank" class="link-text">
                  {{ detail?.promotion_link }}
                </el-link>
              </div>
              <div class="link-actions">
                <el-button type="primary" link @click="handleCopy(detail?.promotion_link)">
                  <el-icon><document-copy /></el-icon>
                  复制链接
                </el-button>
                <el-button type="primary" link @click="handleEditPromotionLink" v-if="canEditPromotionLink">
                  <el-icon><edit /></el-icon>
                  编辑
                </el-button>
              </div>
            </template>
            <template v-else>
              <span class="text-gray-500">暂无推广链接</span>
              <el-button type="primary" link @click="handleEditPromotionLink" v-if="canEditPromotionLink">
                添加链接
              </el-button>
            </template>
          </div>
        </el-descriptions-item>
        <el-descriptions-item v-if="isMergeProduct" label="Universal Link">
          <div class="link-cell">
            <div class="link-content">
              <el-icon><link /></el-icon>
              <el-link type="primary" :href="universalLink" target="_blank" class="link-text">
                {{ universalLink }}
              </el-link>
            </div>
            <div class="link-actions">
              <el-button type="primary" link @click="handleCopy(universalLink)">
                <el-icon><document-copy /></el-icon>
                复制链接
              </el-button>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item v-if="isMergeProduct" label="H5链接">
          <div class="link-cell">
            <div class="link-content">
              <el-icon><link /></el-icon>
              <el-link type="primary" :href="baseTargetUrl" target="_blank" class="link-text">
                {{ baseTargetUrl }}
              </el-link>
            </div>
            <div class="link-actions">
              <el-button type="primary" link @click="handleCopy(baseTargetUrl)">
                <el-icon><document-copy /></el-icon>
                复制链接
              </el-button>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item v-if="isMergeProduct" label="支付宝小程序APPID">
          <div class="link-cell">
            <div class="link-content">
              <span>2018081461095002</span>
            </div>
            <div class="link-actions">
              <el-button type="primary" link @click="handleCopy('2018081461095002')">
                <el-icon><document-copy /></el-icon>
                复制
              </el-button>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item v-if="isMergeProduct" label="支付宝小程序PATH">
          <div class="link-cell">
            <div class="link-content">
              <span>{{ decodedPath }}</span>
            </div>
            <div class="link-actions">
              <el-button type="primary" link @click="handleCopy(decodedPath)">
                <el-icon><document-copy /></el-icon>
                复制
              </el-button>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="短链接">
          <div class="link-cell">
            <template v-if="detail?.short_url">
              <div class="link-content">
                <el-icon><link /></el-icon>
                <el-link type="primary" :href="detail?.short_url" target="_blank" class="link-text">
                  {{ detail?.short_url }}
                </el-link>
              </div>
              <div class="link-actions">
                <el-button type="primary" link @click="handleCopy(detail?.short_url)">
                  <el-icon><document-copy /></el-icon>
                  复制链接
                </el-button>
                <el-button type="primary" link @click="handleEditShortUrl" v-if="canEditShortUrl">
                  <el-icon><edit /></el-icon>
                  编辑
                </el-button>
              </div>
            </template>
            <template v-else>
              <span class="text-gray-500">暂无短链接</span>
              <div class="link-actions">
                <el-button type="primary" link @click="handleGenerateShortUrl" v-if="canEditShortUrl">
                  生成短链
                </el-button>
                <el-button type="primary" link @click="handleEditShortUrl" v-if="canEditShortUrl">
                  手动添加
                </el-button>
              </div>
            </template>
          </div>
        </el-descriptions-item>
        <el-descriptions-item v-if="detail?.promotion_qrcode" label="推广二维码">
          <el-image
            :src="detail?.promotion_qrcode"
            :preview-src-list="[detail?.promotion_qrcode]"
            class="qrcode"
          />
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 推广位信息部分 -->
    <el-card class="mb-4">
      <template #header>
        <div class="card-header">
          <span>投放明细链接</span>
          <div>
            <el-button type="primary" link @click="handleAddPromotionZone">
              <el-icon><plus /></el-icon>
              增加投放链接
            </el-button>
            <el-button type="info" link @click="showDebugInfo = !showDebugInfo">
              {{ showDebugInfo ? '隐藏调试信息' : '显示调试信息' }}
            </el-button>
          </div>
        </div>
      </template>
      <div v-if="showDebugInfo" class="debug-info mb-4">
        <el-alert type="info" :closable="false">
          <p>调试信息：查看推广链接数据结构</p>
          <pre>{{ JSON.stringify(detail?.promotion_zones?.[0] || {}, null, 2) }}</pre>
        </el-alert>
      </div>
      <el-table :data="detail?.promotion_zones" border style="width: 100%" class="responsive-table">
        <el-table-column label="类型" prop="zoneType" min-width="120">
          <template #default="{ row }">
            <el-tag size="small">{{ row.zoneType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="推广链接" min-width="280">
          <template #default="{ row }">
            <div class="link-cell">
              <div class="link-content">
                <el-icon><link /></el-icon>
                <el-link type="primary" :href="row.promotionLink" target="_blank" class="link-text">
                  <div class="link-text-content">{{ row.promotionLink }}</div>
                </el-link>
              </div>
              <div class="link-actions">
                <el-button type="primary" link @click="handleCopy(row.promotionLink)">
                  <el-icon><document-copy /></el-icon>
                  复制链接
                </el-button>
                <el-button type="success" link @click="handleViewMoreDetails(row)">
                  <el-icon><connection /></el-icon>
                  更多链接
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column 
          v-if="isAdminOrOperator" 
          label="推广位名称" 
          prop="zoneName" 
          min-width="150"
          show-overflow-tooltip 
        />
        <el-table-column 
          v-if="isAdminOrOperator" 
          label="PID" 
          min-width="200"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span class="font-mono">{{ row.pid }}</span>
          </template>
        </el-table-column>
        <el-table-column 
          v-if="showDebugInfo"
          label="调试工具" 
          min-width="150"
        >
          <template #default="{ row }">
            <el-tag :type="hasPromotionParams(row) ? 'success' : 'danger'" size="small">
              {{ hasPromotionParams(row) ? '有参数' : '无参数' }}
            </el-tag>
            <div v-if="hasPromotionParams(row)" class="mt-1">
              <el-button type="primary" link size="small" @click="viewRawParams(row)">
                查看原始数据
              </el-button>
            </div>
            <div class="mt-1">
              <el-button type="info" link size="small" @click="viewRowData(row)">
                查看全部数据
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 操作按钮 -->
    <div class="view-buttons" v-if="false">
      <el-button @click="handleBack">返回</el-button>
      <el-button type="primary" @click="handleEdit" v-if="detail?.is_editable">编辑</el-button>
    </div>

    <!-- 添加推广链接编辑弹窗 -->
    <el-dialog
      v-model="promotionLinkDialogVisible"
      :title="detail?.promotion_link ? '编辑推广链接' : '添加推广链接'"
      :width="isMobile ? '95%' : '600px'"
    >
      <el-form :model="promotionLinkForm" label-width="100px">
        <el-form-item label="推广链接" prop="link" :rules="[{ required: true, message: '请输入推广链接' }]">
          <el-input
            v-model="promotionLinkForm.link"
            type="textarea"
            :rows="3"
            placeholder="请输入推广链接"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="promotionLinkDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSavePromotionLink" :loading="savePromotionLinkLoading">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 添加短链接编辑弹窗 -->
    <el-dialog
      v-model="shortUrlDialogVisible"
      :title="detail?.short_url ? '编辑短链接' : '添加短链接'"
      :width="isMobile ? '95%' : '600px'"
    >
      <el-form :model="shortUrlForm" label-width="100px">
        <el-form-item label="短链接" prop="url" :rules="[{ required: true, message: '请输入短链接' }]">
          <el-input
            v-model="shortUrlForm.url"
            type="textarea"
            :rows="3"
            placeholder="请输入短链接"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="shortUrlDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveShortUrl" :loading="saveShortUrlLoading">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 增加投放链接弹窗 -->
    <el-dialog
      v-model="promotionZoneDialogVisible"
      title="增加投放链接"
      :width="isMobile ? '95%' : '500px'"
    >
      <el-form :model="promotionZoneForm" label-width="100px">
        <el-form-item label="投放产品" prop="product_id" :rules="[{ required: true, message: '请选择投放产品' }]">
          <el-select v-model="promotionZoneForm.product_id" placeholder="请选择投放产品">
            <el-option
              v-for="item in adProducts"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="promotionZoneDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSavePromotionZone" :loading="savePromotionZoneLoading">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 修改融合链接信息弹窗 -->
    <el-dialog
      v-model="mergeLinkDialogVisible"
      title="融合链接信息"
      :width="isMobile ? '95%' : '600px'"
    >
      <el-descriptions :column="1" border size="small">
        <el-descriptions-item label="uid">
          <div class="copy-content">
            <span>{{ detail?.code }}</span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="URL1">
          <div v-if="useSelectMode" class="select-container">
            <el-select v-model="selectedUrl1" placeholder="请选择URL1" style="width: 100%" @change="handleUrl1Change">
              <el-option
                v-for="item in selectOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div class="copy-content">
            <div class="url-text">{{ mergeLinks.url1 || '-' }}</div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="URL2">
          <div v-if="useSelectMode" class="select-container">
            <el-select v-model="selectedUrl2" placeholder="请选择URL2" style="width: 100%" @change="handleUrl2Change">
              <el-option
                v-for="item in selectOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div class="copy-content">
            <div class="url-text">{{ mergeLinks.url2 || '-' }}</div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="URL3">
          <div v-if="useSelectMode" class="select-container">
            <el-select v-model="selectedUrl3" placeholder="请选择URL3" style="width: 100%" @change="handleUrl3Change">
              <el-option
                v-for="item in selectOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div class="copy-content">
            <div class="url-text">{{ mergeLinks.url3 || '-' }}</div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="口令词">
          <el-input v-model="kouling" placeholder="请输入口令词,不填默认为'支付餐补'" />
        </el-descriptions-item>
        <el-descriptions-item label="跳转间隔(毫秒)">
          <div class="interval-input">
            <el-input-number 
              v-model="interval" 
              :min="0"
              :step="100"
              placeholder="请输入跳转间隔时间" 
              style="width: 180px;"
            />
            <el-button type="primary" link @click="interval = null">
              <el-icon><delete /></el-icon>
              清除
            </el-button>
          </div>
          <div class="interval-hint" v-if="interval">
            链接之间将间隔 {{ interval }}ms 跳转
          </div>
        </el-descriptions-item>
      </el-descriptions>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleCopyAll">
          一键复制所有信息
        </el-button>
        <el-button @click="mergeLinkDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 查看更多链接信息弹窗 -->
    <el-dialog
      v-model="moreDetailsDialogVisible"
      :title="detailsError ? '查看错误信息' : '推广参数详情'"
      :width="isMobile ? '95%' : '800px'"
      destroy-on-close
      class="promotion-details-dialog"
    >
      <div v-if="detailsError" class="error-message">
        <el-alert type="error" :closable="false" show-icon>
          {{ detailsError }}
        </el-alert>
      </div>
      <div v-else-if="promotionDetails">
        <!-- 添加Tab切换导航 -->
        <div class="promotion-tabs">
          <el-tabs v-model="activeTab" class="promotion-tab-container">
            <el-tab-pane label="通用" name="common">
              <template v-if="hasDataInSection(promotionDetails, generalLinksFields)">
                <div class="details-section">
                  <!-- 先展示非图片类型的链接 -->
                  <div class="links-list">
                    <template v-for="(item, key) in generalLinksFields" :key="key">
                      <div v-if="promotionDetails[key] && item.type !== 'image'" class="link-item">
                        <div class="link-label">{{ item.label }}</div>
                        <div class="link-content">
                          <div class="link-row">
                            <span class="link-text" :title="promotionDetails[key]">{{ promotionDetails[key] }}</span>
                            <el-tooltip content="复制" placement="top" :show-after="300">
                              <el-button type="primary" circle size="small" @click="handleCopy(promotionDetails[key])">
                                <el-icon><document-copy /></el-icon>
                              </el-button>
                            </el-tooltip>
                          </div>
                        </div>
                      </div>
                    </template>
                    
                    <!-- 显示未在预定义字段中的其他非图片类型通用字段 -->
                    <template v-for="(value, key) in promotionDetails" :key="key">
                      <div 
                        v-if="value && !['we_app', 'ali_app', 'tb_app'].includes(key) && !generalLinksFields[key] && typeof value !== 'object' && !(typeof value === 'string' && value.startsWith('data:image'))" 
                        class="link-item"
                      >
                        <div class="link-label">{{ key }} <el-tag size="small" type="info">其他字段</el-tag></div>
                        <div class="link-content">
                          <div class="link-row">
                            <span class="link-text" :title="value">{{ value }}</span>
                            <el-tooltip content="复制" placement="top" :show-after="300">
                              <el-button type="primary" circle size="small" @click="handleCopy(value)">
                                <el-icon><document-copy /></el-icon>
                              </el-button>
                            </el-tooltip>
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                  
                  <!-- 最后展示图片类型内容 -->
                  <div v-if="hasImageData(promotionDetails, generalLinksFields)" class="image-section">
                    <h4 class="section-title">图片资源</h4>
                    <div class="images-grid">
                      <template v-for="(item, key) in generalLinksFields" :key="key">
                        <div v-if="promotionDetails[key] && item.type === 'image'" class="image-item">
                          <div class="image-title">{{ item.label }}</div>
                          <div class="image-wrapper">
                            <el-image
                              :src="promotionDetails[key]"
                              :preview-src-list="[promotionDetails[key]]"
                              fit="contain"
                              class="preview-image"
                            />
                          </div>
                          <div class="image-actions">
                            <el-button type="success" round size="small" @click="downloadImage(promotionDetails[key], key)">
                              <el-icon><download /></el-icon>
                              下载
                            </el-button>
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>
                </div>
              </template>
              <div v-else class="empty-data">
                <el-empty description="暂无通用推广参数信息" />
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="支付宝" name="alipay">
              <template v-if="promotionDetails.ali_app && hasDataInSection(promotionDetails.ali_app)">
                <div class="details-section">
                  <!-- 先展示非图片类型的链接 -->
                  <div class="links-list">
                    <template v-for="(item, key) in appLinksFields" :key="key">
                      <div v-if="promotionDetails.ali_app[key] && item.type !== 'image'" class="link-item">
                        <div class="link-label">{{ item.label }}</div>
                        <div class="link-content">
                          <div class="link-row">
                            <span class="link-text" :title="promotionDetails.ali_app[key]">{{ promotionDetails.ali_app[key] }}</span>
                            <el-tooltip content="复制" placement="top" :show-after="300">
                              <el-button type="primary" circle size="small" @click="handleCopy(promotionDetails.ali_app[key])">
                                <el-icon><document-copy /></el-icon>
                              </el-button>
                            </el-tooltip>
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                  
                  <!-- 最后展示图片类型内容 -->
                  <div v-if="hasImageData(promotionDetails.ali_app, appLinksFields)" class="image-section">
                    <h4 class="section-title">图片资源</h4>
                    <div class="images-grid">
                      <template v-for="(item, key) in appLinksFields" :key="key">
                        <div v-if="promotionDetails.ali_app[key] && item.type === 'image'" class="image-item">
                          <div class="image-title">{{ item.label }}</div>
                          <div class="image-wrapper">
                            <el-image
                              :src="promotionDetails.ali_app[key]"
                              :preview-src-list="[promotionDetails.ali_app[key]]"
                              fit="contain"
                              class="preview-image"
                            />
                          </div>
                          <div class="image-actions">
                            <el-button type="success" round size="small" @click="downloadImage(promotionDetails.ali_app[key], key)">
                              <el-icon><download /></el-icon>
                              下载
                            </el-button>
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>
                </div>
              </template>
              <div v-else class="empty-data">
                <el-empty description="暂无支付宝推广参数信息" />
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="淘宝" name="taobao">
              <template v-if="promotionDetails.tb_app && hasDataInSection(promotionDetails.tb_app)">
                <div class="details-section">
                  <!-- 先展示非图片类型的链接 -->
                  <div class="links-list">
                    <template v-for="(item, key) in appLinksFields" :key="key">
                      <div v-if="promotionDetails.tb_app[key] && item.type !== 'image'" class="link-item">
                        <div class="link-label">{{ item.label }}</div>
                        <div class="link-content">
                          <div class="link-row">
                            <span class="link-text" :title="promotionDetails.tb_app[key]">{{ promotionDetails.tb_app[key] }}</span>
                            <el-tooltip content="复制" placement="top" :show-after="300">
                              <el-button type="primary" circle size="small" @click="handleCopy(promotionDetails.tb_app[key])">
                                <el-icon><document-copy /></el-icon>
                              </el-button>
                            </el-tooltip>
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                  
                  <!-- 最后展示图片类型内容 -->
                  <div v-if="hasImageData(promotionDetails.tb_app, appLinksFields)" class="image-section">
                    <h4 class="section-title">图片资源</h4>
                    <div class="images-grid">
                      <template v-for="(item, key) in appLinksFields" :key="key">
                        <div v-if="promotionDetails.tb_app[key] && item.type === 'image'" class="image-item">
                          <div class="image-title">{{ item.label }}</div>
                          <div class="image-wrapper">
                            <el-image
                              :src="promotionDetails.tb_app[key]"
                              :preview-src-list="[promotionDetails.tb_app[key]]"
                              fit="contain"
                              class="preview-image"
                            />
                          </div>
                          <div class="image-actions">
                            <el-button type="success" round size="small" @click="downloadImage(promotionDetails.tb_app[key], key)">
                              <el-icon><download /></el-icon>
                              下载
                            </el-button>
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>
                </div>
              </template>
              <div v-else class="empty-data">
                <el-empty description="暂无淘宝推广参数信息" />
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="微信" name="wechat">
              <template v-if="promotionDetails.we_app && hasDataInSection(promotionDetails.we_app)">
                <div class="details-section">
                  <!-- 先展示非图片类型的链接 -->
                  <div class="links-list">
                    <template v-for="(item, key) in appLinksFields" :key="key">
                      <div v-if="promotionDetails.we_app[key] && item.type !== 'image'" class="link-item">
                        <div class="link-label">{{ item.label }}</div>
                        <div class="link-content">
                          <div class="link-row">
                            <span class="link-text" :title="promotionDetails.we_app[key]">{{ promotionDetails.we_app[key] }}</span>
                            <el-tooltip content="复制" placement="top" :show-after="300">
                              <el-button type="primary" circle size="small" @click="handleCopy(promotionDetails.we_app[key])">
                                <el-icon><document-copy /></el-icon>
                              </el-button>
                            </el-tooltip>
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                  
                  <!-- 最后展示图片类型内容 -->
                  <div v-if="hasImageData(promotionDetails.we_app, appLinksFields)" class="image-section">
                    <h4 class="section-title">图片资源</h4>
                    <div class="images-grid">
                      <template v-for="(item, key) in appLinksFields" :key="key">
                        <div v-if="promotionDetails.we_app[key] && item.type === 'image'" class="image-item">
                          <div class="image-title">{{ item.label }}</div>
                          <div class="image-wrapper">
                            <el-image
                              :src="promotionDetails.we_app[key]"
                              :preview-src-list="[promotionDetails.we_app[key]]"
                              fit="contain"
                              class="preview-image"
                            />
                          </div>
                          <div class="image-actions">
                            <el-button type="success" round size="small" @click="downloadImage(promotionDetails.we_app[key], key)">
                              <el-icon><download /></el-icon>
                              下载
                            </el-button>
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>
                </div>
              </template>
              <div v-else class="empty-data">
                <el-empty description="暂无微信推广参数信息" />
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <div v-if="!hasAnyData" class="empty-data" style="display: none;">
          <el-empty description="暂无推广参数信息" />
        </div>
      </div>
      <template #footer>
        <el-button @click="moreDetailsDialogVisible = false">关闭</el-button>
        <el-button v-if="!detailsError && promotionDetails" type="primary" @click="exportParams">
          <el-icon><download /></el-icon>导出参数
        </el-button>
      </template>
    </el-dialog>

    <!-- 原始参数查看弹窗 -->
    <el-dialog
      v-model="rawParamsDialogVisible"
      title="原始推广参数数据"
      :width="isMobile ? '95%' : '700px'"
    >
      <pre class="raw-params-content">{{ rawParams }}</pre>
      <template #footer>
        <el-button @click="rawParamsDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 查看行数据弹窗 -->
    <el-dialog
      v-model="rowDataDialogVisible"
      title="行完整数据"
      :width="isMobile ? '95%' : '700px'"
    >
      <pre class="raw-params-content">{{ JSON.stringify(rowData, null, 2) }}</pre>
      <template #footer>
        <el-button @click="rowDataDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useClipboard } from '@vueuse/core'
import { useUserStore } from '@/store/modules/user'
import { formatDateTime } from '@/utils/date'
import { ArrowLeft, Link, DocumentCopy, Plus, Edit, Delete, View, Download, Connection } from '@element-plus/icons-vue'
import { get as getAdSlotPlan, generateShortUrl, updatePromotionLink, updateShortUrl, generatePromotionLink, updateMergeLinks } from '@/api/ad-slot-plan'
import { getAdProducts } from '@/api/ad-product'
import {
  PLAN_TYPE,
  PLAN_TYPE_MAP,
  AUDIT_STATUS,
  AUDIT_STATUS_MAP,
  DELIVERY_STATUS,
  DELIVERY_STATUS_MAP,
  STATUS_TAG_TYPE_MAP,
  DELIVERY_MODE_MAP,
  AD_TYPE_MAP
} from '@/constants'
import { useWindowSize } from '@vueuse/core'

const route = useRoute()
const router = useRouter()
const { copy } = useClipboard()
const userStore = useUserStore()
const { width } = useWindowSize()
const isMobile = computed(() => width.value < 768)

// 数据定义
const loading = ref(false)
const detail = ref({})
const showDebugInfo = ref(false)

// 获取状态标签类型
const getStatusTagType = (status) => {
  return STATUS_TAG_TYPE_MAP[status] || 'info'
}

// 是否显示推广信息
const showPromotionInfo = computed(() => {
  return detail.value?.audit_status === AUDIT_STATUS.APPROVED
})

// 是否可以编辑推广链接
const canEditPromotionLink = computed(() => {
  return detail.value?.audit_status === AUDIT_STATUS.APPROVED && 
    (userStore.isAdmin || detail.value?.user_id === userStore.userId)
})

// 是否可以编辑短链接 - 只要计划审核通过就可以
const canEditShortUrl = computed(() => {
  return detail.value?.audit_status === AUDIT_STATUS.APPROVED
})

// 是否是管理员或运营
const isAdminOrOperator = computed(() => {
  console.log('isAdmin:', userStore.isAdmin)
  console.log('roles:', userStore.roles)
  return userStore.isAdminOrOperator
})

// 获取详情
const fetchDetail = async () => {
  loading.value = true
  try {
    const res = await getAdSlotPlan(route.params.id)
    detail.value = res.data
    console.log('完整计划详情:', detail.value)
    console.log('推广位数据:', detail.value?.promotion_zones)
    
    // 检查推广位数据中的字段
    if (detail.value?.promotion_zones && detail.value.promotion_zones.length > 0) {
      detail.value.promotion_zones.forEach((zone, index) => {
        console.log(`推广位 ${index + 1} 的字段:`, Object.keys(zone))
        
        // 检查可能的推广参数字段
        const possibleParamsField = 
          zone.promotionParams || 
          zone.promotion_params || 
          zone.params || 
          zone.promotion_info
        
        console.log(`推广位 ${index + 1} 推广参数:`, possibleParamsField)
        
        // 如果没有明显的推广参数字段，遍历所有字段
        if (!possibleParamsField) {
          Object.keys(zone).forEach(key => {
            const value = zone[key]
            if (typeof value === 'string' && value.startsWith('{') && value.endsWith('}')) {
              try {
                JSON.parse(value)
                console.log(`推广位 ${index + 1} 可能的JSON字段 ${key}:`, value)
              } catch (e) {
                // 不是有效的JSON
              }
            }
          })
        }
      })
    }
  } catch (error) {
    console.error('获取投放计划详情失败:', error)
    ElMessage.error('获取投放计划详情失败')
  } finally {
    loading.value = false
  }
}

// 生成短链接
const handleGenerateShortUrl = async () => {
  try {
    const res = await generateShortUrl(detail.value?.id)
    if (detail.value) {
      detail.value.short_url = res.data.short_url
    }
    ElMessage.success('生成短链接成功')
  } catch (error) {
    console.error('生成短链接失败:', error)
    ElMessage.error(error?.message || '生成短链接失败')
  }
}

// 复制文本
const handleCopy = async (text) => {
  if (!text) {
    ElMessage.warning('复制的内容不能为空')
    return
  }

  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('复制成功')
  } catch (error) {
    console.error('复制失败:', error)
    // 降级方案：使用 textarea 实现复制
    const textarea = document.createElement('textarea')
    textarea.value = text
    document.body.appendChild(textarea)
    textarea.select()
    try {
      document.execCommand('copy')
      ElMessage.success('复制成功')
    } catch (e) {
      console.error('降级复制失败:', e)
      ElMessage.error('复制失败')
    } finally {
      document.body.removeChild(textarea)
    }
  }
}

// 返回列表
const handleBack = () => {
  router.push('/ad-slot-plans')
}

// 编辑
const handleEdit = () => {
  router.push(`/ad-slot-plans/edit/${detail?.id}`)
}

// 增加投放链接相关
const promotionZoneDialogVisible = ref(false)
const promotionZoneForm = ref({
  product_id: null
})
const savePromotionZoneLoading = ref(false)
const adProducts = ref([])

// 获取投放产品列表
const fetchAdProducts = async () => {
  try {
    const res = await getAdProducts()
    adProducts.value = res.data?.list || []
  } catch (error) {
    console.error('获取投放产品列表失败:', error)
    ElMessage.error('获取投放产品列表失败')
  }
}

// 增加投放链接
const handleAddPromotionZone = () => {
  promotionZoneForm.value.product_id = null
  promotionZoneDialogVisible.value = true
}

// 保存投放链接
const handleSavePromotionZone = async () => {
  const productId = promotionZoneForm.value.product_id
  if (!productId) {
    ElMessage.warning('请选择投放产品')
    return
  }

  const planId = detail.value?.id
  if (!planId) {
    ElMessage.error('计划ID不能为空')
    return
  }

  try {
    savePromotionZoneLoading.value = true
    await generatePromotionLink(planId, productId)
    ElMessage.success('生成投放链接成功')
    promotionZoneDialogVisible.value = false
    fetchDetail()
  } catch (error) {
    console.error('生成投放链接失败:', error)
    ElMessage.error(error?.message || '生成投放链接失败')
  } finally {
    savePromotionZoneLoading.value = false
  }
}

// 推广链接编辑相关
const promotionLinkDialogVisible = ref(false)
const promotionLinkForm = ref({
  link: ''
})
const savePromotionLinkLoading = ref(false)

// 短链接编辑相关
const shortUrlDialogVisible = ref(false)
const shortUrlForm = ref({
  url: ''
})
const saveShortUrlLoading = ref(false)

// 编辑推广链接
const handleEditPromotionLink = () => {
  promotionLinkForm.value.link = detail.value?.promotion_link || ''
  promotionLinkDialogVisible.value = true
}

// 保存推广链接
const handleSavePromotionLink = async () => {
  if (!promotionLinkForm.value.link) {
    ElMessage.warning('请输入推广链接')
    return
  }

  try {
    savePromotionLinkLoading.value = true
    await updatePromotionLink({
      id: detail.value?.id,
      link: promotionLinkForm.value.link
    })
    ElMessage.success('更新推广链接成功')
    promotionLinkDialogVisible.value = false
    fetchDetail()
  } catch (error) {
    console.error('更新推广链接失败:', error)
    ElMessage.error(error?.message || '更新推广链接失败')
  } finally {
    savePromotionLinkLoading.value = false
  }
}

// 编辑短链接
const handleEditShortUrl = () => {
  shortUrlForm.value.url = detail.value?.short_url || ''
  shortUrlDialogVisible.value = true
}

// 保存短链接
const handleSaveShortUrl = async () => {
  if (!shortUrlForm.value.url) {
    ElMessage.warning('请输入短链接')
    return
  }

  try {
    saveShortUrlLoading.value = true
    await updateShortUrl({
      id: detail.value?.id,
      url: shortUrlForm.value.url
    })
    ElMessage.success('更新短链接成功')
    shortUrlDialogVisible.value = false
    fetchDetail()
  } catch (error) {
    console.error('更新短链接失败:', error)
    ElMessage.error(error?.message || '更新短链接失败')
  } finally {
    saveShortUrlLoading.value = false
  }
}

// 融合链接相关数据
const mergeLinkDialogVisible = ref(false)
const mergeLinks = ref({
  url1: '',
  url2: '',
  url3: ''
})
const kouling = ref('') // 口令词
const interval = ref(null) // 链接跳转间隔时间

// 判断是否为融合产品
const isMergeProduct = computed(() => {
  const productId = detail.value?.ad_product_id
  return productId === 9 || productId === 3 || productId === 7
})

// 基础目标链接
const baseTargetUrl = computed(() => {
  return `https://click.daf8.cn/act/tui/alids14/?uid=${detail.value?.code}`
})

// 唤端URL固定链接
const universalLink = computed(() => {
  // 支付宝小程序链接
  const alipayScheme = `alipays://platformapi/startapp?appId=2018081461095002&page=pages%2Fwebview%2Findex%3Furl=${encodeURIComponent(baseTargetUrl.value)}`
  
  // 最终的 Universal Link
  return `https://render.alipay.com/p/s/i/?scheme=${encodeURIComponent(alipayScheme)}`
})

// 支付宝小程序PATH解码
const decodedPath = computed(() => {
  return decodeURIComponent("pages%2Fwebview%2Findex%3Furl%3D") + encodeURIComponent(baseTargetUrl.value)
})

// 新增变量
const useSelectMode = ref(false)
const selectOptions = ref([])
const selectedUrl1 = ref('')
const selectedUrl2 = ref('')
const selectedUrl3 = ref('')

// 转换飞猪链接为支付宝链接
const transformToAlipayLink = (url) => {
  if (!url) return ''
  // 将原链接进行编码
  const encodedUrl = encodeURIComponent(url)
  return `alipays://platformapi/startapp?appId=2018081461095002&page=pages%2Fwebview%2Findex%3Furl%3D${encodedUrl}`
}

// 监听 URL1 的选择变化
const handleUrl1Change = (url) => {
  if (url && url.includes('taobao.com')) {
    // 如果选择的是飞猪链接（包含 taobao.com），自动转换为支付宝格式
    mergeLinks.value.url1 = transformToAlipayLink(url)
  } else {
    mergeLinks.value.url1 = url
  }
}

// 监听 URL2 的选择变化
const handleUrl2Change = (url) => {
  mergeLinks.value.url2 = url
}

// 监听 URL3 的选择变化
const handleUrl3Change = (url) => {
  mergeLinks.value.url3 = url
}

// 生成融合链接
const handleGenerateMergeLink = () => {
  const productId = detail.value?.ad_product_id
  
  if (productId === 9) {
    // 特殊用户组的 URL1 固定值
    mergeLinks.value.url1 = 'alipays://platformapi/startapp?appId=2021001110676437&page=plugin-private%3A%2F%2F2021005125624788%2Fsettings%2Fpages%2Fcoupon%2Findex%3Falsc_exsrc%3Dch_alipay_cap%26cap%3DwcmBjzQJx72N7bCNhYwL6A%253D%253D'
    
    // 直接获取第一个投放链接作为 URL2
    mergeLinks.value.url2 = detail.value?.promotion_zones?.[0]?.promotionLink || '-'
    mergeLinks.value.url3 = ''
    
    // 获取保存的间隔时间
    interval.value = detail.value?.merge_links?.interval || null
    
    useSelectMode.value = false
  } else if (productId === 3 || productId === 7) {
    // 对 productId === 3 的情况，提供选择投放链接的功能
    mergeLinks.value = {
      url1: '',
      url2: '',
      url3: ''
    }
    selectedUrl1.value = ''
    selectedUrl2.value = ''
    selectedUrl3.value = ''
    
    // 获取保存的间隔时间
    interval.value = detail.value?.merge_links?.interval || null
    
    // 初始化下拉选择器数据
    selectOptions.value = detail.value?.promotion_zones?.map(zone => ({
      label: `${zone.zoneType}`,
      value: zone.promotionLink
    })) || []
    
    // 显示选择器模式
    useSelectMode.value = true
  } else {
    // 默认情况下清空值
    mergeLinks.value = {
      url1: '',
      url2: '',
      url3: ''
    }
    useSelectMode.value = false
    
    // 获取保存的间隔时间
    interval.value = detail.value?.merge_links?.interval || null
  }
  
  mergeLinkDialogVisible.value = true
}

// 生成最终投放链接
const generateFinalLink = () => {
  const uid = detail.value?.code
  const kl = kouling.value || '支付餐补'
  
  // 生成基础链接
  let baseUrl = `https://click.daf8.cn/act/tui/alids14/?uid=${uid}&kouling=${kl}`
  
  // 如果设置了间隔时间，添加 interval 参数
  if (interval.value !== null && interval.value !== undefined) {
    baseUrl += `&interval=${interval.value}`
  }
  
  // urlencode 编码后拼接
  const encodedUrl = encodeURIComponent(baseUrl)
  return `alipays://platformapi/startapp?appId=20000067&url=${encodedUrl}`
}

// 更新推广链接
const updatePromotionLinkAfterCopy = async () => {
  const finalLink = generateFinalLink()
  
  try {
    await updatePromotionLink({
      id: detail.value?.id,
      link: finalLink
    })
    ElMessage.success('更新推广链接成功')
    fetchDetail() // 刷新详情
  } catch (error) {
    console.error('更新推广链接失败:', error)
    ElMessage.error(error?.message || '更新推广链接失败')
  }
}

// 一键复制所有信息
const handleCopyAll = async () => {
  // 构建复制内容
  let allContent = `uid: ${detail.value?.code}\nURL1: ${mergeLinks.value.url1}\nURL2: ${mergeLinks.value.url2}\nURL3: ${mergeLinks.value.url3 || '-'}`
  
  // 如果设置了间隔时间，添加到复制内容中
  if (interval.value !== null && interval.value !== undefined) {
    allContent += `\n跳转间隔: ${interval.value}ms`
  }
  
  try {
    await handleCopy(allContent)
    ElMessage.success('复制所有信息成功')
    
    // 复制成功后更新推广链接
    await updatePromotionLinkAfterCopy()
    
    // 保存融合链接信息到数据库
    try {
      await updateMergeLinks({
        id: detail.value?.id,
        merge_links: {
          url1: mergeLinks.value.url1,
          url2: mergeLinks.value.url2,
          url3: mergeLinks.value.url3,
          kouling: kouling.value || '支付餐补',
          interval: interval.value
        }
      })
      console.log('融合链接信息已保存')
    } catch (error) {
      console.error('保存融合链接信息失败:', error)
    }
  } catch (error) {
    ElMessage.error('复制所有信息失败')
  }
}

// 添加更多链接信息弹窗相关变量
const moreDetailsDialogVisible = ref(false)
const promotionDetails = ref(null)
const detailsError = ref(null)

// 链接字段定义
const generalLinksFields = {
  url: { label: '普通H5', type: 'link' },
  short_url: { label: '普通H5的短链接', type: 'link' },
  app_url: { label: '唤醒App链接(中间页)', type: 'link' },
  deeplink: { label: '唤醒App链接', type: 'link' },
  pwd: { label: '口令', type: 'text' },
  long_pwd: { label: '完整口令', type: 'text' },
  poster: { label: '海报', type: 'image' },
  qrcode: { label: '二维码', type: 'image' }
}

const appLinksFields = {
  org_id: { label: '小程序原始ID', type: 'text' },
  app_id: { label: '小程序AppID', type: 'text' },
  path: { label: '小程序Path路径', type: 'text' },
  poster: { label: '海报', type: 'image' },
  qrcode: { label: '二维码', type: 'image' },
  url: { label: '唤端链接', type: 'link' },
  short_url: { label: '普通H5的短链接', type: 'link' },
  deeplink: { label: '唤醒app的链接', type: 'link' },
  pwd: { label: '口令', type: 'text' },
  long_pwd: { label: '完整口令', type: 'text' }
}

// 检查某个部分是否有数据
const hasDataInSection = (section, fields = null) => {
  if (!section) return false
  
  if (fields) {
    // 检查预定义字段
    const hasPreDefinedData = Object.keys(fields).some(key => !!section[key])
    
    // 如果没有预定义字段有数据，检查是否有其他未知字段
    if (!hasPreDefinedData && fields === generalLinksFields) {
      return Object.keys(section).some(key => {
        return !!section[key] && !['we_app', 'ali_app', 'tb_app'].includes(key)
      })
    }
    
    return hasPreDefinedData
  }
  
  return Object.keys(section).some(key => !!section[key])
}

// 检查是否有任何数据
const hasAnyData = computed(() => {
  if (!promotionDetails.value) return false
  
  const hasGeneralData = hasDataInSection(promotionDetails.value, generalLinksFields)
  const hasWeAppData = promotionDetails.value.we_app && hasDataInSection(promotionDetails.value.we_app)
  const hasAliAppData = promotionDetails.value.ali_app && hasDataInSection(promotionDetails.value.ali_app)
  const hasTbAppData = promotionDetails.value.tb_app && hasDataInSection(promotionDetails.value.tb_app)
  
  return hasGeneralData || hasWeAppData || hasAliAppData || hasTbAppData
})

// 原始参数查看相关
const rawParamsDialogVisible = ref(false)
const rawParams = ref('')

// 判断行是否有推广参数
const hasPromotionParams = (row) => {
  // 检查所有可能的字段名
  return !!(
    row.promotionParams || 
    row.promotion_params || 
    row.params || 
    row.promotion_info
  )
}

// 查看原始参数
const viewRawParams = (row) => {
  // 获取可能的推广参数字段
  const paramsStr = 
    row.promotionParams || 
    row.promotion_params || 
    row.params || 
    row.promotion_info
    
  rawParams.value = paramsStr ? paramsStr : '无推广参数'
  rawParamsDialogVisible.value = true
}

// 查看更多详情处理
const handleViewMoreDetails = (row) => {
  detailsError.value = null
  promotionDetails.value = null
  // 设置默认选中的Tab
  activeTab.value = 'common'
  
  // 添加日志来检查数据
  console.log('详情数据:', row)
  console.log('行的所有属性:', Object.keys(row))
  
  try {
    // 尝试获取所有可能的推广参数字段
    let paramsStr = 
      row.promotionParams || 
      row.promotion_params || 
      row.params || 
      row.promotion_info
    
    if (!paramsStr) {
      // 如果未找到，遍历所有字段寻找可能的JSON字符串
      Object.keys(row).forEach(key => {
        const value = row[key]
        if (typeof value === 'string' && value.startsWith('{') && value.endsWith('}')) {
          try {
            JSON.parse(value)
            console.log('找到可能的JSON字符串字段:', key)
            paramsStr = value
          } catch (e) {
            // 不是有效的JSON
          }
        }
      })
    }
    
    if (!paramsStr) {
      console.log('没有找到推广参数字段')
      detailsError.value = '没有可用的推广参数信息'
      moreDetailsDialogVisible.value = true
      return
    }
    
    console.log('原始推广参数:', paramsStr)
    
    // 尝试解析JSON字符串
    const parsedData = JSON.parse(paramsStr)
    console.log('解析后的数据:', parsedData)
    
    promotionDetails.value = parsedData
    
    // 检查数据格式是否符合预期
    if (!promotionDetails.value || typeof promotionDetails.value !== 'object') {
      detailsError.value = '推广参数格式不正确'
    }
  } catch (error) {
    console.error('解析推广参数失败:', error)
    detailsError.value = '推广参数解析失败: ' + error.message
  }
  
  moreDetailsDialogVisible.value = true
}

// Tab相关变量
const activeTab = ref('common')

// 行数据查看相关
const rowDataDialogVisible = ref(false)
const rowData = ref(null)

// 查看行完整数据
const viewRowData = (row) => {
  rowData.value = row
  rowDataDialogVisible.value = true
}

// 导出参数
const exportParams = () => {
  if (!promotionDetails.value) return

  try {
    // 创建一个Blob对象
    const jsonString = JSON.stringify(promotionDetails.value, null, 2)
    const blob = new Blob([jsonString], { type: 'application/json' })
    
    // 创建一个下载链接
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `推广参数_${new Date().getTime()}.json`
    
    // 触发下载
    document.body.appendChild(link)
    link.click()
    
    // 清理
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出参数失败:', error)
    ElMessage.error('导出失败')
  }
}

// 下载图片功能
const downloadImage = async (imageUrl, imageName = 'image') => {
  try {
    // 创建一个a标签来触发下载
    const link = document.createElement('a')
    link.href = imageUrl
    link.download = `${imageName}_${new Date().getTime()}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('图片下载成功')
  } catch (error) {
    console.error('下载图片失败:', error)
    ElMessage.error('下载图片失败')
  }
}

// 预览图片
const previewImage = (imageUrl) => {
  // 使用Element Plus的图片预览功能，已经通过el-image组件的preview实现
  // 这里只是为了按钮点击而添加
  window.open(imageUrl, '_blank')
}

onMounted(() => {
  fetchDetail()
  fetchAdProducts() // 获取投放产品列表
})

// 添加判断是否有图片数据的方法
const hasImageData = (data, fieldsConfig) => {
  if (!data) return false
  
  // 检查是否有预定义的图片类型字段
  const hasPreDefinedImages = Object.keys(fieldsConfig).some(key => {
    return fieldsConfig[key].type === 'image' && !!data[key]
  })
  
  return hasPreDefinedImages
}
</script>

<style lang="scss" scoped>
.ad-slot-plan-view {
  padding: 20px;

  @media screen and (max-width: 768px) {
    padding: 10px;
  }

  .mb-4 {
    margin-bottom: 16px;
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
      font-weight: bold;
      flex: 1;
      text-align: center;
    }

    @media screen and (max-width: 768px) {
      .title {
        font-size: 16px;
        text-align: left;
        margin-left: 8px;
      }
      
      flex-wrap: wrap;
      row-gap: 10px;
      
      .el-button {
        padding: 6px 10px;
        font-size: 13px;
      }
    }
  }

  .link-cell {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 12px;
    width: 100%;

    @media screen and (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .link-content {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      flex: 1;
      min-width: 0;
      word-break: break-all;

      .el-icon {
        flex-shrink: 0;
        margin-top: 2px;
      }

      .link-text {
        flex: 1;
        min-width: 0;
        line-height: 1.5;
        font-size: 14px;
        
        @media screen and (max-width: 768px) {
          font-size: 13px;
        }
        
        .link-text-content {
          word-break: break-all;
          white-space: normal;
          line-height: 1.5;
        }
      }
    }

    .link-actions {
      flex-shrink: 0;
      white-space: nowrap;
      
      @media screen and (max-width: 768px) {
        display: flex;
        width: 100%;
        margin-top: 5px;
        gap: 10px;
        justify-content: flex-start;
      }
    }
  }

  .qrcode {
    width: 200px;
    height: 200px;
    object-fit: contain;

    @media screen and (max-width: 768px) {
      width: 120px;
      height: 120px;
    }
  }

  .view-buttons {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    gap: 12px;
  }

  .mt-1 {
    margin-top: 4px;
  }

  .gap-4 {
    gap: 16px;
  }

  .font-mono {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
  }

  :deep(.el-descriptions) {
    @media screen and (max-width: 768px) {
      font-size: 14px;
      
      .el-descriptions__header {
        margin-bottom: 12px;
      }
    }
  }

  :deep(.el-card__body) {
    @media screen and (max-width: 768px) {
      padding: 12px;
    }
  }

  :deep(.el-card__header) {
    @media screen and (max-width: 768px) {
      padding: 12px;
    }
  }

  :deep(.el-tag) {
    @media screen and (max-width: 768px) {
      font-size: 12px;
      height: 22px;
      line-height: 20px;
      padding: 0 6px;
    }
  }

  :deep(.el-descriptions__label) {
    width: 120px;

    @media screen and (max-width: 768px) {
      width: 90px;
      font-size: 13px;
    }
  }

  :deep(.el-descriptions__cell) {
    @media screen and (max-width: 768px) {
      padding: 12px !important;
      font-size: 13px;
    }
  }

  :deep(.el-descriptions__content) {
    @media screen and (max-width: 768px) {
      word-break: break-all;
    }
  }

  .copy-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    
    .url-text {
      flex: 1;
      word-break: break-all;
      line-height: 1.5;
    }
  }

  .dialog-footer {
    margin-top: 20px;
    text-align: center;
    
    @media screen and (max-width: 768px) {
      display: flex;
      flex-direction: column;
      gap: 10px;
      
      .el-button {
        margin-left: 0 !important;
        width: 100%;
      }
    }
  }

  .select-container {
    width: 100%;
  }

  .interval-input {
    display: flex;
    align-items: center;
    gap: 10px;
    
    @media screen and (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      
      .el-input-number {
        width: 100% !important;
        margin-bottom: 8px;
      }
    }
  }
  
  .interval-hint {
    margin-top: 5px;
    font-size: 12px;
    color: #909399;
  }

  .responsive-table {
    @media screen and (max-width: 768px) {
      display: block;
      overflow-x: auto;
      white-space: nowrap;
    }
  }
  
  :deep(.el-button) {
    @media screen and (max-width: 768px) {
      padding: 8px 12px;
      font-size: 13px;
    }
  }
  
  :deep(.el-dialog__body) {
    @media screen and (max-width: 768px) {
      padding: 15px;
    }
  }
  
  :deep(.el-form-item) {
    @media screen and (max-width: 768px) {
      margin-bottom: 18px;
    }
  }
  
  :deep(.el-form-item__label) {
    @media screen and (max-width: 768px) {
      padding: 0 0 8px;
      line-height: 1.2;
    }
  }

  :deep(.el-table) {
    .el-table__cell {
      vertical-align: top;
      
      @media screen and (max-width: 768px) {
        padding: 8px !important;
      }
    }
    
    @media screen and (max-width: 768px) {
      font-size: 13px;
      
      .el-table__header {
        font-size: 13px;
      }
      
      .cell {
        line-height: 1.3;
        padding: 6px 8px;
      }
    }
  }
  
  :deep(.el-dialog__footer) {
    @media screen and (max-width: 768px) {
      display: flex;
      flex-direction: column;
      padding: 15px;
      
      .el-button + .el-button {
        margin-left: 0;
        margin-top: 10px;
      }
      
      .el-button {
        width: 100%;
      }
    }
  }
  
  :deep(.el-select) {
    @media screen and (max-width: 768px) {
      width: 100%;
    }
  }
  
  :deep(.el-input), :deep(.el-textarea) {
    @media screen and (max-width: 768px) {
      width: 100%;
    }
  }

  .details-section {
    margin-bottom: 20px;
  }
  
  .details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    
    @media screen and (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 15px;
    }
  }
  
  .detail-item {
    margin-bottom: 0;
    padding: 12px;
    border-radius: 6px;
    background-color: #f8f9fa;
    transition: all 0.2s ease;
    border: 1px solid #ebeef5;
    height: 100%;
    display: flex;
    flex-direction: column;
    
    &:hover {
      background-color: #f2f6fc;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      transform: translateY(-2px);
    }
    
    .detail-label {
      font-weight: bold;
      margin-bottom: 10px;
      color: #409EFF;
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 8px;
      font-size: 14px;
    }
    
    .detail-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      
      .link-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        padding: 8px 12px;
        border-radius: 4px;
        margin-bottom: 8px;
        gap: 10px;
        border: 1px dashed #dcdfe6;
        
        .link-text {
          flex: 1;
          word-break: break-all;
          font-size: 13px;
          font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          color: #606266;
        }
        
        @media screen and (max-width: 768px) {
          flex-direction: row;
          align-items: center;
          
          .link-text {
            white-space: nowrap;
            margin-bottom: 0;
            max-width: 70%;
          }
        }
      }
    }
  }
  
  .preview-image {
    max-width: 100%;
    height: auto;
    max-height: 300px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    object-fit: contain;
    display: block;
    margin: 0 auto;
    background-color: #f8f9fa;
    
    @media screen and (max-width: 768px) {
      max-height: 220px;
    }
  }

  .image-container {
    position: relative;
    margin-bottom: 10px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    
    .image-actions {
      display: flex;
      justify-content: center;
      margin-top: 12px;
      gap: 12px;
    }
  }

  .error-message {
    margin-bottom: 20px;
  }

  .empty-data {
    padding: 20px 0;
  }

  .debug-info {
    font-size: 12px;
    
    pre {
      background-color: #f9f9f9;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }

  .raw-params-content {
    background-color: #f5f7fa;
    padding: 12px;
    border-radius: 4px;
    max-height: 400px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 12px;
    white-space: pre-wrap;
    word-break: break-all;
  }

  .help-list {
    margin: 5px 0 0 20px;
    padding: 0;
    font-size: 13px;
    
    li {
      margin-bottom: 4px;
    }
    
    @media screen and (max-width: 768px) {
      font-size: 12px;
      margin-left: 15px;
    }
  }

  :deep(.promotion-details-dialog) {
    .el-dialog__body {
      padding: 20px;
      max-height: 70vh;
      overflow-y: auto;
    }
  }
  
  // 添加Tab切换样式
  .promotion-tabs {
    margin-bottom: 20px;
    
    .promotion-tab-container {
      .el-tabs__header {
        background-color: #f5f7fa;
        padding: 0;
        margin-bottom: 20px;
        border-radius: 4px;
        
        .el-tabs__nav-wrap::after {
          height: 0;
        }
        
        .el-tabs__nav {
          border: none;
        }
        
        .el-tabs__item {
          height: 40px;
          line-height: 40px;
          padding: 0 20px;
          font-size: 14px;
          color: #606266;
          transition: all 0.3s;
          
          &.is-active {
            color: #409EFF;
            font-weight: bold;
          }
          
          &:hover {
            color: #409EFF;
          }
          
          @media screen and (max-width: 768px) {
            padding: 0 12px;
            font-size: 13px;
            height: 36px;
            line-height: 36px;
          }
        }
      }
      
      .el-tabs__content {
        padding: 10px 0;
      }
    }
  }

  .links-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
  }

  .link-item {
    background-color: #f8f9fa;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    padding: 15px;
    transition: all 0.2s ease;
    
    &:hover {
      background-color: #f2f6fc;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    }
    
    .link-label {
      font-weight: bold;
      margin-bottom: 8px;
      color: #409EFF;
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 8px;
      font-size: 14px;
    }
    
    .link-content {
      .link-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        padding: 8px 12px;
        border-radius: 4px;
        gap: 10px;
        border: 1px dashed #dcdfe6;
        
        .link-text {
          flex: 1;
          word-break: break-all;
          font-size: 13px;
          font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
          color: #606266;
          line-height: 1.5;
          
          @media screen and (max-width: 768px) {
            font-size: 12px;
          }
        }
      }
    }
  }

  .image-section {
    margin-top: 20px;
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #303133;
      padding-left: 10px;
      border-left: 3px solid #409EFF;
    }
    
    .images-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 20px;
      
      @media screen and (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }
    
    .image-item {
      background-color: #f8f9fa;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;
      
      .image-title {
        background-color: #f8f9fa;
        color: #409EFF;
        font-weight: bold;
        padding: 12px;
        text-align: center;
        border-bottom: 1px solid #e8e8e8;
      }
      
      .image-wrapper {
        background: white;
        padding: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 280px;
        position: relative;
        
        @media screen and (max-width: 768px) {
          height: 220px;
        }
      }
      
      .preview-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }
      
      .image-actions {
        padding: 15px;
        display: flex;
        justify-content: center;
      }
    }
  }
}
</style> 