<template>
  <div class="app-container">
    <el-card>
      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="计划编号">
          <el-input v-model="searchForm.code" placeholder="请输入计划编号" clearable />
        </el-form-item>
        <el-form-item label="媒体">
          <el-select v-model="searchForm.media_id" placeholder="请选择媒体" clearable @change="handleMediaChange" filterable>
            <el-option
              v-for="item in mediaList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="广告位">
          <el-select v-model="searchForm.ad_slot_id" placeholder="请选择广告位" clearable>
            <el-option
              v-for="item in adSlotList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="search-buttons">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮区 -->
      <div class="table-operations">
        <el-button 
          type="primary" 
          @click="handleCreate"
          v-if="hasPermission('plan:create')"
        >
          新建计划
        </el-button>
        
        <!-- 权限调试工具 -->
        <div v-if="!hasPermission('plan:create')" class="permission-debug" style="margin-left: 10px;">
          <p style="color: #f56c6c; font-size: 12px; margin: 0;">
            [调试] 缺少权限: plan:create
          </p>
          <el-button 
            size="small" 
            type="info" 
            @click="checkPermissionState"
            style="margin-top: 5px;"
          >
            检查权限状态
          </el-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="dataList"
        border
        style="width: 100%"
      >
        <el-table-column prop="code" label="计划编号" min-width="120" />
        <el-table-column label="计划类型" min-width="100">
          <template #default="{ row }">
            <el-tag :type="row.type === PLAN_TYPE.TEST ? 'warning' : 'success'" size="small">
              {{ PLAN_TYPE_MAP[row.type] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="ad_product.name" label="投放产品" min-width="150" />
        <el-table-column label="广告位" min-width="150">
          <template #default="{ row }">
            <div>{{ row.ad_slot?.name }}</div>
            <div class="text-gray-500 text-sm">{{ row.ad_slot?.mediaName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="媒体" min-width="120">
          <template #default="{ row }">
            {{ row.ad_slot?.media_name }}
          </template>
        </el-table-column>
        <el-table-column label="媒介" min-width="120">
          <template #default="{ row }">
            {{ row.user_name || '未分配' }}
          </template>
        </el-table-column>
        <el-table-column label="审核状态" min-width="100">
          <template #default="{ row }">
            <el-tag :type="STATUS_TAG_TYPE_MAP[row.audit_status]" size="small">
              {{ AUDIT_STATUS_MAP[row.audit_status] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="投放状态" min-width="100">
          <template #default="{ row }">
            <el-tag :type="STATUS_TAG_TYPE_MAP[row.delivery_status]" size="small">
              {{ DELIVERY_STATUS_MAP[row.delivery_status] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="投放时间" min-width="200">
          <template #default="{ row }">
            <div>{{ formatDateTime(row.start_date) }}</div>
            <div v-if="row.is_end_date_enabled">{{ formatDateTime(row.end_date) }}</div>
            <div v-else class="text-gray-500">未启用结束时间</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="380">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleView(row)">查看</el-button>
            <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
            <el-button 
              type="success" 
              link 
              @click="handleApprove(row)"
              :loading="approveLoading"
              v-if="row.is_approvable"
            >
              审核通过
            </el-button>
            <el-button 
              type="danger" 
              link 
              @click="handleReject(row)"
              v-if="row.is_rejectable"
            >
              审核拒绝
            </el-button>
            <el-button
              type="warning"
              link
              @click="handleGenerateLink(row)"
              :loading="generateLinkLoading"
              v-if="row.is_link_generable"
            >
              生成链接
            </el-button>
            <el-dropdown v-if="hasMoreOperations(row)">
              <el-button type="primary" link>
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-if="row.is_stoppable" @click="handleStop(row)">停止投放</el-dropdown-item>
                  <el-dropdown-item v-if="row.is_restartable" @click="handleRestart(row)">重新投放</el-dropdown-item>
                  <el-dropdown-item v-if="row.is_delivery_mode_changeable" @click="handleChangeDeliveryMode(row)">修改投放策略</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="page"
          v-model:page-size="size"
          :page-sizes="[10, 20, 30, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 拒绝原因弹窗 -->
    <el-dialog
      v-model="rejectDialogVisible"
      title="审核拒绝"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="rejectForm">
        <el-form-item label="拒绝原因" prop="reason">
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入拒绝原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false" :disabled="rejectLoading">取 消</el-button>
          <el-button type="primary" @click="confirmReject" :loading="rejectLoading">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 投放策略弹窗 -->
    <el-dialog
      v-model="deliveryModeDialogVisible"
      title="修改投放策略"
      width="500px"
    >
      <el-form :model="deliveryModeForm" label-width="80px">
        <el-form-item label="投放策略" prop="mode">
          <el-radio-group v-model="deliveryModeForm.mode">
            <el-radio :label="DELIVERY_MODE.NORMAL">{{ DELIVERY_MODE_MAP[DELIVERY_MODE.NORMAL] }}</el-radio>
            <el-radio :label="DELIVERY_MODE.SPEED">{{ DELIVERY_MODE_MAP[DELIVERY_MODE.SPEED] }}</el-radio>
            <el-radio :label="DELIVERY_MODE.SLOW">{{ DELIVERY_MODE_MAP[DELIVERY_MODE.SLOW] }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="deliveryModeDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmChangeDeliveryMode">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/date'
import { getAdProducts } from '@/api/ad-product'
import { getAdSlots } from '@/api/ad-slot'
import { getMediaList } from '@/api/media'
import { 
  list as fetchAdSlotPlans, 
  approve, 
  reject, 
  updateDeliveryMode,
  generatePromotionLink 
} from '@/api/ad-slot-plan'
import { usePermission } from '@/hooks/usePermission'
import { useUserStore } from '@/store/modules/user'
import {
  PLAN_TYPE,
  PLAN_TYPE_MAP,
  AUDIT_STATUS,
  AUDIT_STATUS_MAP,
  DELIVERY_STATUS,
  DELIVERY_STATUS_MAP,
  STATUS_TAG_TYPE_MAP,
  DELIVERY_MODE,
  DELIVERY_MODE_MAP
} from '@/constants'

const router = useRouter()
const { hasPermission } = usePermission()
const userStore = useUserStore()

// 搜索表单
const searchForm = ref({
  code: '',
  media_id: null,
  ad_slot_id: null
})

// 投放产品列表
const adProducts = ref([])

// 媒体列表
const mediaList = ref([])
// 广告位列表
const adSlotList = ref([])

// 列表数据
const loading = ref(false)
const dataList = ref([])
const page = ref(1)
const size = ref(10)
const total = ref(0)

// 拒绝原因弹窗
const rejectDialogVisible = ref(false)
const rejectForm = ref({
  id: null,
  reason: ''
})
const rejectFormRef = ref(null)
const rejectRules = {
  reason: [
    { required: true, message: '请输入拒绝原因', trigger: 'blur' }
  ]
}

// 投放策略弹窗
const deliveryModeDialogVisible = ref(false)
const deliveryModeForm = ref({
  id: null,
  mode: null
})
const deliveryModeFormRef = ref(null)
const deliveryModeRules = {
  mode: [
    { required: true, message: '请选择投放策略', trigger: 'change' }
  ]
}

// 加载状态
const approveLoading = ref(false)
const rejectLoading = ref(false)

// 生成链接加载状态
const generateLinkLoading = ref(false)

// 计算操作权限
const computeOperationPermissions = (row) => {
  // 判断是否过期
  const isExpired = row.is_end_date_enabled && new Date(row.end_date) < new Date()
  
  // 计算各种操作权限
  const permissions = {
    is_approvable: hasPermission('plan:approve') && row.audit_status === AUDIT_STATUS.PENDING,
    is_rejectable: hasPermission('plan:reject') && row.audit_status === AUDIT_STATUS.PENDING,
    is_stoppable: hasPermission('plan:stop') && row.delivery_status === DELIVERY_STATUS.DELIVERING,
    is_restartable: hasPermission('plan:restart') && row.delivery_status === DELIVERY_STATUS.STOPPED && !isExpired,
    is_delivery_mode_changeable: hasPermission('plan:update_delivery_mode') && row.delivery_status === DELIVERY_STATUS.DELIVERING,
    is_editable: hasPermission('plan:edit') && [AUDIT_STATUS.PENDING, AUDIT_STATUS.REJECTED].includes(row.audit_status),
    // 只在配置中状态时显示生成链接按钮
    is_link_generable: hasPermission('plan:generate_link') && row.delivery_status === DELIVERY_STATUS.CONFIGURING
  }
  
  return {
    ...row,
    ...permissions
  }
}

// 获取投放产品列表
const fetchAdProducts = async () => {
  try {
    const res = await getAdProducts(
      {
        page: 1,
        size: 100
      }
    )
    adProducts.value = res.data?.list || []
  } catch (error) {
    console.error('获取投放产品列表失败:', error)
  }
}

// 获取媒体列表
const fetchMediaList = async () => {
  try {
    // 为下拉选择器获取所有媒体，传递大的page_size参数
    const res = await getMediaList({
      page: 1,
      size: 1000 // 获取最多1000条，应该足够覆盖所有媒体
    })
    mediaList.value = res.data?.list || []
  } catch (error) {
    console.error('获取媒体列表失败:', error)
  }
}

// 获取广告位列表
const fetchAdSlots = async (mediaId = '') => {
  try {
    // 如果传入mediaId则按媒体筛选，否则获取所有可见广告位
    const params = mediaId ? { media_id: mediaId } : {}
    const res = await getAdSlots(params)
    adSlotList.value = res.data?.list || []
  } catch (error) {
    console.error('获取广告位列表失败:', error)
  }
}

// 媒体选择改变时更新广告位列表
const handleMediaChange = (mediaId) => {
  searchForm.value.ad_slot_id = null // 清空已选择的广告位
  fetchAdSlots(mediaId) // 根据选择的媒体重新获取广告位列表
}

// 获取列表数据
const fetchList = async () => {
  loading.value = true
  try {
    const res = await fetchAdSlotPlans({
      page: page.value,
      size: size.value,
      ...searchForm.value
    })
    // 处理返回的数据,添加操作权限
    dataList.value = (res.data?.list || []).map(computeOperationPermissions)
    total.value = res.data?.total || 0
  } catch (error) {
    console.error('获取投放计划列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = (formRef) => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 搜索
const handleSearch = () => {
  page.value = 1
  fetchList()
}

// 重置
const handleReset = () => {
  searchForm.value = {
    code: '',
    media_id: null,
    ad_slot_id: null
  }
  fetchAdSlots() // 重新获取所有可见广告位
  page.value = 1
  fetchList()
}

// 新建
const handleCreate = async () => {
  console.log('🔧 准备跳转到新建计划页面')
  
  // 检查权限
  const hasCreatePermission = hasPermission('plan:create')
  console.log('🔧 权限检查结果:', {
    权限码: 'plan:create',
    有权限: hasCreatePermission,
    用户权限列表: userStore.permissionCodes,
    用户角色: userStore.roles,
    是否管理员: userStore.isAdmin
  })
  
  // 检查目标路由权限
  try {
    const targetRoute = router.resolve('/ad-slot-plans/edit')
    console.log('🔧 目标路由信息:', {
      完整对象: targetRoute,
      路径: targetRoute.href || targetRoute.fullPath,
      元信息: targetRoute.meta || (targetRoute.route && targetRoute.route.meta)
    })
    
    // 使用权限工具检查路由权限
    const { PermissionUtils } = await import('@/utils/permission')
    const routeToCheck = targetRoute.route || targetRoute
    const routePermissionResult = PermissionUtils.checkRoutePermissionDetail(routeToCheck)
    console.log('🔧 路由权限检查结果:', routePermissionResult)
  } catch (error) {
    console.error('🔧 路由检查出错:', error)
  }
  
  if (!hasCreatePermission) {
    console.warn('⚠️  缺少创建权限，但仍然尝试跳转')
    ElMessage.warning('您没有创建投放计划的权限')
    return
  }
  
  console.log('✅ 权限检查通过，开始跳转')
  router.push('/ad-slot-plans/edit')
}

// 检查权限状态（调试用）
const checkPermissionState = () => {
  console.log('🔧 手动权限状态检查:', {
    当前页面: window.location.href,
    用户Store状态: {
      id: userStore.id,
      name: userStore.name,
      roles: userStore.roles,
      permissions数量: userStore.permissions?.length || 0,
      permissionCodes数量: userStore.permissionCodes?.length || 0,
      permissionCodes内容: userStore.permissionCodes
    },
    权限检查结果: {
      'plan:create': userStore.hasPermission('plan:create'),
      'plan:edit': userStore.hasPermission('plan:edit'),
      'plan:view': userStore.hasPermission('plan:view')
    },
    hasPermission函数: hasPermission('plan:create'),
    localStorage: {
      token: localStorage.getItem('token')?.substring(0, 20) + '...'
    }
  })
  
  // 尝试重新获取权限
  console.log('🔧 尝试重新获取权限...')
  userStore.getUserPermissions().then(() => {
    console.log('🔧 权限重新获取完成')
  }).catch(err => {
    console.error('🔧 权限重新获取失败:', err)
  })
}

// 编辑
const handleEdit = (row) => {
  router.push(`/ad-slot-plans/edit/${row.id}`)
}

// 查看
const handleView = (row) => {
  router.push(`/ad-slot-plans/view/${row.id}`)
}

// 审核通过
const handleApprove = async (row) => {
  try {
    await ElMessageBox.confirm('确认审核通过该计划?', '提示', {
      type: 'warning'
    })
    approveLoading.value = true
    await approve(row.id)
    ElMessage.success('审核通过成功')
    // 审核通过后自动生成链接
    try {
      await generatePromotionLink(row.id)
      ElMessage.success('生成链接成功')
    } catch (error) {
      console.error('生成链接失败:', error)
      ElMessage.error(error?.message || '生成链接失败')
    }
    fetchList()
  } catch (error) {
    console.error('审核通过失败:', error)
    if (error !== 'cancel') {
      ElMessage.error(error?.message || '审核通过失败')
    }
  } finally {
    approveLoading.value = false
  }
}

// 审核拒绝
const handleReject = async (row) => {
  rejectForm.value = {
    id: row.id,
    reason: ''
  }
  rejectDialogVisible.value = true
}

// 确认拒绝
const confirmReject = async () => {
  if (!rejectForm.value.reason) {
    ElMessage.warning('请输入拒绝原因')
    return
  }

  try {
    rejectLoading.value = true
    await reject({
      id: rejectForm.value.id,
      reason: rejectForm.value.reason
    })
    ElMessage.success('审核拒绝成功')
    rejectDialogVisible.value = false
    rejectForm.value = {
      id: null,
      reason: ''
    }
    fetchList()
  } catch (error) {
    console.error('审核拒绝失败:', error)
    ElMessage.error(error?.message || '审核拒绝失败')
  } finally {
    rejectLoading.value = false
  }
}

// 更新投放策略
const handleUpdateDeliveryMode = async (row) => {
  deliveryModeForm.value = {
    id: row.id,
    mode: row.delivery_mode
  }
  deliveryModeDialogVisible.value = true
}

const handleDeliveryModeConfirm = async () => {
  if (!deliveryModeFormRef.value) return
  await deliveryModeFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await updateDeliveryMode({
          id: deliveryModeForm.value.id,
          mode: deliveryModeForm.value.mode
        })
        ElMessage.success('更新投放策略成功')
        deliveryModeDialogVisible.value = false
        resetForm(deliveryModeFormRef)
        fetchList()
      } catch (error) {
        console.error(error)
        ElMessage.error('更新投放策略失败')
      }
    }
  })
}

// 分页大小改变
const handleSizeChange = (val) => {
  size.value = val
  fetchList()
}

// 页码改变
const handleCurrentChange = (val) => {
  page.value = val
  fetchList()
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  return STATUS_TAG_TYPE_MAP[status] || 'info'
}

// 判断是否有更多操作
const hasMoreOperations = (row) => {
  return row.is_stoppable || 
         row.is_restartable || 
         row.is_delivery_mode_changeable
}

// 生成链接
const handleGenerateLink = async (row) => {
  try {
    generateLinkLoading.value = true
    await generatePromotionLink(row.id)
    ElMessage.success('生成链接成功')
    fetchList()
  } catch (error) {
    console.error('生成链接失败:', error)
    ElMessage.error(error?.message || '生成链接失败')
  } finally {
    generateLinkLoading.value = false
  }
}

onMounted(() => {
  fetchAdProducts()
  fetchMediaList()
  fetchAdSlots() // 初始加载所有可见广告位
  fetchList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .search-form {
    :deep(.el-form-item) {
      margin-right: 16px;
      width: 280px;
      
      .el-select {
        width: 100%;
      }
    }

    .search-buttons {
      margin-left: auto;
      width: auto;
    }
  }

  .table-operations {
    margin: 16px 0;
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }

  .text-sm {
    font-size: 12px;
  }

  .text-gray-500 {
    color: #909399;
  }
}
</style> 