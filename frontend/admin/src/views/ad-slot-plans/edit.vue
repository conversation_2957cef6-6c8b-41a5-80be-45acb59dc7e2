<template>
  <div class="ad-slot-plan-edit">
    <el-card>
      <template #header>
        <div class="card-header">
          <el-button link @click="handleBack">
            <el-icon><arrow-left /></el-icon>
            返回
          </el-button>
          <span class="title">{{ isEdit ? '编辑计划' : '新建计划' }}</span>
          <div style="width: 60px"></div>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="plan-form"
      >
        <el-card class="mb-4">
          <template #header>
            <div class="sub-title">基本信息</div>
          </template>

          <el-form-item label="计划类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择计划类型">
              <el-option
                v-for="(label, value) in PLAN_TYPE_MAP"
                :key="value"
                :label="label"
                :value="value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="投放产品" prop="ad_product_id">
            <el-select 
              v-model="form.ad_product_id" 
              placeholder="请选择投放产品"
              filterable
            >
              <el-option
                v-for="item in adProducts"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <template v-if="!route.params.id">
          <el-form-item label="媒体" prop="media_id">
            <el-select 
              v-model="form.media_id" 
              placeholder="请选择媒体"
              :disabled="!form.ad_product_id"
              filterable
            >
              <el-option
                v-for="item in mediaList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="广告位" prop="ad_slot_id">
            <el-select 
              v-model="form.ad_slot_id" 
              placeholder="请选择广告位"
              :disabled="!form.media_id"
            >
              <el-option
                v-for="item in adSlots"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="价格" prop="price">
            <el-input-number 
              v-model="form.price" 
              :min="0"
              :precision="2"
              :step="0.01"
              style="width: 200px"
            />
          </el-form-item>
          </template>

          <el-form-item label="开始时间" prop="start_date">
            <el-date-picker
              v-model="form.start_date"
              type="datetime"
              placeholder="选择开始时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DDTHH:mm:ssZ"
            />
          </el-form-item>

          <el-form-item label="创意" prop="ad_creative_id">
            <el-select 
              v-model="form.ad_creative_id" 
              placeholder="请选择创意"
              clearable
            >
              <el-option
                v-for="item in creativeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

        <el-form-item label="素材类型" prop="material_type">
          <el-select v-model="form.material_type" placeholder="请选择素材类型" style="width: 100%" clearable>
            <el-option
              v-for="item in materialTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

          <el-form-item label="关联上游计划" prop="plan_ids">
          <el-select
                        class="w-[280px]"
                        v-model="form.plan_ids"
                        multiple
                        filterable
                        placeholder="请选择上游计划"
                    >
                      <el-option
                          v-for="item in planOptions"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id"
                      />
                    </el-select>
          </el-form-item>

          <el-form-item label="关联口令" prop="pwd_id">
                    <el-select
                                  class="w-[280px]"
                                  v-model="form.pwd_id"
                                  filterable
                                  placeholder="请选择口令"
                              >
                                <el-option
                                    v-for="item in pwdOptions"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                />
                              </el-select>
                    </el-form-item>

          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-card>

        <div class="form-buttons">
          <el-button @click="handleBack">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ isEdit ? '保存' : '创建' }}
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { getActiveAdProducts } from '@/api/ad-product'
import { getAdSlots } from '@/api/ad-slot'
import { create, update, get as getAdSlotPlan, getPlatformObjData } from '@/api/ad-slot-plan'
import { PLAN_TYPE, PLAN_TYPE_MAP } from '@/constants'
import { getMediaList } from '@/api/media'
import { list as getCreativeList } from '@/api/ad-creative'

const route = useRoute()
const router = useRouter()
const formRef = ref(null)
const loading = ref(false)

// 是否是编辑模式
const isEdit = computed(() => !!route.params.id)

// 表单数据
const form = ref({
  type: PLAN_TYPE.TEST,
  ad_product_id: null,
  media_id: null,
  ad_slot_id: null,
  price: 0,
  start_date: null,
  material_type: null,
  remark: '',
  is_end_date_enabled: false,
  ad_creative_id: null,
  rels: [],
  plan_ids: [],
  pwd_id: null,
})

const materialTypeOptions = [
    { label: '图文', value: 1 },
    { label: '视频', value: 2 }
]

// 表单校验规则
const rules = {
  type: [
    { required: true, message: '请选择计划类型', trigger: 'change' }
  ],
  ad_product_id: [
    { required: true, message: '请选择投放产品', trigger: 'change' }
  ],
  media_id: [
    { required: true, message: '请选择媒体', trigger: 'change' }
  ],
  ad_slot_id: [
    { required: true, message: '请选择广告位', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入价格', trigger: 'blur' }
  ],
  start_date: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  material_type: [
    { required: false, message: '请选择素材类型', trigger: 'change' }
  ],
  ad_creative_id: [
    { required: false, message: '请选择创意', trigger: 'change' }
  ]
}

// 投放产品列表
const adProducts = ref([])
// 广告位列表
const adSlots = ref([])
// 媒体列表
const mediaList = ref([])
// 创意列表
const creativeList = ref([])

// 获取投放产品列表
const fetchAdProducts = async () => {
  try {
    const res = await getActiveAdProducts()
    adProducts.value = res.data.list || []
  } catch (error) {
    console.error('获取投放产品列表失败:', error)
    ElMessage.error('获取投放产品列表失败')
  }
}

// 获取媒体列表
const fetchMediaList = async () => {
  try {
    const res = await getMediaList({ 
      size: 1000,
      audit_status: 'approved'
    })
    mediaList.value = res.data?.list || []
  } catch (error) {
    console.error('获取媒体列表失败:', error)
    ElMessage.error('获取媒体列表失败')
  }
}

// 获取广告位列表
const fetchAdSlots = async (mediaId) => {
  try {
    const res = await getAdSlots({ media_id: mediaId, size: 1000 })
    adSlots.value = res.data?.list || []
  } catch (error) {
    console.error('获取广告位列表失败:', error)
    ElMessage.error('获取广告位列表失败')
  }
}

// 获取创意列表
const fetchCreativeList = async () => {
  try {
    const res = await getCreativeList()
    creativeList.value = res.data?.list || []
  } catch (error) {
    console.error('获取创意列表失败:', error)
    ElMessage.error('获取创意列表失败')
  }
}

const planOptions = ref([])
const getPlanOption = async() => {
    const res = await getPlatformObjData({type: 1})
    planOptions.value = res.data.data
}

const pwdOptions = ref([])
const getPwdOption = async() => {
    const res = await getPlatformObjData({type: 2})
    pwdOptions.value = res.data.data
}

// 获取详情
const fetchDetail = async () => {
  try {
    const res = await getAdSlotPlan(route.params.id)
    const detail = res.data
    // 填充表单数据
    form.value = {
      type: detail.type,
      ad_product_id: detail.ad_product_id,
      media_id: detail.media_id,
      ad_slot_id: detail.ad_slot_id,
      price: detail.price,
      start_date: detail.start_date,
      material_type: detail.material_type === 0 ? null : detail.material_type,
      remark: detail.remark,
      is_end_date_enabled: false,
      ad_creative_id: detail.ad_creative_id === 0 ? null : detail.ad_creative_id,
      rels: detail.rels || [],
      plan_ids: [],
      pwd_id: null,
    }
    for (const rel of form.value.rels) {
      if (rel.relate_type == 1) {
        form.value.plan_ids.push(rel.relate_id)
      } else if (rel.relate_type == 2) {
        if (form.value.pwd_id) continue
        form.value.pwd_id = rel.relate_id
      }
    }
    // 加载广告位列表
    if (detail.ad_product_id) {
      await fetchMediaList()
      await fetchAdSlots(detail.media_id)
    }
  } catch (error) {
    console.error('获取投放计划详情失败:', error)
    ElMessage.error('获取投放计划详情失败')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  const rels = []
  if (form.value.plan_ids.length > 0 ){
    for (const planId of form.value.plan_ids) {
      rels.push({ type: 1, id: planId })
    }
  }
  if (form.value.pwd_id) {
    rels.push({ type: 2, id: form.value.pwd_id })
  }
  form.value.rels = rels

  // 将空值的创意和素材类型设置为0
  form.value.ad_creative_id = form.value.ad_creative_id === null ? 0 : form.value.ad_creative_id
  form.value.material_type = form.value.material_type === null ? 0 : form.value.material_type

  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        if (isEdit.value) {
          await update({
            id: route.params.id,
            ...form.value
          })
          ElMessage.success('更新成功')
        } else {
          await create(form.value)
          ElMessage.success('创建成功')
        }
        router.push('/ad-slot-plans')
      } catch (error) {
        console.error(error)
        ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      } finally {
        loading.value = false
      }
    }
  })
}

// 返回列表
const handleBack = () => {
  router.push('/ad-slot-plans')
}

// 监听投放产品变化
watch(() => form.value.ad_product_id, async (newVal) => {
  // 清空媒体和广告位选择
  form.value.media_id = null
  form.value.ad_slot_id = null
  // 清空广告位列表
  adSlots.value = []
})

// 监听媒体变化
watch(() => form.value.media_id, async (newVal) => {
  // 清空广告位选择
  form.value.ad_slot_id = null
  // 清空广告位列表
  adSlots.value = []

  if (newVal) {
    await fetchAdSlots(newVal)
  }
})

onMounted(async () => {
  await Promise.all([
    fetchAdProducts(),
    fetchCreativeList(),
    fetchMediaList() // 在组件挂载时获取所有媒体列表
  ])
  if (isEdit.value) {
    await fetchDetail()
  }
 await getPlanOption()
 await getPwdOption()
})
</script>

<style lang="scss" scoped>
.ad-slot-plan-edit {
  padding: 20px;

  @media screen and (max-width: 768px) {
    padding: 10px;
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
      font-weight: bold;
      flex: 1;
      text-align: center;
    }
  }

  .sub-title {
    font-weight: bold;
    font-size: 16px;
  }

  .plan-form {
    max-width: 800px;
    margin: 0 auto;

    :deep(.el-form-item__content) {
      justify-content: flex-start;
    }

    .el-select {
      width: 100%;
      max-width: 300px;
    }

    .el-date-picker {
      width: 100%;
      max-width: 300px;
    }
  }

  .form-buttons {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 24px;
  }

  .mb-4 {
    margin-bottom: 16px;
  }
}
</style> 