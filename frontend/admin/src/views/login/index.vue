<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-form-container">
        <div class="title-container">
          <h3 class="title">媒介投放管理后台</h3>
          <p class="subtitle">欢迎回来! 请登录您的账号</p>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
        >
          <el-form-item prop="email">
            <el-input
              ref="emailRef"
              v-model="loginForm.email"
              placeholder="请输入邮箱"
              type="email"
              tabindex="1"
              :prefix-icon="Message"
              size="large"
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              ref="passwordRef"
              v-model="loginForm.password"
              :type="passwordVisible ? 'text' : 'password'"
              placeholder="请输入密码"
              tabindex="2"
              :prefix-icon="Lock"
              size="large"
            >
              <template #suffix>
                <el-icon 
                  class="cursor-pointer"
                  @click="passwordVisible = !passwordVisible"
                >
                  <component :is="passwordVisible ? 'View' : 'Hide'" />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-button
            :loading="loading"
            type="primary"
            class="login-button mt-30"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form>
      </div>

      <div class="login-image">
        <div class="image-content">
          <div class="decoration-circle"></div>
          <div class="decoration-square"></div>
          <h2>媒介投放管理后台</h2>
          <p>专业的媒介资源管理与投放分析平台</p>
          <p>助力企业实现精准投放、效果优化</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { Lock, Message, View, Hide } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

const loginFormRef = ref(null)
const emailRef = ref(null)
const passwordRef = ref(null)
const loading = ref(false)
const passwordVisible = ref(false)

const loginForm = ref({
  email: '',
  password: ''
})

const validateEmail = (rule, value, callback) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!value) {
    callback(new Error('请输入邮箱'))
  } else if (!emailRegex.test(value)) {
    callback(new Error('请输入正确的邮箱格式'))
  } else {
    callback()
  }
}

const loginRules = {
  email: [
    { required: true, trigger: 'blur', validator: validateEmail }
  ],
  password: [
    { required: true, trigger: 'blur', message: '请输入密码' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
  ]
}

const handleLogin = () => {
  loginFormRef.value.validate(async valid => {
    if (valid) {
      loading.value = true
      try {
        await userStore.login(loginForm.value)
        ElMessage.success('登录成功')
        router.push({ path: '/' })
      } catch (error) {
        ElMessage.error(error.message || '登录失败')
      } finally {
        loading.value = false
      }
    }
  })
}

onMounted(() => {
  emailRef.value.focus()
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #1a365d 0%, #2d3748 100%);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;

  .login-box {
    width: 1000px;
    height: 600px;
    background: white;
    border-radius: 24px;
    box-shadow: 0 30px 60px -12px rgba(0, 0, 0, 0.35);
    display: flex;
    overflow: hidden;
    position: relative;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-5px);
    }

    .login-form-container {
      width: 50%;
      padding: 48px;
      position: relative;
      z-index: 1;

      .title-container {
        margin-bottom: 48px;
        
        .title {
          font-size: 32px;
          color: #1a365d;
          margin: 0 0 12px 0;
          font-weight: bold;
          letter-spacing: 1px;
        }

        .subtitle {
          font-size: 16px;
          color: #4a5568;
          margin: 0;
          letter-spacing: 0.5px;
        }
      }

      .login-form {
        .login-button {
          width: 100%;
          height: 52px;
          font-size: 16px;
          font-weight: 500;
          letter-spacing: 1px;
          background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
          border: none;
          border-radius: 8px;
          transition: transform 0.2s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }

    .login-image {
      width: 50%;
      background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px;
      color: white;
      text-align: center;
      position: relative;
      overflow: hidden;

      .decoration-circle {
        position: absolute;
        width: 200px;
        height: 200px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        top: -100px;
        right: -100px;
        animation: float 6s ease-in-out infinite;
      }

      .decoration-square {
        position: absolute;
        width: 100px;
        height: 100px;
        background: rgba(255, 255, 255, 0.1);
        bottom: -50px;
        left: -50px;
        transform: rotate(45deg);
        animation: float 8s ease-in-out infinite reverse;
      }

      .image-content {
        position: relative;
        z-index: 1;

        h2 {
          font-size: 36px;
          margin-bottom: 24px;
          font-weight: 600;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        p {
          font-size: 16px;
          opacity: 0.9;
          margin-bottom: 12px;
          line-height: 1.6;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
}

:deep(.el-input) {
  .el-input__wrapper {
    padding: 1px 15px;
    background: #edf2f7;
    border: none;
    height: 52px;
    box-shadow: none;
    transition: all 0.3s;
    border-radius: 8px;

    &:hover {
      background: #e2e8f0;
    }

    &.is-focus {
      background: #fff;
      box-shadow: 0 0 0 2px #4299e1 inset !important;
    }
  }

  .el-input__inner {
    height: 50px;
    font-size: 15px;
  }
}

:deep(.el-checkbox) {
  .el-checkbox__label {
    color: #4a5568;
  }
}

.mb-20 {
  margin-bottom: 24px;
}

.mt-30 {
  margin-top: 30px;
}

.cursor-pointer {
  cursor: pointer;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
}

.login-form-container {
  animation: fadeIn 0.8s ease-out;
}
</style> 