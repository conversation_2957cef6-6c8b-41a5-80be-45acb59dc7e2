<template>
  <div class="dashboard-container">
    <!-- 全局筛选条件 -->
    <el-card shadow="hover" class="filter-card">
      <el-form :model="filterForm" class="filter-form" v-loading="loading">
        <!-- 时间范围行 -->
        <el-row :gutter="16" class="time-range-row">
          <el-col :span="24">
            <el-form-item label="时间范围" class="time-range-item">
              <div class="time-range-content">
                <el-radio-group v-model="filterForm.timeRange" class="filter-radio-group">
                  <el-radio-button label="实时">实时</el-radio-button>
                  <el-radio-button label="昨日">昨日</el-radio-button>
                  <el-radio-button label="近7天">近7天</el-radio-button>
                  <el-radio-button label="近15天">近15天</el-radio-button>
                  <el-radio-button label="近30天">近30天</el-radio-button>
                  <el-radio-button label="自定义">自定义</el-radio-button>
                </el-radio-group>
                
                <el-date-picker
                  v-if="filterForm.timeRange === '自定义'"
                  v-model="filterForm.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :disabled-date="disabledDate"
                  @change="handleDateRangeChange"
                  class="date-picker"
                />
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 类型筛选行 -->
        <el-row :gutter="16" class="type-filter-row">
          <el-col :span="24">
            <el-form-item label="投放类型" class="type-filter-item">
              <div class="type-filter-content">
                <!-- 添加大类选择 -->
                <el-radio-group v-model="filterForm.category" class="filter-radio-group" @change="handleCategoryChange">
                  <el-radio-button label="alipay">支付宝</el-radio-button>
                  <el-radio-button label="wechat">微信</el-radio-button>
                  <el-radio-button label="other">其他</el-radio-button>
                </el-radio-group>

                <!-- 根据大类显示对应的类型选项 -->
                <el-radio-group v-model="filterForm.type" class="filter-radio-group">
                  <template v-if="filterForm.category === 'alipay'">
                    <el-radio-button label="0">全部</el-radio-button>
                    <el-radio-button label="1">普通投放</el-radio-button>
                    <el-radio-button label="3">灯火</el-radio-button>
                  </template>
                  <template v-else-if="filterForm.category === 'wechat'">
                    <el-radio-button label="0">全部</el-radio-button>
                    <el-radio-button label="1">普通投放</el-radio-button>
                    <el-radio-button label="2">CPS合作</el-radio-button>
                  </template>
                </el-radio-group>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 其他筛选条件行 -->
        <el-row :gutter="16" class="filter-row">
          <el-col :xs="24" :sm="24" :md="8" :lg="6" :xl="6" v-if="filterOptions.showMediaList">
            <el-form-item label="媒介" class="filter-item">
              <el-select 
                v-model="filterForm.media" 
                placeholder="请选择媒介" 
                clearable
                class="filter-select"
                @change="handleMediaChange"
              >
                <el-option 
                  v-for="item in filterOptions.userList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="24" :md="8" :lg="6" :xl="6">
            <el-form-item label="媒体账号" class="filter-item">
              <el-select 
                v-model="filterForm.mediaAccount" 
                placeholder="请选择媒体账号" 
                clearable
                class="filter-select"
                @change="handleMediaAccountChange"
                filterable
              >
                <el-option 
                  v-for="item in currentMediaAccounts"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :xs="24" :sm="24" :md="8" :lg="6" :xl="6">
            <el-form-item label="投放计划" class="filter-item">
              <el-select 
                v-model="filterForm.plan" 
                placeholder="请选择投放计划" 
                clearable
                class="filter-select"
                filterable
              >
                <el-option 
                  v-for="item in currentPlans"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <!-- <el-col :xs="24" :sm="24" :md="8" :lg="6" :xl="6">
            <el-form-item label="产品" class="filter-item">
              <el-select 
                v-model="filterForm.product" 
                placeholder="请选择产品" 
                clearable
                class="filter-select"
              >
                <el-option 
                  v-for="item in filterOptions.productList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col> -->
        </el-row>
        
        <!-- 按钮行 -->
        <el-row>
          <el-col :span="24" class="filter-buttons">
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleRefresh">刷新</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 顶部数据卡片 -->
    <el-row :gutter="16" class="mt-16">
      <template v-if="filterForm.timeRange === '实时'">
        <el-col v-for="(metric, key) in metricsData" :key="key" :xs="24" :sm="12" :md="8" :lg="4.8">
          <el-card shadow="hover" class="data-card" :class="{ active: activeMetric === key }" @click="handleMetricClick(key)">
            <div class="card-header">
              <span class="title">{{ metric.label }}</span>
              <el-tooltip content="查看详情" placement="top">
                <el-icon><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
            <div class="card-body">
              <div class="main-value">
                {{ formatMetricValue(metric.value, key) }}
              </div>
              <!-- 去掉较昨日和较上周的比较数据 -->
            </div>
          </el-card>
        </el-col>
      </template>

      <template v-else>
        <el-col v-for="(metric, key) in metricsData" :key="key" :xs="24" :sm="12" :md="8" :lg="4">
          <el-card shadow="hover" class="data-card" :class="{ active: activeMetric === key }" @click="handleMetricClick(key)">
            <div class="card-header">
              <span class="title">{{ metric.label }}</span>
              <el-tooltip content="查看详情" placement="top">
                <el-icon><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
            <div class="card-body">
              <div class="main-value">
                {{ formatMetricValue(metric.value, key) }}
              </div>
              <!-- 根据时间范围条件显示比较数据 -->
              <div class="compare-values" v-if="filterForm.timeRange !== '昨日'">
                <div class="compare-item">
                  <span class="label">较前期</span>
                  <span :class="['value', metric.change > 0 ? 'down' : 'up']">
                    {{ formatPercentage(metric.change) }}
                  </span>
                </div>
                <div class="compare-item" v-if="filterForm.timeRange === '实时'">
                  <span class="label">较同期</span>
                  <span :class="['value', metric.weekChange > 0 ? 'down' : 'up']">
                    {{ formatPercentage(metric.weekChange) }}
                  </span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </template>
    </el-row>

    <!-- 趋势图表 -->
    <el-card shadow="hover" class="chart-card">
      <div class="card-header">
        <span class="title">趋势分析</span>
        <div class="header-actions">
          <el-button 
            @click="handleExportTrend"
            :loading="exportLoading"
          >
            <el-icon><Download /></el-icon>
            导出趋势数据
          </el-button>
          <el-button 
            @click="showRegionDialog"
          >
            <el-icon><Histogram /></el-icon>
            地域分析
          </el-button>
          <el-button 
            @click="showOrderTypeDialog"
          >
            <el-icon><PieChart /></el-icon>
            订单类型分析
          </el-button>
        </div>
      </div>
      <div class="chart-container" ref="trendChartRef"></div>
    </el-card>

    <!-- 地域分析弹窗 -->
    <el-dialog
      v-model="regionDialogVisible"
      title="地域分析"
      width="80%"
      @opened="initRegionChart"
      @closed="destroyRegionChart"
      destroy-on-close
    >
      <div class="dialog-chart-container" ref="regionChartRef"></div>
    </el-dialog>

    <!-- 订单类型分析弹窗 -->
    <el-dialog
      v-model="orderTypeDialogVisible" 
      title="订单类型分析"
      width="80%"
      @opened="initOrderTypeChart"
      @closed="destroyOrderTypeChart"
      destroy-on-close
    >
      <div class="dialog-chart-container" ref="orderTypeChartRef"></div>
    </el-dialog>

    <!-- 分计划数据表格 -->
    <el-card shadow="hover" class="table-card">
      <div class="card-header">
        <span class="title">分计划数据</span>
        <div class="header-actions">
          <el-switch
            v-model="filterZeroValue"
            active-text="过滤零值数据"
            :loading="planTableLoading"
            @change="handleFilterChange"
          />
          <el-button type="primary" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>
      
      <el-table
        :data="filteredPlanTableData"
        :loading="planTableLoading"
        style="width: 100%"
        border
        stripe
        :header-cell-style="{ background: '#f5f7fa' }"
      >
        <el-table-column
          v-for="col in planTableColumns"
          :key="col.prop"
          :prop="col.prop"
          :label="col.label"
          :min-width="col.minWidth"
          :sortable="col.sortable"
          :formatter="col.formatter"
          align="center"
        />
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue'
import { InfoFilled, Histogram, PieChart, Download } from '@element-plus/icons-vue'
import * as echarts from 'echarts/core'
import { LineChart, BarChart, PieChart as PieChartComponent } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent
} from 'echarts/components'
import { UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import { ElMessage } from 'element-plus'
import { getMetrics, getTrendData, getRegionData, getOrderTypeData, getProducts, getPlanStats, getDynamicFilterOptions } from '@/api/dashboard'
import * as XLSX from 'xlsx'
import { useUserStore } from '@/store/modules/user'

echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  LineChart,
  BarChart,
  PieChartComponent,
  CanvasRenderer,
  UniversalTransition
])

const timeRange = ref('实时')
const trendChartRef = ref(null)
const regionChartRef = ref(null)
const orderTypeChartRef = ref(null)
let trendChart = null
let regionChart = null
const orderTypeChart = ref(null)
let resizeObserver = null
const activeMetric = ref('orders') // 默认选中订单数
const chartType = ref('trend') // trend, region, orderType

// 筛选选项数据
const filterOptions = ref({
  showMediaList: false,
  mediaList: [], // 媒体列表
  productList: [],
  userList: [] // 媒介(用户)列表
})

// 筛选条件
const filterForm = ref({
  timeRange: '实时',
  dateRange: null,
  startDate: (() => {
    const d = new Date()
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  })(), // 内联格式化今天日期
  endDate: (() => {
    const d = new Date()
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  })(), // 内联格式化今天日期
  type: '0', // 默认选中全部
  media: '',
  mediaAccount: '',
  plan: '',
  product: '',
  category: 'alipay'
})

// 当前选中媒介的账号列表
const currentMediaAccounts = ref([])

// 当前选中账号的计划列表 
const currentPlans = ref([])

// 加载状态
const loading = ref(false)

// 弹窗可见性控制
const regionDialogVisible = ref(false)
const orderTypeDialogVisible = ref(false)

// 表格数据
const planTableData = ref([])
const planTableLoading = ref(false)

// 指标数据
const metricsData = ref({})

// 添加缓存相关变量
const trendDataCache = ref(new Map()) // 趋势数据缓存
const cacheExpireTime = 5 * 60 * 1000 // 缓存过期时间5分钟

// 添加当前option引用
const currentChartOption = ref(null)

// 订单类型名称映射
const orderTypeNames = {
  '1400': '饿了么CPS',
  '1600': '飞猪',
  '1410': '拼团',
  '1411': '拼团CPS',
  '1401': '叠红包',
  '1100': '淘宝'
}

// 获取用户信息
const userStore = useUserStore()

// 添加过滤零值数据的开关状态
const filterZeroValue = ref(false)

// 添加过滤后的表格数据计算属性
const filteredPlanTableData = computed(() => {
  if (!filterZeroValue.value) {
    return planTableData.value
  }
  
  return planTableData.value.filter(row => {
    // 过滤掉利润为0、成本为0、结算利润为0的数据
    return row.profit !== 0 && row.cost !== 0 && row.settled_profit !== 0
  })
})

// 处理过滤开关变化
const handleFilterChange = (value) => {
  console.log('过滤零值数据:', value)
}

// 初始化仪表盘数据
const initDashboard = async () => {
  try {
    loading.value = true
    
    // 1. 加载筛选选项
    await loadFilterOptions()
    
    // 2. 加载指标数据
    await loadMetricsData()
    
    // 3. 初始化图表
    initChart()

    // 4. 加载分计划数据
    await loadPlanStats()
  } catch (err) {
    console.error('初始化仪表盘失败:', err) 
    ElMessage.error('初始化仪表盘失败')
  } finally {
    loading.value = false
  }
}

// 加载指标数据
const loadMetricsData = async () => {
  try {
    loading.value = true
    
    const params = {
      user_id: filterForm.value.media || '',
      media_id: filterForm.value.mediaAccount || '',
      plan_id: filterForm.value.plan || '',
      product_id: filterForm.value.product || '',
      type: filterForm.value.type === '0' ? '' : filterForm.value.type, // 当选择全部时，不传type参数
      category: filterForm.value.category
    }

    // 如果不是实时数据,添加日期参数
    if (filterForm.value.timeRange !== '实时') {
      // 验证日期参数
      if (!filterForm.value.startDate || !filterForm.value.endDate) {
        ElMessage.warning('请选择时间范围')
        return
      }
      params.start_date = filterForm.value.startDate
      params.end_date = filterForm.value.endDate
    } else {
      // 实时数据使用今天的日期
      const today = formatDate(new Date())
      params.start_date = today
      params.end_date = today
    }

    const res = await getMetrics(params)
    if (res.code === 0) {
      // 计算日均订单数
      const startDate = new Date(params.start_date)
      const endDate = new Date(params.end_date)
      const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1
      
      // 添加日均订单指标
      const dailyOrders = {
        label: '日均订单数',
        value: (res.data.metrics.orders.value / days).toFixed(2),
        change: res.data.metrics.orders.change,
        weekChange: res.data.metrics.orders.weekChange
      }
      
      // 将日均订单插入到订单量后面
      const ordersIndex = Object.keys(res.data.metrics).indexOf('orders')
      const metrics = { ...res.data.metrics }
      const newMetrics = {}
      
      Object.keys(metrics).forEach((key, index) => {
        newMetrics[key] = metrics[key]
        if (index === ordersIndex) {
          newMetrics['daily_orders'] = dailyOrders
        }
      })
      
      metricsData.value = newMetrics
    }
  } catch (err) {
    console.error('获取指标数据失败:', err)
    ElMessage.error('获取指标数据失败')
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (date) => {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 格式化指标值
const formatMetricValue = (value, metric) => {
  if (typeof value !== 'number') return value
  
  // 金额类指标
  if (['estimated_commission', 'settled_commission', 'cost', 'estimated_profit', 'settled_profit'].includes(metric)) {
    return '¥' + value.toFixed(2)
  }
  
  // ROI和CPC
  if (['roi', 'cpc'].includes(metric)) {
    return value.toFixed(2)
  }
  
  // 其他数值指标
  return value.toLocaleString()
}

// 格式化百分比
const formatPercentage = (value) => {
  if (typeof value !== 'number') return '0%'
  return (value >= 0 ? '+' : '') + (value * 100).toFixed(2) + '%'
}

// 获取筛选选项数据
const loadFilterOptions = async () => {
  try {
    loading.value = true
    console.log('开始加载筛选选项...')
    
    // 获取产品列表
    const productRes = await getProducts()
    
    // 初始化筛选选项
    filterOptions.value = {
      showMediaList: true, // 默认显示媒介列表，后端会根据权限控制
      mediaList: [],
      productList: Array.isArray(productRes.data?.list) ? productRes.data.list : [],
      userList: [] // 初始化用户列表
    }

    // 加载所有筛选数据，后端会根据用户权限自动控制数据范围
    await Promise.all([
      loadUserList(),
      loadAllMediaAccounts(),
      loadAllPlans()
    ])
    
    console.log('处理后的 filterOptions:', filterOptions.value)
  } catch (err) {
    console.error('获取筛选选项失败:', err)
    ElMessage.error('获取筛选选项失败')
  } finally {
    loading.value = false
  }
}

// 获取当前用户的媒体账号（兼容其他页面）
const loadCurrentUserMediaAccounts = async () => {
  try {
    // 直接获取所有媒体账号，后端会根据权限控制
    await loadAllMediaAccounts()
  } catch (err) {
    console.error('获取当前用户媒体账号失败:', err)
    ElMessage.error('获取当前用户媒体账号失败')
    currentMediaAccounts.value = []
  }
}

// 获取媒介(用户)列表
const loadUserList = async () => {
  try {
    const res = await getDynamicFilterOptions({ type: 'users' })
    if (res.code === 0 && Array.isArray(res.data?.list)) {
      filterOptions.value.userList = res.data.list.map(user => ({
        id: user.id,
        name: user.name
      }))
    }
  } catch (err) {
    console.error('获取媒介列表失败:', err)
    ElMessage.error('获取媒介列表失败')
  }
}

// 加载所有媒体账号
const loadAllMediaAccounts = async () => {
  try {
    const params = { type: 'media_accounts' }
    
    // 如果选择了投放类型，也加入筛选条件
    if (filterForm.value.type && filterForm.value.type !== '0') {
      params.cooperation_type = filterForm.value.type
    } else {
    }
    
    // 如果选择了渠道分类，也加入筛选条件
    if (filterForm.value.category && filterForm.value.category !== '0') {
      params.category = filterForm.value.category
    }
    
    const res = await getDynamicFilterOptions(params)
    
    if (res.code === 0 && Array.isArray(res.data?.list)) {
      currentMediaAccounts.value = res.data.list.map(media => ({
        id: media.id,
        name: media.name
      }))
    } else {
      currentMediaAccounts.value = []
    }
  } catch (err) {
    console.error('获取媒体账号列表失败:', err)
    currentMediaAccounts.value = []
  }
}

// 加载所有投放计划
const loadAllPlans = async () => {
  try {
    const params = { type: 'plans' }
    
    // 如果选择了投放类型，也加入筛选条件
    if (filterForm.value.type && filterForm.value.type !== '0') {
      params.cooperation_type = filterForm.value.type
    }
    
    // 如果选择了渠道分类，也加入筛选条件
    if (filterForm.value.category && filterForm.value.category !== '0') {
      params.category = filterForm.value.category
    }
    
    const res = await getDynamicFilterOptions(params)
    
    if (res.code === 0 && Array.isArray(res.data?.list)) {
      currentPlans.value = res.data.list.map(plan => ({
        id: plan.id,
        name: plan.name
      }))
    } else {
      currentPlans.value = []
    }
  } catch (err) {
    console.error('获取投放计划列表失败:', err)
    currentPlans.value = []
  }
}

// 根据筛选条件加载投放计划
const loadFilteredPlans = async (mediaAccountId = null, userId = null) => {
  try {
    const params = { type: 'plans' }
    if (mediaAccountId) {
      params.media_account_id = mediaAccountId
    }
    
    // 如果选择了投放类型，也加入筛选条件
    if (filterForm.value.type && filterForm.value.type !== '0') {
      params.cooperation_type = filterForm.value.type
    }
    
    // 如果选择了渠道分类，也加入筛选条件
    if (filterForm.value.category && filterForm.value.category !== '0') {
      params.category = filterForm.value.category
    }
    
    // 注意：这里不直接传递user_id，因为后端的权限控制是通过token中的用户信息来处理的
    
    const res = await getDynamicFilterOptions(params)
    if (res.code === 0 && Array.isArray(res.data?.list)) {
      currentPlans.value = res.data.list.map(plan => ({
        id: plan.id,
        name: plan.name
      }))
      console.log('筛选后的计划列表:', currentPlans.value)
    } else {
      currentPlans.value = []
    }
  } catch (err) {
    console.error('获取投放计划列表失败:', err)
    currentPlans.value = []
  }
}

// 根据媒介筛选媒体账号
const loadFilteredMediaAccounts = async (userId) => {
  try {
    console.log('根据媒介筛选媒体账号,userId:', userId)
    
    const params = { 
      type: 'media_accounts',
      user_id: userId
    }
    
    // 如果选择了投放类型，也加入筛选条件
    if (filterForm.value.type && filterForm.value.type !== '0') {
      params.cooperation_type = filterForm.value.type
    }
    
    // 如果选择了渠道分类，也加入筛选条件
    if (filterForm.value.category && filterForm.value.category !== '0') {
      params.category = filterForm.value.category
    }
    
    const res = await getDynamicFilterOptions(params)
    
    if (res.code === 0) {
      currentMediaAccounts.value = Array.isArray(res.data?.list) 
        ? res.data.list.map(media => ({
            id: media.id,
            name: media.name
          }))
        : []
      
      console.log('筛选后的媒体账号列表:', currentMediaAccounts.value)
    } else {
      currentMediaAccounts.value = []
      ElMessage.error(res.message || '获取媒体列表失败')
    }
  } catch (err) {
    console.error('获取媒体账号列表失败:', err)
    currentMediaAccounts.value = []
  }
}

// 监听投放类型变化
watch(() => filterForm.value.type, async (newVal, oldVal) => {
  // 避免初始化时触发
  if (oldVal === undefined) return
  
  console.log('投放类型变化:', newVal, '当前filterForm.value.type:', filterForm.value.type)
  
  // 重置媒体账号和计划选择
  filterForm.value.mediaAccount = ''
  filterForm.value.plan = ''
  
  // 添加延迟确保状态更新完成
  await nextTick()
  
  console.log('准备重新加载数据，当前投放类型:', filterForm.value.type)
  
  // 根据投放类型重新加载媒体账号和计划
  if (filterForm.value.media) {
    // 已选择媒介：根据媒介和投放类型筛选
    await loadFilteredMediaAccounts(filterForm.value.media)
  } else {
    // 未选择媒介：根据投放类型加载所有（后端会根据权限控制）
    await loadAllMediaAccounts()
  }
  
  // 重新加载计划列表
  await loadAllPlans()
  
})

// 监听渠道分类变化
watch(() => filterForm.value.category, async (newVal, oldVal) => {
  // 避免初始化时触发
  if (oldVal === undefined) return
  
  console.log('渠道分类变化:', newVal, '当前filterForm.value.category:', filterForm.value.category)
  
  // 重置媒体账号和计划选择
  filterForm.value.mediaAccount = ''
  filterForm.value.plan = ''
  
  // 添加延迟确保状态更新完成
  await nextTick()
  
  console.log('准备重新加载数据，当前渠道分类:', filterForm.value.category)
  
  // 根据渠道分类重新加载媒体账号和计划
  if (filterForm.value.media) {
    // 已选择媒介：根据媒介和渠道分类筛选
    await loadFilteredMediaAccounts(filterForm.value.media)
  } else {
    // 未选择媒介：根据渠道分类加载所有（后端会根据权限控制）
    await loadAllMediaAccounts()
  }
  
  // 重新加载计划列表
  await loadAllPlans()
  
})

// 监听媒介变化
watch(() => filterForm.value.media, async (newVal, oldVal) => {
  // 避免初始化时触发
  if (oldVal === undefined) return
  
  console.log('选中的媒介变化:', newVal)
  
  if (!newVal) {
    // 清空选择，恢复显示数据
    filterForm.value.mediaAccount = ''
    filterForm.value.plan = ''
    await loadAllMediaAccounts()
    await loadAllPlans()
  } else {
    // 根据选择的媒介筛选媒体账号
    await loadFilteredMediaAccounts(newVal)
    // 重置计划选择并恢复显示计划
    filterForm.value.plan = ''
    await loadAllPlans()
  }
})

// 处理媒体账号变化
const handleMediaAccountChange = async (accountId) => {
  console.log('媒体账号变化:', accountId)
  
  if (!accountId) {
    // 清空选择，恢复显示计划
    filterForm.value.plan = ''
    await loadAllPlans()
    return
  }

  // 根据选择的媒体账号筛选计划
  await loadFilteredPlans(accountId)
}

// 监听时间范围变化
watch(() => filterForm.value.timeRange, (newVal) => {
  // 根据选择的时间范围设置日期
  const now = new Date()
  const today = formatDate(now)
  
  switch(newVal) {
    case '实时':
      filterForm.value.startDate = today
      filterForm.value.endDate = today
      break
    case '昨日':
      const yesterday = new Date(now)
      yesterday.setDate(yesterday.getDate() - 1)
      const yesterdayStr = formatDate(yesterday)
      filterForm.value.startDate = yesterdayStr
      filterForm.value.endDate = yesterdayStr
      break
    case '近7天':
      const sevenDaysAgo = new Date(now)
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
      const yesterdayDate = new Date(now)
      yesterdayDate.setDate(yesterdayDate.getDate() - 1)
      filterForm.value.startDate = formatDate(sevenDaysAgo)
      filterForm.value.endDate = formatDate(yesterdayDate)
      break
    case '近15天':
      const fifteenDaysAgo = new Date(now)
      fifteenDaysAgo.setDate(fifteenDaysAgo.getDate() - 15)
      const yesterdayFor15 = new Date(now)
      yesterdayFor15.setDate(yesterdayFor15.getDate() - 1)
      filterForm.value.startDate = formatDate(fifteenDaysAgo)
      filterForm.value.endDate = formatDate(yesterdayFor15)
      break
    case '近30天':
      const thirtyDaysAgo = new Date(now)
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      const yesterdayFor30 = new Date(now)
      yesterdayFor30.setDate(yesterdayFor30.getDate() - 1)
      filterForm.value.startDate = formatDate(thirtyDaysAgo)
      filterForm.value.endDate = formatDate(yesterdayFor30)
      break
    case '自定义':
      if (filterForm.value.dateRange) {
        filterForm.value.startDate = formatDate(filterForm.value.dateRange[0])
        filterForm.value.endDate = formatDate(filterForm.value.dateRange[1])
      } else {
        filterForm.value.startDate = ''
        filterForm.value.endDate = ''
      }
      break
  }

  // 只有在非自定义时间范围时才自动更新数据
  if (newVal !== '自定义') {
    updateDashboardData()
  }
})

// 监听日期范围选择器变化
watch(() => filterForm.value.dateRange, (newVal) => {
  if (newVal && filterForm.value.timeRange === '自定义') {
    filterForm.value.startDate = formatDate(newVal[0])
    filterForm.value.endDate = formatDate(newVal[1])
    updateDashboardData()
  }
})

// 监听筛选条件变化
watch(filterForm, (newVal, oldVal) => {
  // 当媒介变化时，清空媒体账号
  if (newVal.media !== oldVal.media) {
    filterForm.value.mediaAccount = ''
    filterForm.value.plan = ''
    filterForm.value.product = ''
  }
  
  // 当媒体账号变化时，清空计划
  if (newVal.mediaAccount !== oldVal.mediaAccount) {
    filterForm.value.plan = ''
    filterForm.value.product = ''
  }
  
  // 当计划变化时，清空产品
  if (newVal.plan !== oldVal.plan) {
    filterForm.value.product = ''
  }
  
  // 只有在非自定义时间范围时才自动更新数据
  if (newVal.timeRange !== '自定义') {
    updateDashboardData()
  }
}, { deep: true })

// 查询
const handleSearch = () => {
  updateDashboardData()
}

// 重置
const handleReset = () => {
  filterForm.value = {
    timeRange: '实时',
    dateRange: null,
    startDate: formatDate(new Date()),
    endDate: formatDate(new Date()),
    type: '0',
    category: 'alipay', // 默认选择支付宝
    media: '',
    mediaAccount: '',
    plan: '',
    product: ''
  }
  updateDashboardData()
}

// 处理指标卡片点击
const handleMetricClick = (metric) => {
  console.log('指标卡片点击:', metric)
  
  // 检查指标是否支持趋势
  const metricData = metricsData.value[metric]
  if (!metricData?.hasTrend) {
    console.warn('该指标不支持趋势分析:', metric)
    ElMessage.warning('该指标暂不支持趋势分析')
    return
  }
  
  console.log('更新活动指标:', metric)
  activeMetric.value = metric
  
  // 更新图表数据
  console.log('触发图表数据更新')
  // 使用 setTimeout 确保在下一个事件循环中更新
  setTimeout(() => {
    updateChartData()
  }, 0)
}

// 添加分析按钮点击处理
const handleAnalysisClick = async (type) => {
  chartType.value = type
  await updateChartData()
}

// 修改图表数据获取方法
const updateChartData = async () => {
  try {
    loading.value = true
    
    // 确保日期参数正确设置
    let startDate = filterForm.value.startDate
    let endDate = filterForm.value.endDate
    
    // 如果是实时模式且日期为空，设置为今天
    if (filterForm.value.timeRange === '实时' && (!startDate || !endDate)) {
      const today = formatDate(new Date())
      startDate = today
      endDate = today
      // 同时更新表单中的日期
      filterForm.value.startDate = today
      filterForm.value.endDate = today
    }
    
    // 验证日期参数
    if (!startDate || !endDate) {
      ElMessage.warning('请选择时间范围')
      return
    }
    
    // 准备请求参数
    const params = {
      metric: activeMetric.value,
      start_date: startDate,
      end_date: endDate,
      user_id: filterForm.value.media || '',
      media_id: filterForm.value.mediaAccount || '',
      plan_id: filterForm.value.plan || '',
      product_id: filterForm.value.product || '',
      type: filterForm.value.type,
      category: filterForm.value.category // 添加大类参数
    }

    console.log('Trend data request params:', params)
    
    // 请求数据
    const res = await getTrendData(params)
    console.log('Trend data response:', res)
    
    if (res.code === 0 && res.data) {
      // 更新图表
      await Promise.resolve() // 确保异步执行
      setChartOption(res.data)
    } else {
      ElMessage.error(res.message || '获取趋势数据失败')
    }
  } catch (err) {
    console.error('获取趋势数据失败:', err)
    ElMessage.error('获取趋势数据失败')
  } finally {
    loading.value = false
  }
}

// 修改图表配置方法
const setChartOption = (data) => {
  if (!trendChart) {
    console.error('趋势图实例未初始化')
    return
  }

  // 验证数据格式
  if (!data || !Array.isArray(data.time_points)) {
    console.error('趋势数据格式错误:', data)
    return
  }

  // 判断是否是同一天数据
  const startDate = filterForm.value.startDate
  const endDate = filterForm.value.endDate
  const isSameDay = startDate === endDate

  const option = {
    color: ['#409EFF', '#67C23A', '#E6A23C'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: (params) => {
        let result = ''
        if (isSameDay) {
          result = params[0].axisValue + ':00<br/>'
        } else {
          result = params[0].axisValue + '<br/>'
        }
        params.forEach(param => {
          const marker = `<span style="display:inline-block;margin-right:4px;border-radius:50%;width:10px;height:10px;background-color:${param.color};"></span>`
          result += marker + param.seriesName + ': ' + formatMetricValue(param.value, activeMetric.value) + '<br/>'
        })
        return result
      }
    },
    legend: {
      data: [data.labels.current, data.labels.previous, data.labels.same_period],
      right: '2%',
      top: 0
    },
    grid: {
      top: 40,
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.time_points,
      axisLine: {
        lineStyle: {
          color: '#DCDFE6'
        }
      },
      axisLabel: {
        color: '#606266'
      }
    },
    yAxis: {
      type: 'value',
      name: getMetricName(activeMetric.value),
      axisLine: {
        show: true,
        lineStyle: {
          color: '#409EFF'
        }
      },
      axisLabel: {
        color: '#606266',
        formatter: (value) => formatMetricValue(value, activeMetric.value)
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#E4E7ED'
        }
      }
    },
    series: [
      {
        name: data.labels.current,
        type: 'line',
        smooth: true,
        emphasis: {
          focus: 'series'
        },
        data: data.current,
        showSymbol: true,
        symbolSize: 6,
        label: {
          show: true,
          formatter: (params) => formatMetricValue(params.value, activeMetric.value),
          position: 'top'
        }
      },
      {
        name: data.labels.previous,
        type: 'line',
        smooth: true,
        emphasis: {
          focus: 'series'
        },
        data: data.previous,
        showSymbol: true,
        symbolSize: 6,
        lineStyle: {
          type: 'dashed'
        },
        label: {
          show: true,
          formatter: (params) => formatMetricValue(params.value, activeMetric.value),
          position: 'top'
        }
      }
    ]
  }

  // 在同一天数据时添加同期数据
  if (isSameDay) {
    option.series.push({
      name: data.labels.same_period,
      type: 'line',
      smooth: true,
      emphasis: {
        focus: 'series'
      },
      data: data.same_period,
      showSymbol: true,
      symbolSize: 6,
      lineStyle: {
        type: 'dashed'
      },
      label: {
        show: true,
        formatter: (params) => formatMetricValue(params.value, activeMetric.value),
        position: 'top'
      }
    })
  }
  
  // 保存当前option
  currentChartOption.value = option
  
  // 使用Promise包装异步更新
  Promise.resolve().then(() => {
    if (trendChart && currentChartOption.value) {
      trendChart.setOption(currentChartOption.value, true)
      trendChart.resize()
    }
  })
}

// 添加获取指标名称的方法
const getMetricName = (metric) => {
  const metricNames = {
    pageviews: '页面访问次数',
    visitors: '页面访问人数',
    orders: '订单量',
    estimated_commission: '预估佣金',
    settled_commission: '结算佣金',
    cost: '投放成本',
    roi: 'ROI',
    cpc: 'CPC',
    estimated_profit: '预估利润',
    settled_profit: '结算利润'
  }
  return metricNames[metric] || metric
}

// 初始化图表
const initChart = () => {
  if (!trendChartRef.value) return
  
  // 确保容器样式正确
  trendChartRef.value.style.width = '100%'
  trendChartRef.value.style.height = window.innerWidth <= 768 ? '300px' : '400px'
  
  // 初始化图表
  trendChart = echarts.init(trendChartRef.value)
  
  // 获取初始数据并设置图表
  Promise.resolve().then(() => {
    updateChartData()
  })
}

// 初始化组件
onMounted(() => {
  console.log('组件已挂载,开始加载数据...')
  initDashboard()
  window.addEventListener('resize', handleResize)
  // 使用ResizeObserver监听容器大小变化
  resizeObserver = new ResizeObserver(() => {
    handleResize()
  })
  if (trendChartRef.value) {
    resizeObserver.observe(trendChartRef.value)
  }
})

// 销毁组件
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
  if (trendChart) {
    trendChart.dispose()
  }
  if (regionChart) {
    regionChart.dispose()
  }
  if (orderTypeChart.value) {
    orderTypeChart.value.dispose()
  }
})

// 修改图表resize处理
const handleResize = () => {
  if (!trendChart || !trendChartRef.value) return
  
  // 获取容器当前宽度
  const width = trendChartRef.value.clientWidth
  const height = window.innerWidth <= 768 ? 300 : 400
  
  // 设置容器和图表大小
  trendChartRef.value.style.width = width + 'px'
  trendChartRef.value.style.height = height + 'px'
  
  // 使用Promise包装异步更新
  Promise.resolve().then(() => {
    if (trendChart) {
      trendChart.resize({
        width,
        height
      })
      
      // 只在有当前option时重新应用
      if (currentChartOption.value) {
        trendChart.setOption(currentChartOption.value, true)
      }
    }
  })
}

// 禁用日期选择范围
const disabledDate = (date) => {
  if (!filterForm.value.dateRange) {
    return false
  }
  const [startDate, endDate] = filterForm.value.dateRange
  const ninetyDaysFromStart = new Date(startDate)
  ninetyDaysFromStart.setDate(ninetyDaysFromStart.getDate() + 90)
  
  if (startDate && !endDate) {
    return date.getTime() < startDate.getTime() || date.getTime() > ninetyDaysFromStart.getTime()
  }
  
  return false
}

// 处理日期范围变化
const handleDateRangeChange = (val) => {
  if (val) {
    const [startDate, endDate] = val
    const diffDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24))
    
    if (diffDays > 90) {
      ElMessage.warning('时间跨度不能超过90天')
      filterForm.value.dateRange = null
      return
    }
  }
}

// 根据选中的指标获取对应的数据
const getMetricData = () => {
  switch (activeMetric.value) {
    case 'pageviews':
      return {
        name: '页面访问次数',
        current: [1177845, 1203456, 1156789, 1234567, 1189012, 1245678, 1198765],
        previous: [1100234, 1156789, 1089012, 1167890, 1123456, 1189012, 1145678],
        samePeriod: [980123, 1023456, 967890, 1045678, 989012, 1067890, 1012345]
      }
    case 'visitors':
      return {
        name: '页面访问人数',
        current: [900764, 923456, 878901, 945678, 912345, 956789, 934567],
        previous: [856789, 889012, 845678, 901234, 878901, 912345, 890123],
        samePeriod: [789012, 812345, 778901, 834567, 801234, 845678, 823456]
      }
    case 'orders':
      return {
        name: '订单量',
        current: [88478, 90234, 87654, 92345, 89765, 93456, 91234],
        previous: [85234, 87654, 84567, 88976, 86543, 90123, 87654],
        samePeriod: [80123, 82345, 79876, 83456, 81234, 84567, 82345]
      }
    case 'estimated_commission':
      return {
        name: '预估佣金',
        current: [98708.71, 101234.56, 97654.32, 103456.78, 99876.54, 104567.89, 100987.65],
        previous: [95432.10, 98765.43, 94321.09, 99876.54, 96543.21, 100987.65, 97654.32],
        samePeriod: [90123.45, 93456.78, 89012.34, 94567.89, 91234.56, 95678.90, 92345.67]
      }
    case 'settled_commission':
      return {
        name: '结算佣金',
        current: [94722.57, 97345.67, 93456.78, 98765.43, 95678.90, 99876.54, 96789.01],
        previous: [91234.56, 94567.89, 90123.45, 95678.90, 92345.67, 96789.01, 93456.78],
        samePeriod: [87654.32, 90987.65, 86543.21, 91234.56, 88765.43, 92345.67, 89876.54]
      }
    case 'ad_cost':
      return {
        name: '投放成本',
        current: [45632.89, 47123.45, 44567.89, 48234.56, 46789.01, 49345.67, 47890.12],
        previous: [43456.78, 45678.90, 42345.67, 46789.01, 44567.89, 47123.45, 45678.90],
        samePeriod: [41234.56, 43456.78, 40123.45, 44567.89, 42345.67, 45678.90, 43456.78]
      }
    case 'roi':
      return {
        name: 'ROI',
        current: [2.16, 2.25, 2.08, 2.31, 2.19, 2.35, 2.27],
        previous: [2.09, 2.18, 2.01, 2.24, 2.12, 2.28, 2.20],
        samePeriod: [1.98, 2.07, 1.90, 2.13, 2.01, 2.17, 2.09]
      }
    case 'cpc':
      return {
        name: 'CPC',
        current: [0.39, 0.41, 0.38, 0.42, 0.40, 0.43, 0.41],
        previous: [0.37, 0.39, 0.36, 0.40, 0.38, 0.41, 0.39],
        samePeriod: [0.35, 0.37, 0.34, 0.38, 0.36, 0.39, 0.37]
      }
    case 'estimated_profit':
      return {
        name: '预估利润',
        current: [53075.82, 54111.11, 53086.43, 55222.22, 53087.53, 55222.22, 53099.53],
        previous: [51975.32, 53011.11, 51986.43, 54122.22, 51987.53, 54122.22, 51999.53],
        samePeriod: [48888.89, 49924.68, 48899.99, 51035.79, 48901.10, 51035.79, 48913.10]
      }
    case 'settled_profit':
      return {
        name: '结算利润',
        current: [49089.68, 50222.22, 49888.89, 51333.33, 49890.00, 51333.33, 49902.00],
        previous: [47989.68, 49122.22, 48788.89, 50233.33, 48790.00, 50233.33, 48802.00],
        samePeriod: [46419.76, 47552.30, 47218.97, 48663.41, 47220.08, 48663.41, 47232.08]
      }
    default:
      return {
        name: '订单量',
        current: [],
        previous: [],
        samePeriod: []
      }
  }
}

// 显示地域分析弹窗
const showRegionDialog = () => {
  regionDialogVisible.value = true
}

// 显示订单类型分析弹窗
const showOrderTypeDialog = () => {
  orderTypeDialogVisible.value = true
}

// 初始化地域分析图表
const initRegionChart = async () => {
  if (!regionChartRef.value) return
  
  try {
    loading.value = true
    
    // 准备请求参数
    const params = {
      metric: activeMetric.value,
      start_date: filterForm.value.startDate,
      end_date: filterForm.value.endDate,
      user_id: filterForm.value.media || '',
      media_id: filterForm.value.mediaAccount || '',
      plan_id: filterForm.value.plan || '',
      product_id: filterForm.value.product || '',
      type: filterForm.value.type,
      category: filterForm.value.category || ''
    }

    // 获取地域分析数据
    const res = await getRegionData(params)
    if (res.code === 0 && res.data) {
      // 确保容器样式正确
      regionChartRef.value.style.width = '100%'
      regionChartRef.value.style.height = '400px'
      
      // 初始化图表
      regionChart = echarts.init(regionChartRef.value)
      
      // 设置图表配置
      const option = {
        title: {
          text: '地域分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: res.data.regions.map(item => item.name)
        },
        series: [
          {
            name: getMetricName(activeMetric.value),
            type: 'pie',
            radius: '50%',
            data: res.data.regions.map(item => ({
              name: item.name,
              value: item.value
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      
      regionChart.setOption(option)
      
      // 确保图表正确渲染
      nextTick(() => {
        regionChart.resize()
      })
    } else {
      ElMessage.error(res.message || '获取地域分析数据失败')
    }
  } catch (err) {
    console.error('初始化地域分析图表失败:', err)
    ElMessage.error('初始化地域分析图表失败')
  } finally {
    loading.value = false
  }
}

// 初始化订单类型分析图表
const initOrderTypeChart = async () => {
  if (!orderTypeChartRef.value) return
  
  // 清理之前的事件监听器和图表实例
  destroyOrderTypeChart()
  
  try {
    loading.value = true
    
    // 设置图表容器样式
    orderTypeChartRef.value.style.width = '100%'
    orderTypeChartRef.value.style.height = '500px'

    // 准备请求参数
    const params = {
      metric: activeMetric.value,
      start_date: filterForm.value.startDate,
      end_date: filterForm.value.endDate,
      user_id: filterForm.value.media || '',
      media_id: filterForm.value.mediaAccount || '',
      plan_id: filterForm.value.plan || '',
      product_id: filterForm.value.product || '',
      type: filterForm.value.type,
      category: filterForm.value.category || ''
    }

    const res = await getOrderTypeData(params)
    console.log('订单类型数据响应:', res)
    
    // 检查响应数据的有效性
    if (!res || typeof res !== 'object') {
      throw new Error('无效的响应数据')
    }

    if (res.code !== 0) {
      throw new Error(res.message || '获取订单类型数据失败')
    }

    if (!res.data || !Array.isArray(res.data.order_types)) {
      throw new Error('返回的数据格式不正确')
    }

    // 确保数据不为空
    if (res.data.order_types.length === 0) {
      ElMessage.warning('暂无订单类型数据')
      return
    }

    // 转换订单类型代码为可读名称
    const orderTypes = res.data.order_types.map(item => ({
      name: orderTypeNames[item.name] || `未知类型(${item.name})`,
      value: item.value
    }))

    // 初始化图表
    orderTypeChart.value = echarts.init(orderTypeChartRef.value)
    
    // 配置图表选项
    const option = {
      title: {
        text: '订单类型分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: orderTypes.map(item => item.name)
      },
      series: [
        {
          name: '订单类型',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: true,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            formatter: '{b}: {c} ({d}%)'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '16',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: true
          },
          data: orderTypes
        }
      ]
    }

    // 渲染图表
    orderTypeChart.value.setOption(option)

    // 监听窗口大小变化
    const handleResize = () => {
      orderTypeChart.value && orderTypeChart.value.resize()
    }
    window.addEventListener('resize', handleResize)

    // 保存事件监听器引用以便后续清理
    orderTypeChart.value._resizeHandler = handleResize
    
  } catch (error) {
    console.error('初始化订单类型图表失败:', error)
    ElMessage.error(error.message || '初始化订单类型图表失败')
  } finally {
    loading.value = false
  }
}

// 销毁地域分析图表
const destroyRegionChart = () => {
  if (regionChart) {
    regionChart.dispose()
    regionChart = null
  }
}

// 销毁订单类型分析图表
const destroyOrderTypeChart = () => {
  if (orderTypeChart.value) {
    // 移除resize事件监听器
    if (orderTypeChart.value._resizeHandler) {
      window.removeEventListener('resize', orderTypeChart.value._resizeHandler)
      orderTypeChart.value._resizeHandler = null
    }
    // 销毁图表实例
    orderTypeChart.value.dispose()
    orderTypeChart.value = null
  }
}

// 导出数据
const handleExport = () => {
  if (!planTableData.value || planTableData.value.length === 0) {
    ElMessage.warning('暂无数据可导出')
    return
  }

  // 获取当前显示的列配置
  const currentColumns = planTableColumns.value

  // 准备表头
  const headers = currentColumns.map(col => col.label)
  
  // 准备数据
  const data = planTableData.value.map(row => {
    return currentColumns.map(col => {
      // 使用formatter格式化数据
      if (col.formatter) {
        return col.formatter(row)
      }
      return row[col.prop]
    })
  })
  
  // 创建工作表
  const ws = XLSX.utils.aoa_to_sheet([headers, ...data])
  
  // 创建工作簿
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, '分计划数据')
  
  // 导出文件
  const fileName = `分计划数据_${formatDate(new Date())}.xlsx`
  XLSX.writeFile(wb, fileName)
  
  ElMessage.success('数据导出成功')
}

// 更新表格数据
const updateTableData = async () => {
  await loadPlanStats()
}

// 添加手动刷新方法
const handleRefresh = async () => {
  try {
    loading.value = true
    await loadMetricsData()
    ElMessage.success('数据已更新')
  } catch (err) {
    console.error('刷新数据失败:', err)
    ElMessage.error('刷新数据失败')
  } finally {
    loading.value = false
  }
}

// 更新仪表盘数据
const updateDashboardData = async () => {
  await loadMetricsData()
  await updateChartData()
  await updateTableData()
}

// 在script setup中添加
const handleMediaChange = async (newVal) => {
  console.log('选中的媒介变化:', newVal)
  
  if (!newVal) {
    // 清空选择，恢复显示数据
    filterForm.value.mediaAccount = ''
    filterForm.value.plan = ''
    await loadAllMediaAccounts()
    await loadAllPlans()
  } else {
    // 根据选择的媒介筛选媒体账号
    await loadFilteredMediaAccounts(newVal)
    // 重置计划选择并恢复显示计划
    filterForm.value.plan = ''
    await loadAllPlans()
  }
}

// 加载分计划数据
const loadPlanStats = async () => {
  planTableLoading.value = true
  try {
    const params = {
      user_id: filterForm.value.media || '',
      media_id: filterForm.value.mediaAccount || '',
      plan_id: filterForm.value.plan || '',
      product_id: filterForm.value.product || '',
      type: filterForm.value.type,
      category: filterForm.value.category // 添加大类参数
    }

    // 如果不是实时数据,添加日期参数
    if (filterForm.value.timeRange !== '实时') {
      // 验证日期参数
      if (!filterForm.value.startDate || !filterForm.value.endDate) {
        ElMessage.warning('请选择时间范围')
        return
      }
      params.start_date = filterForm.value.startDate
      params.end_date = filterForm.value.endDate
    } else {
      // 实时数据使用今天的日期
      const today = formatDate(new Date())
      params.start_date = today
      params.end_date = today
    }

    console.log('Plan stats request params:', params)
    const res = await getPlanStats(params)
    console.log('Plan stats response:', res)
    
    if (res.code === 0) {
      if (!res.data.list || res.data.list.length === 0) {
        planTableData.value = []
      } else {
        planTableData.value = res.data.list
      }
    } else {
      ElMessage.error(res.message || '加载分计划数据失败')
    }
  } catch (error) {
    console.error('Failed to load plan stats:', error)
    ElMessage.error('加载分计划数据失败')
  } finally {
    planTableLoading.value = false
  }
}

// 监听筛选条件变化
watch(filterForm, () => {
  loadPlanStats()
}, { deep: true })

// 格式化数字
const formatNumber = (num) => {
  return num.toLocaleString('zh-CN')
}

// 格式化金额
const formatCurrency = (amount) => {
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// 表格列定义
const baseTableColumns = [
  {
    prop: 'plan_code',
    label: '计划编码',
    minWidth: 120
  },
  {
    prop: 'media_name',
    label: '媒体名称',
    minWidth: 120
  },
  {
    prop: 'clicks',
    label: '点击量',
    minWidth: 100,
    sortable: true,
    formatter: (row) => formatNumber(row.clicks)
  },
  {
    prop: 'pv',
    label: '饿了么PV',
    minWidth: 100,
    sortable: true,
    formatter: (row) => formatNumber(row.pv)
  },
  {
    prop: 'uv', 
    label: '饿了么UV',
    minWidth: 100,
    sortable: true,
    formatter: (row) => formatNumber(row.uv)
  },
  {
    prop: 'cost',
    label: '成本',
    minWidth: 120,
    sortable: true,
    formatter: (row) => formatCurrency(row.cost)
  },
  {
    prop: 'orders',
    label: '订单量',
    minWidth: 100,
    sortable: true,
    formatter: (row) => formatNumber(row.orders)
  },
  {
    prop: 'daily_orders',
    label: '日均订单数',
    minWidth: 100,
    sortable: true,
    formatter: (row) => {
      // 计算日期范围内的天数
      const startDate = new Date(filterForm.value.startDate)
      const endDate = new Date(filterForm.value.endDate)
      const days = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1
      return (row.orders / days).toFixed(2)
    }
  },
  {
    prop: 'revenue',
    label: '收入',
    minWidth: 120,
    sortable: true,
    formatter: (row) => formatCurrency(row.revenue)
  },
  {
    prop: 'profit',
    label: '利润',
    minWidth: 120,
    sortable: true,
    formatter: (row) => formatCurrency(row.profit)
  },
  {
    prop: 'roi',
    label: 'ROI',
    minWidth: 100,
    sortable: true,
    formatter: (row) => row.roi.toFixed(2)
  },
  {
    prop: 'settled_profit',
    label: '结算利润',
    minWidth: 120,
    sortable: true,
    formatter: (row) => formatCurrency(row.settled_profit)
  }
]

// 将表格列改为计算属性
const planTableColumns = computed(() => {
  // 在微信环境下隐藏的列（保留PV和UV，只隐藏财务相关列）
  const hiddenColumnsInWechat = ['cost', 'profit', 'roi', 'settled_profit']
  
  // 如果是微信环境，过滤掉指定的列，但保留PV和UV，并添加日均订单数
  if (filterForm.value.category === 'wechat') {
    const columns = baseTableColumns.filter(col => !hiddenColumnsInWechat.includes(col.prop))
    // 确保日均订单数列在订单量后面
    const orderIndex = columns.findIndex(col => col.prop === 'orders')
    if (orderIndex !== -1) {
      const dailyOrdersColumn = columns.find(col => col.prop === 'daily_orders')
      if (dailyOrdersColumn) {
        columns.splice(orderIndex + 1, 0, ...columns.splice(columns.indexOf(dailyOrdersColumn), 1))
      }
    }
    return columns
  }
  
  // 其他环境显示全部列，但不显示日均订单数
  return baseTableColumns.filter(col => col.prop !== 'daily_orders')
})

// 添加导出加载状态
const exportLoading = ref(false)

// 处理趋势数据导出
const handleExportTrend = () => {
  try {
    if (!currentChartOption.value) {
      ElMessage.warning('暂无数据可导出')
      return
    }

    exportLoading.value = true

    // 从图表配置中提取数据
    const option = currentChartOption.value
    const timePoints = option.xAxis.data
    const series = option.series
    
    // 获取指标名称
    const metricName = getMetricName(activeMetric.value)
    
    // 准备表头
    const headers = ['时间点']
    series.forEach(s => headers.push(s.name))
    
    // 准备数据行
    const rows = timePoints.map((time, index) => {
      const row = [time]
      series.forEach(s => {
        const value = s.data[index]
        // 移除金额符号以便Excel处理
        const formattedValue = typeof value === 'number' ? value : value?.replace('¥', '') || ''
        row.push(formattedValue)
      })
      return row
    })
    
    // 创建工作表
    const ws = XLSX.utils.aoa_to_sheet([
      [metricName], // 第一行：指标名称
      [`统计时间: ${filterForm.value.startDate} 至 ${filterForm.value.endDate}`], // 第二行：时间范围
      headers, // 第三行：表头
      ...rows // 数据行
    ])
    
    // 设置单元格合并
    ws['!merges'] = [
      // 合并第一行
      { s: { r: 0, c: 0 }, e: { r: 0, c: headers.length - 1 } },
      // 合并第二行
      { s: { r: 1, c: 0 }, e: { r: 1, c: headers.length - 1 } }
    ]
    
    // 设置列宽
    const colWidth = headers.map(() => ({ wch: 15 }))
    colWidth[0] = { wch: 20 } // 时间列宽度设置大一点
    ws['!cols'] = colWidth
    
    // 创建工作簿
    const wb = XLSX.utils.book_new()
    
    // 添加工作表
    XLSX.utils.book_append_sheet(wb, ws, metricName)
    
    // 生成文件名
    const exportTimeRange = filterForm.value.timeRange
    const fileName = `${metricName}_趋势数据_${exportTimeRange}_${formatDate(new Date())}.xlsx`
    
    // 导出文件
    XLSX.writeFile(wb, fileName)
    
    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出趋势数据失败:', error)
    ElMessage.error('导出趋势数据失败')
  } finally {
    exportLoading.value = false
  }
}

// 监听大类变化
watch(() => filterForm.value.category, (newVal) => {
  console.log('大类变化:', newVal)
  // 所有环境下都默认选择全部
  filterForm.value.type = '0'
  // 触发数据更新
  updateDashboardData()
})

// 处理大类变化
const handleCategoryChange = async () => {
  console.log('大类变化:', filterForm.value.category)
  // 清空相关选择
  filterForm.value.mediaAccount = ''
  filterForm.value.plan = ''
  currentMediaAccounts.value = []
  currentPlans.value = []
  
  // 如果当前已选择了媒介,重新加载该媒介下的媒体账号
  if (filterForm.value.media) {
    await loadFilteredMediaAccounts(filterForm.value.media)
  } else {
    await loadAllMediaAccounts()
  }
  
  // 重新加载计划列表
  await loadAllPlans()
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 16px;
  
  .filter-card {
    margin-bottom: 16px;
    
    .filter-form {
      .time-range-row,
      .type-filter-row {
        margin-bottom: 16px;
        
        .time-range-item,
        .type-filter-item {
          margin-bottom: 0;
          
          :deep(.el-form-item__content) {
            flex-wrap: nowrap;
          }
          
          .time-range-content,
          .type-filter-content {
            display: flex;
            align-items: center;
            gap: 16px;
            
            .filter-radio-group {
              &:first-child {
                margin-right: 24px;
              }
              
              :deep(.el-radio-button__inner) {
                padding: 8px 15px;
              }
            }
          }
        }
      }
      
      .filter-row {
        margin-bottom: 16px;
      }
      
      :deep(.el-form-item) {
        margin-bottom: 16px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .el-form-item__label {
          padding-right: 12px;
          min-width: 70px;
        }
        
        .el-form-item__content {
          flex: 1;
          min-width: 0;
        }
      }
      
      .filter-select {
        width: 100%;
      }
      
      .filter-radio-group {
        :deep(.el-radio-button__inner) {
          padding: 8px 15px;
        }
      }
      
      .date-picker {
        width: 260px;
      }
      
      .filter-buttons {
        display: flex;
        justify-content: center;
        gap: 16px;
        margin-top: 8px;
      }
    }
  }
  
  .mt-16 {
    margin-top: 16px;
  }

  .data-card {
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      transform: translateY(-2px);
    }
    
    &.active {
      border-color: var(--el-color-primary);
      
      .card-header .title {
        color: var(--el-color-primary);
      }
      
      .card-body .main-value {
        color: var(--el-color-primary);
      }
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .title {
        font-size: 14px;
        color: #606266;
      }
    }

    .card-body {
      .main-value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 16px;
      }

      .compare-values {
        display: flex;
        justify-content: space-between;
        
        .compare-item {
          display: flex;
          flex-direction: column;
          
          .label {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
          }
          
          .value {
            font-size: 14px;
            
            &.up {
              color: #67c23a;
            }
            
            &.down {
              color: #f56c6c;
            }
          }
        }
      }
    }
  }

  .chart-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }
      
      .header-actions {
        display: flex;
        gap: 8px;
        
        .el-button {
          .el-icon {
            margin-right: 4px;
          }
        }
      }
    }
    
    .chart-container {
      width: 100%;
      min-height: 400px;
      position: relative;
      overflow: hidden;
    }
  }

  .filter-radio-group {
    margin-right: 16px;
    
    :deep(.el-radio-button__inner) {
      padding: 8px 15px;
    }
  }

  .date-picker {
    width: 260px;
    margin-left: 16px;
  }

  .table-card {
    margin-top: 16px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }
      
      .header-actions {
        display: flex;
        gap: 16px;
        align-items: center;
        
        :deep(.el-switch) {
          margin-right: 8px;
        }
      }
    }
  }
}

.dialog-chart-container {
  width: 100%;
  height: 400px;
  position: relative;
  overflow: hidden;
}

// 移动端适配
@media screen and (max-width: 768px) {
  .dashboard-container {
    padding: 8px;
    
    .filter-card {
      .filter-form {
        .type-filter-row {
          .type-filter-content {
            flex-direction: column;
            align-items: flex-start;
            
            .filter-radio-group {
              width: 100%;
              margin-bottom: 8px;
              
              &:first-child {
                margin-right: 0;
              }
              
              :deep(.el-radio-button) {
                margin-right: 0;
                margin-bottom: 4px;
                
                .el-radio-button__inner {
                  border-radius: 4px;
                  border: 1px solid #DCDFE6;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style> 