<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-info">
        <div class="error-code">403</div>
        <div class="error-message">抱歉，您没有权限访问此页面</div>
        <div class="error-description">
          <div class="access-info">
            <div v-if="routeName" class="route-info">
              <el-icon class="info-icon"><Document /></el-icon>
              <span>尝试访问页面：<strong>{{ routeName }}</strong></span>
            </div>
            <div v-if="requiredPermission" class="permission-info">
              <el-icon class="info-icon"><Lock /></el-icon>
              <span>需要权限：<code class="permission-code">{{ requiredPermission }}</code></span>
            </div>
            <div v-if="requiredPermissions && requiredPermissions.length > 1" class="permissions-info">
              <el-icon class="info-icon"><Lock /></el-icon>
              <span>需要以下任一权限：</span>
              <div class="permissions-list">
                <code 
                  v-for="permission in requiredPermissions" 
                  :key="permission"
                  class="permission-code"
                >
                  {{ permission }}
                </code>
              </div>
            </div>
            <div v-if="missingPermissions && missingPermissions.length > 0" class="missing-permissions-info">
              <el-icon class="info-icon"><Warning /></el-icon>
              <span>您缺少以下权限：</span>
              <div class="permissions-list">
                <code 
                  v-for="permission in missingPermissions" 
                  :key="permission"
                  class="permission-code missing"
                >
                  {{ permission }}
                </code>
              </div>
            </div>
          </div>
          <div class="help-text">
            请联系管理员为您分配相应权限，或返回首页继续浏览
          </div>
        </div>
        <div class="error-actions">
          <el-button type="primary" @click="goHome">
            <el-icon class="mr-1"><HomeFilled /></el-icon>
            返回首页
          </el-button>
          <el-button @click="goBack">
            <el-icon class="mr-1"><Back /></el-icon>
            返回上页
          </el-button>
          <el-button @click="copyPermissionInfo" type="success" plain>
            <el-icon class="mr-1"><CopyDocument /></el-icon>
            复制权限信息
          </el-button>
        </div>
      </div>
      <div class="error-image">
        <el-icon :size="200" color="#409EFF">
          <Lock />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { HomeFilled, Back, Lock, Document, CopyDocument, Warning } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 从路由查询参数中获取权限信息
const requiredPermission = computed(() => route.query.requiredPermission)
const requiredPermissions = computed(() => {
  if (route.query.requiredPermissions) {
    try {
      return JSON.parse(route.query.requiredPermissions)
    } catch {
      return []
    }
  }
  return requiredPermission.value ? [requiredPermission.value] : []
})
const missingPermissions = computed(() => {
  if (route.query.missingPermissions) {
    try {
      return JSON.parse(route.query.missingPermissions)
    } catch {
      return []
    }
  }
  return []
})
const routePath = computed(() => route.query.routePath)
const routeName = computed(() => route.query.routeName)

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

// 复制权限信息到剪贴板
const copyPermissionInfo = async () => {
  const info = {
    用户: '未获取', // TODO: 可以从用户store获取当前用户信息
    访问页面: routeName.value || routePath.value,
    页面路径: routePath.value,
    需要权限: requiredPermissions.value,
    缺少权限: missingPermissions.value,
    时间: new Date().toLocaleString()
  }
  
  const text = Object.entries(info)
    .filter(([key, value]) => value && (Array.isArray(value) ? value.length > 0 : true))
    .map(([key, value]) => `${key}: ${Array.isArray(value) ? value.join(', ') : value}`)
    .join('\n')
  
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('权限信息已复制到剪贴板')
  } catch (error) {
    // 降级处理：创建临时文本区域
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      ElMessage.success('权限信息已复制到剪贴板')
    } catch {
      ElMessage.error('复制失败，请手动复制权限信息')
    }
    document.body.removeChild(textArea)
  }
}

// 在开发环境下输出权限信息到控制台
onMounted(() => {
  if (process.env.NODE_ENV === 'development') {
    console.group('403 权限不足详情')
    console.log('访问页面:', routeName.value || routePath.value)
    console.log('页面路径:', routePath.value)
    console.log('需要权限:', requiredPermissions.value)
    console.log('缺少权限:', missingPermissions.value)
    console.groupEnd()
  }
})
</script>

<style scoped>
.error-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.error-content {
  display: flex;
  align-items: center;
  max-width: 900px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.error-info {
  flex: 1;
  padding: 60px 40px;
}

.error-code {
  font-size: 72px;
  font-weight: bold;
  color: #f56c6c;
  margin-bottom: 20px;
  line-height: 1;
}

.error-message {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
}

.error-description {
  margin-bottom: 32px;
  line-height: 1.6;
}

.access-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  border-left: 4px solid #409eff;
}

.route-info,
.permission-info,
.permissions-info,
.missing-permissions-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  font-size: 14px;
  color: #606266;
}

.route-info:last-child,
.permission-info:last-child,
.permissions-info:last-child,
.missing-permissions-info:last-child {
  margin-bottom: 0;
}

.info-icon {
  margin-right: 8px;
  margin-top: 2px;
  color: #409eff;
  flex-shrink: 0;
}

.missing-permissions-info .info-icon {
  color: #f56c6c;
}

.permission-code {
  background: #e7f3ff;
  color: #409eff;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  font-weight: 500;
  border: 1px solid #d9ecff;
}

.permission-code.missing {
  background: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fbc4c4;
}

.permissions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
  margin-left: 24px;
}

.help-text {
  color: #909399;
  font-size: 14px;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.error-image {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  padding: 60px 40px;
}

.mr-1 {
  margin-right: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-content {
    flex-direction: column;
    text-align: center;
  }
  
  .error-info {
    padding: 40px 20px;
  }
  
  .error-image {
    padding: 40px 20px;
  }
  
  .error-code {
    font-size: 48px;
  }
  
  .error-message {
    font-size: 20px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .access-info {
    text-align: left;
  }
  
  .route-info,
  .permission-info,
  .permissions-info,
  .missing-permissions-info {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .permissions-list {
    margin-left: 0;
  }
}

/* 打印样式 */
@media print {
  .error-page {
    background: white;
    padding: 20px;
  }
  
  .error-content {
    box-shadow: none;
    border: 1px solid #ddd;
  }
  
  .error-actions {
    display: none;
  }
  
  .error-image {
    display: none;
  }
}
</style> 