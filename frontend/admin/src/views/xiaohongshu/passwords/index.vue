<template>
  <div class="passwords-container">
    <!-- 筛选条件区域 -->
    <div class="filter-section">
      <div class="filter-container">
        <div class="filter-row">
          <!-- 第一行筛选条件 -->
          <div class="filter-line">
            <div class="filter-item">
              <span class="filter-number">1</span>
              <label class="filter-label">口令:</label>
              <el-input
                v-model="queryParams.password_name"
                placeholder="请输入口令名称"
                style="width: 160px"
                clearable
                @change="handlePasswordNameChange"
              />
            </div>

            <div class="filter-item">
              <span class="filter-number">2</span>
              <label class="filter-label">时间:</label>
              <el-select
                v-model="selectedTimeRange"
                placeholder="今日实时"
                style="width: 160px"
                @change="handleTimeRangeChange"
              >
                <el-option label="今日实时" value="today" />
                <el-option label="昨日" value="yesterday" />
                <el-option label="最近7天" value="last7days" />
                <el-option label="最近30天" value="last30days" />
                <el-option label="自定义" value="custom" />
              </el-select>
            </div>

            <!-- 自定义日期选择器 -->
            <div class="filter-item" v-if="selectedTimeRange === 'custom'">
              <el-date-picker
                v-model="customDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                @change="handleCustomDateChange"
                style="width: 240px"
              />
            </div>

            <!-- 更新时间显示 -->
            <div class="update-time">
              <span class="update-text">更新时间: {{ lastUpdateTime }}</span>
            </div>

            <!-- 操作按钮 -->
            <div class="filter-actions">
              <el-button type="primary" @click="handleQuery" :loading="loading">
                查询
              </el-button>
              <el-button @click="handleReset">
                重置
              </el-button>
              <el-button @click="handleExport" :loading="exportLoading">
                导出
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <div class="table-container">
        <el-table
          :data="tableData"
          style="width: 100%"
          v-loading="loading"
          stripe
          border
          :default-sort="{ prop: 'consumption', order: 'descending' }"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: '500' }"
        >
          <!-- 口令名称 -->
          <el-table-column
            prop="password_name"
            label="口令名称"
            min-width="120"
            show-overflow-tooltip
          />

          <!-- 账号 -->
          <el-table-column
            prop="account_name"
            label="账号"
            width="120"
            show-overflow-tooltip
            align="center"
          />

          <!-- 消费 -->
          <el-table-column
            prop="consumption"
            label="消费"
            width="120"
            align="right"
            sortable
          >
            <template #default="scope">
              <span class="currency-text">
                {{ formatCurrency(scope.row.consumption) }}
              </span>
            </template>
          </el-table-column>

          <!-- 实际消费 -->
          <el-table-column
            prop="actual_consumption"
            label="实际消费"
            width="120"
            align="right"
            sortable
          >
            <template #default="scope">
              <span class="currency-text">
                {{ formatCurrency(scope.row.actual_consumption) }}
              </span>
            </template>
          </el-table-column>

          <!-- 展现量 -->
          <el-table-column
            prop="impressions"
            label="展现量"
            width="120"
            align="right"
            sortable
          >
            <template #default="scope">
              <span>{{ formatNumber(scope.row.impressions) }}</span>
            </template>
          </el-table-column>

          <!-- 点击量 -->
          <el-table-column
            prop="click_count"
            label="点击量"
            width="120"
            align="right"
            sortable
          >
            <template #default="scope">
              <span>{{ formatNumber(scope.row.click_count) }}</span>
            </template>
          </el-table-column>

          <!-- 点击率 -->
          <el-table-column
            prop="click_rate"
            label="点击率"
            width="120"
            align="right"
            sortable
          >
            <template #default="scope">
              <span class="rate-text">
                {{ formatClickRate(scope.row.click_rate) }}
              </span>
            </template>
          </el-table-column>

          <!-- 搜索人数 -->
          <el-table-column
            prop="search_count"
            label="搜索人数"
            width="120"
            align="right"
            sortable
          >
            <template #default="scope">
              <span>{{ formatNumber(scope.row.search_count) }}</span>
            </template>
          </el-table-column>

          <!-- 今日新订单 -->
          <el-table-column
            prop="new_orders_today"
            label="今日新订单"
            width="120"
            align="right"
            sortable
          >
            <template #default="scope">
              <span>{{ formatNumber(scope.row.new_orders_today) }}</span>
            </template>
          </el-table-column>

          <!-- 今日订单成本 -->
          <el-table-column
            prop="today_order_cost"
            label="今日订单成本"
            width="120"
            align="right"
            sortable
          >
            <template #default="scope">
              <span class="currency-text">
                {{ formatCurrency(scope.row.today_order_cost) }}
              </span>
            </template>
          </el-table-column>

          <!-- 累计订单 -->
          <el-table-column
            prop="cumulative_orders"
            label="累计订单"
            width="120"
            align="right"
            sortable
          >
            <template #default="scope">
              <span>{{ formatNumber(scope.row.cumulative_orders) }}</span>
            </template>
          </el-table-column>

          <!-- 累计收入 -->
          <el-table-column
            prop="cumulative_income"
            label="累计收入"
            width="120"
            align="right"
            sortable
          >
            <template #default="scope">
              <span class="income-text">
                {{ formatCurrency(scope.row.cumulative_income) }}
              </span>
            </template>
          </el-table-column>

          <!-- 累计回收 -->
          <el-table-column
            prop="cumulative_recovery"
            label="累计回收"
            width="120"
            align="right"
            sortable
          >
            <template #default="scope">
              <span class="recovery-text">
                {{ formatRecoveryRate(scope.row.cumulative_recovery) }}
              </span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="queryParams.page"
            v-model:page-size="queryParams.page_size"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getPasswords,
  getPasswordsStats,
  getPasswordAccounts,
  exportPasswords,
  formatCurrency,
  formatClickRate,
  formatRecoveryRate,
  formatNumber,
  formatDateForAPI,
  DEFAULT_QUERY_PARAMS
} from '@/api/passwords'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const accountOptions = ref([])
const selectedTimeRange = ref('today')
const customDateRange = ref([])
const lastUpdateTime = ref('')

// 查询参数
const queryParams = reactive({
  ...DEFAULT_QUERY_PARAMS
})

// 统计数据
const statsData = reactive({
  total_consumption: 0,
  total_orders: 0,
  total_income: 0,
  total_recovery_rate: 0
})

// 生命周期
onMounted(() => {
  loadAccountOptions()
  loadTableData()
  loadStatsData()
})

// 方法
const loadAccountOptions = async () => {
  try {
    const response = await getPasswordAccounts()
    const accounts = response.data?.list || []
    
    // 转换为下拉选项格式，添加"全部"选项
    accountOptions.value = [
      { id: 'all', name: '全部', value: '' },
      ...accounts.map(account => ({
        id: account.id,
        name: account.account_name,
        value: account.id
      }))
    ]
  } catch (error) {
    console.error('加载账号选项失败:', error)
    // 如果加载失败，至少提供"全部"选项
    accountOptions.value = [
      { id: 'all', name: '全部', value: '' }
    ]
  }
}

const loadTableData = async () => {
  loading.value = true
  try {
    // 构建查询参数，处理账号ID
    const params = { ...queryParams }
    
    // 如果选择了具体账号，使用account_id参数
    if (params.account_id && params.account_id !== '') {
      params.account_id = Number(params.account_id)
    } else {
      // 如果选择"全部"，删除account_id参数
      delete params.account_id
    }
    
    const response = await getPasswords(params)
    const data = response.data
    
    tableData.value = data.list || []
    total.value = data.total || 0
    
    // 更新最后更新时间
    if (tableData.value.length > 0) {
      lastUpdateTime.value = tableData.value[0].last_update || new Date().toLocaleString()
    } else {
      lastUpdateTime.value = new Date().toLocaleString()
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败: ' + (error.response?.data?.message || error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

const loadStatsData = async () => {
  try {
    // 构建查询参数，处理账号ID
    const params = { ...queryParams }

    // 如果选择了具体账号，使用account_id参数
    if (params.account_id && params.account_id !== '') {
      params.account_id = Number(params.account_id)
    } else {
      // 如果选择"全部"，删除account_id参数
      delete params.account_id
    }

    const response = await getPasswordsStats(params)
    Object.assign(statsData, response.data)
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const handlePasswordNameChange = () => {
  queryParams.page = 1
  loadTableData()
  loadStatsData()
}

const handleTimeRangeChange = (value) => {
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)

  switch (value) {
    case 'today':
      queryParams.start_date = formatDateForAPI(today)
      queryParams.end_date = formatDateForAPI(today)
      break
    case 'yesterday':
      queryParams.start_date = formatDateForAPI(yesterday)
      queryParams.end_date = formatDateForAPI(yesterday)
      break
    case 'last7days':
      const last7days = new Date(today)
      last7days.setDate(last7days.getDate() - 7)
      queryParams.start_date = formatDateForAPI(last7days)
      queryParams.end_date = formatDateForAPI(today)
      break
    case 'last30days':
      const last30days = new Date(today)
      last30days.setDate(last30days.getDate() - 30)
      queryParams.start_date = formatDateForAPI(last30days)
      queryParams.end_date = formatDateForAPI(today)
      break
    case 'custom':
      // 自定义时间范围，等待用户选择
      return
  }

  if (value !== 'custom') {
    queryParams.page = 1
    loadTableData()
    loadStatsData()
  }
}

const handleCustomDateChange = (dateRange) => {
  if (dateRange && dateRange.length === 2) {
    queryParams.start_date = dateRange[0]
    queryParams.end_date = dateRange[1]
    queryParams.page = 1
    loadTableData()
    loadStatsData()
  }
}

const handleQuery = () => {
  queryParams.page = 1
  loadTableData()
  loadStatsData()
}

const handleReset = () => {
  Object.assign(queryParams, DEFAULT_QUERY_PARAMS)
  selectedTimeRange.value = 'today'
  customDateRange.value = []
  loadTableData()
  loadStatsData()
}

const handleExport = async () => {
  try {
    const { value: format } = await ElMessageBox.prompt(
      '请选择导出格式',
      '导出数据',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'select',
        inputOptions: [
          { label: 'Excel格式 (.xlsx)', value: 'xlsx' },
          { label: 'CSV格式 (.csv)', value: 'csv' }
        ],
        inputValue: 'xlsx'
      }
    )

    exportLoading.value = true

    const exportParams = {
      ...queryParams,
      format: format || 'xlsx'
    }

    // 处理账号ID参数
    if (exportParams.account_id && exportParams.account_id !== '') {
      exportParams.account_id = Number(exportParams.account_id)
    } else {
      delete exportParams.account_id
    }

    // 删除分页参数
    delete exportParams.page
    delete exportParams.page_size

    await exportPasswords(exportParams)
    ElMessage.success('导出任务已启动，请稍后查看导出结果')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('导出失败:', error)
      ElMessage.error('导出失败: ' + (error.response?.data?.message || error.message || '未知错误'))
    }
  } finally {
    exportLoading.value = false
  }
}

const handleSizeChange = (size) => {
  queryParams.page_size = size
  queryParams.page = 1
  loadTableData()
}

const handleCurrentChange = (page) => {
  queryParams.page = page
  loadTableData()
}
</script>

<style scoped>
.passwords-container {
  padding: 20px;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-container {
  background: #f5f7fa;
  border-radius: 8px;
  padding: 16px 20px;
  border: 1px solid #e4e7ed;
}

.filter-row {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-line {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
}

.filter-label {
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  margin-left: 4px;
}

.update-time {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.update-text {
  color: #666;
  font-size: 14px;
}

.filter-actions {
  display: flex;
  gap: 10px;
}

.table-section {
  margin-bottom: 20px;
}

.table-container {
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

.currency-text {
  color: #f56c6c;
  font-weight: 500;
  font-family: 'Courier New', monospace;
}

.rate-text {
  color: #67c23a;
  font-weight: 500;
  font-family: 'Courier New', monospace;
}

.income-text {
  color: #409eff;
  font-weight: 500;
  font-family: 'Courier New', monospace;
}

.recovery-text {
  color: #e6a23c;
  font-weight: 500;
  font-family: 'Courier New', monospace;
}

:deep(.el-table) {
  border: none;
}

:deep(.el-table__header-wrapper) {
  border-top: none;
}

:deep(.el-table__body-wrapper) {
  border-bottom: none;
}

:deep(.el-table td, .el-table th) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table--border .el-table__cell) {
  border-right: 1px solid #ebeef5;
}

:deep(.el-table--border .el-table__cell:last-child) {
  border-right: none;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
