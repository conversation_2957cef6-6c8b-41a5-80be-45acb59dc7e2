# 小红书投放 - 口令列表功能

## 功能概述

口令列表功能是小红书投放管理系统的重要模块，提供了完整的口令数据查询、筛选、统计和导出功能。页面设计参考了用户提供的样式，实现了直观易用的界面。

## 功能特性

### 1. 筛选功能
- **口令筛选**: 支持按口令名称模糊查询
- **账号筛选**: 使用广告账号管理API，支持按账号筛选
- **时间筛选**: 提供多种时间范围选择
  - 今日实时
  - 昨日
  - 最近7天
  - 最近30天
  - 自定义时间范围
- **实时更新**: 显示数据最后更新时间

### 2. 数据展示
- **丰富字段**: 展示口令的完整数据指标
  - 口令名称
  - 账号名称
  - 消费金额
  - 实际消费
  - 展现量
  - 点击量
  - 点击率
  - 搜索人数
  - 今日新订单
  - 今日订单成本
  - 累计订单
  - 累计收入
  - 累计回收率
- **排序功能**: 支持按各字段排序
- **分页功能**: 支持分页浏览，可调整每页显示数量

### 3. 导出功能
- **格式选择**: 支持Excel (.xlsx) 和CSV (.csv) 格式
- **异步处理**: 大数据量导出采用异步处理，避免超时
- **筛选导出**: 导出当前筛选条件下的数据

### 4. 界面设计
- **响应式布局**: 适配不同屏幕尺寸
- **视觉层次**: 使用数字标识和颜色区分不同功能区域
- **数据可视化**: 不同类型数据使用不同颜色显示
  - 消费金额：红色
  - 点击率：绿色
  - 收入：蓝色
  - 回收率：橙色

## 技术实现

### 1. 前端技术栈
- **Vue 3**: 使用Composition API
- **Element Plus**: UI组件库
- **Vite**: 构建工具
- **JavaScript**: ES6+语法

### 2. API接口
- **GET /api/v1/passwords**: 获取口令列表
- **GET /api/v1/passwords/stats**: 获取统计信息
- **GET /api/v1/ad-accounts**: 获取账号列表（复用广告账号API）
- **POST /api/v1/passwords/export**: 导出数据
- **GET /api/v1/passwords/{name}**: 获取口令详情

### 3. 数据处理
- **智能查询策略**: 
  - 单日查询：直接从统计表查询，性能更优
  - 多日查询：从创意报表聚合数据，确保准确性
- **数据格式化**: 金额、百分比、数字等格式化显示
- **错误处理**: 完善的错误捕获和用户提示

## 文件结构

```
src/views/xiaohongshu/passwords/
├── index.vue              # 主页面组件
├── README.md             # 功能说明文档
└── components/           # 子组件目录（预留）

src/api/
└── passwords.js          # API接口定义

src/router/
└── index.js              # 路由配置（已添加口令列表路由）
```

## 使用说明

### 1. 访问页面
- 登录系统后，在左侧菜单中找到"小红书投放"
- 点击展开后选择"口令列表"

### 2. 筛选数据
1. 输入要查询的口令名称（支持模糊查询）
2. 选择时间范围（默认为"今日实时"）
3. 如选择"自定义"，需要进一步选择具体日期范围
4. 点击"查询"按钮执行查询

### 3. 查看数据
- 表格中显示口令详细数据
- 可点击表头进行排序
- 使用分页控件浏览更多数据

### 4. 导出数据
1. 点击"导出"按钮
2. 选择导出格式（Excel或CSV）
3. 确认导出，系统将异步处理
4. 导出完成后会有提示信息

### 5. 重置筛选
- 点击"重置"按钮可清除所有筛选条件
- 恢复到默认的今日实时数据查询

## 样式特点

### 1. 筛选区域
- 浅灰色背景，圆角边框
- 数字标识（1、2）区分不同筛选项
- 蓝色圆形标识筛选条件

### 2. 数据表格
- 白色背景，清晰边框
- 表头浅灰色背景
- 不同数据类型使用不同颜色：
  - 消费金额：红色显示
  - 点击率：绿色显示
  - 收入：蓝色显示
  - 回收率：橙色显示
- 等宽字体显示数字，便于对比

### 3. 响应式设计
- 筛选条件自动换行适配小屏幕
- 表格支持横向滚动
- 按钮组合理布局

## 数据字段说明

### 核心指标
- **消费/实际消费**: 广告投入成本
- **展现量/点击量**: 广告曝光和互动数据
- **点击率**: 广告效果指标

### 转化指标
- **搜索人数**: 口令搜索热度
- **今日新订单**: 当日转化效果
- **今日订单成本**: 当日获客成本

### 累计指标
- **累计订单**: 历史总转化
- **累计收入**: 历史总收益
- **累计回收率**: 投入产出比

## 性能优化

### 1. 查询优化
- 单日查询使用预聚合数据
- 多日查询按需聚合
- 合理的分页大小设置

### 2. 用户体验
- 加载状态指示
- 异步导出避免页面阻塞
- 错误信息友好提示

### 3. 数据缓存
- 账号列表缓存
- 查询结果本地缓存（可扩展）

## 扩展功能

### 1. 可扩展的功能点
- 口令详情查看
- 批量操作功能
- 数据图表展示
- 更多筛选条件
- 自定义列显示

### 2. 性能优化空间
- 虚拟滚动（大数据量）
- 查询防抖
- 数据预加载

## 注意事项

### 1. 权限控制
- 页面访问需要 `xiaohongshu:passwords:view` 权限
- 导出功能可能需要额外权限控制

### 2. 数据限制
- 导出最大10,000条记录
- 时间范围不超过365天
- 分页大小范围1-1000

### 3. 浏览器兼容
- 现代浏览器支持（Chrome、Firefox、Safari、Edge）
- 移动端响应式支持

## 与计划列表的区别

### 1. 数据维度
- **计划列表**: 以广告计划为维度
- **口令列表**: 以口令为维度

### 2. 字段差异
- **口令列表**: 增加了搜索人数、订单相关、收入回收等电商指标
- **计划列表**: 更关注广告投放的基础指标

### 3. 业务价值
- **口令列表**: 更关注转化效果和ROI
- **计划列表**: 更关注广告投放效率

## 维护说明

### 1. 常见问题
- API接口超时：检查后端服务状态
- 数据不显示：检查权限和网络连接
- 导出失败：检查导出参数和服务器资源

### 2. 更新日志
- v1.0.0: 初始版本，基础功能完整实现
- 支持筛选、查询、导出、分页等核心功能
- 界面样式符合设计要求
- 使用广告账号管理API进行账号筛选

### 3. 技术债务
- 可考虑将大组件拆分为更小的子组件
- 添加单元测试覆盖
- 优化TypeScript类型定义
