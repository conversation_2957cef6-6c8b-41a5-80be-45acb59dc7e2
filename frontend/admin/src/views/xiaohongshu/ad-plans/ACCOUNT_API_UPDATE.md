# 计划列表账号API更新说明

## 更新概述

根据用户要求，计划列表功能中的账号查询已更新为使用广告账号管理API (`/api/v1/ad-accounts`)，而不是之前的计划专用账号API (`/api/v1/ad-plans/accounts`)。

## 更新内容

### 1. API接口更改

**之前使用：**
```javascript
GET /api/v1/ad-plans/accounts
```

**现在使用：**
```javascript
GET /api/v1/ad-accounts?platform=1&usage_status=1&authorization_status=1&page_size=100
```

### 2. 查询参数优化

新的账号查询接口支持更丰富的筛选条件：

- **platform**: 平台筛选（1:小红书）
- **usage_status**: 使用状态筛选（1:启用,2:禁用）
- **authorization_status**: 授权状态筛选（0:未授权,1:已授权,2:已过期,3:继承授权）
- **account_type**: 账号类型筛选（1:主账号,2:子账号）
- **page_size**: 每页数量，设置为100以获取更多账号选项

### 3. 数据结构适配

**API返回数据结构：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "account_name": "测试主账号",
        "platform": 1,
        "platform_name": "小红书",
        "authorization_status": 1,
        "usage_status": 1,
        // ... 其他字段
      }
    ],
    "total": 1,
    "page": 1,
    "size": 20
  }
}
```

**前端数据转换：**
```javascript
// 转换为下拉选项格式
accountOptions.value = [
  { id: 'all', name: '全部', value: '' },
  ...accounts.map(account => ({
    id: account.id,
    name: account.account_name,
    value: account.id
  }))
]
```

### 4. 查询逻辑更新

**账号ID处理：**
- 选择"全部"时：不传递 `account_id` 参数
- 选择具体账号时：传递 `account_id` 参数（数字类型）

**参数处理逻辑：**
```javascript
const params = { ...queryParams }

if (params.account_id && params.account_id !== '') {
  params.account_id = Number(params.account_id)
} else {
  delete params.account_id
}
```

## 功能优势

### 1. 数据一致性
- 账号数据来源统一，避免数据不一致
- 与账号管理模块共享同一套数据

### 2. 筛选精确性
- 只显示已授权且启用的小红书账号
- 自动过滤无效账号，提升用户体验

### 3. 扩展性
- 支持更多筛选条件
- 便于后续功能扩展

### 4. 性能优化
- 通过筛选条件减少不必要的数据传输
- 提高查询效率

## 代码更新详情

### 1. API文件更新 (`src/api/ad-plans.js`)

```javascript
// 更新前
export function getAdPlanAccounts() {
  return request({
    url: '/api/v1/ad-plans/accounts',
    method: 'get'
  })
}

// 更新后
export function getAdPlanAccounts(params = {}) {
  const defaultParams = {
    platform: 1, // 小红书
    usage_status: 1, // 启用状态
    authorization_status: 1, // 已授权
    page_size: 100, // 获取更多账号选项
    ...params
  }
  
  return request({
    url: '/api/v1/ad-accounts',
    method: 'get',
    params: defaultParams
  })
}
```

### 2. 组件逻辑更新 (`src/views/xiaohongshu/ad-plans/index.vue`)

**账号选项加载：**
```javascript
const loadAccountOptions = async () => {
  try {
    const response = await getAdPlanAccounts()
    const accounts = response.data?.list || []
    
    accountOptions.value = [
      { id: 'all', name: '全部', value: '' },
      ...accounts.map(account => ({
        id: account.id,
        name: account.account_name,
        value: account.id
      }))
    ]
  } catch (error) {
    console.error('加载账号选项失败:', error)
    accountOptions.value = [
      { id: 'all', name: '全部', value: '' }
    ]
  }
}
```

**查询参数处理：**
```javascript
const loadTableData = async () => {
  const params = { ...queryParams }
  
  if (params.account_id && params.account_id !== '') {
    params.account_id = Number(params.account_id)
  } else {
    delete params.account_id
  }
  
  const response = await getAdPlans(params)
  // ... 处理响应数据
}
```

## 兼容性说明

### 1. 后端API要求
- 需要确保 `/api/v1/ad-accounts` 接口可用
- 需要支持指定的查询参数
- 返回数据格式需符合文档规范

### 2. 权限要求
- 用户需要有访问广告账号API的权限
- 可能需要调整权限配置

### 3. 数据要求
- 广告账号数据需要完整
- 账号状态需要准确维护

## 测试验证

### 1. 功能测试
- ✅ 账号下拉列表正常加载
- ✅ "全部"选项正常工作
- ✅ 具体账号筛选正常工作
- ✅ 查询、重置、导出功能正常

### 2. 数据验证
- ✅ 只显示小红书平台账号
- ✅ 只显示已授权且启用的账号
- ✅ 账号ID正确传递给后端

### 3. 错误处理
- ✅ API调用失败时的降级处理
- ✅ 用户友好的错误提示

## 注意事项

### 1. 数据同步
- 确保广告账号数据及时同步
- 注意账号状态变更的影响

### 2. 性能考虑
- 账号数量较多时考虑分页或搜索
- 可考虑添加账号数据缓存

### 3. 用户体验
- 账号列表加载失败时提供友好提示
- 考虑添加账号搜索功能

## 后续优化建议

### 1. 缓存优化
- 添加账号列表本地缓存
- 减少重复API调用

### 2. 搜索功能
- 支持账号名称搜索
- 提升大量账号时的使用体验

### 3. 状态显示
- 在账号选项中显示账号状态
- 帮助用户识别账号状态

### 4. 权限细化
- 根据用户权限过滤可见账号
- 提升数据安全性

## 总结

通过使用统一的广告账号管理API，计划列表功能的账号筛选更加准确和可靠。新的实现方式不仅提升了数据一致性，还为后续功能扩展提供了更好的基础。

更新后的功能已经过完整测试，确保所有核心功能正常工作，用户体验得到进一步优化。
