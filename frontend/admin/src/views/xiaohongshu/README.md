# 小红书投放功能

## 功能概述

本模块实现了小红书投放管理功能，包括小红书内容数据的查看、筛选、导出等功能。

## 功能特性

### 1. 菜单结构
- **一级菜单**: 小红书投放
- **二级菜单**: 小红书内容数据

### 2. 小红书内容数据页面功能

#### 搜索筛选
- 时间范围选择（日期范围选择器）
- 账号名称（模糊搜索）
- 计划名称（模糊搜索）
- 单元名称（模糊搜索）
- 标题（模糊搜索）
- 口令（模糊搜索）
- 广告类型选择（信息流、搜索、开屏、全站智投、视频内流）
- 聚合类型选择（分日数据、汇总数据）

#### 统计信息展示
- 总报表数
- 总消费金额
- 总点击数
- 平均ROI

#### 数据表格
- 基本信息：日期、账号名称、标题、口令、内容人员、投手
- 投放信息：计划名称、单元名称、广告类型
- 消费数据：消费金额、曝光量、点击量、点击率、平均点击价格、千次曝光成本
- 互动数据：点赞、评论、收藏、关注、分享、总互动、互动成本
- 转化数据：商品订单、GMV、ROI、成功订单、7日ROI

#### 功能操作
- 数据查询和重置
- Excel导出功能
- 分页显示
- 详情查看（弹窗显示详细信息）

#### 详情弹窗
- 基本信息：创意ID、笔记ID、创意名称、创意图片
- 投放配置：优化目标、推广标的、出价方式、搭建类型、营销诉求
- 数据表现：完整的数据指标展示

## 技术实现

### API接口
- `GET /api/v1/xhs-creative-reports` - 获取创意报表列表
- `GET /api/v1/xhs-creative-reports/stats` - 获取统计信息
- `GET /api/v1/xhs-creative-reports/export` - 导出数据
- `GET /api/v1/xhs-creative-reports/{id}` - 获取详情

### 文件结构
```
frontend/admin/src/views/xiaohongshu/
├── content-data/
│   └── index.vue          # 小红书内容数据页面
├── README.md              # 功能说明文档
```

```
frontend/admin/src/api/
└── xiaohongshu.js         # 小红书相关API接口
```

### 路由配置
在 `frontend/admin/src/router/index.js` 中添加了小红书投放菜单配置。

### 权限配置
- `xiaohongshu:view` - 小红书投放模块查看权限
- `xiaohongshu:content:view` - 小红书内容数据查看权限

## 使用说明

1. 登录系统后，在左侧菜单中找到"小红书投放"菜单
2. 点击"小红书内容数据"进入数据查看页面
3. 使用搜索表单设置筛选条件
4. 点击"查询"按钮获取数据
5. 可以导出Excel文件或查看详细信息

## 注意事项

1. 导出功能限制：最大导出50,000条数据，时间范围不能超过90天
2. 分页限制：每页最大1000条数据，建议使用20-100条
3. 日期格式：统一使用YYYY-MM-DD格式
4. 数据更新：报表数据通过定时任务每日同步

## 样式设计

页面采用了与现有报表页面一致的设计风格：
- 卡片式布局
- 统计信息卡片展示
- 响应式表格设计
- 移动端适配
- 统一的颜色主题（红色表示消费/亏损，绿色表示盈利）
