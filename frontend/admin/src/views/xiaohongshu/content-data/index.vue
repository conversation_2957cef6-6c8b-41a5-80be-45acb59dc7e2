<template>
  <div class="xhs-content-data-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>小红书内容数据</span>
          <el-tag type="primary" size="small">小红书投放</el-tag>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :inline="true" :model="queryParams" class="demo-form-inline mb-3">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
            style="width: 260px"
          />
        </el-form-item>
        
        <el-form-item label="账号名称">
          <el-input
            v-model="queryParams.account_name"
            placeholder="请输入账号名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="计划名称">
          <el-input
            v-model="queryParams.campaign_name"
            placeholder="请输入计划名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="单元名称">
          <el-input
            v-model="queryParams.unit_name"
            placeholder="请输入单元名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="标题">
          <el-input
            v-model="queryParams.title"
            placeholder="请输入标题"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="口令">
          <el-input
            v-model="queryParams.pwd"
            placeholder="请输入口令"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        
        <el-form-item label="广告类型">
          <el-select
            v-model="queryParams.placement"
            placeholder="请选择广告类型"
            clearable
            style="width: 150px"
          >
            <el-option label="信息流" :value="1" />
            <el-option label="搜索" :value="2" />
            <el-option label="开屏" :value="3" />
            <el-option label="全站智投" :value="4" />
            <el-option label="视频内流" :value="7" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="聚合类型">
          <el-select
            v-model="queryParams.aggregate_type"
            placeholder="请选择聚合类型"
            style="width: 150px"
          >
            <el-option label="分日数据" value="daily" />
            <el-option label="汇总数据" value="summary" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleQuery" :loading="loading">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button 
            type="success" 
            @click="handleExport" 
            :loading="exportLoading"
            :disabled="reportList.length === 0"
          >
            导出Excel
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 统计信息 -->
      <el-row :gutter="20" class="stats-cards mb-3" v-if="statsData">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <span class="label">总报表数</span>
              <span class="value">{{ statsData.total_reports || 0 }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <span class="label">总消费</span>
              <span class="value text-red">¥{{ formatAmount(statsData.total_fee) }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <span class="label">总点击</span>
              <span class="value">{{ formatNumber(statsData.total_click) }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-item">
              <span class="label">平均ROI</span>
              <span class="value text-green">{{ formatROI(statsData.total_roi) }}</span>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 数据表格 -->
      <el-table 
        :data="reportList" 
        style="width: 100%" 
        v-loading="loading"
        :stripe="true"
        :border="true"
        :default-sort="{prop: 'time', order: 'descending'}"
      >
        <el-table-column prop="time" label="日期" min-width="120" fixed="left" sortable>
          <template #default="scope">
            {{ formatDate(scope.row.time) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="account_name" label="账号名称" min-width="120" sortable />
        <el-table-column prop="title" label="标题" min-width="150" sortable />
        <el-table-column prop="pwd" label="口令" min-width="120" sortable />
        <el-table-column prop="content_people" label="内容人员" min-width="100" sortable />
        <el-table-column prop="pitcher_name" label="投手" min-width="100" sortable />
        <el-table-column prop="campaign_name" label="计划名称" min-width="150" sortable />
        <el-table-column prop="unit_name" label="单元名称" min-width="150" sortable />
        
        <el-table-column prop="placement" label="广告类型" min-width="100" sortable>
          <template #default="scope">
            <el-tag :type="getPlacementTagType(scope.row.placement)" size="small">
              {{ getPlacementName(scope.row.placement) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="fee" label="消费" min-width="100" sortable>
          <template #default="scope">
            <span class="text-red">¥{{ formatAmount(scope.row.fee) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="impression" label="曝光" min-width="100" sortable>
          <template #default="scope">
            {{ formatNumber(scope.row.impression) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="click" label="点击" min-width="100" sortable>
          <template #default="scope">
            {{ formatNumber(scope.row.click) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="ctr" label="点击率%" min-width="100" sortable>
          <template #default="scope">
            {{ formatPercentage(scope.row.ctr) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="acp" label="平均点击价格" min-width="120" sortable>
          <template #default="scope">
            ¥{{ formatAmount(scope.row.acp) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="cpm" label="千次曝光成本" min-width="120" sortable>
          <template #default="scope">
            ¥{{ formatAmount(scope.row.cpm) }}
          </template>
        </el-table-column>
        
        <el-table-column label="互动数据" align="center">
          <el-table-column prop="like" label="点赞" min-width="80" sortable />
          <el-table-column prop="comment" label="评论" min-width="80" sortable />
          <el-table-column prop="collect" label="收藏" min-width="80" sortable />
          <el-table-column prop="follow" label="关注" min-width="80" sortable />
          <el-table-column prop="share" label="分享" min-width="80" sortable />
          <el-table-column prop="interaction" label="总互动" min-width="100" sortable />
        </el-table-column>
        
        <el-table-column prop="cpi" label="互动成本" min-width="100" sortable>
          <template #default="scope">
            ¥{{ formatAmount(scope.row.cpi) }}
          </template>
        </el-table-column>
        
        <el-table-column label="转化数据" align="center">
          <el-table-column prop="goods_order" label="商品订单" min-width="100" sortable />
          <el-table-column prop="rgmv" label="GMV" min-width="120" sortable>
            <template #default="scope">
              ¥{{ formatAmount(scope.row.rgmv) }}
            </template>
          </el-table-column>
          <el-table-column prop="roi" label="ROI" min-width="100" sortable>
            <template #default="scope">
              <span :class="{'text-green': scope.row.roi > 1, 'text-red': scope.row.roi < 1}">
                {{ formatROI(scope.row.roi) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="success_goods_order" label="成功订单" min-width="100" sortable />
          <el-table-column prop="purchase_order_roi_7d" label="7日ROI" min-width="100" sortable>
            <template #default="scope">
              <span :class="{'text-green': scope.row.purchase_order_roi_7d > 1, 'text-red': scope.row.purchase_order_roi_7d < 1}">
                {{ formatROI(scope.row.purchase_order_roi_7d) }}
              </span>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" min-width="120" fixed="right">
          <template #default="scope">
            <el-button 
              link 
              type="primary" 
              @click="handleViewDetail(scope.row.id)"
              :loading="detailLoading"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-if="total > 0"
        v-model:current-page="queryParams.page"
        v-model:page-size="queryParams.page_size"
        :page-sizes="[20, 50, 100, 200]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="mt-3"
      />

      <!-- 暂无数据 -->
      <el-empty v-if="!loading && reportList.length === 0" description="暂无数据" />
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      :title="`创意详情 - ID: ${currentDetailId}`"
      v-model="detailDialogVisible"
      width="800px"
      @close="handleDetailClose"
    >
      <div v-if="detailData" v-loading="detailLoading">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="创意ID">{{ detailData.creativity_id }}</el-descriptions-item>
          <el-descriptions-item label="笔记ID">{{ detailData.note_id }}</el-descriptions-item>
          <el-descriptions-item label="创意名称">{{ detailData.creativity_name }}</el-descriptions-item>
          <el-descriptions-item label="创意图片">
            <el-image 
              v-if="detailData.creativity_image" 
              :src="detailData.creativity_image" 
              style="width: 100px; height: 100px"
              fit="cover"
              :preview-src-list="[detailData.creativity_image]"
            />
            <span v-else>-</span>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 投放配置 -->
        <el-descriptions title="投放配置" :column="2" border class="mt-3">
          <el-descriptions-item label="优化目标">{{ getOptimizeTargetName(detailData.optimize_target) }}</el-descriptions-item>
          <el-descriptions-item label="推广标的">{{ getPromotionTargetName(detailData.promotion_target) }}</el-descriptions-item>
          <el-descriptions-item label="出价方式">{{ getBiddingStrategyName(detailData.bidding_strategy) }}</el-descriptions-item>
          <el-descriptions-item label="搭建类型">{{ getBuildTypeName(detailData.build_type) }}</el-descriptions-item>
          <el-descriptions-item label="营销诉求">{{ getMarketingTargetName(detailData.marketing_target) }}</el-descriptions-item>
        </el-descriptions>

        <!-- 数据表现 -->
        <el-descriptions title="数据表现" :column="3" border class="mt-3">
          <el-descriptions-item label="消费">¥{{ formatAmount(detailData.fee) }}</el-descriptions-item>
          <el-descriptions-item label="曝光">{{ formatNumber(detailData.impression) }}</el-descriptions-item>
          <el-descriptions-item label="点击">{{ formatNumber(detailData.click) }}</el-descriptions-item>
          <el-descriptions-item label="点击率">{{ formatPercentage(detailData.ctr) }}</el-descriptions-item>
          <el-descriptions-item label="平均点击价格">¥{{ formatAmount(detailData.acp) }}</el-descriptions-item>
          <el-descriptions-item label="千次曝光成本">¥{{ formatAmount(detailData.cpm) }}</el-descriptions-item>
          <el-descriptions-item label="总互动">{{ formatNumber(detailData.interaction) }}</el-descriptions-item>
          <el-descriptions-item label="互动成本">¥{{ formatAmount(detailData.cpi) }}</el-descriptions-item>
          <el-descriptions-item label="ROI">{{ formatROI(detailData.roi) }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 详情对话框底部 -->
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getXhsCreativeReports,
  getXhsCreativeReportsStats,
  exportXhsCreativeReports,
  getXhsCreativeReportDetail,
  PLACEMENT_TYPES,
  OPTIMIZE_TARGETS,
  PROMOTION_TARGETS,
  BIDDING_STRATEGIES,
  BUILD_TYPES,
  MARKETING_TARGETS
} from '@/api/xiaohongshu'

// 查询参数
const queryParams = reactive({
  account_name: '',
  campaign_name: '',
  unit_name: '',
  title: '',
  pwd: '',
  start_date: '',
  end_date: '',
  placement: null,
  aggregate_type: 'daily',
  page: 1,
  page_size: 20
})

// 日期范围
const dateRange = ref([])

// 数据列表
const reportList = ref([])
const total = ref(0)
const loading = ref(false)
const exportLoading = ref(false)

// 统计数据
const statsData = ref(null)

// 详情相关
const detailDialogVisible = ref(false)
const detailData = ref(null)
const detailLoading = ref(false)
const currentDetailId = ref('')

// 初始化日期范围（默认最近7天）
const initDateRange = () => {
  const end = new Date()
  const start = new Date()
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)

  const formatDate = (date) => {
    return date.getFullYear() + '-' +
           String(date.getMonth() + 1).padStart(2, '0') + '-' +
           String(date.getDate()).padStart(2, '0')
  }

  dateRange.value = [formatDate(start), formatDate(end)]
  queryParams.start_date = dateRange.value[0]
  queryParams.end_date = dateRange.value[1]
}

// 处理日期变化
const handleDateChange = (dates) => {
  if (dates && dates.length === 2) {
    queryParams.start_date = dates[0]
    queryParams.end_date = dates[1]
  } else {
    queryParams.start_date = ''
    queryParams.end_date = ''
  }
}

// 查询数据
const handleQuery = async () => {
  loading.value = true
  try {
    // 获取列表数据
    const listResponse = await getXhsCreativeReports(queryParams)
    reportList.value = listResponse.data.list || []
    total.value = listResponse.data.total || 0

    // 获取统计数据
    const statsParams = { ...queryParams }
    delete statsParams.page
    delete statsParams.page_size

    const statsResponse = await getXhsCreativeReportsStats(statsParams)
    statsData.value = statsResponse.data || null

    if (reportList.value.length === 0) {
      ElMessage.info('暂无数据')
    }
  } catch (error) {
    console.error('获取小红书创意报表数据失败:', error)
    ElMessage.error('获取数据失败: ' + (error.response?.data?.message || error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 重置查询
const resetQuery = () => {
  Object.assign(queryParams, {
    account_name: '',
    campaign_name: '',
    unit_name: '',
    title: '',
    pwd: '',
    start_date: '',
    end_date: '',
    placement: null,
    aggregate_type: 'daily',
    page: 1,
    page_size: 20
  })
  dateRange.value = []
  initDateRange()
  handleQuery()
}

// 分页大小变化
const handleSizeChange = (size) => {
  queryParams.page_size = size
  queryParams.page = 1
  handleQuery()
}

// 当前页变化
const handleCurrentChange = (page) => {
  queryParams.page = page
  handleQuery()
}

// 导出Excel
const handleExport = async () => {
  if (!queryParams.start_date || !queryParams.end_date) {
    ElMessage.warning('请先设置时间范围')
    return
  }

  exportLoading.value = true
  try {
    const exportParams = { ...queryParams, format: 'xlsx' }
    delete exportParams.page
    delete exportParams.page_size

    const response = await exportXhsCreativeReports(exportParams)

    // 检查响应是否为blob
    if (response.data instanceof Blob) {
      // 检查blob是否为错误响应（JSON）
      if (response.data.type === 'application/json') {
        const text = await response.data.text()
        const errorData = JSON.parse(text)
        ElMessage.error('导出失败: ' + (errorData.message || '服务器错误'))
        return
      }

      // 创建下载链接
      const url = window.URL.createObjectURL(response.data)
      const link = document.createElement('a')
      link.href = url

      // 生成文件名
      let filename = `小红书创意报表_${queryParams.start_date}_${queryParams.end_date}.xlsx`
      if (response.headers && response.headers['content-disposition']) {
        const contentDisposition = response.headers['content-disposition']
        const match = contentDisposition.match(/filename=([^;]+)/)
        if (match) {
          filename = `小红书创意报表_${queryParams.start_date}_${queryParams.end_date}.xlsx`
        }
      }

      link.setAttribute('download', filename)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)

      ElMessage.success('导出成功')
    } else {
      ElMessage.error('导出失败: 响应格式错误')
    }
  } catch (error) {
    console.error('导出失败:', error)
    if (error.response && error.response.data instanceof Blob) {
      try {
        const text = await error.response.data.text()
        const errorData = JSON.parse(text)
        ElMessage.error('导出失败: ' + (errorData.message || '服务器错误'))
      } catch (parseError) {
        ElMessage.error('导出失败: 服务器错误')
      }
    } else {
      ElMessage.error('导出失败: ' + (error.response?.data?.message || error.message || '未知错误'))
    }
  } finally {
    exportLoading.value = false
  }
}

// 查看详情
const handleViewDetail = async (id) => {
  currentDetailId.value = id
  detailLoading.value = true
  detailDialogVisible.value = true

  try {
    const response = await getXhsCreativeReportDetail(id)
    detailData.value = response.data || null
  } catch (error) {
    console.error('获取详细数据失败:', error)
    ElMessage.error('获取详细数据失败: ' + (error.response?.data?.message || error.message || '未知错误'))
  } finally {
    detailLoading.value = false
  }
}

// 关闭详情对话框
const handleDetailClose = () => {
  detailData.value = null
  currentDetailId.value = ''
}

// 格式化金额
const formatAmount = (amount) => {
  if (amount === null || amount === undefined) return '0.00'
  return Number(amount).toFixed(2)
}

// 格式化数字
const formatNumber = (number) => {
  if (number === null || number === undefined) return '0'
  return Number(number).toLocaleString()
}

// 格式化百分比
const formatPercentage = (percentage) => {
  if (percentage === null || percentage === undefined) return '0.00%'
  return Number(percentage).toFixed(2) + '%'
}

// 格式化ROI
const formatROI = (roi) => {
  if (roi === null || roi === undefined) return '-'
  return Number(roi).toFixed(2)
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''

  // 如果是ISO格式的日期字符串，先转换为Date对象
  if (date.includes('T')) {
    const dateObj = new Date(date)
    return dateObj.getFullYear() + '-' +
           String(dateObj.getMonth() + 1).padStart(2, '0') + '-' +
           String(dateObj.getDate()).padStart(2, '0')
  }

  // 如果已经是YYYY-MM-DD格式，直接返回前10位
  return date.substring(0, 10)
}

// 获取广告类型名称
const getPlacementName = (placement) => {
  return PLACEMENT_TYPES[placement] || '未知'
}

// 获取广告类型标签类型
const getPlacementTagType = (placement) => {
  const typeMap = {
    1: 'primary',   // 信息流
    2: 'success',   // 搜索
    3: 'warning',   // 开屏
    4: 'info',      // 全站智投
    7: 'danger'     // 视频内流
  }
  return typeMap[placement] || 'info'
}

// 获取优化目标名称
const getOptimizeTargetName = (target) => {
  return OPTIMIZE_TARGETS[target] || '未知'
}

// 获取推广标的名称
const getPromotionTargetName = (target) => {
  return PROMOTION_TARGETS[target] || '未知'
}

// 获取出价方式名称
const getBiddingStrategyName = (strategy) => {
  return BIDDING_STRATEGIES[strategy] || '未知'
}

// 获取搭建类型名称
const getBuildTypeName = (type) => {
  return BUILD_TYPES[type] || '未知'
}

// 获取营销诉求名称
const getMarketingTargetName = (target) => {
  return MARKETING_TARGETS[target] || '未知'
}

// 组件挂载时初始化
onMounted(() => {
  initDateRange()
  handleQuery()
})
</script>

<style lang="scss" scoped>
.xhs-content-data-container {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      font-size: 16px;
      font-weight: 600;
    }
  }

  .mb-3 {
    margin-bottom: 20px;
  }

  .mt-3 {
    margin-top: 20px;
  }

  // 统计卡片样式
  .stats-cards {
    .stats-card {
      .stats-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 10px 0;

        .label {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .value {
          font-size: 20px;
          font-weight: 600;
          color: #333;

          &.text-red {
            color: #f56c6c;
          }

          &.text-green {
            color: #67c23a;
          }
        }
      }
    }
  }

  // 表格样式
  .el-table {
    .text-red {
      color: #f56c6c;
    }

    .text-green {
      color: #67c23a;
    }
  }

  // 分页样式
  .el-pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  // 详情对话框样式
  .el-descriptions {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  // 响应式设计
  @media screen and (max-width: 768px) {
    .demo-form-inline {
      .el-form-item {
        width: 100%;
        margin-right: 0;

        .el-input,
        .el-select,
        .el-date-picker {
          width: 100% !important;
        }
      }
    }

    .stats-cards {
      .el-col {
        margin-bottom: 10px;
      }
    }
  }
}
</style>
