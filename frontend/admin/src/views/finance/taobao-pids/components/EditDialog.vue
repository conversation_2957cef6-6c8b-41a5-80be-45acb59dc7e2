<template>
  <el-dialog
    :model-value="modelValue"
    :title="title"
    width="500px"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      @submit.prevent
    >
      <el-form-item
        label="推广位名称"
        prop="zone_name"
      >
        <el-input
          v-model="form.zone_name"
          placeholder="请输入推广位名称"
        />
      </el-form-item>
      <el-form-item
        label="推广位ID"
        prop="pid"
      >
        <el-input
          v-model="form.pid"
          placeholder="请输入推广位ID，格式如：mm_**********_3142800228_115825400115"
          @input="handlePidInput"
        />
      </el-form-item>
      <el-form-item
        label="会员ID"
      >
        <el-input
          v-model="extractedInfo.memberId"
          placeholder="自动提取"
          disabled
        />
      </el-form-item>
      <el-form-item
        label="账户名称"
      >
        <el-input
          v-model="extractedInfo.accountName"
          placeholder="自动提取"
          disabled
        />
      </el-form-item>
      <el-form-item
        label="活动类型"
        prop="act_type"
      >
        <el-select
          v-model="form.act_type"
          placeholder="请选择活动类型"
          style="width: 100%"
        >
          <el-option
            v-for="item in actTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="$emit('update:modelValue', false)">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleConfirm"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'

// 添加活动类型选项
const actTypeOptions = [
  { label: '飞猪', value: 1 },
  { label: '福利购', value: 2 }
]

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  model: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'confirm'])

const formRef = ref(null)
const form = ref({
  zone_name: '',
  pid: '',
  act_type: 1  // 默认选择飞猪
})

// 自动提取的信息
const extractedInfo = ref({
  memberId: '',
  accountName: ''
})

const rules = {
  zone_name: [
    { required: true, message: '请输入推广位名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  pid: [
    { required: true, message: '请输入推广位ID', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  act_type: [
    { required: true, message: '请选择活动类型', trigger: 'change' }
  ]
}

// 账户映射表
const accountMap = {
  '**********': '上海壹棵树@alimama(**********)',
  '**********': '长沙易欢科技@alimama(**********)',
  '**********': '杭州旻天科技2021(**********)',
  '*********': '西典科技'
}

// 处理PID输入，自动提取会员ID和账户名称
const handlePidInput = (value) => {
  extractedInfo.value = { memberId: '', accountName: '' }
  
  if (!value) return
  
  // 使用正则表达式提取会员ID，支持更多格式
  const regex = /mm_(\d+)[_-]/
  const match = value.match(regex)
  
  if (match && match[1]) {
    const memberId = match[1]
    extractedInfo.value.memberId = memberId
    
    // 根据会员ID获取账户名称
    if (accountMap[memberId]) {
      extractedInfo.value.accountName = accountMap[memberId]
    } else {
      extractedInfo.value.accountName = `未知账户(${memberId})`
    }
  }
}

// 监听model变化
watch(
  () => props.model,
  (val) => {
    form.value = {
      ...form.value,
      ...val
    }
    
    // 如果有PID，则自动提取会员ID和账户名称
    if (val.pid) {
      handlePidInput(val.pid)
    }
  },
  { immediate: true, deep: true }
)

// 处理确认
const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 提交表单数据
    emit('confirm', {
      ...props.model,
      ...form.value
    })
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  extractedInfo.value = { memberId: '', accountName: '' }
}

// 监听弹窗关闭
watch(
  () => props.modelValue,
  (val) => {
    if (!val) {
      resetForm()
    }
  }
)
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 