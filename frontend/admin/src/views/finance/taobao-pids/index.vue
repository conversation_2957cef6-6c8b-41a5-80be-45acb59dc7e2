<template>
  <div class="app-container">
    <el-card class="tab-container">
      <el-tabs v-model="activeTab" @tab-click="handleTabChange">
        <el-tab-pane label="未使用" name="unused">
          <template #label>
            <span>未使用 ({{ unusedCount }})</span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="已使用" name="used">
          <template #label>
            <span>已使用 ({{ usedCount }})</span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <el-card class="filter-container">
      <div class="filter-item-container">
        <el-input
          v-model="listQuery.keyword"
          placeholder="搜索推广位名称/ID"
          class="filter-item"
          clearable
          @keyup.enter="handleFilter"
          @clear="handleFilter"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <el-select
          v-model="listQuery.act_type"
          placeholder="活动类型"
          class="filter-item"
          clearable
          @change="handleFilter"
        >
          <el-option
            v-for="item in actTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>

        <el-button
          type="primary"
          :icon="Search"
          class="filter-item"
          @click="handleFilter"
        >
          查询
        </el-button>

        <el-button
          type="primary"
          :icon="Plus"
          class="filter-item"
          @click="handleCreate"
        >
          新增淘联链接
        </el-button>
      </div>
    </el-card>

    <el-card class="table-container">
      <el-table
        v-loading="loading"
        :data="list"
        border
        stripe
        highlight-current-row
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="80"
          sortable="custom"
          align="center"
        />
        <el-table-column
          prop="zone_name"
          label="推广位名称"
          min-width="150"
          align="left"
          show-overflow-tooltip
        />
        <el-table-column
          prop="pid"
          label="推广位ID"
          min-width="150"
          align="left"
          show-overflow-tooltip
        />
        <el-table-column
          prop="act_type"
          label="活动类型"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            <el-tag
              :type="Number(row.act_type) === 1 ? 'warning' : 'success'"
              class="status-tag"
              effect="plain"
            >
              {{ Number(row.act_type) === 1 ? '飞猪' : '福利购' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="is_used"
          label="使用状态"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            <el-tag
              :type="row.is_used ? 'info' : 'primary'"
              class="status-tag"
              effect="plain"
            >
              {{ row.is_used ? '已使用' : '未使用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="used_at"
          label="使用时间"
          width="180"
          align="center"
        />
        <el-table-column
          prop="created_at"
          label="创建时间"
          width="180"
          align="center"
        />
      </el-table>

      <el-pagination
        v-show="total > 0"
        class="pagination-container"
        :current-page="listQuery.page"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="listQuery.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <edit-dialog
      v-model="dialogVisible"
      :title="dialogType === 'create' ? '新增淘联链接' : '编辑淘联链接'"
      :loading="dialogLoading"
      :model="currentModel"
      @confirm="handleDialogConfirm"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'
import EditDialog from './components/EditDialog.vue'
import { getTaobaoPids, createTaobaoPid, updateTaobaoPid, deleteTaobaoPid } from '@/api/taobao-pids'

// 活动类型选项
const actTypeOptions = [
  { label: '飞猪', value: 1 },
  { label: '福利购', value: 2 }
]

const loading = ref(false)
const dialogVisible = ref(false)
const dialogLoading = ref(false)
const dialogType = ref('create')
const currentModel = ref({})
const list = ref([])
const total = ref(0)
const usedCount = ref(0)
const unusedCount = ref(0)
const activeTab = ref('unused')

const listQuery = reactive({
  page: 1,
  limit: 10,
  sort: '-id',
  keyword: '',
  is_used: 0,
  act_type: undefined
})

// 获取列表数据
const getList = async () => {
  try {
    loading.value = true
    const { data } = await getTaobaoPids(listQuery)
    list.value = data.items
    total.value = data.total
    
    // 根据筛选条件更新tab计数
    if (listQuery.keyword || listQuery.act_type) {
      // 如果有筛选条件，只更新当前tab的计数
      if (activeTab.value === 'used') {
        usedCount.value = data.total
      } else {
        unusedCount.value = data.total
      }
    } else {
      // 没有筛选条件时，更新所有tab计数
      usedCount.value = data.used_count
      unusedCount.value = data.unused_count
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 处理Tab切换
const handleTabChange = (tab) => {
  listQuery.is_used = tab.props.name === 'used' ? 1 : 0
  listQuery.page = 1
  getList()
}

// 处理搜索
const handleFilter = () => {
  listQuery.page = 1
  getList()
}

// 处理创建
const handleCreate = () => {
  dialogType.value = 'create'
  currentModel.value = {}
  dialogVisible.value = true
}

// 处理更新
const handleUpdate = (row) => {
  dialogType.value = 'update'
  currentModel.value = { ...row }
  dialogVisible.value = true
}

// 处理删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确认要删除该淘联链接吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteTaobaoPid(row.id)
      ElMessage.success('删除成功')
      getList()
    } catch (error) {
      console.error(error)
      ElMessage.error('删除失败')
    }
  })
}

// 处理弹窗确认
const handleDialogConfirm = async (model) => {
  try {
    dialogLoading.value = true
    if (dialogType.value === 'create') {
      await createTaobaoPid(model)
      ElMessage.success('创建成功')
    } else {
      await updateTaobaoPid(model.id, model)
      ElMessage.success('更新成功')
    }
    dialogVisible.value = false
    getList()
  } catch (error) {
    console.error(error)
    ElMessage.error(dialogType.value === 'create' ? '创建失败' : '更新失败')
  } finally {
    dialogLoading.value = false
  }
}

// 处理排序变化
const handleSortChange = (val) => {
  listQuery.sort = val.prop ? `${val.order === 'ascending' ? '+' : '-'}${val.prop}` : '+id'
  getList()
}

// 处理每页数量变化
const handleSizeChange = (val) => {
  listQuery.limit = val
  getList()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  listQuery.page = val
  getList()
}

// 初始化
getList()
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  
  .tab-container {
    margin-bottom: 20px;
  }
  
  .filter-container {
    margin-bottom: 20px;
    
    .filter-item-container {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .filter-item {
        width: 200px;
      }
    }
  }
  
  .table-container {
    margin-bottom: 20px;
    
    .status-tag {
      min-width: 60px;
    }
  }
  
  .pagination-container {
    padding: 20px 0;
    display: flex;
    justify-content: flex-end;
  }
}
</style> 