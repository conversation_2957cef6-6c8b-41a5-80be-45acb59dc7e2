<template>
  <div class="lighthouse-data">
    <div class="page-header">
      <h2>灯火数据</h2>
      <p class="page-description">灯火数据分析与统计报表</p>
    </div>
    
    <div class="content-wrapper">
      <!-- 筛选条件区域 -->
      <el-card class="filter-card" shadow="never">
        <div class="filter-row">
          <el-form :inline="true" :model="filterForm" class="filter-form">
            <el-form-item label="日期范围">
              <el-date-picker
                v-model="filterForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            
            <el-form-item label="媒体主体">
              <el-select v-model="filterForm.mediaEntity" placeholder="请选择媒体主体" clearable filterable>
                <el-option label="全部" value="" />
                <el-option label="抖音" value="douyin" />
                <el-option label="快手" value="kuaishou" />
                <el-option label="小红书" value="xiaohongshu" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="广告位">
              <el-select v-model="filterForm.dataType" placeholder="请选择数据类型" clearable filterable>
                <el-option label="全部" value="" />
                <el-option label="消费数据" value="cost" />
                <el-option label="转化数据" value="conversion" />
                <el-option label="效果数据" value="effect" />
              </el-select>
            </el-form-item>

            <el-form-item label="广告位">
              <el-select v-model="filterForm.planId" placeholder="请选择数据类型" clearable filterable>
                <el-option label="全部" value="" />
                <el-option label="消费数据" value="cost" />
                <el-option label="转化数据" value="conversion" />
                <el-option label="效果数据" value="effect" />
              </el-select>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="handleSearch" :loading="loading">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
              <el-button @click="handleReset">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
              <el-button type="warning" @click="handleSync">
                <el-icon><Operation /></el-icon>
                同步数据
              </el-button>
              <el-button type="success" @click="handleExport">
                <el-icon><Download /></el-icon>
                导出
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>

      <!-- 数据表格 -->
      <el-card class="table-card" shadow="never">
        <template #header>
          <div class="card-header">
            <h3>灯火数据列表</h3>
            <div class="header-actions">
              <el-button size="small" @click="handleRefresh">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </div>
        </template>
        
        <div class="table-wrapper">
          <el-table
            v-loading="loading"
            :data="tableData"
            stripe
            border
            style="width: 100%"
            @sort-change="handleSortChange"
            :scroll-y="400"
          >
            <!-- 基础信息列 -->
            <el-table-column prop="mediaEntity" label="媒体主体" width="100" fixed="left" />
            <el-table-column prop="materialType" label="素材类型" width="100" fixed="left"/>
            <el-table-column prop="date" label="日期" width="110" fixed="left" />
            <el-table-column prop="name" label="计划名称" width="110" fixed="left" />
            <el-table-column prop="name" label="链接名" width="110" fixed="left" />
            <!-- 消费相关 -->
             <!-- cps维度区域 -->
            <el-table-column label="广告维度" align="center">
              <el-table-column prop="actualCost" label="实际消费" width="120" sortable>
              <template #default="scope">
                <span class="amount">￥{{ scope.row.actualCost }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="adCost" label="广告消耗" width="120" sortable>
                <template #default="scope">
                  <span class="amount">￥{{ scope.row.adCost }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="exposure" label="曝光量" width="120" sortable>
                <template #default="scope">
                  <span>{{ scope.row.exposure }}</span>
                </template>
            </el-table-column>
            <!-- 点击相关 -->
            <el-table-column prop="clicks" label="点击数" width="100" sortable />
            <el-table-column prop="ctr" label="CTR" width="100" sortable>
              <template #default="scope">
                {{ scope.row.ctr }}%
              </template>
            </el-table-column>
            <el-table-column prop="cpc" label="CPC单价" width="100" sortable>
              <template #default="scope">
                ￥{{ scope.row.cpc }}
              </template>
            </el-table-column>
            </el-table-column>
            
            
            <!-- cps维度区域 -->
            <el-table-column label="cps维度" align="center">
              <el-table-column prop="effectiveUv" label="锁佣UV" width="100" sortable />
              <el-table-column prop="uvValue" label="uv价值" width="100" sortable>
                <template #default="scope">
                  ￥{{ scope.row.uvValue }}
                </template>
              </el-table-column>
              <el-table-column prop="uvCost" label="uv成本" width="100" sortable>
                <template #default="scope">
                  ￥{{ scope.row.uvCost }}
                </template>
              </el-table-column>
              <el-table-column prop="clickConversionRate" label="点击转化率" width="120" sortable>
                <template #default="scope">
                  {{ scope.row.clickConversionRate }}%
                </template>
              </el-table-column>
              <el-table-column prop="orders" label="订单" width="100" sortable />
              <el-table-column prop="commission" label="佣金（订单金额）" width="140" sortable>
                <template #default="scope">
                  ￥{{ scope.row.commission }}
                </template>
              </el-table-column>
              <el-table-column prop="effectiveRecoveryRoi" label="roi" width="130" sortable>
                <template #default="scope">
                  {{ scope.row.effectiveRecoveryRoi }}
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </div>
        
        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handlePageSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 同步数据对话框 -->
    <el-dialog
      v-model="syncDialog.visible"
      title="同步灯火数据"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="syncFormRef"
        :model="syncForm"
        :rules="syncRules"
        label-width="100px"
      >
        <el-form-item label="媒体ID" prop="mediaId">
          <el-select
            v-model="syncForm.mediaId"
            placeholder="请选择媒体"
            style="width: 100%"
            filterable
          >
            <el-option label="抖音媒体" :value="101" />
            <el-option label="快手媒体" :value="102" />
            <el-option label="小红书媒体" :value="103" />
            <el-option label="微博媒体" :value="104" />
          </el-select>
        </el-form-item>

        <el-form-item label="代理商ID" prop="agentId">
          <el-select
            v-model="syncForm.agentId"
            placeholder="请选择代理商"
            style="width: 100%"
            filterable
          >
            <el-option label="代理商A" :value="456" />
            <el-option label="代理商B" :value="457" />
            <el-option label="代理商C" :value="458" />
          </el-select>
        </el-form-item>

        <el-form-item label="日期范围" prop="dateRange">
          <el-date-picker
            v-model="syncForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
            :disabled-date="disabledDate"
          />
        </el-form-item>

        <el-alert
          title="同步说明"
          description="同步过程可能需要几分钟时间，请耐心等待。同步完成后会自动刷新数据列表。"
          type="info"
          :closable="false"
          style="margin-bottom: 20px"
        />
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="syncDialog.visible = false" :disabled="syncDialog.loading">
            取消
          </el-button>
          <el-button
            type="primary"
            @click="handleConfirmSync"
            :loading="syncDialog.loading"
          >
            开始同步
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Download, Money, Mouse, TrendCharts, User, Operation } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)

// 筛选表单
const filterForm = reactive({
  dateRange: [],
  mediaEntity: '',
  dataType: '',
  planId: ''
})

// 数据概览
const overview = reactive({
  totalCost: 0,
  totalClicks: 0,
  avgCtr: 0,
  totalUv: 0
})

// 表格数据
const tableData = ref([])

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 同步对话框
const syncDialog = reactive({
  visible: false,
  loading: false
})

// 同步表单
const syncForm = reactive({
  mediaId: '',
  agentId: '',
  dateRange: []
})

// 同步表单验证规则
const syncRules = {
  mediaId: [
    { required: true, message: '请选择媒体', trigger: 'change' }
  ],
  agentId: [
    { required: true, message: '请选择代理商', trigger: 'change' }
  ],
  dateRange: [
    { required: true, message: '请选择日期范围', trigger: 'change' }
  ]
}

// 同步表单引用
const syncFormRef = ref(null)



// 方法
const handleSearch = () => {
  console.log('查询数据', filterForm)
  loadData()
}

const handleReset = () => {
  filterForm.dateRange = []
  filterForm.mediaEntity = ''
  filterForm.dataType = ''
  filterForm.planId = ''
  loadData()
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const handleRefresh = () => {
  loadData()
}

const handleSortChange = (sort) => {
  console.log('排序变化', sort)
  // TODO: 实现排序逻辑
}

// 处理同步按钮点击
const handleSync = () => {
  syncDialog.visible = true
  // 重置表单
  syncForm.mediaId = ''
  syncForm.agentId = ''
  syncForm.dateRange = []
}

// 确认同步
const handleConfirmSync = async () => {
  if (!syncFormRef.value) return
  
  try {
    // 验证表单
    await syncFormRef.value.validate()
    
    syncDialog.loading = true
    
    // 构建同步参数
    const syncParams = {
      media_id: syncForm.mediaId,
      agent_id: syncForm.agentId,
      start_date: syncForm.dateRange[0],
      end_date: syncForm.dateRange[1]
    }
    
    console.log('同步参数:', syncParams)
    
    // 调用同步接口
    const response = await fetch('/api/v1/sync/denghuo-plus', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(syncParams)
    })
    
    if (!response.ok) {
      throw new Error(`同步失败: ${response.status} ${response.statusText}`)
    }
    
    const result = await response.json()
    
    ElMessage.success('数据同步已启动，请稍候查看结果')
    syncDialog.visible = false
    
    // 同步完成后刷新数据
    setTimeout(() => {
      loadData()
    }, 2000)
    
  } catch (error) {
    console.error('同步失败:', error)
    ElMessage.error(error.message || '同步失败，请重试')
  } finally {
    syncDialog.loading = false
  }
}

// 日期选择限制（不能选择未来日期）
const disabledDate = (time) => {
  return time.getTime() > Date.now()
}



const handlePageSizeChange = (size) => {
  pagination.pageSize = size
  loadData()
}

const handlePageChange = (page) => {
  pagination.current = page
  loadData()
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // TODO: 调用 API 获取实际数据
    // 模拟数据
    setTimeout(() => {
      overview.totalCost = 85600.23
      overview.totalClicks = 12650
      overview.avgCtr = 2.84
      overview.totalUv = 8950

      // 生成模拟表格数据，符合Excel结构
      tableData.value = Array.from({ length: 20 }, (_, index) => {
        const mediaEntities = ['抖音', '快手', '小红书', '微博']
        const randomMedia = mediaEntities[Math.floor(Math.random() * mediaEntities.length)]
        
        return {
          id: index + 1,
          mediaEntity: randomMedia,
          dataDate: `2024/12/${String(index + 1).padStart(2, '0')}`,
          date: `2024-12-${String(index + 1).padStart(2, '0')}`,
          planCount: Math.floor(Math.random() * 20) + 5,
          materialType: ['视频', '图片', '文字', '直播'][Math.floor(Math.random() * 4)],
          mediaCount: Math.floor(Math.random() * 15) + 3,
          planName: `计划${index + 1}`,
          linkName: `链接${index + 1}`,
          actualCost: (Math.random() * 5000 + 1000).toFixed(2),
          adCost: (Math.random() * 3000 + 800).toFixed(2),
          exposure: Math.floor(Math.random() * 50000) + 10000,
          clicks: Math.floor(Math.random() * 1000) + 200,
          ctr: (Math.random() * 5 + 1).toFixed(2),
          cpc: (Math.random() * 2 + 0.5).toFixed(2),
          effectiveUv: Math.floor(Math.random() * 800) + 150,
          uvValue: (Math.random() * 10 + 2).toFixed(2),
          uvCost: (Math.random() * 5 + 1).toFixed(2),
          clickConversionRate: (Math.random() * 8 + 2).toFixed(2),
          orders: Math.floor(Math.random() * 100) + 20,
          commission: (Math.random() * 100 + 20).toFixed(2),
          effectiveRecoveryRoi: (Math.random() * 1.5 + 0.5).toFixed(2)
        }
      })

      pagination.total = 150
      loading.value = false
    }, 1000)
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
    loading.value = false
  }
}

// 页面初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.lighthouse-data {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filter-card {
  border: 1px solid #e4e7ed;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  align-items: end;
}

.overview-cards {
  margin-bottom: 20px;
}

.overview-card {
  height: 100px;
  cursor: pointer;
  transition: all 0.3s;
}

.overview-card:hover {
  transform: translateY(-2px);
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.card-icon.cost {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.click {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.conversion {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-icon.uv {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-info h3 {
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.card-info p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.table-card {
  border: 1px solid #e4e7ed;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.table-wrapper {
  max-height: 600px;
  overflow: auto;
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

/* 数据样式 */
.amount {
  font-weight: 600;
  color: #409eff;
}

.amount.positive {
  color: #67c23a;
}



/* 表格样式优化 */
:deep(.el-table) {
  font-size: 12px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #303133;
  font-weight: 600;
}

:deep(.el-table .cell) {
  padding: 0 8px;
}

/* 响应式处理 */
@media (max-width: 1200px) {
  .overview-cards .el-col {
    margin-bottom: 20px;
  }
}

/* 同步对话框样式 */
:deep(.el-dialog__body) {
  padding: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 