<template>
  <div class="promotion-report-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>推广报表管理（基于promotion_reports表）</span>
          <el-tag type="success" size="small">新版API</el-tag>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :inline="true" :model="queryParams" class="demo-form-inline mb-3">
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item label="口令组">
          <el-select
            v-model="queryParams.group_id"
            placeholder="请选择口令组"
            clearable
            filterable
            :loading="commandLoading"
          >
            <el-option
              v-for="item in commandOptions"
              :key="item.id"
              :label="item.groupName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="分类">
          <el-select
            v-model="queryParams.category_id"
            placeholder="请选择分类"
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button type="info" @click="getSummary">获取汇总</el-button>
        </el-form-item>
      </el-form>

      <!-- 汇总信息 -->
      <el-row :gutter="20" class="summary-cards" v-if="summaryData">
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-item">
              <span class="label">总费用</span>
              <span class="value">¥{{ formatAmount(summaryData.total_cost) }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-item">
              <span class="label">总订单数</span>
              <span class="value">{{ summaryData.total_orders }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-item">
              <span class="label">总佣金</span>
              <span class="value">¥{{ formatAmount(summaryData.total_commission) }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-item">
              <span class="label">平均费用</span>
              <span class="value">¥{{ formatAmount(summaryData.avg_cost) }}</span>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 数据表格 -->
      <el-table :data="reportList" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" min-width="80" />
        <el-table-column prop="report_date" label="日期" min-width="120" />
        <el-table-column prop="group_name" label="口令组" min-width="150" />
                 <el-table-column prop="category_name" label="分类" min-width="120" />
        <el-table-column prop="cost" label="费用" min-width="120">
          <template #default="scope">
            ¥{{ formatAmount(scope.row.cost) }}
          </template>
        </el-table-column>
        <el-table-column prop="total_orders" label="订单数" min-width="100" />
        <el-table-column prop="total_commission" label="佣金" min-width="120">
          <template #default="scope">
            ¥{{ formatAmount(scope.row.total_commission) }}
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" min-width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="100" fixed="right">
          <template #default="scope">
            <el-button link type="primary" @click="handleViewDetail(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.page_size"
          :page-sizes="[10, 20, 30, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      title="推广报表详情"
      v-model="detailDialogVisible"
      width="600px"
    >
      <div v-if="currentDetail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ currentDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="报表日期">{{ currentDetail.report_date }}</el-descriptions-item>
          <el-descriptions-item label="口令组">{{ currentDetail.group_name }}</el-descriptions-item>
                     <el-descriptions-item label="分类">{{ currentDetail.category_name }}</el-descriptions-item>
          <el-descriptions-item label="费用">¥{{ formatAmount(currentDetail.cost) }}</el-descriptions-item>
          <el-descriptions-item label="订单数">{{ currentDetail.total_orders }}</el-descriptions-item>
          <el-descriptions-item label="佣金">¥{{ formatAmount(currentDetail.total_commission) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(currentDetail.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime(currentDetail.updated_at) }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getPromotionReportList, getPromotionReportSummary, getPromotionReportById } from '@/api/cost'
import { getCommandList } from '@/api/command'

// 查询参数
const queryParams = reactive({
  page: 1,
  page_size: 10,
  start_date: '',
  end_date: '',
  group_id: undefined,
  category_id: undefined
})

// 日期范围
const dateRange = ref([])

// 数据列表
const reportList = ref([])
const total = ref(0)
const loading = ref(false)

// 汇总数据
const summaryData = ref(null)

// 口令组相关
const commandOptions = ref([])
const commandLoading = ref(false)

// 分类选项
const categoryOptions = [
  { value: 55, label: '小红书' },
  { value: 173, label: '抖音' },
  { value: 284, label: '闪购' },
]

// 详情对话框
const detailDialogVisible = ref(false)
const currentDetail = ref(null)

// 格式化金额
const formatAmount = (amount) => {
  if (!amount) return '0.00'
  return Number(amount).toFixed(2)
}

// 格式化日期时间
const formatDateTime = (datetime) => {
  if (!datetime) return '-'
  return new Date(datetime).toLocaleString()
}

// 获取口令组列表
const loadCommandList = async (keyword = '') => {
  commandLoading.value = true
  try {
    const res = await getCommandList({ 
      page: 1,
      pageSize: 1000,
      keyword,
      status: 1
    })
    commandOptions.value = res.data.list || []
  } catch (error) {
    console.error('获取口令组列表失败:', error)
    commandOptions.value = []
  } finally {
    commandLoading.value = false
  }
}

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    const params = { ...queryParams }
    // 清除undefined值
    Object.keys(params).forEach(key => {
      if (params[key] === undefined || params[key] === '') {
        delete params[key]
      }
    })
    
    const res = await getPromotionReportList(params)
    reportList.value = res.data.data || []
    total.value = res.data.total || 0
  } catch (error) {
    console.error('获取推广报表列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 获取汇总数据
const getSummary = async () => {
  try {
    const params = { ...queryParams }
    // 清除分页参数和undefined值
    delete params.page
    delete params.page_size
    Object.keys(params).forEach(key => {
      if (params[key] === undefined || params[key] === '') {
        delete params[key]
      }
    })
    
    const res = await getPromotionReportSummary(params)
    summaryData.value = res.data
    ElMessage.success('汇总数据已更新')
  } catch (error) {
    console.error('获取汇总数据失败:', error)
    ElMessage.error('获取汇总数据失败')
  }
}

// 日期范围变化
const handleDateChange = (val) => {
  if (val) {
    queryParams.start_date = val[0]
    queryParams.end_date = val[1]
  } else {
    queryParams.start_date = ''
    queryParams.end_date = ''
  }
}

// 查询
const handleQuery = () => {
  queryParams.page = 1
  getList()
}

// 重置查询
const resetQuery = () => {
  dateRange.value = []
  Object.assign(queryParams, {
    page: 1,
    page_size: 10,
    start_date: '',
    end_date: '',
    group_id: undefined,
    category_id: undefined
  })
  summaryData.value = null
  getList()
}

// 查看详情
const handleViewDetail = async (row) => {
  try {
    const res = await getPromotionReportById(row.id)
    currentDetail.value = res.data
    detailDialogVisible.value = true
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

// 分页相关
const handleSizeChange = (val) => {
  queryParams.page_size = val
  getList()
}

const handleCurrentChange = (val) => {
  queryParams.page = val
  getList()
}

// 初始化
onMounted(() => {
  loadCommandList()
  getList()
})
</script>

<style scoped>
.promotion-report-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-cards {
  margin-bottom: 20px;
}

.summary-card .summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-card .label {
  color: #606266;
  font-size: 14px;
}

.summary-card .value {
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.mb-3 {
  margin-bottom: 1rem;
}
</style> 