<template>
  <div class="weekly-report-container">
    <!-- 查询表单 -->
    <el-card class="query-card" shadow="never">
      <el-form :model="queryForm" :inline="true" size="default">
        <el-form-item label="查询时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :picker-options="pickerOptions"
            style="width: 280px"
          />
        </el-form-item>
        <el-form-item label="媒体筛选">
          <el-select
            v-model="queryForm.media_id"
            placeholder="请选择媒体"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="media in mediaOptions"
              :key="media.value"
              :label="media.label"
              :value="media.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button icon="Refresh" @click="handleReset">重置</el-button>
          <el-button type="success" icon="Download" @click="handleExport" :loading="exportLoading">导出</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 周报表数据表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>周报表数据</span>
        </div>
      </template>
      
      <!-- 周报表数据表格 -->
      <el-table 
        :data="reportList" 
        style="width: 100%" 
        v-loading="loading"
        :stripe="true"
        :border="true"
        :default-sort="{prop: 'year_week', order: 'descending'}"
      >
        <el-table-column prop="year_week" label="年周" min-width="80" fixed="left" sortable />
        <el-table-column label="周期间" min-width="180" fixed="left">
          <template #default="scope">
            {{ formatDate(scope.row.week_start) }} ~ {{ formatDate(scope.row.week_end) }}
          </template>
        </el-table-column>
        
        <!-- 支付宝相关 -->
        <el-table-column label="支付宝数据" align="center">
          <el-table-column prop="alipay_profit" label="利润" min-width="100" sortable>
            <template #default="scope">
              <span :class="{'text-red': scope.row.alipay_profit < 0, 'text-green': scope.row.alipay_profit > 0}">
                ¥{{ formatAmount(scope.row.alipay_profit) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="alipay_orders" label="订单数" min-width="80" sortable>
            <template #default="scope">
              {{ scope.row.alipay_orders || 0 }}
            </template>
          </el-table-column>
          <el-table-column prop="alipay_revenue" label="佣金收入" min-width="100" sortable>
            <template #default="scope">
              ¥{{ formatAmount(scope.row.alipay_revenue) }}
            </template>
          </el-table-column>
          <el-table-column prop="alipay_cost" label="成本" min-width="100" sortable>
            <template #default="scope">
              ¥{{ formatAmount(scope.row.alipay_cost) }}
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 微信相关 -->
        <el-table-column label="微信数据" align="center">
          <el-table-column prop="wechat_profit" label="利润" min-width="100" sortable>
            <template #default="scope">
              <span :class="{'text-red': scope.row.wechat_profit < 0, 'text-green': scope.row.wechat_profit > 0}">
                ¥{{ formatAmount(scope.row.wechat_profit) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="wechat_orders" label="订单数" min-width="80" sortable>
            <template #default="scope">
              {{ scope.row.wechat_orders || 0 }}
            </template>
          </el-table-column>
          <el-table-column prop="wechat_revenue" label="佣金收入" min-width="100" sortable>
            <template #default="scope">
              ¥{{ formatAmount(scope.row.wechat_revenue) }}
            </template>
          </el-table-column>
          <el-table-column prop="wechat_cost" label="成本" min-width="100" sortable>
            <template #default="scope">
              ¥{{ formatAmount(scope.row.wechat_cost) }}
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 汇总数据 -->
        <el-table-column label="汇总数据" align="center">
          <el-table-column prop="total_profit" label="总利润" min-width="100" sortable>
            <template #default="scope">
              <span :class="{'text-red': scope.row.total_profit < 0, 'text-green': scope.row.total_profit > 0}">
                ¥{{ formatAmount(scope.row.total_profit) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="total_orders" label="总订单" min-width="80" sortable>
            <template #default="scope">
              {{ scope.row.total_orders || 0 }}
            </template>
          </el-table-column>
          <el-table-column prop="roi" label="ROI" min-width="80" sortable>
            <template #default="scope">
              {{ formatROI(scope.row.roi) }}
            </template>
          </el-table-column>
          <el-table-column prop="profit_margin_rate" label="利润率" min-width="80" sortable>
            <template #default="scope">
              {{ formatPercentage(scope.row.profit_margin_rate) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="80" fixed="right">
            <template #default="scope">
              <el-button size="small" type="primary" @click="handleViewDetail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      title="周报表详情"
      v-model="detailDialogVisible"
      width="90%"
      :before-close="handleCloseDetail"
    >
      <div class="detail-header">
        <h3>{{ currentDetail.year_week }} 周详情（{{ formatDate(currentDetail.week_start) }} ~ {{ formatDate(currentDetail.week_end) }}）</h3>
        <div class="summary-info">
          <el-tag type="success">总利润: ¥{{ formatAmount(currentDetail.summary?.total_profit || 0) }}</el-tag>
          <el-tag type="info">总订单: {{ currentDetail.summary?.total_orders || 0 }}</el-tag>
          <el-tag type="warning">盈利媒体: {{ currentDetail.summary?.profit_media_count || 0 }}个</el-tag>
          <el-tag type="danger">亏损媒体: {{ currentDetail.summary?.loss_media_count || 0 }}个</el-tag>
        </div>
      </div>

      <!-- 详细数据表格 -->
      <el-table 
        :data="detailList" 
        style="width: 100%; margin-top: 20px;" 
        v-loading="detailLoading"
        :stripe="true"
        :border="true"
        max-height="500"
        :default-sort="{prop: 'profit', order: 'descending'}"
      >
        <el-table-column prop="media_name" label="媒体名称" min-width="120" fixed="left" sortable />
        <el-table-column prop="user_name" label="归属媒介" min-width="100" sortable />
        <el-table-column prop="promotion_channel" label="推广渠道" min-width="120" sortable>
          <template #default="scope">
            <el-tag :type="getChannelTagType(scope.row.promotion_channel)" size="small">
              {{ getChannelName(scope.row.promotion_channel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="is_profit" label="盈亏状态" min-width="100" sortable>
          <template #default="scope">
            <el-tag :type="scope.row.is_profit ? 'success' : 'danger'" size="small">
              {{ scope.row.is_profit ? '盈利' : '亏损' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="profit" label="利润" min-width="100" sortable>
          <template #default="scope">
            <span :class="{'text-red': scope.row.profit < 0, 'text-green': scope.row.profit > 0}">
              ¥{{ formatAmount(scope.row.profit) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="orders" label="订单数" min-width="80" sortable>
          <template #default="scope">
            {{ scope.row.orders || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="revenue" label="佣金收入" min-width="100" sortable>
          <template #default="scope">
            ¥{{ formatAmount(scope.row.revenue) }}
          </template>
        </el-table-column>
        <el-table-column prop="cost" label="成本" min-width="100" sortable>
          <template #default="scope">
            ¥{{ formatAmount(scope.row.cost) }}
          </template>
        </el-table-column>
        <el-table-column prop="clicks" label="点击量" min-width="80" sortable>
          <template #default="scope">
            {{ scope.row.clicks || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="roi" label="ROI" min-width="80" sortable>
          <template #default="scope">
            {{ formatROI(scope.row.roi) }}
          </template>
        </el-table-column>
        <el-table-column prop="profit_margin_rate" label="利润率" min-width="80" sortable>
          <template #default="scope">
            {{ formatPercentage(scope.row.profit_margin_rate) }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getWeeklyReport, getWeeklyDetail, exportWeeklyReport } from '@/api/report'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const detailLoading = ref(false)
const reportList = ref([])
const detailList = ref([])
const detailDialogVisible = ref(false)
const currentDetail = ref({})

// 查询表单
const queryForm = reactive({
  media_id: ''
})

// 日期范围选择
const dateRange = ref([])

// 日期选择器配置
const pickerOptions = {
  shortcuts: [
    {
      text: '最近一周',
      value: (() => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      })()
    },
    {
      text: '最近一个月',
      value: (() => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        return [start, end]
      })()
    },
    {
      text: '最近三个月',
      value: (() => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        return [start, end]
      })()
    }
  ]
}

// 媒体选项（示例）
const mediaOptions = ref([
  { label: '全部媒体', value: '' },
  // 这里可以从API获取真实的媒体列表
])

// 获取周报表数据
const getReportData = async () => {
  if (!dateRange.value || dateRange.value.length !== 2) {
    ElMessage.warning('请选择查询时间范围')
    return
  }

  loading.value = true
  try {
    const params = {
      start_date: dateRange.value[0],
      end_date: dateRange.value[1]
    }
    
    if (queryForm.media_id) {
      params.media_id = queryForm.media_id
    }

    const response = await getWeeklyReport(params)
    reportList.value = response.data.list || []
  } catch (error) {
    console.error('获取周报表数据失败:', error)
    ElMessage.error('获取周报表数据失败')
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  getReportData()
}

// 重置
const handleReset = () => {
  queryForm.media_id = ''
  dateRange.value = []
  reportList.value = []
}

// 导出
const handleExport = async () => {
  if (!dateRange.value || dateRange.value.length !== 2) {
    ElMessage.warning('请选择查询时间范围')
    return
  }

  exportLoading.value = true
  try {
    const params = {
      start_date: dateRange.value[0],
      end_date: dateRange.value[1]
    }
    
    if (queryForm.media_id) {
      params.media_id = queryForm.media_id
    }

    const response = await exportWeeklyReport(params)
    
    // 创建下载链接
    const blob = new Blob([response], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `周报表_${params.start_date}_${params.end_date}.xlsx`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 查看详情
const handleViewDetail = async (row) => {
  detailLoading.value = true
  try {
    const params = {
      year_week: row.year_week
    }
    
    if (queryForm.media_id) {
      params.media_id = queryForm.media_id
    }

    const response = await getWeeklyDetail(params)
    currentDetail.value = response.data || {}
    detailList.value = response.data.list || []
    detailDialogVisible.value = true
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    detailLoading.value = false
  }
}

// 关闭详情对话框
const handleCloseDetail = () => {
  detailDialogVisible.value = false
  currentDetail.value = {}
  detailList.value = []
}

// 格式化金额
const formatAmount = (amount) => {
  if (amount === null || amount === undefined) return '0.00'
  return parseFloat(amount).toFixed(2)
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  try {
    // 处理 ISO 8601 格式的日期字符串
    const dateObj = new Date(date)
    return dateObj.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch {
    return date
  }
}

// 格式化百分比
const formatPercentage = (percentage) => {
  if (percentage === null || percentage === undefined) return '-'
  return percentage.toFixed(2) + '%'
}

// 格式化ROI
const formatROI = (roi) => {
  if (roi === null || roi === undefined) return '-'
  return roi.toFixed(2)
}

// 获取渠道标签类型
const getChannelTagType = (channel) => {
  const typeMap = {
    'alipay_h5': 'primary',
    'alipay_mp': 'primary', 
    'wechat_mp': 'success',
    'wechat_plugin': 'success',
    'other': 'info'
  }
  return typeMap[channel] || 'info'
}

// 获取渠道名称
const getChannelName = (channel) => {
  const nameMap = {
    'alipay_h5': '支付宝H5',
    'alipay_mp': '支付宝小程序',
    'wechat_mp': '微信小程序', 
    'wechat_plugin': '微信插件',
    'other': '其他'
  }
  return nameMap[channel] || channel
}

// 初始化
onMounted(() => {
  // 设置默认查询时间为最近一周
  const end = new Date()
  const start = new Date()
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
  dateRange.value = [
    start.toISOString().substr(0, 10),
    end.toISOString().substr(0, 10)
  ]
  
  // 初始加载数据
  getReportData()
})
</script>

<style scoped>
.weekly-report-container {
  padding: 16px;
}

.query-card {
  margin-bottom: 16px;
}

.table-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-header {
  margin-bottom: 20px;
}

.detail-header h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.summary-info {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.text-red {
  color: #f56c6c;
}

.text-green {
  color: #67c23a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .weekly-report-container {
    padding: 8px;
  }
  
  :deep(.el-table) {
    font-size: 12px;
  }
  
  :deep(.el-table .cell) {
    padding: 4px 8px;
  }
}
</style> 