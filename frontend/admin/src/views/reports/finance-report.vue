<template>
  <div class="finance-report">
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="filter-form">
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :shortcuts="dateShortcuts"
            value-format="YYYY-MM-DD"
            style="width: 400px"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item label="媒体">
          <el-select
            v-model="queryParams.mediaId"
            placeholder="请选择媒体"
            clearable
            @change="handleQueryChange"
          >
            <el-option
              v-for="item in mediaOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQueryChange">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button type="success" @click="handleExport">导出报表</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 财务数据表格 -->
    <el-card shadow="hover" class="table-card">
      <template #header>
        <div class="card-header">
          <span>财务报表数据</span>
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
      >
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column prop="mediaName" label="媒体名称" width="150" />
        <el-table-column prop="revenue" label="收入" width="120">
          <template #default="scope">
            {{ formatCurrency(scope.row.revenue) }}
          </template>
        </el-table-column>
        <el-table-column prop="cost" label="成本" width="120">
          <template #default="scope">
            {{ formatCurrency(scope.row.cost) }}
          </template>
        </el-table-column>
        <el-table-column prop="profit" label="利润" width="120">
          <template #default="scope">
            {{ formatCurrency(scope.row.profit) }}
          </template>
        </el-table-column>
        <el-table-column prop="profitRate" label="利润率" width="120">
          <template #default="scope">
            {{ formatPercentage(scope.row.profitRate) }}
          </template>
        </el-table-column>
        <el-table-column prop="orderCount" label="订单数" width="100" />
        <el-table-column prop="avgOrderValue" label="客单价" width="120">
          <template #default="scope">
            {{ formatCurrency(scope.row.avgOrderValue) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="结算状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="150">
          <template #default="scope">
            <el-button
              type="primary"
              link
              size="small"
              @click="handleDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              v-if="scope.row.status === 'pending'"
              type="success"
              link
              size="small"
              @click="handleSettle(scope.row)"
            >
              结算
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 财务统计卡片 -->
    <el-row :gutter="20" class="data-cards">
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>总收入</span>
              <el-tooltip content="所有媒体的总收入" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-value">{{ formatCurrency(stats.totalRevenue) }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>总成本</span>
              <el-tooltip content="所有媒体的总成本" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-value">{{ formatCurrency(stats.totalCost) }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>总利润</span>
              <el-tooltip content="所有媒体的总利润" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-value">{{ formatCurrency(stats.totalProfit) }}</div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>平均利润率</span>
              <el-tooltip content="所有媒体的平均利润率" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-value">{{ formatPercentage(stats.avgProfitRate) }}</div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getMediaList } from '@/api/media'

const dateRange = ref([])
const queryParams = reactive({
  startDate: '',
  endDate: '',
  mediaId: '',
  page: 1,
  pageSize: 10
})

const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const mediaOptions = ref([])

const stats = ref({
  totalRevenue: 0,
  totalCost: 0,
  totalProfit: 0,
  avgProfitRate: 0
})

const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]

// 格式化货币
const formatCurrency = (value) => {
  return `¥ ${parseFloat(value).toFixed(2)}`
}

// 格式化百分比
const formatPercentage = (value) => {
  return `${(parseFloat(value) * 100).toFixed(2)}%`
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'settled': 'success',
    'cancelled': 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'pending': '待结算',
    'settled': '已结算',
    'cancelled': '已取消'
  }
  return statusMap[status] || '未知'
}

const handleDateChange = async () => {
  if (!dateRange.value || dateRange.value.length !== 2) return
  queryParams.startDate = dateRange.value[0]
  queryParams.endDate = dateRange.value[1]
  await fetchData()
}

const handleQueryChange = async () => {
  queryParams.page = 1
  await fetchData()
}

const resetQuery = () => {
  queryParams.mediaId = ''
  // 重置日期为最近一周
  dateRange.value = dateShortcuts[0].value()
  queryParams.startDate = dateRange.value[0]
  queryParams.endDate = dateRange.value[1]
  queryParams.page = 1
  fetchData()
}

const handleSizeChange = (size) => {
  queryParams.pageSize = size
  fetchData()
}

const handleCurrentChange = (page) => {
  queryParams.page = page
  fetchData()
}

const handleDetail = (row) => {
  ElMessage.info(`查看详情: ${row.date} - ${row.mediaName}`)
  // TODO: 实现详情查看功能
}

const handleSettle = (row) => {
  ElMessageBox.confirm(
    `确定要结算 ${row.date} - ${row.mediaName} 的财务数据吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // TODO: 实现结算功能
    ElMessage.success(`结算成功: ${row.date} - ${row.mediaName}`)
    fetchData()
  }).catch(() => {
    // 取消操作
  })
}

const handleExport = () => {
  ElMessage.success('财务报表导出成功')
  // TODO: 实现导出功能
}

const fetchData = async () => {
  loading.value = true
  try {
    // TODO: 调用后端API获取财务报表数据
    // 模拟数据
    setTimeout(() => {
      const mockData = []
      for (let i = 0; i < 20; i++) {
        const revenue = Math.random() * 10000
        const cost = revenue * (0.3 + Math.random() * 0.4)
        const profit = revenue - cost
        const profitRate = profit / revenue
        
        mockData.push({
          id: i + 1,
          date: `2023-03-${String(i + 1).padStart(2, '0')}`,
          mediaName: `媒体${i + 1}`,
          revenue,
          cost,
          profit,
          profitRate,
          orderCount: Math.floor(Math.random() * 100),
          avgOrderValue: revenue / Math.floor(Math.random() * 100),
          status: ['pending', 'settled', 'cancelled'][Math.floor(Math.random() * 3)]
        })
      }
      
      tableData.value = mockData.slice(
        (queryParams.page - 1) * queryParams.pageSize,
        queryParams.page * queryParams.pageSize
      )
      total.value = mockData.length
      
      // 计算统计数据
      stats.value = {
        totalRevenue: mockData.reduce((sum, item) => sum + item.revenue, 0),
        totalCost: mockData.reduce((sum, item) => sum + item.cost, 0),
        totalProfit: mockData.reduce((sum, item) => sum + item.profit, 0),
        avgProfitRate: mockData.reduce((sum, item) => sum + item.profitRate, 0) / mockData.length
      }
      
      loading.value = false
    }, 500)
  } catch (error) {
    ElMessage.error('获取财务报表数据失败')
    loading.value = false
  }
}

const fetchMediaOptions = async () => {
  try {
    const response = await getMediaList()
    mediaOptions.value = response.data.list || []
  } catch (error) {
    ElMessage.error('获取媒体列表失败')
  }
}

onMounted(() => {
  // 设置默认时间范围为最近一周
  dateRange.value = dateShortcuts[0].value()
  queryParams.startDate = dateRange.value[0]
  queryParams.endDate = dateRange.value[1]
  
  // 获取筛选条件选项
  fetchMediaOptions()
  
  // 获取财务报表数据
  fetchData()
})
</script>

<style scoped>
.finance-report {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.data-cards {
  margin-top: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}
</style> 