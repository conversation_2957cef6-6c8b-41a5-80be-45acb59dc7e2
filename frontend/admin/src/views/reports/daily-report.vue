<template>
  <div class="daily-report-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>日报表</span>
          <el-tag type="primary" size="small">数据报表</el-tag>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :inline="true" :model="queryParams" class="demo-form-inline mb-3">
        <el-form-item label="日期范围" required>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
            style="width: 260px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery" :loading="loading">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button 
            type="success" 
            @click="handleExport" 
            :loading="exportLoading"
            :disabled="reportList.length === 0"
          >
            导出Excel
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 日报表数据表格 -->
      <el-table 
        :data="reportList" 
        style="width: 100%" 
        v-loading="loading"
        :stripe="true"
        :border="true"
        :default-sort="{prop: 'date', order: 'descending'}"
      >
        <el-table-column prop="date" label="日期" min-width="120" fixed="left" sortable>
          <template #default="scope">
            {{ formatDate(scope.row.date) }}
          </template>
        </el-table-column>
        
        <!-- 支付宝相关 -->
        <el-table-column label="支付宝数据" align="center">
          <el-table-column prop="alipay_profit" label="利润" min-width="100" sortable>
            <template #default="scope">
              <span :class="{'text-red': scope.row.alipay_profit < 0, 'text-green': scope.row.alipay_profit > 0}">
                ¥{{ formatAmount(scope.row.alipay_profit) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="alipay_orders" label="订单" min-width="80" sortable />
          <el-table-column prop="alipay_revenue" label="佣金" min-width="100" sortable>
            <template #default="scope">
              ¥{{ formatAmount(scope.row.alipay_revenue) }}
            </template>
          </el-table-column>
          <el-table-column prop="alipay_cost" label="成本" min-width="100" sortable>
            <template #default="scope">
              ¥{{ formatAmount(scope.row.alipay_cost) }}
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 微信相关 -->
        <el-table-column label="微信数据" align="center">
          <el-table-column prop="wechat_profit" label="利润" min-width="100" sortable>
            <template #default="scope">
              <span :class="{'text-red': scope.row.wechat_profit < 0, 'text-green': scope.row.wechat_profit > 0}">
                ¥{{ formatAmount(scope.row.wechat_profit) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="wechat_orders" label="订单" min-width="80" sortable />
          <el-table-column prop="wechat_revenue" label="佣金" min-width="100" sortable>
            <template #default="scope">
              ¥{{ formatAmount(scope.row.wechat_revenue) }}
            </template>
          </el-table-column>
          <el-table-column prop="wechat_cost" label="成本" min-width="100" sortable>
            <template #default="scope">
              ¥{{ formatAmount(scope.row.wechat_cost) }}
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 其他相关 -->
        <el-table-column label="其他数据" align="center">
          <el-table-column prop="other_profit" label="利润" min-width="100" sortable>
            <template #default="scope">
              <span :class="{'text-red': scope.row.other_profit < 0, 'text-green': scope.row.other_profit > 0}">
                ¥{{ formatAmount(scope.row.other_profit) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="other_revenue" label="佣金" min-width="100" sortable>
            <template #default="scope">
              ¥{{ formatAmount(scope.row.other_revenue) }}
            </template>
          </el-table-column>
          <el-table-column prop="other_cost" label="成本" min-width="100" sortable>
            <template #default="scope">
              ¥{{ formatAmount(scope.row.other_cost) }}
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 汇总数据 -->
        <el-table-column label="汇总数据" align="center">
          <el-table-column prop="total_profit" label="总利润" min-width="120" sortable>
            <template #default="scope">
              <span :class="{'text-red': scope.row.total_profit < 0, 'text-green': scope.row.total_profit > 0}">
                <strong>¥{{ formatAmount(scope.row.total_profit) }}</strong>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="total_orders" label="总订单" min-width="100" sortable />
          <el-table-column prop="roi" label="ROI" min-width="100" sortable>
            <template #default="scope">
              {{ formatROI(scope.row.roi) }}
            </template>
          </el-table-column>
          <el-table-column prop="profit_margin_rate" label="利润率%" min-width="100" sortable>
            <template #default="scope">
              <span :class="{'text-red': scope.row.profit_margin_rate < 0, 'text-green': scope.row.profit_margin_rate > 0}">
                {{ formatPercentage(scope.row.profit_margin_rate) }}
              </span>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" min-width="120" fixed="right">
          <template #default="scope">
            <el-button 
              link 
              type="primary" 
              @click="handleViewDetail(scope.row.date)"
              :loading="detailLoading"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 暂无数据 -->
      <el-empty v-if="!loading && reportList.length === 0" description="暂无数据" />
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      :title="`${currentDate} 日详细数据`"
      v-model="detailDialogVisible"
      width="1200px"
      @close="handleDetailClose"
    >
      <!-- 汇总信息 -->
      <el-row :gutter="20" class="summary-cards" v-if="detailSummary">
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-item">
              <span class="label">总利润</span>
              <span :class="['value', {'text-red': detailSummary.total_profit < 0, 'text-green': detailSummary.total_profit > 0}]">
                ¥{{ formatAmount(detailSummary.total_profit) }}
              </span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-item">
              <span class="label">总订单数</span>
              <span class="value">{{ detailSummary.total_orders }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-item">
              <span class="label">盈利媒体</span>
              <span class="value text-green">{{ detailSummary.profit_media_count }}</span>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="summary-card">
            <div class="summary-item">
              <span class="label">亏损媒体</span>
              <span class="value text-red">{{ detailSummary.loss_media_count }}</span>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 详细数据表格 -->
      <el-table 
        :data="detailList" 
        style="width: 100%; margin-top: 20px;" 
        v-loading="detailLoading"
        :stripe="true"
        :border="true"
        max-height="500"
        :default-sort="{prop: 'profit', order: 'descending'}"
      >
        <el-table-column prop="media_name" label="媒体名称" min-width="120" fixed="left" sortable />
        <el-table-column prop="user_name" label="归属媒介" min-width="100" sortable />
        <el-table-column prop="promotion_channel" label="推广渠道" min-width="120" sortable>
          <template #default="scope">
            <el-tag :type="getChannelTagType(scope.row.promotion_channel)" size="small">
              {{ getChannelName(scope.row.promotion_channel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="is_profit" label="盈亏状态" min-width="100" sortable>
          <template #default="scope">
            <el-tag :type="scope.row.is_profit ? 'success' : 'danger'" size="small">
              {{ scope.row.is_profit ? '盈利' : '亏损' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="profit" label="利润" min-width="120" sortable>
          <template #default="scope">
            <span :class="{'text-red': scope.row.profit < 0, 'text-green': scope.row.profit > 0}">
              <strong>¥{{ formatAmount(scope.row.profit) }}</strong>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="orders" label="订单数" min-width="100" sortable />
        <el-table-column prop="revenue" label="佣金收入" min-width="120" sortable>
          <template #default="scope">
            ¥{{ formatAmount(scope.row.revenue) }}
          </template>
        </el-table-column>
        <el-table-column prop="cost" label="成本" min-width="120" sortable>
          <template #default="scope">
            ¥{{ formatAmount(scope.row.cost) }}
          </template>
        </el-table-column>
        <el-table-column prop="clicks" label="点击量" min-width="100" sortable />
        <el-table-column prop="roi" label="ROI" min-width="100" sortable>
          <template #default="scope">
            {{ formatROI(scope.row.roi) }}
          </template>
        </el-table-column>
        <el-table-column prop="profit_margin_rate" label="利润率%" min-width="100" sortable>
          <template #default="scope">
            <span :class="{'text-red': scope.row.profit_margin_rate < 0, 'text-green': scope.row.profit_margin_rate > 0}">
              {{ formatPercentage(scope.row.profit_margin_rate) }}
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 详情对话框底部 -->
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getDailyReport, getDailyDetail, exportDailyReport } from '@/api/report'

// 查询参数
const queryParams = reactive({
  start_date: '',
  end_date: ''
})

// 日期范围
const dateRange = ref([])

// 数据列表
const reportList = ref([])
const loading = ref(false)
const exportLoading = ref(false)

// 详情相关
const detailDialogVisible = ref(false)
const detailList = ref([])
const detailSummary = ref(null)
const detailLoading = ref(false)
const currentDate = ref('')

// 初始化日期范围（默认最近7天）
const initDateRange = () => {
  const end = new Date()
  const start = new Date()
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
  
  const formatDate = (date) => {
    return date.getFullYear() + '-' + 
           String(date.getMonth() + 1).padStart(2, '0') + '-' + 
           String(date.getDate()).padStart(2, '0')
  }
  
  dateRange.value = [formatDate(start), formatDate(end)]
  queryParams.start_date = dateRange.value[0]
  queryParams.end_date = dateRange.value[1]
}

// 处理日期变化
const handleDateChange = (dates) => {
  if (dates && dates.length === 2) {
    queryParams.start_date = dates[0]
    queryParams.end_date = dates[1]
  } else {
    queryParams.start_date = ''
    queryParams.end_date = ''
  }
}

// 查询数据
const handleQuery = async () => {
  if (!queryParams.start_date || !queryParams.end_date) {
    ElMessage.warning('请选择日期范围')
    return
  }

  loading.value = true
  try {
    const response = await getDailyReport(queryParams)
    reportList.value = response.data.daily_report || []
    
    if (reportList.value.length === 0) {
      ElMessage.info('暂无数据')
    }
  } catch (error) {
    console.error('获取日报表数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 重置查询
const resetQuery = () => {
  initDateRange()
  handleQuery()
}

// 导出Excel
const handleExport = async () => {
  if (!queryParams.start_date || !queryParams.end_date) {
    ElMessage.warning('请先查询数据')
    return
  }

  exportLoading.value = true
  try {
    const response = await exportDailyReport(queryParams)
    
    // 检查响应是否为blob
    if (response.data instanceof Blob) {
      // 检查blob是否为错误响应（JSON）
      if (response.data.type === 'application/json') {
        const text = await response.data.text()
        const errorData = JSON.parse(text)
        ElMessage.error('导出失败: ' + (errorData.message || '服务器错误'))
        return
      }
      
      // 创建下载链接
      const url = window.URL.createObjectURL(response.data)
      const link = document.createElement('a')
      link.href = url
      
      // 尝试从响应头获取文件名
      let filename = `日报表_${queryParams.start_date}_${queryParams.end_date}.xlsx`
      if (response.headers && response.headers['content-disposition']) {
        const contentDisposition = response.headers['content-disposition']
        const match = contentDisposition.match(/filename=([^;]+)/)
        if (match) {
          // 使用服务器返回的文件名，但保持中文显示名
          filename = `日报表_${queryParams.start_date}_${queryParams.end_date}.xlsx`
        }
      }
      
      link.setAttribute('download', filename)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
      
      ElMessage.success('导出成功')
    } else {
      ElMessage.error('导出失败: 响应格式错误')
    }
  } catch (error) {
    console.error('导出失败:', error)
    if (error.response && error.response.data instanceof Blob) {
      // 尝试解析blob中的错误信息
      try {
        const text = await error.response.data.text()
        const errorData = JSON.parse(text)
        ElMessage.error('导出失败: ' + (errorData.message || '服务器错误'))
      } catch (parseError) {
        ElMessage.error('导出失败: 服务器错误')
      }
    } else {
      ElMessage.error('导出失败: ' + (error.response?.data?.message || error.message || '未知错误'))
    }
  } finally {
    exportLoading.value = false
  }
}

// 查看详情
const handleViewDetail = async (date) => {
  // 格式化日期为YYYY-MM-DD格式
  const formattedDate = formatDate(date)
  currentDate.value = formattedDate
  detailLoading.value = true
  detailDialogVisible.value = true
  
  try {
    const response = await getDailyDetail({ date: formattedDate })
    detailList.value = response.data.list || []
    detailSummary.value = response.data.summary || null
  } catch (error) {
    console.error('获取详细数据失败:', error)
    ElMessage.error('获取详细数据失败: ' + (error.response?.data?.message || error.message || '未知错误'))
  } finally {
    detailLoading.value = false
  }
}

// 关闭详情对话框
const handleDetailClose = () => {
  detailList.value = []
  detailSummary.value = null
  currentDate.value = ''
}

// 格式化金额
const formatAmount = (amount) => {
  if (amount === null || amount === undefined) return '0.00'
  return Number(amount).toFixed(2)
}

// 获取渠道名称
const getChannelName = (channel) => {
  const channelMap = {
    'alipay_h5': '支付宝H5',
    'alipay_mp': '支付宝小程序',
    'wechat_mp': '微信小程序',
    'wechat_plugin': '微信插件',
    'other': '其他'
  }
  return channelMap[channel] || channel
}

// 获取渠道标签类型
const getChannelTagType = (channel) => {
  const typeMap = {
    'alipay_h5': 'primary',
    'alipay_mp': 'primary',
    'wechat_mp': 'success',
    'wechat_plugin': 'success',
    'other': 'info'
  }
  return typeMap[channel] || 'info'
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''
  
  // 如果是ISO格式的日期字符串，先转换为Date对象
  if (date.includes('T')) {
    const dateObj = new Date(date)
    return dateObj.getFullYear() + '-' + 
           String(dateObj.getMonth() + 1).padStart(2, '0') + '-' + 
           String(dateObj.getDate()).padStart(2, '0')
  }
  
  // 如果已经是YYYY-MM-DD格式，直接返回前10位
  return date.substring(0, 10)
}

// 格式化ROI
const formatROI = (roi) => {
  if (roi === null || roi === undefined) return '-'
  return roi.toFixed(2)
}

// 格式化百分比
const formatPercentage = (percentage) => {
  if (percentage === null || percentage === undefined) return '-'
  return percentage.toFixed(2) + '%'
}

// 页面初始化
onMounted(() => {
  initDateRange()
  handleQuery()
})
</script>

<style scoped>
.daily-report-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-3 {
  margin-bottom: 20px;
}

.text-red {
  color: #f56c6c;
}

.text-green {
  color: #67c23a;
}

.summary-cards {
  margin-bottom: 20px;
}

.summary-card {
  text-align: center;
}

.summary-item {
  padding: 10px;
}

.summary-item .label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.summary-item .value {
  display: block;
  font-size: 20px;
  font-weight: bold;
  color: #333;
}
</style> 