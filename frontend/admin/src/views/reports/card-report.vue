<template>
  <div class="card-report">
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="filter-form">
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :shortcuts="dateShortcuts"
            value-format="YYYY-MM-DD"
            style="width: 400px"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item label="媒体">
          <el-select
            v-model="queryParams.mediaId"
            placeholder="请选择媒体"
            clearable
            @change="handleQueryChange"
          >
            <el-option
              v-for="item in mediaOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="推广位">
          <el-select
            v-model="queryParams.zoneId"
            placeholder="请选择推广位"
            clearable
            @change="handleQueryChange"
          >
            <el-option
              v-for="item in zoneOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQueryChange">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-row :gutter="20" class="data-cards">
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>页面访问uv</span>
              <el-tooltip content="页面的独立访客数" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-value">{{ stats.uv }}</div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>页面访问pv</span>
              <el-tooltip content="页面的访问次数" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-value">{{ stats.pv }}</div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>总开卡数</span>
              <el-tooltip content="总的开卡数量" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-value">{{ stats.cardCnt }}</div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>高校开卡数</span>
              <el-tooltip content="高校用户开卡数量" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-value">{{ stats.cardCntCampus }}</div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>普通开卡数</span>
              <el-tooltip content="普通用户开卡数量" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-value">{{ stats.cardCntNormal }}</div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>新客首单数</span>
              <el-tooltip content="新客户的首次订单数" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-value">{{ stats.orderCntNew }}</div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>高校核销开卡数</span>
              <el-tooltip content="高校用户核销的开卡数量" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-value">{{ stats.cardUsedCntCampus }}</div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>普通核销开卡数</span>
              <el-tooltip content="普通用户核销的开卡数量" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-value">{{ stats.cardUsedCntNormal }}</div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>核销订单数</span>
              <el-tooltip content="总的核销订单数量" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-value">{{ stats.orderCntUse }}</div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>卡券金额</span>
              <el-tooltip content="卡券的总金额" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-value">{{ stats.cardAmount }}</div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>订单收入</span>
              <el-tooltip content="预计可获得的收入" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-value">{{ stats.orderIncome }}</div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>订单结算</span>
              <el-tooltip content="订单结算金额" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-value">{{ stats.orderSettle }}</div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getCardStats } from '@/api/analysis'
import { getMediaList } from '@/api/media'
import { getSlotList } from '@/api/slot'

const dateRange = ref([])
const queryParams = reactive({
  startDate: '',
  endDate: '',
  mediaId: '',
  zoneId: ''
})

const stats = ref({
  pv: 0,
  uv: 0,
  cardCnt: 0,
  cardCntCampus: 0,
  cardCntNormal: 0,
  cardUsedCnt: 0,
  cardUsedCntCampus: 0,
  cardUsedCntNormal: 0,
  orderCntNew: 0,
  orderCntUse: 0,
  cardAmount: 0,
  cardAmountCampus: 0,
  cardAmountNormal: 0,
  orderIncome: 0,
  orderSettle: 0
})

const mediaOptions = ref([])
const zoneOptions = ref([])

const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]

const handleDateChange = async () => {
  if (!dateRange.value || dateRange.value.length !== 2) return
  queryParams.startDate = dateRange.value[0]
  queryParams.endDate = dateRange.value[1]
  await fetchStats()
}

const handleQueryChange = async () => {
  await fetchStats()
}

const resetQuery = () => {
  queryParams.mediaId = ''
  queryParams.zoneId = ''
  // 重置日期为最近一周
  dateRange.value = dateShortcuts[0].value()
  queryParams.startDate = dateRange.value[0]
  queryParams.endDate = dateRange.value[1]
  fetchStats()
}

const fetchStats = async () => {
  try {
    const response = await getCardStats(queryParams)
    stats.value = response.data || {
      pv: 0,
      uv: 0,
      cardCnt: 0,
      cardCntCampus: 0,
      cardCntNormal: 0,
      cardUsedCnt: 0,
      cardUsedCntCampus: 0,
      cardUsedCntNormal: 0,
      orderCntNew: 0,
      orderCntUse: 0,
      cardAmount: 0,
      cardAmountCampus: 0,
      cardAmountNormal: 0,
      orderIncome: 0,
      orderSettle: 0
    }
  } catch (error) {
    ElMessage.error('获取统计数据失败')
  }
}

const fetchMediaOptions = async () => {
  try {
    const response = await getMediaList()
    mediaOptions.value = response.data.list || []
  } catch (error) {
    ElMessage.error('获取媒体列表失败')
  }
}

const fetchZoneOptions = async () => {
  try {
    const response = await getSlotList()
    zoneOptions.value = response.data.list || []
  } catch (error) {
    ElMessage.error('获取推广位列表失败')
  }
}

onMounted(() => {
  // 设置默认时间范围为最近7天
  dateRange.value = dateShortcuts[0].value()
  queryParams.startDate = dateRange.value[0]
  queryParams.endDate = dateRange.value[1]
  
  // 获取筛选条件选项
  fetchMediaOptions()
  fetchZoneOptions()
  
  // 获取统计数据
  fetchStats()
})
</script>

<style scoped>
.card-report {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.data-cards {
  margin-bottom: 20px;
}

.data-cards .el-col {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}
</style> 