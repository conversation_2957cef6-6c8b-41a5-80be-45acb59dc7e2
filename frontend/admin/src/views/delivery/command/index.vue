<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>口令管理</span>
          <el-button
            type="primary"
            @click="handleAdd"
          >
            新增口令组
          </el-button>
        </div>
      </template>

      <div class="filter-container">
        <div class="filter-item">
          <span class="label">分类</span>
          <el-select
            v-model="queryParams.categoryId"
            placeholder="请选择分类"
            clearable
            class="filter-select"
            @clear="handleFilterChange"
            @change="handleFilterChange"
          >
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <span class="label">口令组名称</span>
          <el-input
            v-model="queryParams.groupName"
            placeholder="请输入口令组名称"
            clearable
            class="filter-input"
            @clear="handleFilterChange"
          />
        </div>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </div>

     

      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%; margin-top: 20px;"
        row-key="id"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="80"
        />
        <el-table-column
          label="分类"
          width="120"
        >
          <template #default="{ row }">
            {{ categoryOptions.find(item => item.value === row.categoryId)?.label || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="groupName"
          label="口令组名称"
          width="200"
        />
        <el-table-column
          label="口令信息"
          min-width="370"
        >
          <template #default="{ row }">
            <div v-if="row.passwords && row.passwords.length > 0" class="password-list">
              <div v-for="password in row.passwords" :key="password.id" class="password-tag">
                <span>{{ password.name }}：{{ password.pid }}</span>
              </div>
            </div>
            <div v-else class="no-passwords">暂无口令</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="updatedAt"
          label="更新时间"
          width="180"
        />
        <el-table-column
          fixed="right"
          label="操作"
          width="150"
        >
          <template #default="{ row }">
            <el-button
              link
              type="primary"
              :icon="Edit"
              @click="handleEditGroup(row)"
            >
              编辑
            </el-button>
            <el-button
              link
              type="danger"
              :icon="Delete"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.pageSize"
          :total="total"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑口令组弹窗 -->
    <el-dialog
      v-model="groupDialogVisible"
      :title="dialogType === 'add' ? '新增口令组' : '编辑口令组'"
      width="800px"
    >
      <el-form
        ref="groupFormRef"
        :model="groupForm"
        :rules="groupRules"
        label-width="100px"
        v-loading="formLoading"
        element-loading-text="加载数据中..."
      >
        <el-form-item label="选择分类" prop="categoryId">
          <el-select
            v-model="groupForm.categoryId"
            placeholder="请选择分类"
            style="width: 100%"
          >
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="选择口令" required>
          <el-select
            v-model="selectedPasswords"
            multiple
            filterable
            :loading="oriPasswordLoading"
            style="width: 100%"
            placeholder="请选择口令"
            clearable
            value-key="uniqueKey"
          >
            <el-option
              v-for="item in oriPasswordList"
              :key="item.uniqueKey"
              :label="`${item.name}：${item.pid}`"
              :value="item"
            >
              <span>{{ item.name }}：{{ item.pid }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="口令组名称" prop="groupName">
          <el-input v-model="groupForm.groupName" placeholder="请输入口令组名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="groupDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitGroupForm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加/编辑口令弹窗 -->
    <el-dialog
      v-model="passwordDialogVisible"
      :title="passwordDialogType === 'add' ? '新增口令' : '编辑口令'"
      width="500px"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="口令名称" prop="name">
          <el-input v-model="passwordForm.name" placeholder="请输入口令名称" />
        </el-form-item>
        <el-form-item label="口令标识" prop="pid">
          <el-input v-model="passwordForm.pid" placeholder="请输入口令标识" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPasswordForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, Delete } from '@element-plus/icons-vue'
import { getCommandList, createCommand, updateCommand, deleteCommand } from '@/api/command'
import request from '@/utils/request'

// 表格数据
const tableData = ref([])
const loading = ref(false)
const total = ref(0)

// 表单加载状态
const formLoading = ref(false)

// 查询参数
const queryParams = reactive({
  page: 1,
  pageSize: 10,
  groupName: '',
  categoryId: undefined
})

// 分类选项
const categoryOptions = [
  { value: 55, label: '小红书' },
  { value: 173, label: '抖音' },
  { value: 284, label: '闪购' },
]

// 口令组表单数据
const groupDialogVisible = ref(false)
const dialogType = ref('add') // add or edit
const groupFormRef = ref(null)
const groupForm = reactive({
  id: undefined,
  groupName: '',
  categoryId: undefined,
  passwords: []
})

// 原始口令列表
const oriPasswordList = ref([])
const oriPasswordLoading = ref(false)

// 选中的口令列表
const selectedPasswords = ref([])

// 获取原始口令列表
const fetchOriPasswordList = async (categoryId) => {
  oriPasswordLoading.value = true
  try {
    const response = await request({
      url: '/api/v1/password/oriList',
      method: 'get',
      params: {
        categoryId,
        pageSize: 2000,
      }
    })
    if (response.code === 0) {
      // 对列表进行去重处理，使用 pid 和 name 的组合作为唯一标识
      const uniquePasswords = []
      const seen = new Set()
      
      ;(response.data.list || []).forEach(item => {
        const uniqueKey = `${item.pid}_${item.name}` // 使用 pid 和 name 组合作为唯一标识
        if (!seen.has(uniqueKey)) {
          seen.add(uniqueKey)
          uniquePasswords.push({
            ...item,
            uniqueKey // 使用组合的唯一标识
          })
        }
      })
      
      oriPasswordList.value = uniquePasswords
    } else {
      ElMessage.error(response.message || '获取口令列表失败')
    }
  } catch (error) {
    ElMessage.error('获取原始口令列表失败')
  } finally {
    oriPasswordLoading.value = false
  }
}

// 监听分类变化
watch(() => groupForm.categoryId, (newVal) => {
  if (newVal) {
    fetchOriPasswordList(newVal)
    // 清空已选择的口令
    selectedPasswords.value = []
    groupForm.groupName = ''
  }
})

// 监听选中口令变化
watch(() => selectedPasswords.value, (newVal) => {
  if (newVal && newVal.length > 0) {
    // 设置口令组名称为第一个选中口令的名称
    groupForm.groupName = newVal[0].name
  }
}, { deep: true })

// 口令表单数据
const passwordDialogVisible = ref(false)
const passwordDialogType = ref('add') // add or edit
const passwordFormRef = ref(null)
const passwordForm = reactive({
  id: undefined,
  groupId: undefined,
  name: '',
  pid: ''
})

// 当前选中的口令组
const currentGroup = ref(null)

// 表单验证规则
const groupRules = {
  categoryId: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  groupName: [
    { required: true, message: '请输入口令组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

const passwordRules = {
  name: [
    { required: true, message: '请输入口令名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  pid: [
    { required: true, message: '请输入口令标识', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 获取口令列表
const fetchData = async () => {
  loading.value = true
  try {
    const response = await getCommandList(queryParams)
    if (response && response.code === 0 && response.data) {
      tableData.value = response.data.list || []
      total.value = response.data.total || 0
    } else {
      ElMessage.error(response?.message || '数据格式不正确')
    }
  } catch (error) {
    ElMessage.error(error.response?.data?.message || error.message || '获取口令列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  queryParams.page = 1
  fetchData()
}

// 重置
const handleReset = () => {
  queryParams.groupName = ''
  queryParams.categoryId = undefined
  handleSearch()
}

// 过滤条件变化
const handleFilterChange = () => {
  handleSearch()
}

// 分页大小变化
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  fetchData()
}

// 页码变化
const handleCurrentChange = (page) => {
  queryParams.page = page
  fetchData()
}

// 新增口令组
const handleAdd = () => {
  dialogType.value = 'add'
  resetGroupForm()
  groupDialogVisible.value = true
}

// 编辑口令组
const handleEditGroup = async (row) => {
  dialogType.value = 'edit'
  resetGroupForm()
  groupDialogVisible.value = true
  formLoading.value = true
  
  try {
    // 先设置分类ID以触发口令列表加载
    groupForm.categoryId = row.categoryId
    
    // 等待口令列表加载完成
    await fetchOriPasswordList(row.categoryId)
    
    // 设置表单数据
    Object.assign(groupForm, {
      id: row.id,
      groupName: row.groupName
    })
    
    // 确保 row.passwords 是数组并且有值
    if (Array.isArray(row.passwords) && row.passwords.length > 0) {
      // 将每个口令映射为带有 uniqueKey 的对象，使用 pid 和 name 组合作为唯一标识
      const mappedPasswords = row.passwords.map(password => ({
        id: password.id,
        name: password.name,
        pid: password.pid,
        uniqueKey: `${password.pid}_${password.name}`
      }))
      
      // 确保所有口令都在 oriPasswordList 中
      const allPasswordsExist = mappedPasswords.every(password => 
        oriPasswordList.value.some(oriPassword => 
          oriPassword.uniqueKey === password.uniqueKey
        )
      )
      
      if (allPasswordsExist) {
        selectedPasswords.value = mappedPasswords
      } else {
        // 如果有口令不在原始列表中，重新获取口令列表
        await fetchOriPasswordList(row.categoryId)
        selectedPasswords.value = mappedPasswords
      }
    } else {
      selectedPasswords.value = []
    }
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    formLoading.value = false
  }
}

// 重置口令组表单
const resetGroupForm = () => {
  if (groupFormRef.value) {
    groupFormRef.value.resetFields()
  }
  groupForm.id = undefined
  groupForm.groupName = ''
  groupForm.categoryId = undefined
  selectedPasswords.value = []
  oriPasswordList.value = []
}

// 提交口令组表单
const submitGroupForm = async () => {
  if (!groupFormRef.value) return
  
  await groupFormRef.value.validate(async (valid) => {
    if (valid) {
      if (selectedPasswords.value.length === 0) {
        ElMessage.warning('请至少选择一个口令')
        return
      }

      try {
        const data = {
          ...groupForm,
          passwords: selectedPasswords.value.map(p => ({
            id: p.id,
            name: p.name,
            pid: p.pid
          }))
        }
        
        let res
        if (dialogType.value === 'add') {
          res = await createCommand(data)
        } else {
          res = await updateCommand(data.id, data)
        }

        if (res && res.code === 0) {
          ElMessage.success(dialogType.value === 'add' ? '新增口令组成功' : '更新口令组成功')
          groupDialogVisible.value = false
          fetchData()
        } else {
          ElMessage.error(res?.message || '操作失败')
        }
      } catch (error) {
        ElMessage.error(error.response?.data?.message || error.message || '提交口令组失败')
      }
    }
  })
}

// 新增口令
const handleAddPassword = (group) => {
  passwordDialogType.value = 'add'
  resetPasswordForm()
  passwordForm.groupId = group.id
  currentGroup.value = group
  passwordDialogVisible.value = true
}

// 编辑口令
const handleEditPassword = (passwordRow, group) => {
  passwordDialogType.value = 'edit'
  resetPasswordForm()
  passwordForm.id = passwordRow.id
  passwordForm.groupId = group.id
  passwordForm.name = passwordRow.name
  passwordForm.pid = passwordRow.pid
  currentGroup.value = group
  passwordDialogVisible.value = true
}

// 重置口令表单
const resetPasswordForm = () => {
  if (passwordFormRef.value) {
    passwordFormRef.value.resetFields()
  }
  passwordForm.id = undefined
  passwordForm.groupId = undefined
  passwordForm.name = ''
  passwordForm.pid = ''
}

// 提交口令表单
const submitPasswordForm = async () => {
  if (!passwordFormRef.value) return
  
  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const data = {
          ...passwordForm,
          groupId: currentGroup.value.id
        }
        
        if (passwordDialogType.value === 'add') {
          await createCommand(data)
          ElMessage.success('新增口令成功')
        } else {
          await updateCommand(passwordForm.id, data)
          ElMessage.success('更新口令成功')
        }
        passwordDialogVisible.value = false
        fetchData()
      } catch (error) {
        ElMessage.error('提交口令失败')
      }
    }
  })
}

// 删除口令组
const handleDelete = (row) => {
  ElMessageBox.confirm(
    '此操作将永久删除该口令组，是否继续？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        const response = await request({
          url: `/api/v1/password/${row.id}`,
          method: 'delete',
        })
        if (response.code === 0) {
          ElMessage.success('删除成功')
          fetchData()
        } else {
          ElMessage.error(response.message || '删除失败')
        }
      } catch (error) {
        ElMessage.error(error.response?.data?.message || error.message || '删除失败')
      }
    })
    .catch(() => {
      ElMessage.info('已取消删除')
    })
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.filter-item {
  display: flex;
  align-items: center;
}

.label {
  margin-right: 8px;
  white-space: nowrap;
  color: var(--el-text-color-regular);
  font-size: 14px;
  line-height: 32px;
}

.filter-input {
  width: 200px;
}

.filter-select {
  width: 150px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.ellipsis-text {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.password-expand {
  padding: 20px;
}

.password-item {
  margin: 10px 0;
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.password-item:last-child {
  border-bottom: none;
}

.password-actions {
  margin-top: 15px;
  padding: 0 10px;
}

.no-data {
  padding: 20px 0;
}

.debug-info {
  margin: 10px 0;
  padding: 10px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: auto;
  max-height: 300px;
}

.password-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.password-tag {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.no-passwords {
  color: #909399;
  font-size: 14px;
}

.password-select-container,
.password-select-left,
.password-select-right,
.selected-passwords,
.selected-password-item {
  display: none;
}
</style> 