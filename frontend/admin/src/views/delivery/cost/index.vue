<template>
  <div class="cost-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>费用管理</span>
          <div style="display: flex; align-items: center; gap: 10px;">
            <el-button type="primary" @click="handleImport">导入费用</el-button>
            <el-button type="primary" @click="handleAdd">新增费用</el-button>
            <el-button type="success" plain class="task-btn" @click="handleViewTasks" ref="taskBtn">查看任务</el-button>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :inline="true" :model="queryParams" class="demo-form-inline mb-3">
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYYMMDD"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item label="口令组">
          <el-select
            v-model="queryParams.groupId"
            placeholder="请选择口令组"
            clearable
            filterable
            :loading="commandLoading"
          >
            <el-option
              v-for="item in commandOptions"
              :key="item.id"
              :label="item.groupName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="分类">
          <el-select
            v-model="queryParams.categoryId"
            placeholder="请选择分类"
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table :data="costList" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" min-width="80" />
        <el-table-column prop="report_date" label="日期" min-width="120" />
        <el-table-column prop="group_name" label="口令组" min-width="150" />
        <el-table-column prop="category_name" label="分类" min-width="120">
          <template #default="scope">
            {{ scope.row.category_name || getCategoryNameById(scope.row.category_id) }}
          </template>
        </el-table-column>
        <el-table-column prop="cost" label="费用" min-width="120">
          <template #default="scope">
            ¥{{ formatAmount(scope.row.cost) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="100" fixed="right">
          <template #default="scope">
            <el-button link type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
        v-loading="formLoading"
        element-loading-text="提交中..."
      >
        <el-form-item label="口令组" prop="groupId">
          <el-select
            v-model="form.groupId"
            placeholder="请选择口令组"
            filterable
            :loading="commandLoading"
            style="width: 100%"
            @filter-change="handleCommandFilter"
          >
            <el-option
              v-for="item in commandOptions"
              :key="item.id"
              :label="item.groupName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="日期" prop="date">
          <el-date-picker
            v-model="form.date"
            type="date"
            placeholder="选择日期"
            value-format="YYYYMMDD"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="费用" prop="amount">
          <el-input-number
            v-model="form.amount"
            :precision="2"
            :step="0.1"
            :min="0"
            :controls="false"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false" :disabled="formLoading">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="formLoading">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入费用对话框 -->
    <el-dialog
      title="导入费用"
      v-model="importDialogVisible"
      width="500px"
      @close="handleImportDialogClose"
    >
      <el-form
        ref="importFormRef"
        :model="importForm"
        :rules="importRules"
        label-width="80px"
        v-loading="importLoading"
        element-loading-text="提交中..."
      >
        <el-form-item label="分类" prop="categoryId">
          <el-select
            v-model="importForm.categoryId"
            placeholder="请选择分类"
            class="form-select"
          >
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="导入文件">
          <el-upload
            class="upload-demo"
            action=""
            :http-request="handleFileUpload"
            :show-file-list="true"
            :file-list="fileList"
            accept=".xlsx,.xls"
          >
            <el-button type="primary">点击上传</el-button>
            <template #tip>
              <div class="el-upload__tip">
                只能上传 xlsx/xls 文件
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="importLoading" @click="handleImportSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 任务列表弹出层 -->
    <el-popover
      v-model:visible="taskPopoverVisible"
      placement="bottom-end"
      :width="400"
      trigger="manual"
      popper-class="task-popover"
      :teleported="true"
      :virtual-ref="taskBtn"
      :virtual-triggering="true"
      :hide-after="0"
      :show-arrow="false"
      @hide="taskPopoverVisible = false"
    >
      <template #reference>
        <div></div>
      </template>
      <div class="task-list-container">
        <div class="task-list-header">
          <span class="header-title">任务列表</span>
        </div>
        <el-divider />
        <el-table 
          :data="taskList" 
          style="width: 100%" 
          size="small" 
          v-loading="taskLoading"
          :header-cell-style="{
            background: '#f5f7fa',
            color: '#606266',
            fontWeight: 500
          }"
        >
          <el-table-column prop="name" label="任务名称" min-width="200" show-overflow-tooltip />
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="scope">
              <el-tag
                :type="getStatusType(scope.row.status)"
                effect="light"
                size="small"
                round
              >
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-popover>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { createCost, getCostList, getPromotionReportList, updateCost, importCost, getTaskList } from '@/api/cost'
import { getCommandList } from '@/api/command'
import { Close } from '@element-plus/icons-vue'

// 查询参数
const queryParams = reactive({
  page: 1,
  pageSize: 10,
  startDate: '',
  endDate: '',
  groupId: undefined,
  categoryId: 0  // 默认为0，表示全部
})

// 日期范围
const dateRange = ref([])

// 数据列表
const costList = ref([])
const total = ref(0)
const loading = ref(false)

// 口令组相关
const commandOptions = ref([])
const commandLoading = ref(false)

// 分类选项
const categoryOptions = [
  { value: 0, label: '全部' },
  { value: 55, label: '小红书' },
  { value: 173, label: '抖音' },
  { value: 284, label: '闪购' },
]

// 表单相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref(null)
const formLoading = ref(false)
const form = reactive({
  id: undefined,
  date: '',
  amount: undefined,
  groupId: undefined
})

// 表单校验规则
const rules = {
  groupId: [{ required: true, message: '请选择口令组', trigger: 'change' }],
  date: [{ required: true, message: '请选择日期', trigger: 'change' }],
  amount: [{ required: true, message: '请输入金额', trigger: 'blur' }]
}

// 格式化金额
const formatAmount = (amount) => {
  return Number(amount).toFixed(2)
}

// 获取口令组列表
const loadCommandList = async (keyword = '') => {
  commandLoading.value = true
  try {
    const res = await getCommandList({ 
      page: 1,
      pageSize: 1000,
      keyword,
      status: 1  // 只获取启用的口令组
    })
    commandOptions.value = res.data.list || []
  } catch (error) {
    console.error('获取口令组列表失败:', error)
    commandOptions.value = []
  } finally {
    commandLoading.value = false
  }
}

// 口令组过滤
const handleCommandFilter = (val) => {
  if (val) {
    loadCommandList(val)
  }
}

// 使用新API的开关（你可以根据需要切换）
const USE_NEW_API = true

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    // 构造请求参数，当 categoryId 为 0 时不传递该字段
    const params = { ...queryParams }
    if (params.categoryId === 0) {
      delete params.categoryId
    }
    let res
    res = await getCostList(params)
    costList.value = res.data.list
    total.value = res.data.total
  } catch (error) {
    console.error('获取费用列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 日期范围变化
const handleDateChange = (val) => {
  if (val) {
    queryParams.startDate = val[0]
    queryParams.endDate = val[1]
  } else {
    queryParams.startDate = ''
    queryParams.endDate = ''
  }
}

// 查询
const handleQuery = () => {
  queryParams.page = 1
  getList()
}

// 重置查询
const resetQuery = () => {
  dateRange.value = []
  Object.assign(queryParams, {
    page: 1,
    pageSize: 10,
    startDate: '',
    endDate: '',
    groupId: undefined,
    categoryId: 0  // 重置时也设为0
  })
  getList()
}

// 新增费用
const handleAdd = () => {
  dialogTitle.value = '新增费用'
  dialogVisible.value = true
}

// 编辑费用
const handleEdit = (row) => {
  dialogTitle.value = '编辑费用'
  Object.assign(form, {
    id: row.id,
    date: row.report_date,
    amount: row.cost,
    groupId: row.group_id
  })
  // 打开弹窗时加载口令组列表，并确保当前选中的口令组在列表中
  loadCommandList().then(() => {
    const existingOption = commandOptions.value.find(item => item.id === row.group_id)
    if (!existingOption && row.group_id && row.group_name) {
      commandOptions.value.push({
        id: row.group_id,
        groupName: row.group_name
      })
    }
  })
  dialogVisible.value = true
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      formLoading.value = true
      try {
        // 构造提交参数
        const params = {
          group_id: form.groupId,
          report_date: form.date,
          cost: form.amount
        }
        
        if (form.id) {
          params.id = form.id
          await updateCost(params)
          ElMessage.success('更新成功')
        } else {
          await createCost(params)
          ElMessage.success('添加成功')
        }
        dialogVisible.value = false
        getList()
      } catch (error) {
        console.error('操作失败:', error)
      } finally {
        formLoading.value = false
      }
    }
  })
}

// 对话框关闭
const handleDialogClose = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  // 清空口令组选项
  commandOptions.value = []
  // 重置表单数据
  Object.assign(form, {
    id: undefined,
    date: '',
    amount: undefined,
    groupId: undefined
  })
}

// 分页相关
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

const handleCurrentChange = (val) => {
  queryParams.page = val
  getList()
}

// 导入相关
const importDialogVisible = ref(false)
const importFormRef = ref()
const importLoading = ref(false)
const fileList = ref([])

const importForm = reactive({
  categoryId: '',
  file: null
})

const importRules = {
  categoryId: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ]
}

// 处理导入按钮点击
const handleImport = () => {
  importDialogVisible.value = true
}

// 导入对话框关闭时重置表单
const handleImportDialogClose = () => {
  if (importFormRef.value) {
    importFormRef.value.resetFields()
  }
  Object.assign(importForm, {
    categoryId: '',
    file: null
  })
  fileList.value = []
}

// 处理文件上传
const handleFileUpload = (options) => {
  const { file } = options
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                 file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    ElMessage.error('只能上传 Excel 文件！')
    return
  }
  importForm.file = file
  fileList.value = [file]
  ElMessage.success('文件已选择')
}

// 提交导入
const handleImportSubmit = async () => {
  if (!importFormRef.value) return
  await importFormRef.value.validate(async (valid) => {
    if (valid) {
      if (!importForm.file) {
        ElMessage.error('请选择要导入的文件')
        return
      }
      
      importLoading.value = true
      try {
        const formData = new FormData()
        formData.append('file', importForm.file)
        formData.append('categoryId', importForm.categoryId)
        const res = await importCost(formData)
        ElMessage.success(res.data.tip || '导入成功')
        importDialogVisible.value = false
        getList()
      } catch (error) {
        console.error('导入失败:', error)
        ElMessage.error(error.response?.data?.message || '导入失败')
      } finally {
        importLoading.value = false
      }
    }
  })
}

// 任务列表相关
const taskPopoverVisible = ref(false)
const taskBtn = ref(null)
const taskList = ref([])
const taskLoading = ref(false)

// 获取任务列表
const loadTaskList = async () => {
  taskLoading.value = true
  try {
    const res = await getTaskList()
    taskList.value = res.data || []
    // 如果存在提示信息，则显示
    if (res.data.tip) {
      ElMessage.info(res.data.tip)
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    ElMessage.error('获取任务列表失败')
  } finally {
    taskLoading.value = false
  }
}

// 获取状态对应的类型和文本
const getStatusType = (status) => {
  const statusMap = {
    'pending': 'warning',
    'processing': 'primary',
    'completed': 'success',
    'failed': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusTextMap = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '成功',
    'failed': '失败'
  }
  return statusTextMap[status] || status
}

// 查看任务
const handleViewTasks = () => {
  if (!taskPopoverVisible.value) {
    taskPopoverVisible.value = true
  }
  loadTaskList()
}

// 根据分类ID获取分类名称
const getCategoryNameById = (categoryId) => {
  const category = categoryOptions.find(item => item.value === categoryId)
  return category ? category.label : '未知分类'
}

// 初始化
onMounted(() => {
  loadCommandList()
  getList()
})
</script>

<style scoped>
.cost-container {
  padding: 20px;
  position: relative;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.mb-3 {
  margin-bottom: 1rem;
}

.form-select {
  width: 100%;
}

:deep(.el-upload__tip) {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.task-list-container {
  padding: 12px;
  display: flex;
  flex-direction: column;
  min-height: 150px;
  background: #fff;
  border-radius: 4px;
}

.task-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 4px;
  flex-shrink: 0;
  margin-bottom: 4px;
}

.header-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

:deep(.el-popover.el-popper) {
  padding: 0;
  min-width: 150px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.task-popover.el-popper) {
  z-index: 2000;
  max-height: 400px;
  overflow: hidden;
  border-radius: 4px;
}

:deep(.task-popover .el-table) {
  flex: 1;
  height: calc(100% - 50px);
}

:deep(.task-popover .el-table__body-wrapper) {
  overflow-y: auto;
}

:deep(.task-popover .el-table--small) {
  font-size: 13px;
}

:deep(.task-popover .el-tag) {
  margin: 0 2px;
}

:deep(.el-divider--horizontal) {
  margin: 12px 0;
}

:deep(.el-popper__arrow) {
  display: none !important;
}

.task-btn {
  margin-left: 8px;
}
</style> 