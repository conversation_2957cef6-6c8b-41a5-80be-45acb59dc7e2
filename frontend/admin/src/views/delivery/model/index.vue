<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>模型管理</span>
          <div>
            <el-button
              type="primary"
              @click="handleAdd"
            >
              新增模型
            </el-button>
          </div>
        </div>
      </template>

      <div class="filter-container">
        <div class="filter-item">
          <span class="label">模型名称</span>
          <el-input
            v-model="queryParams.model_name"
            placeholder="请输入模型名称"
            clearable
            class="filter-input"
            @clear="handleFilterChange"
          />
        </div>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        row-key="id"
      >
        <el-table-column
          prop="id"
          label="ID"
          min-width="80"
        />
        <el-table-column
          prop="model_name"
          label="模型名称"
          min-width="120"
        />
        <el-table-column
          prop="created_at"
          label="创建时间"
          min-width="160"
        />
      </el-table>

      <el-pagination
        v-if="total > 0"
        :current-page="queryParams.page"
        :page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        :total="total"
        class="pagination-container"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </el-card>

    <!-- 新增/编辑模型对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        v-loading="submitLoading"
        element-loading-text="提交中..."
      >
        <el-form-item label="模型名称" prop="modelName">
          <el-input v-model="form.modelName" placeholder="请输入模型名称" />
        </el-form-item>
        <el-form-item label="导入模型" v-if="!form.id">
          <el-upload
            class="upload-demo"
            action=""
            :http-request="handleImport"
            :show-file-list="true"
            :file-list="fileList"
            accept=".xlsx,.xls"
          >
            <el-button type="primary">点击上传</el-button>
            <template #tip>
              <div class="el-upload__tip">
                只能上传 xlsx/xls 文件
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="handleSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getModelList, createModel, updateModel, deleteModel, importModel } from '@/api/model'

const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()
const total = ref(0)
const tableData = ref([])
const fileList = ref([])
const submitLoading = ref(false)

const queryParams = reactive({
  page: 1,
  page_size: 10,
  model_name: ''
})

const form = reactive({
  id: undefined,
  modelName: '',
  file: null
})

const rules = {
  modelName: [
    { required: true, message: '请输入模型名称', trigger: 'blur' }
  ]
}

// 获取列表数据
const fetchData = async () => {
  loading.value = true
  try {
    const res = await getModelList(queryParams)
    tableData.value = res.data.list
    total.value = res.data.total
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  queryParams.page = 1
  fetchData()
}

// 重置
const handleReset = () => {
  queryParams.modelName = ''
  handleSearch()
}

// 筛选变化
const handleFilterChange = () => {
  handleSearch()
}

// 页码变化
const handlePageChange = (page) => {
  queryParams.page = page
  fetchData()
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增模型'
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  dialogTitle.value = '编辑模型'
  Object.assign(form, {
    id: row.id,
    modelName: row.name
  })
  dialogVisible.value = true
}


// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      try {
        if (form.id) {
          await updateModel({
            id: form.id,
            modelName: form.modelName
          })
          ElMessage.success('更新成功')
        } else {
          if (form.file) {
            const formData = new FormData()
            formData.append('file', form.file)
            formData.append('modelName', form.modelName)
            await importModel(formData)
            ElMessage.success('导入成功')
          } else {
            await createModel({
              modelName: form.modelName
            })
            ElMessage.success('创建成功')
          }
        }
        dialogVisible.value = false
        fileList.value = []
        fetchData()
      } catch (error) {
        console.error(error)
        ElMessage.error(error.response?.data?.message || '操作失败')
      } finally {
        submitLoading.value = false
      }
    }
  })
}

// 处理导入
const handleImport = (options) => {
  const { file } = options
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                 file.type === 'application/vnd.ms-excel'
  if (!isExcel) {
    ElMessage.error('只能上传 Excel 文件！')
    return
  }
  form.file = file
  fileList.value = [file]
  ElMessage.success('文件已选择')
}

// 添加分页大小变化处理函数
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  queryParams.page = 1
  fetchData()
}

// 对话框关闭时重置表单
const handleDialogClose = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    id: undefined,
    modelName: '',
    file: null
  })
  fileList.value = []
}

onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;

  .filter-item {
    display: flex;
    align-items: center;

    .label {
      margin-right: 10px;
      white-space: nowrap;
      font-size: 14px;
      color: #606266;
    }

    .filter-input {
      width: 200px;
    }
  }
}

.pagination-container {
  padding: 10px 0;
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  background: #fff;
}

.form-select {
  width: 100%;
}

:deep(.el-form-item__label) {
  font-size: 14px;
  color: #606266;
}

:deep(.el-input__inner) {
  font-size: 14px;
}

:deep(.el-select__input) {
  font-size: 14px;
}

:deep(.el-upload__tip) {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

:deep(.el-table) {
  margin-top: 20px;
  
  .cell {
    padding: 2px 0;
  }
}
</style> 