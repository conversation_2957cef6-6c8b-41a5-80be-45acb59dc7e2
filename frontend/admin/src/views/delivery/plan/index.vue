<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <el-tabs v-model="activeTab" class="custom-tabs" @tab-click="handleTabClick">
            <el-tab-pane label="计划" name="plan" />
            <el-tab-pane label="单元" name="unit" />
            <el-tab-pane label="创意" name="creative" />
          </el-tabs>
        </div>
      </template>

      <!-- 计划筛选表单 -->
      <div v-if="activeTab === 'plan'" class="filter-container" style="margin-top: -10px">
        <div class="filter-left">
          <div class="filter-item">
            <el-select
              v-model="queryParams.agentId"
              placeholder="选择代理"
              clearable
              class="filter-select"
              @change="handleFilterChange"
            >
              <el-option label="全部" value="" />
              <el-option 
                v-for="item in agentOptions" 
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </div>
          <div class="filter-item">
            <el-select
              v-model="queryParams.mediaId"
              placeholder="选择媒体"
              clearable
              class="filter-select"
              @change="handleFilterChange"
            >
              <el-option label="全部" value="" />
              <el-option 
                v-for="item in mediaOptions" 
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </div>
          <div class="filter-item">
            <el-select
              v-model="queryParams.marketTargetName"
              placeholder="营销目标"
              clearable
              class="filter-select"
              @change="handleFilterChange"
            >
              <el-option label="全部" value="" />
              <el-option 
                v-for="item in marketTargetOptions" 
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </div>
          <div class="filter-item">
            <el-input
              v-model="queryParams.keyword"
              placeholder="计划名称/平台ID"
              clearable
              class="filter-input"
              @clear="handleFilterChange"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
      </div>

      <!-- 单元筛选表单 -->
      <div v-if="activeTab === 'unit'" class="filter-container" style="margin-top: -10px">
        <div class="filter-left">
          <div class="filter-item">
            <el-select
              v-model="unitsQueryParams.agentId"
              placeholder="选择代理"
              clearable
              class="filter-select"
              @change="handleUnitsFilterChange"
            >
              <el-option label="全部" value="" />
              <el-option 
                v-for="item in agentOptions" 
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </div>
          <div class="filter-item">
            <el-select
              v-model="unitsQueryParams.mediaId"
              placeholder="选择媒体"
              clearable
              class="filter-select"
              @change="handleUnitsFilterChange"
            >
              <el-option label="全部" value="" />
              <el-option 
                v-for="item in mediaOptions" 
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </div>
          <div class="filter-item">
            <el-select
              v-model="unitsQueryParams.marketTargetName"
              placeholder="营销目标"
              clearable
              class="filter-select"
              @change="handleUnitsFilterChange"
            >
              <el-option label="全部" value="" />
              <el-option 
                v-for="item in marketTargetOptions" 
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </div>
          <div class="filter-item">
            <el-input
              v-model="unitsQueryParams.planKeyword"
              placeholder="计划名称/平台ID"
              clearable
              class="filter-input"
              @clear="handleUnitsFilterChange"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          <div class="filter-item">
            <el-input
              v-model="unitsQueryParams.keyword"
              placeholder="单元名称/平台ID"
              clearable
              class="filter-input"
              @clear="handleUnitsFilterChange"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          <el-button type="primary" @click="handleUnitsSearch">搜索</el-button>
          <el-button @click="handleUnitsReset">重置</el-button>
        </div>
      </div>

      <!-- 创意筛选表单 -->
      <div v-if="activeTab === 'creative'" class="filter-container creative-filter" style="margin-top: -10px">
        <div class="filter-wrap">
          <div class="filter-item">
            <el-select
              v-model="creativesQueryParams.agentId"
              placeholder="选择代理"
              clearable
              class="filter-select"
              @change="handleCreativesFilterChange"
            >
              <el-option label="全部" value="" />
              <el-option 
                v-for="item in agentOptions" 
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </div>
          <div class="filter-item">
            <el-select
              v-model="creativesQueryParams.mediaId"
              placeholder="选择媒体"
              clearable
              class="filter-select"
              @change="handleCreativesFilterChange"
            >
              <el-option label="全部" value="" />
              <el-option 
                v-for="item in mediaOptions" 
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </div>
          <div class="filter-item">
            <el-select
              v-model="creativesQueryParams.marketTargetName"
              placeholder="营销目标"
              clearable
              class="filter-select"
              @change="handleCreativesFilterChange"
            >
              <el-option label="全部" value="" />
              <el-option 
                v-for="item in marketTargetOptions" 
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </div>
          <div class="filter-item">
            <el-input
              v-model="creativesQueryParams.planKeyword"
              placeholder="计划名称/平台ID"
              clearable
              class="filter-input"
              @clear="handleCreativesFilterChange"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          <div class="filter-item">
            <el-input
              v-model="creativesQueryParams.groupKeyword"
              placeholder="单元名称/平台ID"
              clearable
              class="filter-input"
              @clear="handleCreativesFilterChange"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          <div class="filter-item">
            <el-input
              v-model="creativesQueryParams.keyword"
              placeholder="创意名称/平台ID"
              clearable
              class="filter-input"
              @clear="handleCreativesFilterChange"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          <div class="filter-item filter-btns">
            <el-button type="primary" @click="handleCreativesSearch">搜索</el-button>
            <el-button @click="handleCreativesReset">重置</el-button>
          </div>
        </div>
      </div>

      <!-- 计划列表 -->
      <el-table
        v-if="activeTab === 'plan'"
        v-loading="loading"
        :data="tableData"
        style="width: 100%; margin-top: 20px;"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="80"
        />
        <el-table-column
          prop="platform_type"
          label="平台类型"
          min-width="120"
        >
          <template #default="{ row }">
            <el-tag
              :type="row.platform_type === 'denghuoplus' ? 'success' : ''"
            >
              {{ row.platform_type === 'denghuoplus' ? '灯火' : row.platform_type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="data_id"
          label="平台ID"
          width="120"
        />
        <el-table-column
          prop="name"
          label="计划名称"
          min-width="300"
        />
        <el-table-column
          prop="agent_name"
          label="代理名称"
          min-width="120"
        />
        <el-table-column
          prop="media_name"
          label="媒体名称"
          min-width="120"
        />
        <el-table-column
          prop="market_target_name"
          label="营销目标名称"
          min-width="120"
        />
        <el-table-column
          prop="plan_type"
          label="计划类型"
          min-width="120"
        >
          <template #default="{ row }">
            <el-tag v-if="row.plan_type" type="info">
              {{ row.plan_type === 'video' ? '视频' : '图文' }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="120"
        >
          <template #default="{ row }">
            <el-button
              link
              type="primary"
              :icon="Edit"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 单元列表 -->
      <el-table
        v-if="activeTab === 'unit'"
        v-loading="unitsLoading"
        :data="unitsData"
        style="width: 100%; margin-top: 20px;"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="80"
        />
        <el-table-column
          prop="platform_type"
          label="平台类型"
          min-width="120"
        >
          <template #default="{ row }">
            <el-tag
              :type="row.platform_type === 'denghuoplus' ? 'success' : ''"
            >
              {{ row.platform_type === 'denghuoplus' ? '灯火' : row.platform_type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="data_id"
          label="平台ID"
          width="120"
        />
        <el-table-column
          prop="agent_name"
          label="代理名称"
          min-width="120"
        />
        <el-table-column
          prop="media_name"
          label="媒体名称"
          min-width="120"
        />
        <el-table-column
          prop="plan_name"
          label="计划名称"
          min-width="120"
        />
        <el-table-column
          prop="name"
          label="单元名称"
          min-width="120"
        />
        <el-table-column
          prop="description"
          label="备注"
          min-width="200"
        >
          <template #default="{ row }">
            <el-tooltip
              v-if="row.description"
              :content="row.description"
              placement="top"
              :hide-after="2000"
            >
              <span class="ellipsis-text">{{ row.description }}</span>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="120"
        >
          <template #default="{ row }">
            <el-button
              link
              type="primary"
              :icon="Edit"
              @click="handleEditUnit(row)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 创意列表 -->
      <el-table
        v-if="activeTab === 'creative'"
        v-loading="creativesLoading"
        :data="creativesData"
        style="width: 100%; margin-top: 20px;"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="80"
        />
        <el-table-column
          prop="platform_type"
          label="平台类型"
          min-width="120"
        >
          <template #default="{ row }">
            <el-tag
              :type="row.platform_type === 'denghuoplus' ? 'success' : ''"
            >
              {{ row.platform_type === 'denghuoplus' ? '灯火' : row.platform_type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="data_id"
          label="平台ID"
          width="120"
        />
        <el-table-column
          prop="agent_name"
          label="代理名称"
          min-width="120"
        />
        <el-table-column
          prop="media_name"
          label="媒体名称"
          min-width="120"
        />
        <el-table-column
          prop="plan_name"
          label="计划名称"
          min-width="120"
        />
        <el-table-column
          prop="title"
          label="创意名称"
          min-width="120"
        />
        <el-table-column
          prop="description"
          label="备注"
          min-width="200"
        >
          <template #default="{ row }">
            <el-tooltip
              v-if="row.remark"
              :content="row.remark"
              placement="top"
              :hide-after="2000"
            >
              <span class="ellipsis-text">{{ row.remark }}</span>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="120"
        >
          <template #default="{ row }">
            <el-button
              link
              type="primary"
              :icon="Edit"
              @click="handleEditCreative(row)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div v-if="activeTab === 'plan'" class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.pageSize"
          :total="total"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <div v-if="activeTab === 'unit'" class="pagination-container">
        <el-pagination
          v-model:current-page="unitsQueryParams.page"
          v-model:page-size="unitsQueryParams.pageSize"
          :total="unitsTotal"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleUnitsSizeChange"
          @current-change="handleUnitsCurrentChange"
        />
      </div>

      <div v-if="activeTab === 'creative'" class="pagination-container">
        <el-pagination
          v-model:current-page="creativesQueryParams.page"
          v-model:page-size="creativesQueryParams.pageSize"
          :total="creativesTotal"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleCreativesSizeChange"
          @current-change="handleCreativesCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加编辑弹窗 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑计划"
      width="500px"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        label-width="120px"
      >
        <el-form-item label="计划类型">
          <el-radio-group v-model="editForm.planType">
            <el-radio label="image_text">图文</el-radio>
            <el-radio label="video">视频</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleEditSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加编辑单元弹窗 -->
    <el-dialog
      v-model="editUnitDialogVisible"
      title="编辑单元"
      width="500px"
    >
      <el-form
        ref="editUnitFormRef"
        :model="editUnitForm"
        :rules="editUnitRules"
        label-width="120px"
      >
        <el-form-item label="备注" prop="description">
          <el-input
            v-model="editUnitForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editUnitDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleEditUnitSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 编辑创意弹窗 -->
    <el-dialog
      v-model="editCreativeDialogVisible"
      title="编辑创意"
      width="500px"
    >
      <el-form
        ref="editCreativeFormRef"
        :model="editCreativeForm"
        :rules="editCreativeRules"
        label-width="120px"
      >
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="editCreativeForm.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editCreativeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleEditCreativeSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, View, Search } from '@element-plus/icons-vue'
import request from '@/utils/request'

const loading = ref(false)
const tableData = ref([])
const total = ref(0)
const queryParams = ref({
  page: 1,
  pageSize: 10,
  platformType: '',
  planName: '',
  agentId: '',
  mediaId: '', 
  marketTargetName: '',
  keyword: ''
})

const agentOptions = ref([])
const mediaOptions = ref([])

// 获取代理列表
const getAgentList = async () => {
  try {
    const response = await request({
      url: '/api/v1/agents/list',
      method: 'get',
      params: { 
        page: 1, 
        pageSize: 1000,
        type: 'delivery'  // 只获取投放类型的代理
      }
    })
    agentOptions.value = response.data.list || []
  } catch (error) {
    console.error('获取代理列表失败:', error)
  }
}

// 获取媒体列表
const getMediaList = async () => {
  try {
    const response = await request({
      url: '/api/v1/media/list',
      method: 'get',
      params: { 
        page: 1, 
        pageSize: 1000,
        cooperationType: 'delivery'  // 只获取投放类型的媒体
      }
    })
    mediaOptions.value = response.data.list || []
  } catch (error) {
    console.error('获取媒体列表失败:', error)
  }
}

const unitsDialogVisible = ref(false)
const unitsLoading = ref(false)
const unitsData = ref([])
const unitsTotal = ref(0)
const currentPlanId = ref(null)
const unitsQueryParams = ref({
  page: 1,
  pageSize: 10,
  agentId: '',
  mediaId: '',
  marketTargetName: '',
  planKeyword: '',
  keyword: ''
})

const creativesDialogVisible = ref(false)
const creativesLoading = ref(false)
const creativesData = ref([])
const creativesTotal = ref(0)
const creativesQueryParams = ref({
  page: 1,
  pageSize: 10,
  agentId: '',
  mediaId: '',
  marketTargetName: '',
  planKeyword: '',
  groupKeyword: '',
  keyword: ''
})

const editDialogVisible = ref(false)
const editForm = ref({
  id: null,
  planType: ''
})

const editUnitDialogVisible = ref(false)
const editUnitForm = ref({
  id: null,
  description: ''
})
const editUnitRules = {
  description: [
    { max: 500, message: '备注长度不能超过500个字符', trigger: 'blur' }
  ]
}

const editCreativeDialogVisible = ref(false)
const editCreativeForm = ref({
  id: null,
  remark: ''
})
const editCreativeFormRef = ref(null)
const editCreativeRules = {
  remark: [
    { max: 500, message: '备注长度不能超过500个字符', trigger: 'blur' }
  ]
}

const activeTab = ref('plan')
const marketTargetOptions = ref([])

// 获取营销目标列表
const getMarketTargetList = async () => {
  try {
    const response = await request({
      url: '/api/v1/delivery/plans/market-targets',
      method: 'get',
      params: {
        platformType: 'denghuoplus'
      }
    })
    marketTargetOptions.value = response.data?.targets || []
  } catch (error) {
    console.error('获取营销目标列表失败:', error)
    ElMessage.error('获取营销目标列表失败')
  }
}

const getList = async () => {
  loading.value = true
  try {
    const response = await request({
      url: '/api/v1/delivery/plans/list',
      method: 'get',
      params: queryParams.value
    })
    tableData.value = response.data.list
    total.value = response.data.total
  } catch (error) {
    console.error('获取计划列表失败:', error)
    ElMessage.error('获取计划列表失败')
  } finally {
    loading.value = false
  }
}

const handleFilterChange = () => {
  queryParams.value.page = 1
}

const handleSearch = () => {
  getList()
}

const handleReset = () => {
  queryParams.value = {
    page: 1,
    pageSize: 10,
    platformType: '',
    planName: '',
    agentId: '',
    mediaId: '',
    marketTargetName: '',
    keyword: ''
  }
  getList()
}

const handleEdit = (row) => {
  editForm.value = {
    id: row.id,
    planType: row.plan_type || 'image_text'
  }
  editDialogVisible.value = true
}

const handleViewUnits = (row) => {
  currentPlanId.value = row.id
  unitsQueryParams.value.planId = row.id
  unitsQueryParams.value.page = 1
  unitsDialogVisible.value = true
  getUnitsList()
}

const handleViewCreatives = (row) => {
  creativesQueryParams.value.planId = row.id
  creativesQueryParams.value.page = 1
  creativesDialogVisible.value = true
  getCreativesList()
}

const handleSizeChange = (val) => {
  queryParams.value.pageSize = val
  getList()
}

const handleCurrentChange = (val) => {
  queryParams.value.page = val
  getList()
}

const getUnitsList = async () => {
  unitsLoading.value = true
  try {
    const response = await request({
      url: '/api/v1/delivery/groups/list',
      method: 'get',
      params: {
        ...unitsQueryParams.value,
        withPlan: true
      }
    })
    if (response.data) {
      unitsData.value = response.data.list || []
      unitsTotal.value = response.data.total || 0
    } else {
      unitsData.value = []
      unitsTotal.value = 0
      ElMessage.warning('没有找到相关单元数据')
    }
  } catch (error) {
    console.error('获取单元列表失败:', error)
    const errorMsg = error.response?.data?.message || '获取单元列表失败'
    ElMessage.error(errorMsg)
    unitsData.value = []
    unitsTotal.value = 0
  } finally {
    unitsLoading.value = false
  }
}

const handleUnitsSizeChange = (val) => {
  unitsQueryParams.value.pageSize = val
  getUnitsList()
}

const handleUnitsCurrentChange = (val) => {
  unitsQueryParams.value.page = val
  getUnitsList()
}

const getCreativesList = async () => {
  creativesLoading.value = true
  try {
    const response = await request({
      url: '/api/v1/delivery/creatives/list',
      method: 'get',
      params: {
        ...creativesQueryParams.value,
        withPlan: true
      }
    })
    if (response.data) {
      creativesData.value = response.data.list || []
      creativesTotal.value = response.data.total || 0
    } else {
      creativesData.value = []
      creativesTotal.value = 0
      ElMessage.warning('没有找到相关创意数据')
    }
  } catch (error) {
    console.error('获取创意列表失败:', error)
    const errorMsg = error.response?.data?.message || '获取创意列表失败'
    ElMessage.error(errorMsg)
    creativesData.value = []
    creativesTotal.value = 0
  } finally {
    creativesLoading.value = false
  }
}

const handleCreativesSizeChange = (val) => {
  creativesQueryParams.value.pageSize = val
  getCreativesList()
}

const handleCreativesCurrentChange = (val) => {
  creativesQueryParams.value.page = val
  getCreativesList()
}

const handleEditSubmit = async () => {
  try {
    await request({
      url: `/api/v1/delivery/plans/${editForm.value.id}`,
      method: 'put',
      data: {
        planType: editForm.value.planType
      }
    })
    ElMessage.success('修改成功')
    editDialogVisible.value = false
    getList() // 刷新列表
  } catch (error) {
    console.error('修改计划失败:', error)
    ElMessage.error(error.response?.data?.message || '修改计划失败')
  }
}

const handleEditUnit = (row) => {
  editUnitForm.value = {
    id: row.id,
    description: row.description || ''
  }
  editUnitDialogVisible.value = true
}

const handleEditUnitSubmit = async () => {
  try {
    await request({
      url: `/api/v1/delivery/groups/${editUnitForm.value.id}`,
      method: 'put',
      data: {
        description: editUnitForm.value.description
      }
    })
    ElMessage.success('修改成功')
    editUnitDialogVisible.value = false
    getUnitsList() // 刷新列表
  } catch (error) {
    console.error('修改单元失败:', error)
    ElMessage.error(error.response?.data?.message || '修改单元失败')
  }
}

const handleEditCreative = (row) => {
  editCreativeForm.value = {
    id: row.id,
    remark: row.remark || ''
  }
  editCreativeDialogVisible.value = true
}

const handleEditCreativeSubmit = async () => {
  if (!editCreativeFormRef.value) return
  
  await editCreativeFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await request({
          url: `/api/v1/delivery/creatives/${editCreativeForm.value.id}`,
          method: 'put',
          data: {
            remark: editCreativeForm.value.remark
          }
        })
        ElMessage.success('编辑成功')
        editCreativeDialogVisible.value = false
        getCreativesList() // 刷新列表
      } catch (error) {
        console.error('编辑创意失败:', error)
        ElMessage.error('编辑创意失败')
      }
    }
  })
}

const handleTabClick = () => {
  if (activeTab.value === 'plan') {
    queryParams.value.page = 1
    getList()
  } else if (activeTab.value === 'unit') {
    unitsQueryParams.value.page = 1
    getUnitsList()
  } else if (activeTab.value === 'creative') {
    creativesQueryParams.value.page = 1
    getCreativesList()
  }
}

// 单元筛选相关方法
const handleUnitsFilterChange = () => {
  unitsQueryParams.value.page = 1
}

const handleUnitsSearch = () => {
  getUnitsList()
}

const handleUnitsReset = () => {
  unitsQueryParams.value = {
    page: 1,
    pageSize: 10,
    agentId: '',
    mediaId: '',
    marketTargetName: '',
    planKeyword: '',
    keyword: ''
  }
  getUnitsList()
}

// 创意筛选相关方法
const handleCreativesFilterChange = () => {
  creativesQueryParams.value.page = 1
}

const handleCreativesSearch = () => {
  getCreativesList()
}

const handleCreativesReset = () => {
  creativesQueryParams.value = {
    page: 1,
    pageSize: 10,
    agentId: '',
    mediaId: '',
    marketTargetName: '',
    planKeyword: '',
    groupKeyword: '',
    keyword: ''
  }
  getCreativesList()
}

onMounted(() => {
  getList()
  getAgentList()
  getMediaList()
  getMarketTargetList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 0;
}

:deep(.custom-tabs) {
  .el-tabs__header {
    margin: 0;
  }
  .el-tabs__nav-wrap {
    padding: 0;
  }
  .el-tabs__content {
    display: none;
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
}

.filter-container {
  display: flex;
  align-items: center;
  padding: 20px 0 0 0;
}

.filter-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-item .label {
  margin-right: 8px;
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.filter-select {
  width: 200px;
}

.filter-input {
  width: 200px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.ellipsis-text {
  display: inline-block;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.creative-filter {
  padding: 16px 0;
}

.filter-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-btns {
  margin-left: auto;
}

.filter-select,
.filter-input {
  width: 180px;
}
</style> 