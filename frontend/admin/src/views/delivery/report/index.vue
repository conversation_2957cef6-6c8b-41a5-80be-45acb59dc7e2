<template>
  <div class="report-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>投放报表</span>
        </div>
      </template>

      <!-- 查询表单 -->
      <div class="search-form mb-3">
        <el-form :model="form" inline>
          <el-form-item label="模型">
            <el-select 
              v-model="form.modelId" 
              placeholder="请选择模型" 
              class="w-200"
            >
              <el-option
                v-for="item in modelList"
                :key="item.id"
                :label="item.model_name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="分类">
            <el-select 
              v-model="form.categoryId" 
              placeholder="请选择分类" 
              class="w-200"
              @change="handleCategoryChange"
            >
              <el-option
                v-for="item in categoryList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="口令组">
            <el-select 
              v-model="form.groupId" 
              placeholder="请选择口令组" 
              class="w-200"
              :disabled="!form.categoryId"
              filterable
              remote
              :remote-method="handleGroupSearch"
            >
              <el-option
                v-for="item in groupList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="日期范围">
            <el-date-picker
              v-model="form.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYYMMDD"
              :default-time="[
                new Date(2000, 1, 1, 0, 0, 0),
                new Date(2000, 1, 1, 23, 59, 59),
              ]"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :loading="loading" @click="handleSearch" :disabled="!canSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table 
        :data="filteredTableData" 
        style="width: 100%" 
        v-loading="loading"
        :max-height="tableHeight"
      >
        <el-table-column prop="date" label="日期" min-width="120">
          <template #default="scope">
            {{ formatDate(scope.row.date) }}
          </template>
        </el-table-column>
        <el-table-column prop="total_orders" label="总订单数" min-width="120" />
        <el-table-column prop="total_commission" label="总佣金" min-width="120">
          <template #default="scope">
            ¥{{ formatAmount(scope.row.total_commission) }}
          </template>
        </el-table-column>
        <el-table-column prop="average_commission" label="单均佣金" min-width="120">
          <template #default="scope">
            ¥{{ formatAmount(scope.row.average_commission) }}
          </template>
        </el-table-column>
        <el-table-column prop="cost" label="费用" min-width="120">
          <template #default="scope">
            ¥{{ formatAmount(scope.row.cost) }}
          </template>
        </el-table-column>
        <el-table-column prop="new_orders" label="今日新订单" min-width="120" />
        <el-table-column prop="daily_cost" label="今日订单成本" min-width="120">
          <template #default="scope">
            ¥{{ formatAmount(scope.row.daily_cost) }}
          </template>
        </el-table-column>
        <el-table-column prop="payback_days" label="回本周期" min-width="120">
          <template #default="scope">
            {{ scope.row.payback_days === -1 ? '无法回本' : `${scope.row.payback_days}天` }}
          </template>
        </el-table-column>
      </el-table>

    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getDeliveryReport } from '../../../api/report'
import { getModelList } from '@/api/delivery/model'
import { getCommandList } from '@/api/command'

// 表单数据
const form = reactive({
  categoryId: undefined,
  groupId: undefined,
  modelId: undefined,
  dateRange: []
})

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
  isManualPageChange: false
})

// 基础数据
const modelList = ref([])
const categoryList = ref([
  { id: 55, name: '小红书' },
  { id: 173, name: '抖音' },
  { id: 284, name: '闪购' },
])
const groupList = ref([])
const loading = ref(false)
const allData = ref([]) // 存储所有数据，用于前端筛选

// 计算属性
const canSearch = computed(() => {
  return form.modelId && form.categoryId
})

const filteredTableData = computed(() => {
  if (!form.dateRange || form.dateRange.length !== 2) {
    return allData.value
  }
  
  return allData.value.filter(item => {
    const itemDate = parseInt(item.date)
    const startDate = parseInt(form.dateRange[0])
    const endDate = parseInt(form.dateRange[1])
    return itemDate >= startDate && itemDate <= endDate
  })
})

// 初始化日期范围 - 移除默认日期设置
const initDateRange = () => {
  form.dateRange = []
}

// 获取模型列表
const loadModels = async () => {
  try {
    const res = await getModelList()
    if (res.code === 0 && res.data) {
      modelList.value = res.data.list || []
    } else {
      ElMessage.warning('请先配置模型后再查看报表')
    }
  } catch (error) {
    console.error('获取模型列表失败:', error)
    ElMessage.error('获取模型列表失败')
  }
}

// 获取报表数据
const loadData = async () => {
  if (!canSearch.value) {
    ElMessage.warning('请选择必要的筛选条件')
    return
  }

  if (loading.value) return
  loading.value = true

  try {
    const params = {
      model_id: form.modelId,
      category_id: form.categoryId,
      group_id: form.groupId,
    }

    const res = await getDeliveryReport(params)
    if (res.code === 0) {
      allData.value = res.data.list
      pagination.total = res.data.total
    }
  } catch (error) {
    console.error('获取报表数据失败:', error)
    ElMessage.error('获取报表数据失败')
  } finally {
    loading.value = false
  }
}

// 查询
const handleSearch = () => {
  pagination.isManualPageChange = false
  pagination.page = 1
  loadData()
}

// 重置
const handleReset = () => {
  pagination.isManualPageChange = false
  form.categoryId = undefined
  form.groupId = undefined
  form.modelId = undefined
  form.dateRange = []
  pagination.page = 1
  pagination.pageSize = 10
  allData.value = []
  // 重置口令组列表
  if (form.categoryId) {
    handleCategoryChange()
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pagination.isManualPageChange = true
  pagination.pageSize = val
  loadData()
}

const handleCurrentChange = (val) => {
  if (pagination.isManualPageChange) {
    pagination.page = val
    loadData()
  }
  pagination.isManualPageChange = true
}

// 格式化函数
const formatAmount = (amount) => Number(amount).toFixed(2)
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`
}

// 处理分类变化
const handleCategoryChange = async () => {
  form.groupId = undefined
  groupList.value = []
  
  if (!form.categoryId) {
    return
  }
  
  try {
    const res = await getCommandList({
      categoryId: form.categoryId,
      size: 1000
    })
    if (res.code === 0 && res.data && res.data.list) {
      // 添加"全部"选项
      groupList.value = [
        { id: undefined, name: '全部' },
        ...res.data.list.map(group => ({
          id: group.id,
          name: group.groupName
        }))
      ]
      // 默认选择"全部"选项
      form.groupId = undefined
    } else {
      groupList.value = [{ id: undefined, name: '全部' }]
    }
  } catch (error) {
    console.error('获取口令组列表失败:', error)
    groupList.value = [{ id: undefined, name: '全部' }]
  }
}

// 处理口令组搜索
const handleGroupSearch = (query) => {
  if (!query) {
    handleCategoryChange() // 如果清空搜索，重新加载所有口令组
    return
  }
  
  // 保留"全部"选项，并过滤其他选项
  const allOption = groupList.value.find(item => item.id === undefined)
  const filteredGroups = groupList.value.filter(item => 
    item.id !== undefined && item.name.toLowerCase().includes(query.toLowerCase())
  )
  groupList.value = [allOption, ...filteredGroups]
}

// 表格高度
const tableHeight = ref(500)

// 动态计算表格高度
const calculateTableHeight = () => {
  // 获取视窗高度
  const windowHeight = window.innerHeight
  // 减去其他元素的高度（头部、搜索表单、分页、边距等）
  // 预留200px给其他元素
  tableHeight.value = windowHeight - 200
}

// 监听窗口大小变化
onMounted(() => {
  initDateRange()
  loadModels()
  calculateTableHeight()
  window.addEventListener('resize', calculateTableHeight)
})

// 组件销毁时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', calculateTableHeight)
})
</script>

<style scoped>
.report-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.mb-3 {
  margin-bottom: 1rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.text-gray-400 {
  color: #9ca3af;
}

/* 下拉菜单样式 */
.w-200 {
  width: 280px !important;
}

.model-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.model-name {
  font-weight: 500;
}

.model-category {
  color: #666;
  font-size: 13px;
}

.model-group {
  margin-left: auto;
  font-size: 12px;
}

:deep(.el-select-dropdown__item) {
  padding: 0 12px !important;
}
</style>

<style>
.model-select-dropdown {
  min-width: 280px !important;
}

.model-select-dropdown .el-select-dropdown__item {
  height: auto !important;
  padding: 8px 12px !important;
}
</style> 