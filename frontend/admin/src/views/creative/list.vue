<template>
  <div class="app-container">
    <el-card>
      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="创意名称">
          <el-input v-model="searchForm.name" placeholder="请输入创意名称" clearable />
        </el-form-item>
        <el-form-item class="search-buttons">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮区 -->
      <div class="table-operations">
        <el-button type="primary" @click="handleCreate">新增创意</el-button>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="dataList"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" min-width="80" />
        <el-table-column prop="name" label="创意名称" min-width="100" />
        <el-table-column label="操作" fixed="right" width="300">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)">修改</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="page"
          v-model:page-size="size"
          :page-sizes="[10, 20, 30, 50]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { list as fetchAdCreatives } from '@/api/ad-creative'

const router = useRouter()

// 搜索表单
const searchForm = ref({
  name: ''
})

// 列表数据
const loading = ref(false)
const dataList = ref([])
const page = ref(1)
const size = ref(10)
const total = ref(0)

// 获取列表数据
const fetchList = async () => {
  loading.value = true
  try {
    const res = await fetchAdCreatives({
      page: page.value,
      page_size: size.value,
      ...searchForm.value
    })
    dataList.value = res.data?.list || []
    total.value = res.data?.total || 0
  } catch (error) {
    console.error('获取创意列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  page.value = 1
  fetchList()
}

// 重置
const handleReset = () => {
  searchForm.value = {
    name: ''
  }
  page.value = 1
  fetchList()
}

// 新建
const handleCreate = () => {
  router.push('/creative/edit')
}

// 编辑
const handleEdit = (row) => {
  router.push(`/creative/edit/${row.id}`)
}

// 分页大小改变
const handleSizeChange = (val) => {
  size.value = val
  fetchList()
}

// 页码改变
const handleCurrentChange = (val) => {
  page.value = val
  fetchList()
}

onMounted(() => {
  fetchList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .search-form {
    :deep(.el-form-item) {
      margin-right: 16px;
      width: 280px;
      
      .el-select {
        width: 100%;
      }
    }

    .search-buttons {
      margin-left: auto;
      width: auto;
    }
  }

  .table-operations {
    margin: 16px 0;
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}
</style> 