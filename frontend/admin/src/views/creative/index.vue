<template>
  <div class="creative-container">
    <div class="preview-area" :style="{ backgroundColor: backgroundColor }">
      <div class="upload-container" :style="{ width: `${previewWidth}px` }">
        <div v-if="!imageUrl" class="upload-wrapper">
          <el-upload
            class="uploader"
            action="/api/v1/upload/image"
            :show-file-list="false"
            :on-success="handleUploadSuccess"
            :before-upload="beforeUpload"
            :headers="headers"
          >
            <el-button type="primary">点击上传图片</el-button>
          </el-upload>
        </div>
        <div v-else class="image-preview-wrapper">
          <div class="image-container" @mousedown="startDrawHotspot" ref="imageContainerRef">
            <img :src="imageUrl" class="preview-image" draggable="false" @dragstart.prevent />
            <div 
              v-for="(hotspot, index) in hotspots" 
              :key="index" 
              class="hotspot"
              :style="{
                left: hotspot.x + 'px',
                top: hotspot.y + 'px',
                width: hotspot.width + 'px',
                height: hotspot.height + 'px'
              }"
            ></div>
            <div 
              v-if="isDrawing" 
              class="drawing-hotspot"
              :style="{
                left: Math.min(startPos.x, currentPos.x) + 'px',
                top: Math.min(startPos.y, currentPos.y) + 'px',
                width: Math.abs(currentPos.x - startPos.x) + 'px',
                height: Math.abs(currentPos.y - startPos.y) + 'px'
              }"
            ></div>
          </div>
          <div class="image-actions">
            <el-button type="danger" @click="removeImage">删除图片</el-button>
          </div>
        </div>
      </div>
    </div>
    
    <div class="form-area">
      <el-form label-width="100px">
        <div class="form-content">
          <el-form-item label="创意名称" required>
            <el-input v-model="creativeName" placeholder="请输入创意名称"></el-input>
          </el-form-item>

          <el-form-item label="背景颜色">
            <el-color-picker
              v-model="backgroundColor"
              show-alpha
              color-format="rgb"
            />
          </el-form-item>

          <el-form-item label="预览宽度">
            <el-input-number
              v-model="previewWidth"
              :min="200"
              :max="500"
              :step="10"
            />
            <span class="unit-text">px</span>
          </el-form-item>
          
          <div class="hotspots-section">
            <div class="section-title">热区列表<span class="required">*</span></div>
            
            <div v-if="hotspots.length === 0" class="no-hotspots">
              请在左侧图片上划分热区
            </div>
            
            <div class="hotspots-grid">
              <div v-for="(hotspot, index) in hotspots" :key="index" class="hotspot-item">
                <div class="hotspot-header">
                  <span>热区 {{ index + 1 }}</span>
                  <el-button type="danger" size="small" @click="removeHotspot(index)">删除</el-button>
                </div>
                
                <el-form-item label="事件" label-width="60px">
                  <el-radio-group v-model="hotspot.event_type">
                    <el-radio :label="1">确定</el-radio>
                    <el-radio :label="2">关闭</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item label="坐标" label-width="60px">
                  <div class="coordinates">
                    <span>X: {{ hotspot.x }}</span>
                    <span>Y: {{ hotspot.y }}</span>
                  </div>
                </el-form-item>
                
                <el-form-item label="尺寸" label-width="60px">
                  <span class="size-info">{{ hotspot.width }} x {{ hotspot.height }} px</span>
                </el-form-item>
              </div>
            </div>
          </div>

          <div class="submit-section">
            <el-button type="primary" size="large" @click="handleSubmit" :disabled="!isValid">提交</el-button>
            <el-button size="large" @click="handleBack">返回</el-button>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store/modules/user'
import { useRoute, useRouter } from 'vue-router'
import { getDetail as fetchCreativeDetail, create as createCreative, update as updateCreative } from '@/api/ad-creative'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const imageUrl = ref('')
const creativeName = ref('')
const hotspots = ref([])
const isDrawing = ref(false)
const startPos = ref({ x: 0, y: 0 })
const currentPos = ref({ x: 0, y: 0 })
const imageContainerRef = ref(null)
const isLoading = ref(false)
const isEdit = computed(() => !!route.params.id || !!route.query.id)
const backgroundColor = ref('rgba(0,0,0,0.5)')
const previewWidth = ref(310)

// 设置请求头，包含token
const headers = computed(() => {
  return {
    Authorization: userStore.token ? `Bearer ${userStore.token}` : ''
  }
})

// 表单验证
const isValid = computed(() => {
  return creativeName.value.trim() !== ''
})

// 提交处理
const handleSubmit = async () => {
  if (!isValid.value) {
    ElMessage.warning('请填写创意名称')
    return
  }

  // 转换热区数据为API需要的格式
  const hotAreasData = hotspots.value.map((h, index) => ({
    id: h.id || index + 1, // 优先使用热区自己的 id
    width: h.width,
    height: h.height,
    unit: 'px',
    x: h.x,
    y: h.y,
    event_type: h.event_type
  }))

  const submitData = {
    name: creativeName.value,
    image_url: imageUrl.value,
    image_area: `${previewWidth.value}px`,
    background_color: backgroundColor.value,
    hotAreas: hotAreasData
  }

  isLoading.value = true
  try {
    if (isEdit.value) {
      // 更新创意
      await updateCreative({
        ...submitData,
        id: Number(route.params.id || route.query.id)
      })
      ElMessage.success('更新成功')
    } else {
      // 创建创意
      await createCreative(submitData)
      ElMessage.success('创建成功')
    }
    // 返回列表页
    router.push('/creative')
  } catch (error) {
    console.error('提交失败：', error)
    ElMessage.error(error.message || '提交失败')
  } finally {
    isLoading.value = false
  }
}

// 获取创意详情
const getCreativeDetail = async (id) => {
  isLoading.value = true
  try {
    const response = await fetchCreativeDetail(id)
    
    const detail = response.data
    if (detail) {
      creativeName.value = detail.name
      imageUrl.value = detail.image_url || ''
      backgroundColor.value = detail.background_color || 'rgba(0,0,0,0.5)'
      
      // 从 image_area 中提取预览宽度
      if (detail.image_area) {
        const width = parseInt(detail.image_area)
        if (!isNaN(width)) {
          previewWidth.value = width
        }
      } else {
        previewWidth.value = 310 // 默认值
      }
      
      // 转换热区数据
      if (detail.hotAreas && detail.hotAreas.length > 0) {
        hotspots.value = detail.hotAreas.map((area, index) => ({
          id: area.id || index + 1, // 优先使用原有的 id，如果没有才使用索引+1
          x: area.x,
          y: area.y,
          width: area.width,
          height: area.height,
          event_type: area.event_type
        }))
      }
    }
  } catch (error) {
    console.error('获取创意详情失败：', error)
    ElMessage.error('获取创意详情失败')
    router.push('/creative')
  } finally {
    isLoading.value = false
  }
}

// 初始化
onMounted(() => {
  if (isEdit.value) {
    const id = route.params.id || route.query.id
    getCreativeDetail(id)
  }
})

const handleUploadSuccess = (response) => {
  if (response.code === 0) {
    imageUrl.value = response.data.url
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error(response.message || '上传失败')
  }
}

const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const removeImage = () => {
  imageUrl.value = ''
  hotspots.value = []
}

// 热区绘制相关方法
const startDrawHotspot = (e) => {
  if (!imageContainerRef.value) return
  
  const rect = imageContainerRef.value.getBoundingClientRect()
  const x = e.clientX - rect.left
  const y = e.clientY - rect.top
  
  startPos.value = { x, y }
  currentPos.value = { x, y }
  isDrawing.value = true
  
  document.addEventListener('mousemove', drawHotspot)
  document.addEventListener('mouseup', endDrawHotspot)
}

const drawHotspot = (e) => {
  if (!isDrawing.value || !imageContainerRef.value) return
  
  const rect = imageContainerRef.value.getBoundingClientRect()
  const x = e.clientX - rect.left
  const y = e.clientY - rect.top
  
  currentPos.value = { x, y }
}

const endDrawHotspot = () => {
  if (!isDrawing.value) return
  
  const width = Math.abs(currentPos.value.x - startPos.value.x)
  const height = Math.abs(currentPos.value.y - startPos.value.y)
  
  // 如果热区太小，不添加
  if (width < 10 || height < 10) {
    isDrawing.value = false
    document.removeEventListener('mousemove', drawHotspot)
    document.removeEventListener('mouseup', endDrawHotspot)
    return
  }
  
  const x = Math.min(startPos.value.x, currentPos.value.x)
  const y = Math.min(startPos.value.y, currentPos.value.y)
  
  hotspots.value.push({
    id: hotspots.value.length + 1,
    x,
    y,
    width,
    height,
    event_type: 1 // 默认为"确定"类型
  })
  
  isDrawing.value = false
  document.removeEventListener('mousemove', drawHotspot)
  document.removeEventListener('mouseup', endDrawHotspot)
}

const removeHotspot = (index) => {
  hotspots.value.splice(index, 1)
  // 重新分配 id
  hotspots.value.forEach((hotspot, idx) => {
    hotspot.id = idx + 1
  })
}

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', drawHotspot)
  document.removeEventListener('mouseup', endDrawHotspot)
})

// 返回列表页
const handleBack = () => {
  router.push('/creative')
}
</script>

<style scoped>
.creative-container {
  padding: 20px;
  display: flex;
  gap: 30px;
}

.preview-area {
  width: 375px;
  height: 812px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.upload-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.upload-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-preview-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0;
}

.image-container {
  position: relative;
  width: 100%;
  cursor: crosshair;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
}

.preview-image {
  width: 100%;
  height: auto;
  object-fit: contain;
  user-select: none;
  -webkit-user-drag: none;
  display: block;
  margin: 0;
  padding: 0;
}

.image-actions {
  position: absolute;
  bottom: -50px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  padding: 10px;
}

.hotspot {
  position: absolute;
  border: 2px solid red;
  background-color: rgba(255, 0, 0, 0.2);
  pointer-events: none;
}

.drawing-hotspot {
  position: absolute;
  border: 2px dashed red;
  background-color: rgba(255, 0, 0, 0.1);
  pointer-events: none;
}

.form-area {
  flex: 1;
  min-width: 600px;
  max-width: 800px;
}

.form-content {
  padding: 0 20px;
}

.hotspots-section {
  margin-top: 20px;
  border-top: 1px solid #eee;
  padding-top: 20px;
  margin-left: 32px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  padding-left: 0;
}

.required {
  color: #f56c6c;
  margin-left: 4px;
}

.no-hotspots {
  color: #999;
  font-style: italic;
  margin-bottom: 15px;
}

.hotspots-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-top: 15px;
  padding: 0;
}

.hotspot-item {
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.hotspot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-weight: bold;
}

.coordinates {
  display: flex;
  gap: 20px;
  color: #333;
}

.size-info {
  color: #333;
  font-weight: 500;
}

.submit-section {
  margin-top: 30px;
  padding: 20px;
  border-top: 1px solid #eee;
  text-align: center;
}

.submit-section .el-button {
  min-width: 120px;
  font-weight: 500;
}

.el-form-item {
  margin-bottom: 12px;
}

.unit-text {
  margin-left: 8px;
  color: #606266;
}
</style> 