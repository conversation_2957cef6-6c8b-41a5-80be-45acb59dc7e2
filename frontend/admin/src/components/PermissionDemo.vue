<template>
  <div class="permission-demo">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>权限系统演示</span>
        </div>
      </template>
      
      <!-- 用户权限信息 -->
      <div class="section">
        <h3>当前用户权限信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户角色">
            <el-tag 
              v-for="role in userStore.roles" 
              :key="role" 
              type="success"
              class="mr-1"
            >
              {{ role }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="权限数量">
            {{ userStore.permissionCodes?.length || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="是否管理员">
            <el-tag :type="userStore.isAdmin ? 'success' : 'info'">
              {{ userStore.isAdmin ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="permissions-list" v-if="userStore.permissionCodes?.length">
          <h4>权限列表：</h4>
          <el-tag 
            v-for="permission in userStore.permissionCodes" 
            :key="permission"
            size="small"
            class="permission-tag"
          >
            {{ permission }}
          </el-tag>
        </div>
      </div>

      <!-- 权限控制演示 -->
      <div class="section">
        <h3>权限控制演示</h3>
        
        <!-- 指令控制 -->
        <div class="demo-group">
          <h4>指令控制 (v-permission)</h4>
          <div class="button-group">
            <el-button 
              type="primary" 
              v-permission="'user.create'"
            >
              创建用户 (user.create)
            </el-button>
            <el-button 
              type="warning" 
              v-permission="'user.edit'"
            >
              编辑用户 (user.edit)
            </el-button>
            <el-button 
              type="danger" 
              v-permission="'user.delete'"
            >
              删除用户 (user.delete)
            </el-button>
            <el-button 
              type="info" 
              v-permission="['user.create', 'user.edit'].all"
            >
              批量操作 (需要所有权限)
            </el-button>
            <el-button 
              type="success" 
              v-permission="['user.create', 'user.edit']"
            >
              任一权限 (user.create 或 user.edit)
            </el-button>
          </div>
        </div>

        <!-- 组件控制 -->
        <div class="demo-group">
          <h4>组件控制 (PermissionWrapper)</h4>
          <PermissionWrapper permission="dashboard:view">
            <el-alert 
              title="仪表盘内容" 
              type="success" 
              description="您有权限查看仪表盘内容"
              show-icon
            />
          </PermissionWrapper>
          
          <PermissionWrapper permission="finance.view">
            <el-alert 
              title="财务内容" 
              type="warning" 
              description="您有权限查看财务内容"
              show-icon
            />
            <template #fallback>
              <el-alert 
                title="权限不足" 
                type="error" 
                description="您没有权限查看财务内容"
                show-icon
              />
            </template>
          </PermissionWrapper>
        </div>

        <!-- 编程式检查 -->
        <div class="demo-group">
          <h4>编程式权限检查</h4>
          <div class="check-results">
            <p>
              检查 'user.create' 权限: 
              <el-tag :type="hasUserCreate ? 'success' : 'danger'">
                {{ hasUserCreate ? '有权限' : '无权限' }}
              </el-tag>
            </p>
            <p>
              检查 'admin.system' 权限: 
              <el-tag :type="hasAdminSystem ? 'success' : 'danger'">
                {{ hasAdminSystem ? '有权限' : '无权限' }}
              </el-tag>
            </p>
          </div>
        </div>
      </div>

      <!-- 403页面测试 -->
      <div class="section">
        <h3>403权限不足页面测试</h3>
        <p class="section-desc">点击以下按钮测试不同的权限不足场景：</p>
        <div class="test-buttons">
          <el-button 
            type="danger" 
            @click="testSinglePermission"
          >
            测试单权限不足
          </el-button>
          <el-button 
            type="danger" 
            @click="testMultiplePermissions"
          >
            测试多权限不足
          </el-button>
          <el-button 
            type="danger" 
            @click="testComplexPermissions"
          >
            测试复杂权限场景
          </el-button>
        </div>
      </div>

      <!-- 权限工具演示 -->
      <div class="section">
        <h3>权限工具函数演示</h3>
        <div class="demo-group">
          <el-button @click="testPermissionUtils">测试权限工具</el-button>
          <el-button @click="generateErrorMessage">生成错误信息</el-button>
        </div>
        
        <div v-if="testResults.length" class="test-results">
          <h4>测试结果：</h4>
          <div 
            v-for="(result, index) in testResults" 
            :key="index"
            class="result-item"
          >
            <pre>{{ result }}</pre>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { usePermission } from '@/hooks/usePermission'
import { PermissionUtils } from '@/utils/permission'
import PermissionWrapper from './PermissionWrapper.vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()
const { hasPermission } = usePermission()

// 权限检查结果
const hasUserCreate = computed(() => hasPermission('user.create'))
const hasAdminSystem = computed(() => hasPermission('admin.system'))

// 测试结果
const testResults = ref([])

// 测试单权限不足场景
const testSinglePermission = () => {
  router.push({
    path: '/403',
    query: {
      routePath: '/admin/users/create',
      routeName: '创建用户',
      requiredPermission: 'user.create',
      missingPermissions: JSON.stringify(['user.create'])
    }
  })
}

// 测试多权限不足场景
const testMultiplePermissions = () => {
  router.push({
    path: '/403',
    query: {
      routePath: '/admin/finance/reports',
      routeName: '财务报告',
      requiredPermission: 'finance.view',
      requiredPermissions: JSON.stringify(['finance.view', 'finance.report']),
      missingPermissions: JSON.stringify(['finance.view', 'finance.report'])
    }
  })
}

// 测试复杂权限场景
const testComplexPermissions = () => {
  router.push({
    path: '/403',
    query: {
      routePath: '/admin/system/settings',
      routeName: '系统设置',
      requiredPermission: 'admin.system',
      requiredPermissions: JSON.stringify(['admin.system', 'admin.config', 'admin.security']),
      missingPermissions: JSON.stringify(['admin.system', 'admin.config'])
    }
  })
}

// 测试权限工具函数
const testPermissionUtils = () => {
  testResults.value = []
  
  // 测试基础权限检查
  const basicCheck = PermissionUtils.hasPermission('user.create')
  testResults.value.push(`基础权限检查 'user.create': ${basicCheck}`)
  
  // 测试详细权限检查
  const detailCheck = PermissionUtils.checkPermissionDetail('user.create')
  testResults.value.push(`详细权限检查 'user.create': ${JSON.stringify(detailCheck, null, 2)}`)
  
  // 测试多权限检查
  const multiCheck = PermissionUtils.checkPermissionDetail(['user.create', 'user.edit'])
  testResults.value.push(`多权限检查 ['user.create', 'user.edit']: ${JSON.stringify(multiCheck, null, 2)}`)
  
  // 测试所有权限检查
  const allCheck = PermissionUtils.checkPermissionDetail(['user.create', 'user.edit'], { all: true })
  testResults.value.push(`所有权限检查 ['user.create', 'user.edit']: ${JSON.stringify(allCheck, null, 2)}`)
  
  ElMessage.success('权限工具测试完成，请查看结果')
}

// 生成错误信息
const generateErrorMessage = () => {
  const singleMessage = PermissionUtils.generatePermissionErrorMessage('user.create', '创建用户')
  const multiMessage = PermissionUtils.generatePermissionErrorMessage(['finance.view', 'finance.report'], '查看财务报告')
  
  testResults.value = []
  testResults.value.push(`单权限错误信息: ${singleMessage}`)
  testResults.value.push(`多权限错误信息: ${multiMessage}`)
  
  ElMessage.success('错误信息生成完成')
}
</script>

<style scoped>
.permission-demo {
  padding: 20px;
}

.section {
  margin-bottom: 30px;
}

.section h3 {
  color: #303133;
  font-size: 18px;
  margin-bottom: 15px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 5px;
}

.section h4 {
  color: #606266;
  font-size: 16px;
  margin-bottom: 10px;
}

.section-desc {
  color: #909399;
  margin-bottom: 15px;
}

.permissions-list {
  margin-top: 15px;
}

.permission-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.demo-group {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.check-results p {
  margin-bottom: 10px;
}

.test-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.test-results {
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 8px;
}

.result-item {
  margin-bottom: 15px;
  padding: 10px;
  background: white;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.result-item pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: 12px;
  color: #606266;
}

.mr-1 {
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .button-group,
  .test-buttons {
    flex-direction: column;
    align-items: stretch;
  }
  
  .button-group .el-button,
  .test-buttons .el-button {
    margin-bottom: 10px;
  }
}
</style> 