<template>
  <div v-if="hasPermission" :class="wrapperClass">
    <slot />
  </div>
  <div v-else-if="showFallback" :class="wrapperClass">
    <slot name="fallback">
      <span class="permission-fallback">{{ fallbackText }}</span>
    </slot>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { usePermission } from '@/hooks/usePermission'

const props = defineProps({
  // 权限标识（单个）
  permission: {
    type: String,
    default: ''
  },
  // 权限标识数组（多个）
  permissions: {
    type: Array,
    default: () => []
  },
  // 权限检查模式：any-任一权限，all-所有权限
  mode: {
    type: String,
    default: 'any',
    validator: (value) => ['any', 'all'].includes(value)
  },
  // 是否显示无权限时的占位内容
  showFallback: {
    type: Boolean,
    default: false
  },
  // 无权限时的占位文本
  fallbackText: {
    type: String,
    default: '无权限'
  },
  // 包装器CSS类名
  wrapperClass: {
    type: String,
    default: ''
  }
})

const { hasPermission: checkPermission, hasAnyPermission, hasAllPermissions } = usePermission()

const hasPermission = computed(() => {
  // 如果单个权限和权限数组都没有提供，默认有权限
  if (!props.permission && (!props.permissions || props.permissions.length === 0)) {
    return true
  }

  // 优先检查单个权限
  if (props.permission) {
    return checkPermission(props.permission)
  }

  // 检查权限数组
  if (props.permissions && props.permissions.length > 0) {
    if (props.mode === 'all') {
      return hasAllPermissions(props.permissions)
    } else {
      return hasAnyPermission(props.permissions)
    }
  }

  return true
})
</script>

<style scoped>
.permission-fallback {
  color: #999;
  font-size: 12px;
  font-style: italic;
}
</style> 