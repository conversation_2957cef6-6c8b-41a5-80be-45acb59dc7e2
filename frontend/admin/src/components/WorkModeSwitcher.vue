<template>
  <div class="work-mode-switcher">
    <!-- 切换loading遮罩 -->
    <div v-if="switching" class="switching-overlay">
      <div class="switching-content">
        <el-icon class="rotating">
          <Loading />
        </el-icon>
        <span>正在切换模式...</span>
      </div>
    </div>
    
    <el-select
      v-model="currentMode"
      @change="handleModeChange"
      :loading="switching"
      :disabled="switching"
      placeholder="选择工作模式"
      class="mode-selector"
    >
      <el-option
        v-for="mode in availableModes"
        :key="mode.value"
        :label="mode.label"
        :value="mode.value"
        :disabled="!mode.enabled"
      >
        <div class="mode-option">
          <el-icon class="mode-icon">
            <component :is="mode.icon" />
          </el-icon>
          <span class="mode-text">{{ mode.label }}</span>
          <span v-if="!mode.enabled" class="mode-disabled-tip">无权限</span>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, markRaw, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { workModeApi } from '@/api/workMode'
import { 
  ShoppingCart, 
  TrendCharts,
  Loading
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

const currentMode = ref('')
const availableModes = ref([])
const switching = ref(false)
const isInitialized = ref(false)

// 模式配置 - 使用 markRaw 防止图标组件被响应式化
const modeConfig = {
  traffic: {
    value: 'traffic',
    label: '流量采买',
    icon: markRaw(ShoppingCart), // 标记为非响应式
    description: '媒体资源采购、投放计划管理'
  },
  ad: {
    value: 'ad',
    label: '广告投放',
    icon: markRaw(TrendCharts), // 标记为非响应式
    description: '数据报表、口令管理、费用管理'
  }
}

// 获取用户可用的工作模式
const fetchUserModes = async () => {
  return
  if (isInitialized.value) {
    return
  }
  
  try {
    console.log('🚀 开始获取用户工作模式')
    const response = await workModeApi.getUserModes()
    if (response.code === 200) {
      const { default_mode, current_mode, modes } = response.data
      
      // 从 modes 数组中提取 code 值，作为 available_modes
      const available_modes = modes.map(mode => mode.code)
      
      console.log('📋 获取到工作模式配置:', { default_mode, current_mode, available_modes })
      
      // 同步 currentMode 和 userStore.workMode
      currentMode.value = current_mode || default_mode
      
      // 只有当工作模式确实需要更新时才设置
      if (userStore.workMode !== currentMode.value) {
        userStore.setWorkMode(currentMode.value)
      }
      
      // 构建可用模式列表
      availableModes.value = Object.keys(modeConfig).map(key => ({
        ...modeConfig[key],
        enabled: available_modes.includes(key)
      }))
      
      console.log('📋 构建的可用模式列表:', availableModes.value)
      
      // 如果当前模式不可用，切换到第一个可用模式
      if (!available_modes.includes(currentMode.value) && available_modes.length > 0) {
        console.log('⚠️ 当前模式不可用，切换到第一个可用模式')
        await handleModeChange(available_modes[0])
      } else {
        // 只有在菜单为空时才获取当前模式的菜单和权限
        if (!userStore.menuItems || userStore.menuItems.length === 0) {
          console.log('📄 菜单为空，加载当前模式的菜单')
          await loadModeMenuAndPermissions(currentMode.value)
        }
      }
      
      // 标记为已初始化
      isInitialized.value = true
      console.log('✅ 工作模式初始化完成')
    }
  } catch (error) {
    console.error('❌ 获取工作模式失败:', error)
    ElMessage.error('获取工作模式失败: ' + error.message)
  }
}

// 加载指定模式的菜单和权限
const loadModeMenuAndPermissions = async (mode) => {
  try {
    const response = await workModeApi.switchMode(mode)
    
    if (response.code === 200) {
      const { menu_items, permissions } = response.data
      
      // 使用 nextTick 确保在下一个事件循环中更新，避免同步更新导致的递归
      await nextTick()
      
      // 更新用户store中的菜单，但不覆盖用户的完整权限
      userStore.setMenus(menu_items)
      // ❌ 注释掉权限覆盖，工作模式不应该改变用户的基础权限
      // userStore.setPermissions(permissions)
    }
  } catch (error) {
    console.error('加载模式菜单失败:', error)
  }
}

// 处理模式切换
const handleModeChange = async (mode) => {
  console.log('🎯 handleModeChange 被调用:', { 
    新模式: mode, 
    当前UI选择: currentMode.value,
    Store中的工作模式: userStore.workMode,
    是否正在切换: switching.value 
  })
  
  // 使用 userStore.workMode 进行比较，而不是 currentMode.value
  if (switching.value || userStore.workMode === mode) {
    console.log('⏸️ 跳过切换:', switching.value ? '正在切换中' : 'Store中模式相同')
    return
  }
  
  try {
    switching.value = true
    
    // 添加短暂延迟，让loading动画更明显
    await new Promise(resolve => setTimeout(resolve, 300))
    
    const response = await workModeApi.switchMode(mode)
    
    if (response.code === 200) {
      const { menu_items, permissions } = response.data
      
      console.log('🎯 开始切换工作模式:', {
        新模式: mode,
        旧模式: userStore.workMode,
        新菜单长度: menu_items?.length || 0
      })
      
      // 先更新工作模式，触发响应式更新
      userStore.setWorkMode(mode)
      currentMode.value = mode
      
      // 等待一个tick后更新菜单，确保工作模式变化被检测到
      await nextTick()
      
      // 清空旧菜单，触发菜单变化检测
      userStore.setMenus([])
      
      // 再等待一个tick
      await nextTick()
      
      // 设置新菜单，但不覆盖用户的完整权限
      userStore.setMenus(menu_items)
      // ❌ 注释掉权限覆盖，工作模式切换不应该改变用户的基础权限
      // userStore.setPermissions(permissions)
      
      console.log('✅ 工作模式切换完成')
      
      // 显示成功消息，使用更明显的样式
      ElMessage({
        message: `已切换到${modeConfig[mode].label}模式`,
        type: 'success',
        duration: 3000,
        showClose: true,
        customClass: 'work-mode-success-message'
      })
      
      // 短暂延迟后跳转，让用户看到切换完成和动画效果
      setTimeout(async () => {
        await router.push('/dashboard')
      }, 500)
    }
  } catch (error) {
    ElMessage.error('切换工作模式失败: ' + error.message)
    console.error('切换模式失败:', error)
    // 恢复之前的选择
    currentMode.value = userStore.workMode // 恢复为store中的模式
  } finally {
    switching.value = false
  }
}

// 监听 currentMode 变化（调试用）
watch(
  () => currentMode.value,
  (newMode, oldMode) => {
    console.log('👀 currentMode 变化:', { 旧值: oldMode, 新值: newMode })
  }
)

onMounted(async () => {
  // 延迟执行避免与其他组件初始化冲突
  await nextTick()
  console.log('🔥 WorkModeSwitcher 组件挂载，开始初始化')
  fetchUserModes()
})
</script>

<style scoped>
.work-mode-switcher {
  display: inline-block;
  margin: 0 8px;
  position: relative;
}

.switching-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease;
}

.switching-content {
  background: white;
  padding: 24px 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  color: #333;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.rotating {
  animation: rotate 1s linear infinite;
  font-size: 20px;
  color: var(--el-color-primary);
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.mode-selector {
  width: 140px;
  height: 32px;
  transition: all 0.3s ease;
}

.mode-selector:disabled {
  opacity: 0.6;
}

.mode-selector :deep(.el-input__inner) {
  height: 32px;
  line-height: 32px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  transition: all 0.3s ease;
}

.mode-selector :deep(.el-input__suffix) {
  height: 32px;
  line-height: 32px;
}

.mode-option {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.mode-icon {
  margin-right: 8px;
  font-size: 16px;
  color: var(--el-color-primary);
}

.mode-text {
  flex: 1;
  font-size: 14px;
}

.mode-disabled-tip {
  font-size: 12px;
  color: var(--el-color-info);
  margin-left: 8px;
}

:deep(.el-select-dropdown__item.is-disabled) {
  color: var(--el-color-info) !important;
  cursor: not-allowed;
}

:deep(.el-select-dropdown__item.is-disabled .mode-icon) {
  color: var(--el-color-info) !important;
}
</style> 