import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
// 导入Element Plus中文语言包
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import App from './App.vue'
import router from './router'
import store from './store'
import './router/permission'

// 导入权限相关
import PermissionPlugin from './utils/permission'
import PermissionWrapper from './components/PermissionWrapper.vue'

const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册全局组件
app.component('PermissionWrapper', PermissionWrapper)

// 使用插件
app.use(ElementPlus, {
  locale: zhCn,
})
app.use(store)
app.use(router)
app.use(PermissionPlugin)

app.mount('#app')
