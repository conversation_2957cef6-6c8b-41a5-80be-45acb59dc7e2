// 全局样式变量
:root {
  --primary-color: #409EFF;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  
  --text-color-primary: #303133;
  --text-color-regular: #606266;
  --text-color-secondary: #909399;
  
  --border-color-base: #DCDFE6;
  --border-color-light: #E4E7ED;
  --border-color-lighter: #EBEEF5;
  --border-color-extra-light: #F2F6FC;
  
  --background-color-base: #F5F7FA;
}

// 全局通用样式
.app-container {
  padding: 20px;
}

// flex布局工具类
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

// margin工具类
.mt-10 {
  margin-top: 10px;
}

.mr-10 {
  margin-right: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.ml-10 {
  margin-left: 10px;
}

// padding工具类
.p-10 {
  padding: 10px;
}

.pt-10 {
  padding-top: 10px;
}

.pr-10 {
  padding-right: 10px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pl-10 {
  padding-left: 10px;
}

// 文本工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: var(--primary-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-danger {
  color: var(--danger-color);
}

.text-info {
  color: var(--info-color);
}

// 边框工具类
.border {
  border: 1px solid var(--border-color-base);
}

.border-t {
  border-top: 1px solid var(--border-color-base);
}

.border-r {
  border-right: 1px solid var(--border-color-base);
}

.border-b {
  border-bottom: 1px solid var(--border-color-base);
}

.border-l {
  border-left: 1px solid var(--border-color-base);
}

// 圆角工具类
.rounded {
  border-radius: 4px;
}

.rounded-lg {
  border-radius: 8px;
}

.rounded-full {
  border-radius: 9999px;
}

// 阴影工具类
.shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
}

.shadow-lg {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--border-color-extra-light);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--text-color-secondary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-color-regular);
}

// Element Plus 组件全局样式
.el-select .el-input {
  width: 240px;
}

.el-select .el-input__wrapper {
  width: 240px;
}

.el-select__popper.el-popper {
  width: 240px !important;
  
  .el-select-dropdown__wrap {
    width: 100%;
  }
  
  .el-select-dropdown__list {
    width: 100%;
  }
}

// gap工具类
.gap-1 {
  gap: 4px;
}

.gap-2 {
  gap: 8px;
}

.gap-3 {
  gap: 12px;
}

.gap-4 {
  gap: 16px;
}

.gap-5 {
  gap: 20px;
} 