// 计划类型
export const PLAN_TYPE = {
  FORMAL: 'normal',
  TEST: 'test'
}

export const PLAN_TYPE_MAP = {
  [PLAN_TYPE.FORMAL]: '正式',
  [PLAN_TYPE.TEST]: '测试'
}

// 审核状态
export const AUDIT_STATUS = {
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected'
}

export const AUDIT_STATUS_MAP = {
  [AUDIT_STATUS.PENDING]: '待审核',
  [AUDIT_STATUS.APPROVED]: '已通过',
  [AUDIT_STATUS.REJECTED]: '已拒绝'
}

// 投放状态
export const DELIVERY_STATUS = {
  INIT: 'init',
  CONFIGURING: 'configuring',
  WAIT: 'wait',
  RUNNING: 'running',
  STOPPED: 'stopped',
  STOPPED_AUTO: 'stopped_auto',
  COMPLETED: 'completed'
}

export const DELIVERY_STATUS_MAP = {
  [DELIVERY_STATUS.INIT]: '初始状态',
  [DELIVERY_STATUS.CONFIGURING]: '配置中',
  [DELIVERY_STATUS.WAIT]: '待开始',
  [DELIVERY_STATUS.RUNNING]: '投放中',
  [DELIVERY_STATUS.STOPPED]: '手动停投',
  [DELIVERY_STATUS.STOPPED_AUTO]: '自动停投',
  [DELIVERY_STATUS.COMPLETED]: '已完结'
}

// 投放模式
export const DELIVERY_MODE = {
  NORMAL: 1,
  SPEED: 2,
  SLOW: 3
}

export const DELIVERY_MODE_MAP = {
  [DELIVERY_MODE.NORMAL]: '正常投放',
  [DELIVERY_MODE.SPEED]: '加速投放',
  [DELIVERY_MODE.SLOW]: '减速投放'
}

// 状态标签类型映射
export const STATUS_TAG_TYPE_MAP = {
  [AUDIT_STATUS.PENDING]: 'warning',
  [AUDIT_STATUS.APPROVED]: 'success', 
  [AUDIT_STATUS.REJECTED]: 'danger',
  [DELIVERY_STATUS.INIT]: 'info',
  [DELIVERY_STATUS.CONFIGURING]: 'warning',
  [DELIVERY_STATUS.WAIT]: 'warning',
  [DELIVERY_STATUS.RUNNING]: 'success',
  [DELIVERY_STATUS.STOPPED]: 'danger',
  [DELIVERY_STATUS.STOPPED_AUTO]: 'danger',
  [DELIVERY_STATUS.COMPLETED]: 'info'
}

// 广告类型
export const AD_TYPE_MAP = {
  'alipay_h5': '支付宝H5',
  'wechat_h5': '微信H5',
  'browser_h5': '浏览器H5',
  'wechat_mp': '微信小程序',
  'alipay_mp': '支付宝小程序'
}

// 角色定义
export const ROLE = {
  MEDIA: 1,      // 媒介
  OPERATOR: 2,   // 运营
  ADMIN: 3,      // 管理员
  FINANCE: 4,    // 财务
  ADVERTISER: 5  // 投手
}

export const ROLE_MAP = {
  [ROLE.MEDIA]: '媒介',
  [ROLE.OPERATOR]: '运营',
  [ROLE.ADMIN]: '管理员',
  [ROLE.FINANCE]: '财务',
  [ROLE.ADVERTISER]: '投手'
}

// 向后兼容,保留旧的常量名
export const USER_ROLE = ROLE
export const USER_ROLE_MAP = ROLE_MAP 