# API路由修复说明

## 问题描述
前端调用的权限相关API路由与后端实际实现的路由不匹配，导致404错误。

## 修复内容

### 1. 用户权限API

#### 修复前 (404错误)
```javascript
// 获取用户权限
GET /api/v1/users/{userId}/permissions

// 检查用户权限  
POST /api/v1/users/{userId}/permissions/check
```

#### 修复后 (匹配后端)
```javascript
// 获取用户权限
GET /api/v1/user-permissions?user_id={userId}

// 检查用户权限
GET /api/v1/user-permissions/check?user_id={userId}&permission_code={code}
```

### 2. 角色权限API

#### 修复前
```javascript
// 获取角色权限
GET /api/v1/roles/{roleId}/permissions

// 更新角色权限
PUT /api/v1/roles/{roleId}/permissions
```

#### 修复后
```javascript
// 获取角色权限
GET /api/v1/role-permissions?role_id={roleId}

// 分配角色权限
POST /api/v1/role-permissions/assign
```

## 后端实际路由

根据后端代码分析，实际的API路由如下：

### 角色管理
- `GET /api/v1/roles` - 获取角色列表
- `POST /api/v1/roles` - 创建角色
- `GET /api/v1/roles/options` - 获取角色选项
- `GET /api/v1/roles/{id}` - 获取角色详情
- `PUT /api/v1/roles/{id}` - 更新角色
- `DELETE /api/v1/roles/{id}` - 删除角色

### 权限管理
- `GET /api/v1/permissions` - 获取权限列表
- `POST /api/v1/permissions` - 创建权限
- `GET /api/v1/permissions/modules` - 获取权限模块
- `GET /api/v1/permissions/{id}` - 获取权限详情
- `PUT /api/v1/permissions/{id}` - 更新权限
- `DELETE /api/v1/permissions/{id}` - 删除权限

### 角色权限关联
- `GET /api/v1/role-permissions?role_id={roleId}` - 获取角色权限
- `POST /api/v1/role-permissions/assign` - 分配权限给角色
- `POST /api/v1/role-permissions/revoke` - 撤销角色权限

### 用户权限管理
- `GET /api/v1/user-permissions?user_id={userId}` - 获取用户权限列表
- `GET /api/v1/user-permissions/check?user_id={userId}&permission_code={code}` - 检查用户权限

### 权限日志
- `GET /api/v1/permission-logs` - 获取权限操作日志

## 修复的文件

1. `frontend/admin/src/api/role.js` - 修复权限相关API调用
2. `frontend/admin/src/store/modules/user.js` - 用户权限获取（无需修改，已正确使用修复后的API）

## 注意事项

1. 所有权限相关的API都需要JWT认证
2. 用户权限API使用查询参数而不是路径参数
3. 角色权限分配使用POST而不是PUT方法
4. 需要确保后端路由已正确配置并启用

## 测试建议

修复后建议测试以下功能：

1. 用户登录后权限获取
2. 账户管理页面的角色和部门选择
3. 权限相关的页面和按钮控制
4. 403权限不足页面的权限信息显示 