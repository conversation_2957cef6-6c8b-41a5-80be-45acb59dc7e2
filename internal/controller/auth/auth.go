package auth

import (
	"context"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"

	v1 "ad-pro-v2/api/auth/v1"
	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/service"
)

// Controller Auth API控制器
type Controller struct{}

// Login 处理登录请求
func (c *Controller) Login(ctx context.Context, req *v1.LoginReq) (res *v1.LoginRes, err error) {
	// 调用service层处理登录
	user, token, err := service.Auth().Login(ctx, req.Email, req.Password)
	if err != nil {
		// 将业务错误包装为标准响应错误
		return nil, gerror.NewCode(gcode.CodeBusinessValidationFailed, err.Error())
	}

	if user == nil {
		return nil, gerror.NewCode(gcode.CodeNotFound, "用户不存在")
	}

	return &v1.LoginRes{
		Token: token,
		User: &v1.User{
			Id:    uint(user.Id),
			Email: user.Email,
			Name:  user.Name,
		},
	}, nil
}

// ChangePassword 处理修改密码请求
func (c *Controller) ChangePassword(ctx context.Context, req *v1.ChangePasswordReq) (res *v1.ChangePasswordRes, err error) {
	// 从上下文中获取用户ID
	userId := ctx.Value("userId").(uint64)
	if userId == 0 {
		return nil, gerror.NewCode(gcode.CodeNotAuthorized, "未登录或非法访问")
	}

	// 调用service层处理修改密码
	err = service.Auth().ChangePassword(ctx, userId, req.OldPassword, req.NewPassword)
	if err != nil {
		return nil, gerror.NewCode(gcode.CodeBusinessValidationFailed, err.Error())
	}

	return &v1.ChangePasswordRes{
		Message: "密码修改成功",
	}, nil
}

// Info 获取用户信息
func (c *Controller) Info(ctx context.Context, req *v1.UserInfoReq) (res *v1.UserInfoRes, err error) {
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, gerror.New("用户未登录")
	}

	var dbUser *model.User
	err = dao.Users.Ctx(ctx).Where(dao.Users.Columns().Id, userInfo.Id).Scan(&dbUser)
	if err != nil {
		return nil, err
	}

	return &v1.UserInfoRes{
		Id:       dbUser.Id,
		Name:     dbUser.Username,
		Email:    dbUser.Email,
		RealName: dbUser.RealName,
		Avatar:   dbUser.Avatar,
		Role:     dbUser.Role,
		Status:   dbUser.Status,
	}, nil
}

// Logout 登出
func (c *Controller) Logout(ctx context.Context, req *model.LogoutReq) (res *model.LogoutRes, err error) {
	return &model.LogoutRes{}, nil
}
