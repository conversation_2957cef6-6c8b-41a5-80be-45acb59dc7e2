package platform_plan

import (
	"context"

	v1 "ad-pro-v2/api/platform_plan/v1"
	"ad-pro-v2/internal/service"
)

// Controller PlatformPlan控制器
type Controller struct{}

// List 获取平台计划列表
func (c *Controller) List(ctx context.Context, req *v1.ListReq) (res *v1.ListRes, err error) {
	return service.PlatformPlan().List(ctx, req)
}

// Create 创建平台计划
func (c *Controller) Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error) {
	res, err = service.PlatformPlan().Create(ctx, req)
	return
}

// Update 更新平台计划
func (c *Controller) Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error) {
	res, err = service.PlatformPlan().Update(ctx, req)
	return
}

// Delete 删除平台计划
func (c *Controller) Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error) {
	res, err = service.PlatformPlan().Delete(ctx, req)
	return
}

// UpdatePlanType 更新计划类型
func (c *Controller) UpdatePlanType(ctx context.Context, req *v1.UpdatePlanTypeReq) (res *v1.UpdatePlanTypeRes, err error) {
	err = service.PlatformPlan().UpdatePlanType(ctx, req.ID, req.PlanType)
	if err != nil {
		return nil, err
	}

	res = &v1.UpdatePlanTypeRes{}
	return
}

// MarketTargets 获取市场目标列表
func (c *Controller) MarketTargets(ctx context.Context, req *v1.MarketTargetsReq) (res *v1.MarketTargetsRes, err error) {
	targets, err := service.PlatformPlan().GetMarketTargets(ctx, req.PlatformType)
	if err != nil {
		return nil, err
	}

	return &v1.MarketTargetsRes{
		Targets: targets,
	}, nil
}
