package model

import (
	"context"

	v1 "ad-pro-v2/api/model/v1"
	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/model/entity"
	"ad-pro-v2/internal/service"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// Controller Model控制器
type Controller struct{}

// Create 创建模型
func (c *Controller) Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 创建模型
	input := &entity.Model{
		Name: req.Name,
	}
	id, err := service.Model.Create(ctx, input)
	if err != nil {
		return nil, err
	}

	return &v1.CreateRes{Id: id}, nil
}

// Delete 删除模型
func (c *Controller) Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 删除模型
	err = service.Model.Delete(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &v1.DeleteRes{}, nil
}

// Update 更新模型
func (c *Controller) Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 更新模型
	input := &entity.Model{
		Id:   uint(req.Id),
		Name: req.Name,
	}
	err = service.Model.Update(ctx, input)
	if err != nil {
		return nil, err
	}

	return &v1.UpdateRes{}, nil
}

// List 获取模型列表
func (c *Controller) List(ctx context.Context, req *v1.ListReq) (res *v1.ListRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 获取模型列表
	input := &service.ModelListInput{
		Page:     req.Page,
		PageSize: req.PageSize,
		Name:     req.ModelName,
	}
	output, err := service.Model.GetList(ctx, input)
	if err != nil {
		return nil, err
	}

	// 转换为响应结构
	var list []*v1.ModelInfo
	for _, item := range output.List {
		list = append(list, &v1.ModelInfo{
			Id:        item.Id,
			Name:      item.Name,
			CreatedAt: item.CreatedAt,
		})
	}

	return &v1.ListRes{
		List:  list,
		Total: output.Total,
		Page:  output.Page,
	}, nil
}

// Import 导入模型
func (c *Controller) Import(ctx context.Context, req *v1.ImportReq) (res *v1.ImportRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 获取请求对象
	r := g.RequestFromCtx(ctx)
	if r == nil {
		return nil, gerror.New("无法获取请求对象")
	}

	// 导入模型
	input := &model.ModelImportInput{
		Name:    req.ModelName,
		Request: r,
	}
	err = service.Model.ImportModel(ctx, input)
	if err != nil {
		return nil, err
	}

	return &v1.ImportRes{}, nil
}
