package password

import (
	v1 "ad-pro-v2/api/password/v1"
	"ad-pro-v2/internal/service"
	"context"
)

// Controller 口令控制器
type Controller struct{}

// NewController 创建控制器实例
func NewController() *Controller {
	return &Controller{}
}

// Create 创建口令组及口令
func (c *Controller) Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error) {
	// 转换请求参数
	var passwords []service.PasswordInfo
	for _, p := range req.Passwords {
		passwords = append(passwords, service.PasswordInfo{
			Name: p.Name,
			Pid:  p.Pid,
		})
	}

	// 调用服务层创建口令
	err = service.Password().CreateWithGroup(ctx, req.GroupName, req.CategoryId, passwords)
	if err != nil {
		return nil, err
	}

	return &v1.CreateRes{}, nil
}

// List 获取口令组列表
func (c *Controller) List(ctx context.Context, req *v1.ListReq) (res *v1.ListRes, err error) {
	// 调用服务层获取列表
	groups, total, err := service.Password().List(ctx, req.Page, req.PageSize, req.GroupName, req.CategoryId)
	if err != nil {
		return nil, err
	}

	// 转换为响应结构
	var list []v1.GroupInfo
	for _, group := range groups {
		groupInfo := v1.GroupInfo{
			Id:         group.Id,
			Name:       group.Name,
			CategoryId: int64(group.CategoryId),
			CreatedAt:  group.CreatedAt,
			UpdatedAt:  group.UpdatedAt,
		}

		// 转换口令信息
		for _, pwd := range group.Passwords {
			groupInfo.Passwords = append(groupInfo.Passwords, v1.PasswordRes{
				Name: pwd.Name,
				Pid:  pwd.Pid,
				Id:   pwd.Id,
			})
		}

		list = append(list, groupInfo)
	}

	return &v1.ListRes{
		List:  list,
		Total: total,
		Page:  req.Page,
	}, nil
}

// OriList 获取原始口令列表
func (c *Controller) OriList(ctx context.Context, req *v1.OriListReq) (res *v1.OriListRes, err error) {
	// 调用服务层获取列表
	passwords, total, err := service.Password().OriList(ctx, req.Page, req.PageSize, req.CategoryId)
	if err != nil {
		return nil, err
	}

	// 转换为响应结构
	var list []v1.OriPasswordInfo
	for _, pwd := range passwords {
		list = append(list, v1.OriPasswordInfo{
			Name: pwd.Name,
			Pid:  pwd.Pid,
		})
	}

	return &v1.OriListRes{
		List:  list,
		Total: total,
		Page:  req.Page,
	}, nil
}

// Update 编辑口令组及口令
func (c *Controller) Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error) {
	// 转换请求参数
	var passwords []service.PasswordInfo
	for _, p := range req.Passwords {
		passwords = append(passwords, service.PasswordInfo{
			Name: p.Name,
			Pid:  p.Pid,
		})
	}

	// 调用服务层更新口令组
	err = service.Password().Update(ctx, req.Id, req.GroupName, req.CategoryId, passwords)
	if err != nil {
		return nil, err
	}

	return &v1.UpdateRes{}, nil
}

// Delete 删除口令组
func (c *Controller) Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error) {
	// 调用服务层删除口令组
	err = service.Password().Delete(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	return &v1.DeleteRes{}, nil
}
