package sync

import (
	"context"
	"time"

	v1 "ad-pro-v2/api/sync/v1"
	"ad-pro-v2/internal/service"
)

// Controller 同步控制器
type Controller struct{}

// DenghuoPlus 同步灯火数据
func (c *Controller) DenghuoPlus(ctx context.Context, req *v1.DenghuoPlusReq) (res *v1.DenghuoPlusRes, err error) {
	// 1. 解析日期
	startDate, err := time.ParseInLocation(time.DateOnly, req.StartDate, time.Local)
	if err != nil {
		return nil, err
	}

	endDate, err := time.ParseInLocation(time.DateOnly, req.EndDate, time.Local)
	if err != nil {
		return nil, err
	}

	// 2. 调用服务层同步数据
	err = service.Sync().SyncDenghuoPlusData(ctx, req.MediaId, req.AgentId, startDate, endDate)
	if err != nil {
		return nil, err
	}

	return &v1.DenghuoPlusRes{
		Status: "success",
	}, nil
}
