package ad_slot_plan

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"

	v1 "ad-pro-v2/api/ad_slot_plan/v1"
	"ad-pro-v2/internal/constants"
	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/model/entity"
	"ad-pro-v2/internal/service"
)

// Controller AdSlotPlan控制器
type Controller struct{}

func (c *Controller) GetAdPage(ctx context.Context, req *v1.GetAdPageReq) (res *v1.GetAdPageRes, err error) {
	g.Log().Debugf(ctx, "获取广告页面请求: slotId=%d", req.SoltId)

	// way 有客户自己决定，使用插件的组件能力，则传递 1，页面能力则传递 2
	// way =1 走的是方案1，组件的形式
	//   组件的形式下，会根据 DialogType 的值决定是否弹框以及弹框的类型
	// way =2 走的是方案2，中间页的形式
	//   中间页的形式下，会根据 DialogType 的值决定是否弹框以及弹框的类型
	//   如果有背景图，那么中间页会显示背景图，否则不显示
	//   点击背景图会跳转，如果跳转链接为空，则取DialogLinkInfo的内容
	//   如果不为空，BackgroundLink 的结构也要配置成DialogLinkInfo的结构
	// way = 3 走的是接口的形式，直接调用GetAdLink

	// 初始化响应
	res = &v1.GetAdPageRes{
		BackgroundImage: "",  // 硬编码的背景图URL
		BackgroundLink:  nil, // 硬编码的背景图链接
		DialogType:      1,   // 默认显示系统弹窗
		Dialog: map[string]any{
			"title":         "",
			"content":       "支付成功",
			"button":        "确定",
			"cancel_button": "",
		},
	}

	// 获取广告链接
	adLink, err := c.GetAdLinkV2(ctx, &v1.GetAdLinkReq{
		SoltId: req.SoltId,
		OpenId: req.OpenId,
	})
	if err != nil {
		return nil, err
	}

	res.DialogLinkInfo = adLink

	if len(adLink.CreativeInfo) > 0 {
		res.Dialog = adLink.CreativeInfo
		res.DialogType = 2
	}

	return res, nil
}

func (c *Controller) GetAdLinkV2(ctx context.Context, req *v1.GetAdLinkReq) (res *v1.GetAdLinkRes, err error) {
	startTime := time.Now()
	g.Log().Infof(ctx, "开始处理广告链接请求: slotId=%d", req.SoltId)

	defer func() {
		if err != nil {
			g.Log().Errorf(ctx, "获取广告链接失败: %v", err)
		}
		g.Log().Infof(ctx, "广告链接请求处理完成: slotId=%d, 耗时=%dms", req.SoltId, time.Since(startTime).Milliseconds())
	}()

	g.Log().Debugf(ctx, "获取广告链接请求: slotId=%d", req.SoltId)

	res = &v1.GetAdLinkRes{}

	// 生成Redis缓存键
	plansKey := fmt.Sprintf("ad_plans:slot:%d", req.SoltId)
	callCountKey := fmt.Sprintf("ad_plans:slot:%d:call_count", req.SoltId)

	// 计算今天结束时间作为缓存过期时间
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
	expireSeconds := int64(today.Sub(now).Seconds())
	if expireSeconds <= 0 {
		expireSeconds = 86400 // 如果已经是今天的最后时刻，设置为24小时
	}

	// 尝试从缓存获取广告计划列表
	var plans []*model.AdPlanInfo
	cache, err := g.Redis().Get(ctx, plansKey)
	if err != nil {
		g.Log().Errorf(ctx, "Redis获取广告计划失败: %v", err)
		return nil, err
	}

	g.Log().Infof(ctx, "Redis获取广告计划完成: slotId=%d, 耗时=%dms", req.SoltId, time.Since(startTime).Milliseconds())

	// 缓存不存在，从数据库读取并缓存
	if cache.IsEmpty() {
		g.Log().Debug(ctx, "缓存不存在，从数据库查询广告计划")

		// 查询条件：审核状态为通过，投放状态为投放中，按id升序
		var adPlans []*entity.AdSlotPlan
		err = g.DB().Model("ad_slot_plans").
			Where("ad_slot_id", req.SoltId).
			Where("audit_status", constants.PLAN_AUDIT_STATUS_APPROVED).      // 审核通过
			Where("delivery_status", constants.PLAN_DELIVERY_STATUS_RUNNING). // 投放中
			OrderAsc("id").
			Scan(&adPlans)

		if err != nil {
			g.Log().Errorf(ctx, "查询广告计划失败: %v", err)
			return nil, err
		}

		// 如果没有符合条件的计划，直接返回空
		if len(adPlans) == 0 {
			g.Log().Info(ctx, "没有找到符合条件的广告计划")
			return res, nil
		}

		// 获取所有计划的ID
		planIds := make([]uint64, len(adPlans))
		for i, plan := range adPlans {
			planIds[i] = plan.ID
		}

		// 获取计划对应的推广链接
		var promotionZones []struct {
			PlanID          uint64 `json:"plan_id"`
			PromotionLink   string `json:"promotion_link"`
			PromotionParams string `json:"promotion_params"`
		}

		// 直接查询所有计划的推广链接
		err = g.DB().Model("promotion_zones").
			Fields("plan_id, promotion_link,promotion_params").
			Where("plan_id IN (?)", planIds).
			OrderAsc("plan_id").
			OrderAsc("id"). // 确保每个计划的推广位按ID升序排序
			Scan(&promotionZones)

		if err != nil {
			g.Log().Errorf(ctx, "查询推广链接失败: %v", err)
			return nil, err
		}

		// 创建plan_id到promotion_link的映射，只保留每个计划的第一个链接
		promotionLinkMap := make(map[uint64]string)
		for _, pz := range promotionZones {
			// 如果该计划ID还没有对应的链接，则保存这个链接（因为已经按ID排序，所以是最小ID的链接）
			if _, exists := promotionLinkMap[pz.PlanID]; !exists {
				promotionLinkMap[pz.PlanID] = pz.PromotionParams
			}
		}

		// 提取需要的计划信息
		plans = make([]*model.AdPlanInfo, 0, len(adPlans))
		for _, plan := range adPlans {
			promotionLinkInfo, ok := promotionLinkMap[plan.ID]
			if !ok || promotionLinkInfo == "" {
				continue
			}
			var info map[string]any
			err = json.Unmarshal([]byte(promotionLinkInfo), &info)
			if err != nil {
				g.Log().Errorf(ctx, "解析推广链接参数失败: %v", err)
				continue
			}
			plans = append(plans, &model.AdPlanInfo{
				ID:                plan.ID,
				PromotionLinkInfo: info,
				AdCreativeID:      plan.AdCreativeID,
				StartTime:         plan.StartDate,
				EndTime:           plan.EndDate,
			})
		}

		// 如果没有计划，直接返回空
		if len(plans) == 0 {
			g.Log().Info(ctx, "没有可用的广告计划")
			return res, nil
		}

		// 序列化计划信息并存入Redis
		plansJson, err := json.Marshal(plans)
		if err != nil {
			g.Log().Errorf(ctx, "序列化广告计划失败: %v", err)
			return nil, err
		}

		// 设置缓存，过期时间到今天结束
		err = g.Redis().SetEX(ctx, plansKey, string(plansJson), expireSeconds)
		if err != nil {
			g.Log().Errorf(ctx, "缓存广告计划失败: %v", err)
			// 继续执行，不影响返回结果
		}

		// 初始化调用次数为0
		err = g.Redis().SetEX(ctx, callCountKey, 0, expireSeconds)
		if err != nil {
			g.Log().Errorf(ctx, "初始化调用次数失败: %v", err)
		}
	} else {
		// 从缓存解析广告计划
		err = json.Unmarshal([]byte(cache.String()), &plans)
		if err != nil {
			g.Log().Errorf(ctx, "解析缓存的广告计划失败: %v", err)
			return nil, err
		}
	}

	// 处理计划，处理在当前时间生效的计划
	newPlans := make([]*model.AdPlanInfo, 0, len(plans))
	for _, plan := range plans {
		if plan.StartTime.Before(now) && (plan.EndTime == nil || plan.EndTime.After(now)) {
			newPlans = append(newPlans, plan)
		}
	}

	plans = newPlans

	// 如果没有计划，直接返回空
	if len(plans) == 0 {
		g.Log().Info(ctx, "没有可用的广告计划")
		return res, nil
	}

	// 获取并递增调用次数
	callCount := 0
	callCountCache, err := g.Redis().Get(ctx, callCountKey)
	if err == nil && !callCountCache.IsEmpty() {
		callCount = callCountCache.Int()
	}

	// 计算当前应该返回的计划索引
	index := callCount % len(plans)

	// 获取当前索引对应的计划
	plan := plans[index]
	res.ID = plan.ID
	res.PromotionLinkInfo = plan.PromotionLinkInfo

	// 如果计划有关联的创意ID，从创意缓存中获取创意信息
	if plan.AdCreativeID > 0 {
		creativeKey := fmt.Sprintf("ad_creative:%d", plan.AdCreativeID)
		creativeCache, err := g.Redis().Get(ctx, creativeKey)
		if err == nil && !creativeCache.IsEmpty() {
			var creativeInfo map[string]any
			err = json.Unmarshal([]byte(creativeCache.String()), &creativeInfo)
			if err == nil {
				res.CreativeInfo = creativeInfo
			}

			g.Log().Infof(ctx, "Redis获取创意信息完成: slotId=%d, 耗时=%dms", req.SoltId, time.Since(startTime).Milliseconds())
		} else {
			// 缓存不存在或出错，从数据库查询创意信息
			var creative entity.AdCreatives
			err = g.DB().Model("ad_creatives").
				Where("id", plan.AdCreativeID).
				Scan(&creative)

			if err == nil {
				// 解析热区信息
				var hotAreas []map[string]any
				if creative.HotAreas != "" {
					err = json.Unmarshal([]byte(creative.HotAreas), &hotAreas)
					if err != nil {
						g.Log().Errorf(ctx, "解析创意热区失败: %v", err)
					}
				}

				creativeInfo := map[string]any{
					"id":               creative.Id,
					"name":             creative.Name,
					"hot_areas":        hotAreas,
					"image_url":        creative.ImageUrl,
					"image_area":       creative.ImageArea,
					"background_color": creative.BackgroundColor,
				}

				res.CreativeInfo = creativeInfo

				// 序列化创意信息并存入Redis
				creativeJson, err := json.Marshal(creativeInfo)
				if err == nil {
					err = g.Redis().SetEX(ctx, creativeKey, string(creativeJson), expireSeconds)
					if err != nil {
						g.Log().Errorf(ctx, "缓存创意信息失败: %v", err)
					}
				}
			} else {
				g.Log().Errorf(ctx, "查询创意信息失败: %v", err)
			}
		}
	}

	// 更新调用次数
	nextCallCount := callCount + 1
	err = g.Redis().SetEX(ctx, callCountKey, nextCallCount, expireSeconds)
	if err != nil {
		g.Log().Errorf(ctx, "更新调用次数失败: %v", err)
		// 继续执行，不影响返回结果
	}

	g.Log().Infof(ctx, "更新调用次数完成: slotId=%d, 耗时=%dms", req.SoltId, time.Since(startTime).Milliseconds())

	g.Log().Debugf(ctx, "返回广告链接: %v, 计划ID: %d, 调用次数: %d->%d", res.PromotionLinkInfo, plan.ID, callCount, nextCallCount)
	return res, nil
}

// GetAdLink 获取广告链接
func (c *Controller) GetAdLink(ctx context.Context, req *v1.GetAdLinkReq) (res *v1.GetAdLinkRes, err error) {
	startTime := time.Now()
	g.Log().Infof(ctx, "开始处理广告链接请求: slotId=%d, openId=%s", req.SoltId, req.OpenId)

	defer func() {
		if err != nil {
			g.Log().Errorf(ctx, "获取广告链接失败: %v", err)
		}
		g.Log().Infof(ctx, "广告链接请求处理完成: slotId=%d, openId=%s, 耗时=%dms", req.SoltId, req.OpenId, time.Since(startTime).Milliseconds())
	}()

	g.Log().Debugf(ctx, "获取广告链接请求: slotId=%d, openId=%s", req.SoltId, req.OpenId)

	res = &v1.GetAdLinkRes{}

	// 生成Redis缓存键
	plansKey := fmt.Sprintf("ad_plans:slot:%d", req.SoltId)
	indexKey := fmt.Sprintf("ad_plans:slot:%d:index:%s", req.SoltId, req.OpenId)

	// 计算今天结束时间作为缓存过期时间
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
	expireSeconds := int64(today.Sub(now).Seconds())
	if expireSeconds <= 0 {
		expireSeconds = 86400 // 如果已经是今天的最后时刻，设置为24小时
	}

	// 尝试从缓存获取广告计划列表
	var plans []*model.AdPlanInfo
	cache, err := g.Redis().Get(ctx, plansKey)
	if err != nil {
		g.Log().Errorf(ctx, "Redis获取广告计划失败: %v", err)
		return nil, err
	}

	g.Log().Infof(ctx, "Redis获取广告计划完成: slotId=%d, openId=%s, 耗时=%dms", req.SoltId, req.OpenId, time.Since(startTime).Milliseconds())

	// 缓存不存在，从数据库读取并缓存
	if cache.IsEmpty() {
		g.Log().Debug(ctx, "缓存不存在，从数据库查询广告计划")

		// 查询条件：审核状态为通过，投放状态为投放中，按id升序
		var adPlans []*entity.AdSlotPlan
		err = g.DB().Model("ad_slot_plans").
			Where("ad_slot_id", req.SoltId).
			Where("audit_status", constants.PLAN_AUDIT_STATUS_APPROVED).      // 审核通过
			Where("delivery_status", constants.PLAN_DELIVERY_STATUS_RUNNING). // 投放中
			OrderAsc("id").
			Scan(&adPlans)

		if err != nil {
			g.Log().Errorf(ctx, "查询广告计划失败: %v", err)
			return nil, err
		}

		// 如果没有符合条件的计划，直接返回空
		if len(adPlans) == 0 {
			g.Log().Info(ctx, "没有找到符合条件的广告计划")
			return res, nil
		}

		// 获取所有计划的ID
		planIds := make([]uint64, len(adPlans))
		for i, plan := range adPlans {
			planIds[i] = plan.ID
		}

		// 获取计划对应的推广链接
		var promotionZones []struct {
			PlanID          uint64 `json:"plan_id"`
			PromotionLink   string `json:"promotion_link"`
			PromotionParams string `json:"promotion_params"`
		}

		// 直接查询所有计划的推广链接
		err = g.DB().Model("promotion_zones").
			Fields("plan_id, promotion_link,promotion_params").
			Where("plan_id IN (?)", planIds).
			OrderAsc("plan_id").
			OrderAsc("id"). // 确保每个计划的推广位按ID升序排序
			Scan(&promotionZones)

		if err != nil {
			g.Log().Errorf(ctx, "查询推广链接失败: %v", err)
			return nil, err
		}

		// 创建plan_id到promotion_link的映射，只保留每个计划的第一个链接
		promotionLinkMap := make(map[uint64]string)
		for _, pz := range promotionZones {
			// 如果该计划ID还没有对应的链接，则保存这个链接（因为已经按ID排序，所以是最小ID的链接）
			if _, exists := promotionLinkMap[pz.PlanID]; !exists {
				promotionLinkMap[pz.PlanID] = pz.PromotionParams
			}
		}

		// 提取需要的计划信息
		plans = make([]*model.AdPlanInfo, 0, len(adPlans))
		for _, plan := range adPlans {
			promotionLinkInfo, ok := promotionLinkMap[plan.ID]
			if !ok || promotionLinkInfo == "" {
				continue
			}
			var info map[string]any
			err = json.Unmarshal([]byte(promotionLinkInfo), &info)
			if err != nil {
				g.Log().Errorf(ctx, "解析推广链接参数失败: %v", err)
				continue
			}
			plans = append(plans, &model.AdPlanInfo{
				ID:                plan.ID,
				PromotionLinkInfo: info,
				AdCreativeID:      plan.AdCreativeID,
				StartTime:         plan.StartDate,
				EndTime:           plan.EndDate,
			})
		}

		// 如果没有计划，直接返回空
		if len(plans) == 0 {
			g.Log().Info(ctx, "没有可用的广告计划")
			return res, nil
		}

		// 序列化计划信息并存入Redis
		plansJson, err := json.Marshal(plans)
		if err != nil {
			g.Log().Errorf(ctx, "序列化广告计划失败: %v", err)
			return nil, err
		}

		// 设置缓存，过期时间到今天结束
		err = g.Redis().SetEX(ctx, plansKey, string(plansJson), expireSeconds)
		if err != nil {
			g.Log().Errorf(ctx, "缓存广告计划失败: %v", err)
			// 继续执行，不影响返回结果
		}
	} else {
		// 从缓存解析广告计划
		err = json.Unmarshal([]byte(cache.String()), &plans)
		if err != nil {
			g.Log().Errorf(ctx, "解析缓存的广告计划失败: %v", err)
			return nil, err
		}
	}

	// 处理计划，处理在当前时间生效的计划
	newPlans := make([]*model.AdPlanInfo, 0, len(plans))
	for _, plan := range plans {
		if plan.StartTime.Before(now) && (plan.EndTime == nil || plan.EndTime.After(now)) {
			newPlans = append(newPlans, plan)
		}
	}

	plans = newPlans

	// 如果没有计划，直接返回空
	if len(plans) == 0 {
		g.Log().Info(ctx, "没有可用的广告计划")
		return res, nil
	}

	// 获取当前用户的轮询索引
	indexCache, err := g.Redis().Get(ctx, indexKey)
	if err != nil {
		g.Log().Errorf(ctx, "获取轮询索引失败: %v", err)
		// 出错时默认返回第一个计划
		res.PromotionLinkInfo = plans[0].PromotionLinkInfo
		return res, nil
	}

	g.Log().Infof(ctx, "Redis获取轮询索引完成: slotId=%d, openId=%s, 耗时=%dms", req.SoltId, req.OpenId, time.Since(startTime).Milliseconds())

	// 计算当前应该返回的计划索引
	index := 0
	if !indexCache.IsEmpty() {
		index = indexCache.Int()
	}

	// 确保索引在有效范围内
	if index >= len(plans) {
		index = 0
	}

	// 获取当前索引对应的计划
	plan := plans[index]
	res.ID = plan.ID
	res.PromotionLinkInfo = plan.PromotionLinkInfo

	// 如果计划有关联的创意ID，从创意缓存中获取创意信息
	if plan.AdCreativeID > 0 {
		creativeKey := fmt.Sprintf("ad_creative:%d", plan.AdCreativeID)
		creativeCache, err := g.Redis().Get(ctx, creativeKey)
		if err == nil && !creativeCache.IsEmpty() {
			var creativeInfo map[string]any
			err = json.Unmarshal([]byte(creativeCache.String()), &creativeInfo)
			if err == nil {
				res.CreativeInfo = creativeInfo
			}

			g.Log().Infof(ctx, "Redis获取创意信息完成: slotId=%d, openId=%s, 耗时=%dms", req.SoltId, req.OpenId, time.Since(startTime).Milliseconds())
		} else {
			// 缓存不存在或出错，从数据库查询创意信息
			var creative entity.AdCreatives
			err = g.DB().Model("ad_creatives").
				Where("id", plan.AdCreativeID).
				Scan(&creative)

			if err == nil {
				// 解析热区信息
				var hotAreas []map[string]any
				if creative.HotAreas != "" {
					err = json.Unmarshal([]byte(creative.HotAreas), &hotAreas)
					if err != nil {
						g.Log().Errorf(ctx, "解析创意热区失败: %v", err)
					}
				}

				creativeInfo := map[string]any{
					"id":               creative.Id,
					"name":             creative.Name,
					"hot_areas":        hotAreas,
					"image_url":        creative.ImageUrl,
					"image_area":       creative.ImageArea,
					"background_color": creative.BackgroundColor,
				}

				res.CreativeInfo = creativeInfo

				// 序列化创意信息并存入Redis
				creativeJson, err := json.Marshal(creativeInfo)
				if err == nil {
					err = g.Redis().SetEX(ctx, creativeKey, string(creativeJson), expireSeconds)
					if err != nil {
						g.Log().Errorf(ctx, "缓存创意信息失败: %v", err)
					}
				}
			} else {
				g.Log().Errorf(ctx, "查询创意信息失败: %v", err)
			}
		}
	}

	// 更新轮询索引，为下次请求准备
	nextIndex := (index + 1) % len(plans)
	err = g.Redis().SetEX(ctx, indexKey, nextIndex, expireSeconds)
	if err != nil {
		g.Log().Errorf(ctx, "更新轮询索引失败: %v", err)
		// 继续执行，不影响返回结果
	}

	g.Log().Infof(ctx, "更新轮询索引完成: slotId=%d, openId=%s, 耗时=%dms", req.SoltId, req.OpenId, time.Since(startTime).Milliseconds())

	g.Log().Debugf(ctx, "返回广告链接: %s, 计划ID: %d, 索引: %d->%d", res.PromotionLinkInfo, plan.ID, index, nextIndex)
	return res, nil
}

// List 获取投放计划列表
func (c *Controller) List(ctx context.Context, req *v1.ListReq) (res *v1.ListRes, err error) {
	filters := make(map[string]interface{})

	// 使用新的筛选条件
	if req.Code != "" {
		filters["code"] = req.Code
	}
	if req.MediaID > 0 {
		filters["media_id"] = req.MediaID
	}
	if req.AdSlotID > 0 {
		filters["ad_slot_id"] = req.AdSlotID
	}

	loginUser := service.Context().GetUser(ctx)

	// 处理用户权限
	var queryUserId int
	if req.UserId > 0 {
		queryUserId = req.UserId
		g.Log().Debug(ctx, "Using provided user ID:", queryUserId)
	} else {
		if loginUser.Role == 2 || loginUser.Role == 3 || loginUser.Role == 4 {
			queryUserId = 0
			g.Log().Debug(ctx, "Admin/Operator/Finance role, not filtering by user")
		} else {
			queryUserId = loginUser.Id
			g.Log().Debug(ctx, "Using current user ID:", loginUser.Id)
		}
	}
	if queryUserId > 0 {
		filters["user_id"] = queryUserId
	}

	plans, total, err := service.AdSlotPlan().List(ctx, req.Page, req.Size, filters)
	if err != nil {
		return nil, err
	}

	// 处理返回数据，确保包含媒体和广告位信息
	for i := range plans {
		// 如果广告位已加载但媒体信息不完整
		if plans[i].AdSlot != nil && plans[i].AdSlot.MediaName == "" {
			// 尝试单独加载媒体信息
			mediaInfo, err := service.Media().GetById(ctx, int(plans[i].AdSlot.MediaId))
			if err == nil && mediaInfo != nil {
				plans[i].AdSlot.MediaName = mediaInfo.Name
			}
		}

		// 加载媒介（负责人）信息
		if plans[i].UserId > 0 {
			// 从数据库中查询用户信息
			var user struct {
				Name     string `json:"name"`
				RealName string `json:"real_name"`
			}
			err := g.DB().Model("users").Where("id", plans[i].UserId).Scan(&user)
			if err == nil {
				// 优先使用真实姓名，如果没有则使用用户名
				if user.RealName != "" {
					plans[i].UserName = user.RealName
				} else if user.Name != "" {
					plans[i].UserName = user.Name
				}
			}
		}
	}

	res = &v1.ListRes{
		List:  plans,
		Total: total,
	}
	return
}

// Get 获取投放计划详情
func (c *Controller) Get(ctx context.Context, req *v1.GetReq) (res *v1.GetRes, err error) {
	plan, err := service.AdSlotPlan().Get(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	res = &v1.GetRes{
		Plan: plan,
	}
	return
}

// Create 创建投放计划
func (c *Controller) Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error) {
	rels := make([]*entity.PlanRelateRels, 0, len(req.Rels))
	for _, rel := range req.Rels {
		rels = append(rels, &entity.PlanRelateRels{
			RelateType: rel.Type,
			RelateId:   rel.Id,
		})
	}

	plan := &model.AdSlotPlan{
		AdSlotPlan: &entity.AdSlotPlan{
			Type:             req.Type,
			AdProductID:      req.AdProductID,
			AdSlotID:         req.AdSlotID,
			AdCreativeID:     req.AdCreativeID,
			StartDate:        req.StartDate,
			EndDate:          req.EndDate,
			IsEndDateEnabled: req.IsEndDateEnabled,
			Remark:           req.Remark,
			MaterialType:     req.MaterialType,
		},
		Rels: rels,
	}

	err = service.AdSlotPlan().Create(ctx, plan)
	if err != nil {
		return nil, err
	}

	res = &v1.CreateRes{
		ID: plan.ID,
	}
	return
}

// Update 更新投放计划
func (c *Controller) Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error) {
	plan, err := service.AdSlotPlan().Get(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	// 只允许更新部分字段
	if req.Remark != "" {
		plan.Remark = req.Remark
	}
	plan.MaterialType = req.MaterialType
	plan.AdCreativeID = req.AdCreativeID
	rels := make([]*entity.PlanRelateRels, 0, len(req.Rels))
	for _, rel := range req.Rels {
		rels = append(rels, &entity.PlanRelateRels{
			PlanId:     int64(req.ID),
			RelateType: rel.Type,
			RelateId:   rel.Id,
		})
	}
	plan.Rels = rels

	err = service.AdSlotPlan().Update(ctx, plan)
	if err != nil {
		return nil, err
	}

	res = &v1.UpdateRes{}
	return
}

// Delete 删除投放计划
func (c *Controller) Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error) {
	err = service.AdSlotPlan().Delete(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	res = &v1.DeleteRes{}
	return
}

// Approve 审核通过
func (c *Controller) Approve(ctx context.Context, req *v1.ApproveReq) (res *v1.ApproveRes, err error) {
	// 获取当前用户ID
	userID := g.RequestFromCtx(ctx).GetCtxVar("user_id").Uint64()

	// 只调用审核通过,不生成链接
	err = service.AdSlotPlan().Approve(ctx, req.ID, userID)
	if err != nil {
		return nil, err
	}

	res = &v1.ApproveRes{}
	return
}

// Reject 审核拒绝
func (c *Controller) Reject(ctx context.Context, req *v1.RejectReq) (res *v1.RejectRes, err error) {
	// 获取当前用户ID
	userID := g.RequestFromCtx(ctx).GetCtxVar("user_id").Uint64()

	err = service.AdSlotPlan().Reject(ctx, req.ID, userID, req.Reason)
	if err != nil {
		return nil, err
	}

	res = &v1.RejectRes{}
	return
}

// UpdateDeliveryMode 更新投放策略
func (c *Controller) UpdateDeliveryMode(ctx context.Context, req *v1.UpdateDeliveryModeReq) (res *v1.UpdateDeliveryModeRes, err error) {
	// 获取当前用户ID
	userID := g.RequestFromCtx(ctx).GetCtxVar("user_id").Uint64()

	err = service.AdSlotPlan().UpdateDeliveryMode(ctx, req.ID, req.Mode, userID)
	if err != nil {
		return nil, err
	}

	res = &v1.UpdateDeliveryModeRes{}
	return
}

// GeneratePromotionLink 生成推广链接
func (c *Controller) GeneratePromotionLink(ctx context.Context, req *v1.GeneratePromotionLinkReq) (res *v1.GeneratePromotionLinkRes, err error) {
	// 获取当前计划信息
	plan, err := service.AdSlotPlan().Get(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	// 生成推广链接
	err = service.AdSlotPlan().GeneratePromotionLink(ctx, req.ID, req.ProductID)
	if err != nil {
		return nil, err
	}

	// 如果当前是配置中状态，更新投放状态
	if plan.DeliveryStatus == constants.PLAN_DELIVERY_STATUS_CONFIGURING || plan.DeliveryStatus == constants.PLAN_DELIVERY_STATUS_INIT {
		now := time.Now()
		newStatus := constants.PLAN_DELIVERY_STATUS_WAIT
		if now.After(plan.StartDate) || now.Equal(plan.StartDate) {
			newStatus = constants.PLAN_DELIVERY_STATUS_RUNNING
		}
		// 开启事务
		err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			// 更新状态
			_, err = tx.Model("ad_slot_plans").
				Where("id", req.ID).
				Data(g.Map{
					"delivery_status": newStatus,
					"updated_at":      now,
				}).
				Update()

			if err != nil {
				return err
			}

			// 记录操作日志
			operationParams, _ := json.Marshal(g.Map{
				"old_status": constants.PLAN_DELIVERY_STATUS_CONFIGURING,
				"new_status": newStatus,
			})

			_, err = tx.Model("ad_slot_plan_operation_logs").Insert(g.Map{
				"plan_id":          req.ID,
				"user_id":          0, // 系统操作
				"action":           constants.PLAN_ACTION_UPDATE_STATUS,
				"operation_desc":   "更新投放状态",
				"operation_params": operationParams,
				"old_status":       constants.PLAN_DELIVERY_STATUS_CONFIGURING,
				"new_status":       newStatus,
				"remark":           fmt.Sprintf("生成推广链接成功，状态更新为：%s", constants.PlanDeliveryStatusMap[newStatus]),
				"created_at":       now,
				"updated_at":       now,
			})
			return err
		})

		if err != nil {
			return nil, err
		}
	}

	// 获取最新的计划信息
	plan, err = service.AdSlotPlan().Get(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	res = &v1.GeneratePromotionLinkRes{
		PromotionLink:   plan.PromotionLink,
		PromotionQrcode: plan.PromotionQrcode,
	}
	return
}

// GenerateShortUrl 生成短链接
func (c *Controller) GenerateShortUrl(ctx context.Context, req *v1.GenerateShortUrlReq) (res *v1.GenerateShortUrlRes, err error) {
	err = service.AdSlotPlan().GenerateShortUrl(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	// 获取最新的计划信息
	plan, err := service.AdSlotPlan().Get(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	res = &v1.GenerateShortUrlRes{
		ShortUrl: plan.ShortUrl,
	}
	return
}

// UpdatePromotionLink 更新推广链接
func (c *Controller) UpdatePromotionLink(ctx context.Context, req *v1.UpdatePromotionLinkReq) (res *v1.UpdatePromotionLinkRes, err error) {
	// 获取当前用户ID
	userID := g.RequestFromCtx(ctx).GetCtxVar("user_id").Uint64()

	// 更新推广链接
	err = service.AdSlotPlan().UpdatePromotionLink(ctx, req.ID, req.Link, userID)
	if err != nil {
		return nil, err
	}

	res = &v1.UpdatePromotionLinkRes{}
	return
}

// UpdateShortUrl 更新短链接
func (c *Controller) UpdateShortUrl(ctx context.Context, req *v1.UpdateShortUrlReq) (res *v1.UpdateShortUrlRes, err error) {
	// 获取当前用户ID
	userID := g.RequestFromCtx(ctx).GetCtxVar("user_id").Uint64()

	// 更新短链接
	err = service.AdSlotPlan().UpdateShortUrl(ctx, req.ID, req.URL, userID)
	if err != nil {
		return nil, err
	}

	res = &v1.UpdateShortUrlRes{}
	return
}

// UpdateMergeLinks 更新融合链接
func (c *Controller) UpdateMergeLinks(ctx context.Context, req *v1.UpdateMergeLinksReq) (res *v1.UpdateMergeLinksRes, err error) {
	// 记录操作日志
	g.Log().Infof(ctx, "用户更新融合链接: 计划ID=%d", req.ID)

	// 更新融合链接
	err = service.AdSlotPlan().UpdateMergeLinks(ctx, req.ID, req.MergeLinks)
	if err != nil {
		return nil, err
	}

	res = &v1.UpdateMergeLinksRes{}
	return
}

// GetMergeLinks 获取融合链接信息
func (c *Controller) GetMergeLinks(ctx context.Context, req *v1.GetMergeLinksReq) (res *v1.GetMergeLinksRes, err error) {
	g.Log().Debugf(ctx, "获取融合链接信息请求: code=%s", req.Code)

	// 获取融合链接
	mergeLinks, err := service.AdSlotPlan().GetMergeLinksByCode(ctx, req.Code)
	if err != nil {
		return nil, err
	}

	res = &v1.GetMergeLinksRes{
		MergeLinks: mergeLinks,
	}
	return
}

func (c Controller) GetPlatformObjectData(ctx context.Context, req *v1.GetPlatformObjectDataReq) (res *v1.GetPlatformObjectDataRes, err error) {
	data, err := service.AdSlotPlan().GetPlatformObjectData(ctx, req)
	if err != nil {
		return nil, err
	}
	return &v1.GetPlatformObjectDataRes{
		Data: data,
	}, nil
}
