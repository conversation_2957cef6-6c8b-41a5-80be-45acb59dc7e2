package agent

import (
	"context"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"

	"ad-pro-v2/internal/consts"
	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/service"
)

type Controller struct{}

// List 获取代理列表
func (c *Controller) List(ctx context.Context, req *model.AgentListReq) (res *model.AgentListRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 设置查询条件
	condition := g.Map{}

	// 如果是媒介,只能查看自己的代理
	if userInfo.IsMedia() {
		condition["user_id"] = userInfo.Id
	} else if req.MediaId > 0 { // 非媒介角色且指定了媒介ID
		condition["user_id"] = req.MediaId
	}

	// 添加筛选条件
	if req.Name != "" {
		condition["name"] = req.Name
	}
	if req.Type != "" {
		// 验证代理类型
		if req.Type != consts.AgentTypeTraffic && req.Type != consts.AgentTypeDelivery {
			return nil, fmt.Errorf("无效的代理类型")
		}
		condition["type"] = req.Type
	}
	if req.AuditStatus != "" {
		condition["audit_status"] = req.AuditStatus
	}

	// 获取列表数据
	list, err := dao.AdAgents.Ctx(ctx).
		Where(condition).
		Page(req.Page, req.Size).
		Order("id DESC"). // 按ID倒序排序
		All()
	if err != nil {
		return nil, err
	}

	// 获取总数
	total, err := dao.AdAgents.Ctx(ctx).Where(condition).Count()
	if err != nil {
		return nil, err
	}

	var agents []*model.Agent
	if err = list.Structs(&agents); err != nil {
		return nil, err
	}

	return &model.AgentListRes{
		List:  agents,
		Total: total,
		Page:  req.Page,
		Size:  req.Size,
	}, nil
}

// Detail 获取代理详情
func (c *Controller) Detail(ctx context.Context, req *model.AgentDetailReq) (res *model.AgentDetailRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 获取代理信息
	agent, err := service.Agent().GetById(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// 检查权限
	if !userInfo.IsAdmin() && !userInfo.IsOperator() && !userInfo.IsFinance() && agent.UserId != userInfo.Id {
		return nil, model.ErrNotAuthorized
	}

	return &model.AgentDetailRes{Agent: agent}, nil
}

// Create 创建代理
func (c *Controller) Create(ctx context.Context, req *model.AgentCreateReq) (res *model.AgentCreateRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 设置创建人ID
	req.UserId = userInfo.Id

	return service.Agent().Create(ctx, req)
}

// Update 更新代理
func (c *Controller) Update(ctx context.Context, req *model.AgentUpdateReq) (res *model.AgentUpdateRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		g.Log().Error(ctx, "用户未登录")
		return nil, model.ErrNotAuthorized
	}

	// 打印用户角色信息
	g.Log().Info(ctx, "当前用户角色:", userInfo.Role)
	g.Log().Info(ctx, "是否管理员:", userInfo.IsAdmin())
	g.Log().Info(ctx, "是否运营:", userInfo.IsOperator())

	// 获取代理信息
	agent, err := service.Agent().GetById(ctx, req.Id)
	if err != nil {
		g.Log().Error(ctx, "获取代理信息失败:", err)
		return nil, err
	}

	// 打印代理创建者信息
	g.Log().Info(ctx, "代理创建者ID:", agent.UserId)
	g.Log().Info(ctx, "当前用户ID:", userInfo.Id)

	// 检查权限
	if !userInfo.IsAdmin() && !userInfo.IsOperator() && agent.UserId != userInfo.Id {
		g.Log().Error(ctx, "用户无权限更新此代理")
		return nil, model.ErrNotAuthorized
	}

	return service.Agent().Update(ctx, req)
}

// Delete 删除代理
func (c *Controller) Delete(ctx context.Context, req *model.AgentDeleteReq) (res *model.AgentDeleteRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 获取代理信息
	agent, err := service.Agent().GetById(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// 检查权限
	if !userInfo.IsAdmin() && !userInfo.IsOperator() && agent.UserId != userInfo.Id {
		return nil, model.ErrNotAuthorized
	}

	return service.Agent().Delete(ctx, req.Id)
}

// Audit 审核代理
func (c *Controller) Audit(ctx context.Context, req *model.AgentAuditReq) (res *model.AgentAuditRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 检查权限 - 只有管理员和运营可以审核
	if !userInfo.IsAdmin() && !userInfo.IsOperator() {
		return nil, model.ErrNotAuthorized
	}

	return service.Agent().Audit(ctx, req)
}
