package slot

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"

	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/service"
)

type Controller struct{}

// List 获取资源位列表
func (c *Controller) List(ctx context.Context, req *model.SlotListReq) (res *model.SlotListRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 设置查询条件
	condition := g.Map{}

	// 如果不是管理员/运营/财务，只能查看自己的资源位
	if !userInfo.IsAdmin() && !userInfo.IsOperator() && !userInfo.IsFinance() {
		condition["user_id"] = userInfo.Id
	}

	// 添加筛选条件
	if req.Name != "" {
		condition["name"] = req.Name
	}
	if req.MediaId != 0 {
		condition["media_id"] = req.MediaId
	}
	if req.UserId != 0 {
		condition["userid"] = req.UserId
	}
	if req.Type != "" {
		condition["type"] = req.Type
	}
	if req.Status != 0 {
		condition["status"] = req.Status
	}

	return service.Slot().GetList(ctx, condition, req.Page, req.Size)
}

// Detail 获取资源位详情
func (c *Controller) Detail(ctx context.Context, req *model.SlotDetailReq) (res *model.SlotDetailRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 获取资源位信息
	slot, err := service.Slot().GetById(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// 检查权限
	if !userInfo.IsAdmin() && !userInfo.IsOperator() && !userInfo.IsFinance() && slot.UserId != userInfo.Id {
		return nil, model.ErrNotAuthorized
	}

	return &model.SlotDetailRes{Slot: slot}, nil
}

// Create 创建资源位
func (c *Controller) Create(ctx context.Context, req *model.SlotCreateReq) (res *model.SlotCreateRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 设置创建人ID
	req.UserId = userInfo.Id

	return service.Slot().Create(ctx, req)
}

// Update 更新资源位
func (c *Controller) Update(ctx context.Context, req *model.SlotUpdateReq) (res *model.SlotUpdateRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 获取资源位信息
	slot, err := service.Slot().GetById(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// 检查权限
	if !userInfo.IsAdmin() && !userInfo.IsOperator() && slot.UserId != userInfo.Id {
		return nil, model.ErrNotAuthorized
	}

	return service.Slot().Update(ctx, req)
}

// Delete 删除资源位
func (c *Controller) Delete(ctx context.Context, req *model.SlotDeleteReq) (res *model.SlotDeleteRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 获取资源位信息
	slot, err := service.Slot().GetById(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// 检查权限
	if !userInfo.IsAdmin() && !userInfo.IsOperator() && slot.UserId != userInfo.Id {
		return nil, model.ErrNotAuthorized
	}

	return service.Slot().Delete(ctx, req.Id)
}

// Audit 审核资源位
func (c *Controller) Audit(ctx context.Context, req *model.SlotAuditReq) (res *model.SlotAuditRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 检查权限 - 只有管理员和运营可以审核
	if !userInfo.IsAdmin() && !userInfo.IsOperator() {
		return nil, model.ErrNotAuthorized
	}

	return service.Slot().Audit(ctx, req)
}
