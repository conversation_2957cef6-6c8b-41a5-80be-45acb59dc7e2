package upload

import (
	"context"

	v1 "ad-pro-v2/api/upload/v1"
	"ad-pro-v2/internal/service"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// Controller 上传控制器
type Controller struct{}

// Upload 上传文件
func (c *Controller) Upload(ctx context.Context, req *v1.UploadReq) (res *v1.UploadRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, gerror.New("未授权的操作")
	}

	// 获取请求对象
	r := g.RequestFromCtx(ctx)
	if r == nil {
		return nil, gerror.New("无法获取请求对象")
	}

	// 获取上传文件
	file := r.GetUploadFile("file")
	if file == nil {
		return nil, gerror.New("请选择要上传的文件")
	}

	// 上传文件到OSS
	url, err := service.OSS.UploadFile(ctx, file)
	if err != nil {
		return nil, err
	}

	return &v1.UploadRes{
		Url: url,
	}, nil
}

// UploadImage 上传图片
func (c *Controller) UploadImage(ctx context.Context, req *v1.UploadImageReq) (res *v1.UploadImageRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, gerror.New("未授权的操作")
	}

	// 获取请求对象
	r := g.RequestFromCtx(ctx)
	if r == nil {
		return nil, gerror.New("无法获取请求对象")
	}

	// 获取上传文件
	file := r.GetUploadFile("file")
	if file == nil {
		return nil, gerror.New("请选择要上传的图片")
	}

	// 上传图片到OSS
	url, err := service.OSS.UploadImage(ctx, file)
	if err != nil {
		return nil, err
	}

	return &v1.UploadImageRes{
		Url: url,
	}, nil
}
