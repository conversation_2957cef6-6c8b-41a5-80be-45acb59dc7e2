package platform_creative

import (
	"context"

	v1 "ad-pro-v2/api/platform_creative/v1"
	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/service"
)

// Controller PlatformCreative控制器
type Controller struct{}

// List 获取广告创意列表
func (c *Controller) List(ctx context.Context, req *v1.ListReq) (res *v1.ListRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	return service.PlatformCreative().List(ctx, req)
}

// Create 创建广告创意
func (c *Controller) Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	return service.PlatformCreative().Create(ctx, req)
}

// Update 更新广告创意
func (c *Controller) Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	return service.PlatformCreative().Update(ctx, req)
}

// Delete 删除广告创意
func (c *Controller) Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	return service.PlatformCreative().Delete(ctx, req)
}
