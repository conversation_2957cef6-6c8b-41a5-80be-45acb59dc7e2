package plan_statistics

import (
	"context"

	v1 "ad-pro-v2/api/plan_statistics/v1"
	"ad-pro-v2/internal/service"
)

// Controller 计划统计控制器
type Controller struct{}

// GetPlanPvUv 获取计划PV/UV数据
func (c *Controller) GetPlanPvUv(ctx context.Context, req *v1.GetPlanPvUvReq) (res *v1.GetPlanPvUvRes, err error) {
	// 调用服务层获取数据
	pvUvData, err := service.PlanStatistics().GetPlanPvUv(ctx, req.PlanId, req.Date)
	if err != nil {
		return nil, err
	}

	// 返回响应数据
	return &v1.GetPlanPvUvRes{
		PV: pvUvData["pv"],
		UV: pvUvData["uv"],
	}, nil
}
