package media

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"

	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/service"
)

// 媒体类型枚举
const (
	MediaTypeWechat   = "wechat"   // 微信
	MediaTypeAlipay   = "alipay"   // 支付宝
	MediaTypeDouyin   = "douyin"   // 抖音
	MediaTypeKuaishou = "kuaishou" // 快手
	MediaTypeOther    = "other"    // 其他
)

// 所属行业枚举
const (
	IndustryPayment  = "payment"  // 支付
	IndustryExpress  = "express"  // 快递
	IndustryInvoice  = "invoice"  // 开票
	IndustryParking  = "parking"  // 停车
	IndustryCharging = "charging" // 充电
	IndustryDevice   = "device"   // 设备
	IndustryOther    = "other"    // 其他
)

// 审核状态枚举
const (
	AuditStatusPending  = "pending"  // 待审核
	AuditStatusApproved = "approved" // 已通过
	AuditStatusRejected = "rejected" // 已拒绝
)

// 合作状态枚举
const (
	CoopStatusNotStarted = "not_started" // 未开始
	CoopStatusActive     = "active"      // 合作中
	CoopStatusTerminated = "terminated"  // 已终止
)

// 合作类型枚举
const (
	CoopTypeCPS      = "cps"     // CPS合作
	CoopTypeTraffic  = "traffic" // 流量采买
	CoopTypeDelivery = "dh"      // 灯火投放
	CoopTypeOther    = "other"   // 其他投放
)

type Controller struct{}

// List 获取媒体列表
func (c *Controller) List(ctx context.Context, req *model.MediaListReq) (res *model.MediaListRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 设置查询条件
	condition := g.Map{}

	// 如果不是管理员/运营/财务，只能查看自己的媒体
	if !userInfo.IsAdmin() && !userInfo.IsOperator() && !userInfo.IsFinance() {
		condition["user_id"] = userInfo.Id
	}

	// 添加筛选条件
	if req.Name != "" {
		condition["name"] = req.Name
	}
	if len(req.Types) > 0 {
		condition["types"] = req.Types
	}
	if req.Industry != "" {
		condition["industry"] = req.Industry
	}
	if req.AuditStatus != "" {
		condition["auditStatus"] = req.AuditStatus
	}
	if req.CooperationStatus != "" {
		condition["cooperationStatus"] = req.CooperationStatus
	}
	if req.CooperationType != "" {
		condition["cooperationType"] = req.CooperationType
	}
	if req.UserId != 0 {
		condition["userId"] = req.UserId
	}

	return service.Media().GetList(ctx, condition, req.Page, req.Size)
}

// Detail 获取媒体详情
func (c *Controller) Detail(ctx context.Context, req *model.MediaDetailReq) (res *model.MediaDetailRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 获取媒体信息
	media, err := service.Media().GetById(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// 检查权限
	if !userInfo.IsAdmin() && !userInfo.IsOperator() && !userInfo.IsFinance() && media.UserId != userInfo.Id {
		return nil, model.ErrNotAuthorized
	}

	return &model.MediaDetailRes{Media: media}, nil
}

// Create 创建媒体
func (c *Controller) Create(ctx context.Context, req *model.MediaCreateReq) (res *model.MediaCreateRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 设置创建人ID
	req.UserId = userInfo.Id

	// 设置初始状态
	req.AuditStatus = AuditStatusPending
	req.CooperationStatus = CoopStatusNotStarted

	// 如果是CPS合作类型,校验账号密码
	if req.CooperationType == CoopTypeCPS {
		if req.Account == "" || req.Password == "" {
			return nil, model.ErrInvalidParam.WithMsg("CPS合作类型需要设置账号密码")
		}
	}

	return service.Media().Create(ctx, req)
}

// Update 更新媒体
func (c *Controller) Update(ctx context.Context, req *model.MediaUpdateReq) (res *model.MediaUpdateRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 获取媒体信息
	media, err := service.Media().GetById(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// 检查权限
	if !userInfo.IsAdmin() && !userInfo.IsOperator() && media.UserId != userInfo.Id {
		return nil, model.ErrNotAuthorized
	}

	return service.Media().Update(ctx, req)
}

// Delete 删除媒体
func (c *Controller) Delete(ctx context.Context, req *model.MediaDeleteReq) (res *model.MediaDeleteRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 获取媒体信息
	media, err := service.Media().GetById(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// 检查权限
	if !userInfo.IsAdmin() && !userInfo.IsOperator() && media.UserId != userInfo.Id {
		return nil, model.ErrNotAuthorized
	}

	return service.Media().Delete(ctx, req.Id)
}

// Audit 审核媒体
func (c *Controller) Audit(ctx context.Context, req *model.MediaAuditReq) (res *model.MediaAuditRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 检查权限 - 只有管理员和运营可以审核
	if !userInfo.IsAdmin() && !userInfo.IsOperator() {
		return nil, model.ErrNotAuthorized
	}

	return service.Media().Audit(ctx, req)
}
