package ad_product

import (
	"context"

	v1 "ad-pro-v2/api/ad_product/v1"
	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/service"
)

// Controller AdProduct控制器
type Controller struct{}

// List 获取投放产品列表
func (c *Controller) List(ctx context.Context, req *v1.ListReq) (res *v1.ListRes, err error) {
	products, total, err := service.AdProduct().List(ctx, req.Page, req.Size)
	if err != nil {
		return nil, err
	}

	// 转换为 model.AdProduct 切片
	modelProducts := make([]model.AdProduct, len(products))
	for i, p := range products {
		modelProducts[i] = model.AdProduct{
			AdProducts: &p,
		}
	}

	res = &v1.ListRes{
		List:  modelProducts,
		Total: total,
	}
	return
}
