package analysis

import (
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/service"
)

type Controller struct{}

type GetProfitDataReq struct {
	g.Meta    `path:"/profit" method:"get"`
	StartDate string `json:"start_date" v:"required"`
	EndDate   string `json:"end_date" v:"required"`
	Type      string `json:"type"`
	UserId    int    `json:"user_id"`
	MediaId   int    `json:"media_id"`
	PlanId    int    `json:"plan_id"`
	ProductId int    `json:"product_id"`
}

type ProfitData struct {
	Date               string  `json:"date"`
	TotalOrders        int     `json:"total_orders"`         // 总订单数
	TotalPreCommission float64 `json:"total_pre_commission"` // 总预估佣金
	TotalCommission    float64 `json:"total_commission"`     // 总结算佣金

	ElmOrders        int     `json:"elm_orders"`         // 饿了么订单数
	ElmPreCommission float64 `json:"elm_pre_commission"` // 饿了么预估佣金
	ElmCommission    float64 `json:"elm_commission"`     // 饿了么结算佣金

	FliggyOrders        int     `json:"fliggy_orders"`         // 飞猪订单数
	FliggyPreCommission float64 `json:"fliggy_pre_commission"` // 飞猪预估佣金
	FliggyCommission    float64 `json:"fliggy_commission"`     // 飞猪结算佣金

	TaobaoOrders        int     `json:"taobao_orders"`         // 淘宝订单数
	TaobaoPreCommission float64 `json:"taobao_pre_commission"` // 淘宝预估佣金
	TaobaoCommission    float64 `json:"taobao_commission"`     // 淘宝结算佣金

	Cost float64 `json:"cost"` // 成本
}

type GetProfitDataRes struct {
	List []ProfitData `json:"list"`
}

// GetProfitData 获取收益分析数据
func (c *Controller) GetProfitData(ctx context.Context, req *GetProfitDataReq) (res *GetProfitDataRes, err error) {
	res = &GetProfitDataRes{}

	// 记录输入参数
	g.Log().Debug(ctx, "GetProfitData input:", g.Map{
		"start_date": req.StartDate,
		"end_date":   req.EndDate,
		"type":       req.Type,
		"user_id":    req.UserId,
		"media_id":   req.MediaId,
		"plan_id":    req.PlanId,
		"product_id": req.ProductId,
	})

	// 解析日期范围
	parsedStartTime, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		return nil, fmt.Errorf("invalid start date format: %v", err)
	}
	parsedEndTime, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		return nil, fmt.Errorf("invalid end date format: %v", err)
	}
	startTime := gtime.NewFromTime(parsedStartTime)
	endTime := gtime.NewFromTime(parsedEndTime)

	g.Log().Debug(ctx, "Parsed dates:", g.Map{
		"startDate": req.StartDate,
		"startTime": startTime.String(),
		"formatted": startTime.Format("200601"),
	})

	// 获取当前登录用户
	loginUser := service.Context().GetUser(ctx)
	if loginUser == nil {
		return nil, fmt.Errorf("user not logged in")
	}
	g.Log().Debug(ctx, "Current user info:", loginUser)

	// 获取筛选后的计划ID列表
	var planIds []int64
	query := dao.AdSlotPlans.Ctx(ctx)

	// 处理用户权限
	var queryUserId int
	if req.UserId > 0 {
		queryUserId = req.UserId
		g.Log().Debug(ctx, "Using provided user ID:", queryUserId)
	} else {
		if loginUser.Role == 2 || loginUser.Role == 3 || loginUser.Role == 4 {
			queryUserId = 0
			g.Log().Debug(ctx, "Admin/Operator/Finance role, not filtering by user")
		} else {
			queryUserId = loginUser.Id
			g.Log().Debug(ctx, "Using current user ID:", loginUser.Id)
		}
	}
	if queryUserId > 0 {
		query = query.Where("user_id", queryUserId)
		g.Log().Debug(ctx, "Added user filter:", queryUserId)
	}

	// 根据类型，添加筛选条件
	var mediaIds []int
	if req.Type == "1" {
		// 获取普通投放媒体ID
		mediaQuery := dao.AdMedia.Ctx(ctx).Where("cooperation_type", "traffic")
		values, err := mediaQuery.Array("id")
		if err != nil {
			g.Log().Error(ctx, "Failed to get traffic media IDs:", err)
			return nil, err
		}
		g.Log().Debug(ctx, "Traffic media values:", values)

		mediaIds = make([]int, len(values))
		for i, v := range values {
			mediaIds[i] = v.Int()
		}
		g.Log().Debug(ctx, "Converted traffic media IDs:", mediaIds)
	} else if req.Type == "3" {
		// 获取灯火媒体ID
		mediaQuery := dao.AdMedia.Ctx(ctx).Where("cooperation_type", "dh")
		values, err := mediaQuery.Array("id")
		if err != nil {
			g.Log().Error(ctx, "Failed to get DH media IDs:", err)
			return nil, err
		}
		g.Log().Debug(ctx, "DH media values:", values)

		mediaIds = make([]int, len(values))
		for i, v := range values {
			mediaIds[i] = v.Int()
		}
		g.Log().Debug(ctx, "Converted DH media IDs:", mediaIds)
	} else {
		mediaQuery := dao.AdMedia.Ctx(ctx).WhereIn("cooperation_type", []string{"traffic", "cps", "dh"})
		values, err := mediaQuery.Array("id")
		if err != nil {
			g.Log().Error(ctx, "Failed to get DH media IDs:", err)
			return nil, err
		}
		g.Log().Debug(ctx, "DH media values:", values)

		mediaIds = make([]int, len(values))
		for i, v := range values {
			mediaIds[i] = v.Int()
		}
	}

	g.Log().Debug(ctx, "Final filtered media IDs:", mediaIds)

	// 只有在 mediaIds 非空时才添加 WhereIn 条件
	if len(mediaIds) > 0 {
		query = query.WhereIn("media_id", mediaIds)
		g.Log().Debug(ctx, "Added media_id IN condition:", mediaIds)
	} else {
		// 如果没有符合条件的媒体ID，返回空结果
		g.Log().Debug(ctx, "No matching media IDs found, returning empty result")
		return
	}

	// 添加其他筛选条件
	if req.PlanId > 0 {
		query = query.Where("id", req.PlanId)
		g.Log().Debug(ctx, "Added plan filter:", req.PlanId)
	}
	if req.MediaId > 0 {
		query = query.Where("media_id", req.MediaId)
		g.Log().Debug(ctx, "Added media filter:", req.MediaId)
	}
	if req.ProductId > 0 {
		query = query.Where("ad_product_id", req.ProductId)
		g.Log().Debug(ctx, "Added product filter:", req.ProductId)
	}

	query = query.WhereNot("ad_product_id", 2)

	// 获取计划ID
	planValues, err := query.Array("id")
	if err != nil {
		g.Log().Error(ctx, "Failed to get plan IDs:", err)
		return nil, err
	}

	g.Log().Debug(ctx, "Raw plan IDs result:", planValues)

	// 转换为 int64 切片
	planIds = make([]int64, len(planValues))
	for i, id := range planValues {
		planIds[i] = id.Int64()
	}

	g.Log().Debug(ctx, "Final converted plan IDs:", planIds)

	// 如果没有符合条件的计划,返回空数据
	if len(planIds) == 0 {
		dates := getDatesInRange(startTime.Time, endTime.Time)
		res.List = make([]ProfitData, len(dates))
		for i, date := range dates {
			res.List[i] = ProfitData{
				Date:                date,
				TotalOrders:         0,
				TotalPreCommission:  0,
				TotalCommission:     0,
				ElmOrders:           0,
				ElmPreCommission:    0,
				ElmCommission:       0,
				FliggyOrders:        0,
				FliggyPreCommission: 0,
				FliggyCommission:    0,
				TaobaoOrders:        0,
				TaobaoPreCommission: 0,
				TaobaoCommission:    0,
				Cost:                0,
			}
		}
		return
	}

	// 获取分表名
	yearMonth := parsedStartTime.Format("200601")
	tableName := fmt.Sprintf("ad_orders_%s", yearMonth)
	g.Log().Debug(ctx, "Table name details:", g.Map{
		"yearMonth": yearMonth,
		"tableName": tableName,
	})

	// 构建查询条件
	m := g.DB().Model(tableName)
	m = m.Where("ad_plan_id IN(?)", planIds)
	m = m.Where("leak = 0")
	m = m.Where("order_status IN (2,3,4)") // 已付款,已完成,已结算

	// 时间范围
	startTimeStr := req.StartDate + " 00:00:00"
	endTimeStr := req.EndDate + " 23:59:59"
	m = m.Where("create_time BETWEEN ? AND ?", startTimeStr, endTimeStr)
	g.Log().Debug(ctx, "Time range:", g.Map{
		"start": startTimeStr,
		"end":   endTimeStr,
	})

	// 按日期分组统计
	var records []struct {
		Date                string  `json:"date"`
		TotalOrders         int     `json:"total_orders"`
		TotalPreCommission  float64 `json:"total_pre_commission"`
		TotalCommission     float64 `json:"total_commission"`
		ElmOrders           int     `json:"elm_orders"`
		ElmPreCommission    float64 `json:"elm_pre_commission"`
		ElmCommission       float64 `json:"elm_commission"`
		FliggyOrders        int     `json:"fliggy_orders"`
		FliggyPreCommission float64 `json:"fliggy_pre_commission"`
		FliggyCommission    float64 `json:"fliggy_commission"`
		TaobaoOrders        int     `json:"taobao_orders"`
		TaobaoPreCommission float64 `json:"taobao_pre_commission"`
		TaobaoCommission    float64 `json:"taobao_commission"`
		Cost                float64 `json:"cost"`
	}

	// 查询订单数据
	err = m.Fields(
		"TRIM(left(create_time,10)) as date",
		"COUNT(1) as total_orders",
		"SUM(pre_commission + activity_fee) as total_pre_commission",
		"SUM(commission + activity_fee) as total_commission",
		"COUNT(CASE WHEN order_type IN (1400,1401,1410,1411) THEN 1 END) as elm_orders",
		"SUM(CASE WHEN order_type IN (1400,1401,1410,1411) THEN pre_commission END) as elm_pre_commission",
		"SUM(CASE WHEN order_type IN (1400,1401,1410,1411) THEN commission END) as elm_commission",
		"COUNT(CASE WHEN order_type = 1600 THEN 1 END) as fliggy_orders",
		"SUM(CASE WHEN order_type = 1600 THEN pre_commission + activity_fee END) as fliggy_pre_commission",
		"SUM(CASE WHEN order_type = 1600 THEN commission + activity_fee END) as fliggy_commission",
		"COUNT(CASE WHEN order_type = 1100 THEN 1 END) as taobao_orders",
		"SUM(CASE WHEN order_type = 1100 THEN pre_commission END) as taobao_pre_commission",
		"SUM(CASE WHEN order_type = 1100 THEN commission END) as taobao_commission",
	).Group("TRIM(left(create_time,10))").
		Order("date ASC").
		Scan(&records)

	if err != nil {
		return nil, err
	}

	// 查询成本数据
	var costRecords []struct {
		Date string  `json:"date"`
		Cost float64 `json:"cost"`
	}
	err = g.DB().Model("ad_slot_plan_costs").
		Fields("date", "SUM(cost) as cost").
		Where("plan_id IN(?)", planIds).
		Where("audit_status = ?", "approved").
		Where("deleted_at IS NULL").
		Where("date BETWEEN ? AND ?", req.StartDate, req.EndDate).
		Group("date").
		Order("date ASC").
		Scan(&costRecords)

	if err != nil {
		return nil, err
	}

	// 将成本数据合并到记录中
	costMap := make(map[string]float64)
	for _, cr := range costRecords {
		costMap[cr.Date] = cr.Cost
	}

	// 初始化日期数据
	dates := getDatesInRange(startTime.Time, endTime.Time)
	dateMap := make(map[string]*ProfitData)
	for _, date := range dates {
		dateMap[date] = &ProfitData{
			Date:                date,
			TotalOrders:         0,
			TotalPreCommission:  0,
			TotalCommission:     0,
			ElmOrders:           0,
			ElmPreCommission:    0,
			ElmCommission:       0,
			FliggyOrders:        0,
			FliggyPreCommission: 0,
			FliggyCommission:    0,
			TaobaoOrders:        0,
			TaobaoPreCommission: 0,
			TaobaoCommission:    0,
			Cost:                0,
		}
	}

	// 填充订单数据
	for _, r := range records {
		if data, ok := dateMap[r.Date]; ok {
			data.TotalOrders = r.TotalOrders
			data.TotalPreCommission = r.TotalPreCommission
			data.TotalCommission = r.TotalCommission
			data.ElmOrders = r.ElmOrders
			data.ElmPreCommission = r.ElmPreCommission
			data.ElmCommission = r.ElmCommission
			data.FliggyOrders = r.FliggyOrders
			data.FliggyPreCommission = r.FliggyPreCommission
			data.FliggyCommission = r.FliggyCommission
			data.TaobaoOrders = r.TaobaoOrders
			data.TaobaoPreCommission = r.TaobaoPreCommission
			data.TaobaoCommission = r.TaobaoCommission
		}
	}

	// 填充成本数据
	for date, data := range dateMap {
		if cost, ok := costMap[date]; ok {
			data.Cost = cost
		}
	}

	// 转换为有序数组
	res.List = make([]ProfitData, len(dates))
	for i, date := range dates {
		res.List[i] = *dateMap[date]
	}

	return
}

// getDatesInRange 获取两个日期之间的日期列表
func getDatesInRange(start, end time.Time) []string {
	var dates []string
	current := time.Date(start.Year(), start.Month(), start.Day(), 0, 0, 0, 0, start.Location())

	for !current.After(end) {
		dates = append(dates, current.Format("2006-01-02"))
		current = current.AddDate(0, 0, 1)
	}
	return dates
}
