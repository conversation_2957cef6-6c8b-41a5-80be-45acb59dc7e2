package dashboard

import (
	v1 "ad-pro-v2/api/dashboard/v1"
	"ad-pro-v2/internal/logic/dashboard"
	"context"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

type Controller struct{}

// GetFilterOptions 获取仪表盘筛选项数据
func (c *Controller) GetFilterOptions(ctx context.Context, req *v1.GetFilterOptionsReq) (res *v1.GetFilterOptionsRes, err error) {
	// 1. 参数验证 (此接口无需额外验证)
	g.Log().Debug(ctx, "GetFilterOptions request:", req)

	// 2. 调用Logic层处理业务逻辑
	res, err = dashboard.Logic().GetFilterOptions(ctx, req)
	if err != nil {
		g.Log().<PERSON><PERSON>r(ctx, "GetFilterOptions failed:", err)
		return nil, gerror.WrapCode(gcode.CodeInternalError, err, "获取筛选选项失败")
	}

	g.Log().Debug(ctx, "GetFilterOptions response:", res)
	return res, nil
}

// GetMetrics 获取指标数据
func (c *Controller) GetMetrics(ctx context.Context, req *v1.GetMetricsReq) (res *v1.GetMetricsRes, err error) {
	// 1. 参数验证
	g.Log().Debug(ctx, "GetMetrics request:", req)

	if err = g.Validator().Data(req).Run(ctx); err != nil {
		g.Log().Error(ctx, "GetMetrics validation failed:", err)
		return nil, gerror.WrapCode(gcode.CodeValidationFailed, err, "参数验证失败")
	}

	// 2. 调用Logic层处理业务逻辑
	res, err = dashboard.Logic().GetMetrics(ctx, req)
	if err != nil {
		g.Log().Error(ctx, "GetMetrics failed:", err)
		return nil, err // Logic层已经包装了错误信息
	}

	g.Log().Debug(ctx, "GetMetrics response:", res)
	return res, nil
}

// GetTrendData 获取趋势数据
func (c *Controller) GetTrendData(ctx context.Context, req *v1.GetTrendDataReq) (res *v1.GetTrendDataRes, err error) {
	// 1. 参数验证
	g.Log().Debug(ctx, "GetTrendData request:", req)

	if err = g.Validator().Data(req).Run(ctx); err != nil {
		g.Log().Error(ctx, "GetTrendData validation failed:", err)
		return nil, gerror.WrapCode(gcode.CodeValidationFailed, err, "参数验证失败")
	}

	// 2. 调用Logic层处理业务逻辑
	res, err = dashboard.Logic().GetTrendData(ctx, req)
	if err != nil {
		g.Log().Error(ctx, "GetTrendData failed:", err)
		return nil, err
	}

	g.Log().Debug(ctx, "GetTrendData response:", res)
	return res, nil
}

// GetRegionData 获取区域数据
func (c *Controller) GetRegionData(ctx context.Context, req *v1.GetRegionDataReq) (res *v1.GetRegionDataRes, err error) {
	// 1. 参数验证
	g.Log().Debug(ctx, "GetRegionData request:", req)

	if err = g.Validator().Data(req).Run(ctx); err != nil {
		g.Log().Error(ctx, "GetRegionData validation failed:", err)
		return nil, gerror.WrapCode(gcode.CodeValidationFailed, err, "参数验证失败")
	}

	// 2. 调用Logic层处理业务逻辑
	res, err = dashboard.Logic().GetRegionData(ctx, req)
	if err != nil {
		g.Log().Error(ctx, "GetRegionData failed:", err)
		return nil, err
	}

	g.Log().Debug(ctx, "GetRegionData response:", res)
	return res, nil
}

// GetOrderTypeData 获取订单类型数据
func (c *Controller) GetOrderTypeData(ctx context.Context, req *v1.GetOrderTypeDataReq) (res *v1.GetOrderTypeDataRes, err error) {
	// 1. 参数验证
	g.Log().Debug(ctx, "GetOrderTypeData request:", req)

	if err = g.Validator().Data(req).Run(ctx); err != nil {
		g.Log().Error(ctx, "GetOrderTypeData validation failed:", err)
		return nil, gerror.WrapCode(gcode.CodeValidationFailed, err, "参数验证失败")
	}

	// 2. 调用Logic层处理业务逻辑
	res, err = dashboard.Logic().GetOrderTypeData(ctx, req)
	if err != nil {
		g.Log().Error(ctx, "GetOrderTypeData failed:", err)
		return nil, err
	}

	g.Log().Debug(ctx, "GetOrderTypeData response:", res)
	return res, nil
}

// GetMediaList 获取媒体列表
func (c *Controller) GetMediaList(ctx context.Context, req *v1.GetMediaListReq) (res *v1.GetMediaListRes, err error) {
	// 1. 参数验证
	g.Log().Debug(ctx, "GetMediaList request:", req)

	if err = g.Validator().Data(req).Run(ctx); err != nil {
		g.Log().Error(ctx, "GetMediaList validation failed:", err)
		return nil, gerror.WrapCode(gcode.CodeValidationFailed, err, "参数验证失败")
	}

	// 2. 调用Logic层处理业务逻辑
	res, err = dashboard.Logic().GetMediaList(ctx, req)
	if err != nil {
		g.Log().Error(ctx, "GetMediaList failed:", err)
		return nil, err
	}

	g.Log().Debug(ctx, "GetMediaList response:", res)
	return res, nil
}

// GetPlans 获取投放计划列表
func (c *Controller) GetPlans(ctx context.Context, req *v1.GetPlansReq) (res *v1.GetPlansRes, err error) {
	// 1. 参数验证
	g.Log().Debug(ctx, "GetPlans request:", req)

	if err = g.Validator().Data(req).Run(ctx); err != nil {
		g.Log().Error(ctx, "GetPlans validation failed:", err)
		return nil, gerror.WrapCode(gcode.CodeValidationFailed, err, "参数验证失败")
	}

	// 2. 调用Logic层处理业务逻辑
	res, err = dashboard.Logic().GetPlans(ctx, req)
	if err != nil {
		g.Log().Error(ctx, "GetPlans failed:", err)
		return nil, err
	}

	g.Log().Debug(ctx, "GetPlans response:", res)
	return res, nil
}

// GetProducts 获取产品列表
func (c *Controller) GetProducts(ctx context.Context, req *v1.GetProductsReq) (res *v1.GetProductsRes, err error) {
	// 1. 参数验证 (此接口无需额外验证)
	g.Log().Debug(ctx, "GetProducts request:", req)

	// 2. 调用Logic层处理业务逻辑
	res, err = dashboard.Logic().GetProducts(ctx, req)
	if err != nil {
		g.Log().Error(ctx, "GetProducts failed:", err)
		return nil, err
	}

	g.Log().Debug(ctx, "GetProducts response:", res)
	return res, nil
}

// GetPlanStats 获取分计划数据
func (c *Controller) GetPlanStats(ctx context.Context, req *v1.GetPlanStatsReq) (res *v1.GetPlanStatsRes, err error) {
	// 1. 参数验证
	g.Log().Debug(ctx, "GetPlanStats request:", req)

	if err = g.Validator().Data(req).Run(ctx); err != nil {
		g.Log().Error(ctx, "GetPlanStats validation failed:", err)
		return nil, gerror.WrapCode(gcode.CodeValidationFailed, err, "参数验证失败")
	}

	// 2. 调用Logic层处理业务逻辑
	res, err = dashboard.Logic().GetPlanStats(ctx, req)
	if err != nil {
		g.Log().Error(ctx, "GetPlanStats failed:", err)
		return nil, err
	}

	g.Log().Debug(ctx, "GetPlanStats response:", res)
	return res, nil
}
