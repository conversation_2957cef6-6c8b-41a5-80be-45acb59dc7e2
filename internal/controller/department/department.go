package department

import (
	v1 "ad-pro-v2/api/v1"
	"ad-pro-v2/internal/service"
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
)

// Controller 部门管理控制器
type Controller struct{}

// New 创建部门管理控制器
func New() *Controller {
	return &Controller{}
}

// GetDepartments 获取部门列表
func (c *Controller) GetDepartments(ctx context.Context, req *v1.GetDepartmentsReq) (res *v1.GetDepartmentsRes, err error) {
	// 调用Logic层
	return service.Department.GetDepartments(ctx, req)
}

// GetDepartmentTree 获取部门树
func (c *Controller) GetDepartmentTree(ctx context.Context, req *v1.GetDepartmentTreeReq) (res *v1.GetDepartmentTreeRes, err error) {
	// 调用Logic层
	return service.Department.GetDepartmentTree(ctx, req)
}

// CreateDepartment 创建部门
func (c *Controller) CreateDepartment(ctx context.Context, req *v1.CreateDepartmentReq) (res *v1.CreateDepartmentRes, err error) {
	// 参数验证已在API定义中通过tag完成

	// 调用Logic层
	return service.Department.CreateDepartment(ctx, req)
}

// UpdateDepartment 更新部门
func (c *Controller) UpdateDepartment(ctx context.Context, req *v1.UpdateDepartmentReq) (res *v1.UpdateDepartmentRes, err error) {
	// 参数验证已在API定义中通过tag完成

	// 调用Logic层
	return service.Department.UpdateDepartment(ctx, req)
}

// DeleteDepartment 删除部门
func (c *Controller) DeleteDepartment(ctx context.Context, req *v1.DeleteDepartmentReq) (res *v1.DeleteDepartmentRes, err error) {
	if req.Id <= 0 {
		return nil, gerror.New("部门ID不能为空")
	}

	// 调用Logic层
	return service.Department.DeleteDepartment(ctx, req)
}

// GetDepartmentOptions 获取部门选项
func (c *Controller) GetDepartmentOptions(ctx context.Context, req *v1.GetDepartmentOptionsReq) (res *v1.GetDepartmentOptionsRes, err error) {
	// 调用Logic层
	return service.Department.GetDepartmentOptions(ctx, req)
}
