package promotion_report

import (
	"context"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"

	v1 "ad-pro-v2/api/promotion_report"
	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/service"
)

// Controller 推广报表控制器
type Controller struct{}

// UpdateCost 更新推广成本
func (c *Controller) UpdateCost(ctx context.Context, req *v1.UpdateCostReq) (res *v1.UpdateCostRes, err error) {
	if req.Cost < 0 {
		return nil, gerror.NewCode(gcode.CodeInvalidParameter, "成本不能为负数")
	}

	in := &model.PromotionReportUpdateInput{
		GroupId:    req.GroupId,
		ReportDate: req.ReportDate,
		Cost:       req.Cost,
	}

	if err = service.PromotionReport.UpdateCost(ctx, in.GroupId, in.ReportDate, in.Cost); err != nil {
		return nil, err
	}

	return &v1.UpdateCostRes{}, nil
}

// GetCostList 获取推广成本列表
func (c *Controller) GetCostList(ctx context.Context, req *v1.GetCostListReq) (res *v1.GetCostListRes, err error) {
	// 1. 构造请求参数
	in := &model.PromotionReportListReq{
		Page:       req.Page,
		Size:       req.PageSize,
		GroupId:    req.GroupId,
		CategoryId: req.CategoryId,
		StartDate:  req.StartDate,
		EndDate:    req.EndDate,
	}

	// 2. 调用服务层获取数据
	out, err := service.PromotionReport.GetCostList(ctx, in)
	if err != nil {
		return nil, err
	}

	// 3. 转换响应数据
	res = &v1.GetCostListRes{
		List:  make([]v1.GetCostListItem, len(out.List)),
		Total: out.Total,
	}

	// 4. 复制列表数据
	for i, item := range out.List {
		res.List[i] = v1.GetCostListItem{
			Id:           item.Id,
			GroupId:      item.GroupId,
			GroupName:    item.GroupName,
			ReportDate:   item.ReportDate,
			CategoryName: item.CategoryName,
			CategoryId:   item.CategoryId,
			Cost:         item.Cost,
			UpdateAt:     item.UpdatedAt,
		}
	}

	return res, nil
}

// GetModelReport 获取模型报表数据
func (c *Controller) GetModelReport(ctx context.Context, req *v1.GetModelReportReq) (res *v1.GetModelReportRes, err error) {
	// 1. 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 2. 调用服务层获取数据
	out, err := service.PromotionReport.GetModelReport(ctx, uint(req.ModelId), uint(req.CategoryId), req.GroupId)
	if err != nil {
		return nil, err
	}

	// 3. 转换响应数据
	res = &v1.GetModelReportRes{
		List: make([]v1.ReportItem, len(out.List)),
	}

	// 4. 复制列表数据
	for i, item := range out.List {
		res.List[i] = v1.ReportItem{
			Date:              item.Date,
			TotalOrders:       item.TotalOrders,
			Cost:              item.Cost,
			TotalCommission:   item.TotalCommission,
			NewOrders:         item.NewOrders,
			DailyCost:         item.DailyCost,
			AverageCommission: item.AverageCommission,
			PaybackDays:       item.PaybackDays,
		}
	}

	return res, nil
}

// ImportCost 导入推广成本
func (c *Controller) ImportCost(ctx context.Context, req *v1.ImportCostReq) (res *v1.ImportCostRes, err error) {
	// 1. 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 2. 调用服务层导入成本
	hasTask, err := service.PromotionReport.ImportCost(ctx, req.CategoryId, req.File)
	if err != nil {
		return nil, err
	}

	// 3. 根据是否创建任务返回不同的提示信息
	res = &v1.ImportCostRes{}
	if hasTask {
		res.Message = "导入成功，等待任务执行完成后，报表数据才会准确"
	} else {
		res.Message = "导入成功"
	}

	return res, nil
}

// ImportXhsCost 导入小红书成本
func (c *Controller) ImportXhsCost(ctx context.Context, req *v1.ImportXhsCostReq) (res *v1.ImportXhsCostRes, err error) {
	// 1. 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 2. 调用服务层导入成本
	err = service.PromotionReport.ImportXhsCost(ctx)
	if err != nil {
		return nil, err
	}

	return &v1.ImportXhsCostRes{}, nil
}

// GetTaskList 获取任务列表
func (c *Controller) GetTaskList(ctx context.Context, req *v1.GetTaskListReq) (res *v1.GetTaskListRes, err error) {
	// 1. 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 2. 调用服务层获取任务列表
	list, err := service.PromotionReport.GetTaskList(ctx)
	if err != nil {
		return nil, err
	}

	// 3. 构造响应数据
	res = &v1.GetTaskListRes{
		List: make([]v1.TaskListItem, len(list)),
	}

	// 4. 复制数据
	for i, item := range list {
		res.List[i] = v1.TaskListItem{
			Id:        item.Id,
			Name:      item.Name,
			Status:    item.Status,
			CreatedAt: item.CreatedAt,
		}
	}

	return res, nil
}
