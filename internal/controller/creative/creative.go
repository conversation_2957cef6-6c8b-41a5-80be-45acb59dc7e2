package creative

import (
	"context"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"

	v1 "ad-pro-v2/api/creative/v1"
	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/service"
)

type Controller struct{}

// List 获取创意列表
func (c *Controller) List(ctx context.Context, req *v1.CreativeListReq) (res *v1.CreativeListRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 转换请求
	modelReq := &model.CreativeListReq{
		Name: req.Name,
		Page: req.Page,
		Size: req.Size,
	}

	// 调用服务层获取列表
	modelRes, err := service.Creative().GetList(ctx, modelReq)
	if err != nil {
		return nil, err
	}

	// 转换响应
	res = &v1.CreativeListRes{
		Total: modelRes.Total,
		Page:  modelRes.Page,
		Size:  modelRes.Size,
	}

	// 转换列表数据
	res.List = make([]*v1.Creative, len(modelRes.List))
	for i, item := range modelRes.List {
		creative := &v1.Creative{
			Id:              item.Id,
			Name:            item.Name,
			ImageUrl:        item.ImageUrl,
			ImageArea:       item.ImageArea,
			BackgroundColor: item.BackgroundColor,
			CreatedAt:       item.CreatedAt,
			UpdatedAt:       item.UpdatedAt,
		}

		// 转换热区数据
		creative.HotAreas = make([]v1.HotArea, len(item.HotAreas))
		for j, area := range item.HotAreas {
			creative.HotAreas[j] = v1.HotArea{
				Width:     area.Width,
				Height:    area.Height,
				Unit:      area.Unit,
				X:         area.X,
				Y:         area.Y,
				EventType: area.EventType,
			}
		}

		res.List[i] = creative
	}

	return res, nil
}

// Detail 获取创意详情
func (c *Controller) Detail(ctx context.Context, req *v1.CreativeDetailReq) (res *v1.CreativeDetailRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 调用服务层获取创意信息
	modelCreative, err := service.Creative().GetById(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// 转换响应
	creative := &v1.Creative{
		Id:              modelCreative.Id,
		Name:            modelCreative.Name,
		ImageUrl:        modelCreative.ImageUrl,
		ImageArea:       modelCreative.ImageArea,
		BackgroundColor: modelCreative.BackgroundColor,
		CreatedAt:       modelCreative.CreatedAt,
		UpdatedAt:       modelCreative.UpdatedAt,
	}

	// 转换热区数据
	creative.HotAreas = make([]v1.HotArea, len(modelCreative.HotAreas))
	for i, area := range modelCreative.HotAreas {
		creative.HotAreas[i] = v1.HotArea{
			Id:        area.Id,
			Width:     area.Width,
			Height:    area.Height,
			Unit:      area.Unit,
			X:         area.X,
			Y:         area.Y,
			EventType: area.EventType,
		}
	}

	return &v1.CreativeDetailRes{Creative: creative}, nil
}

// Create 创建创意
func (c *Controller) Create(ctx context.Context, req *v1.CreativeCreateReq) (res *v1.CreativeCreateRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 转换请求
	modelReq := &model.CreativeCreateReq{
		Name:            req.Name,
		ImageUrl:        req.ImageUrl,
		ImageArea:       req.ImageArea,
		BackgroundColor: req.BackgroundColor,
	}

	// 转换热区数据
	modelReq.HotAreas = make([]model.HotArea, len(req.HotAreas))
	for i, area := range req.HotAreas {
		modelReq.HotAreas[i] = model.HotArea{
			Width:     area.Width,
			Height:    area.Height,
			Unit:      area.Unit,
			X:         area.X,
			Y:         area.Y,
			EventType: area.EventType,
			Id:        area.Id,
		}
	}

	// 调用服务层创建创意
	modelRes, err := service.Creative().Create(ctx, modelReq)
	if err != nil {
		return nil, err
	}

	// 转换响应
	return &v1.CreativeCreateRes{Id: modelRes.Id}, nil
}

// Update 更新创意
func (c *Controller) Update(ctx context.Context, req *v1.CreativeUpdateReq) (res *v1.CreativeUpdateRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 转换请求
	modelReq := &model.CreativeUpdateReq{
		Id:              req.Id,
		Name:            req.Name,
		ImageUrl:        req.ImageUrl,
		ImageArea:       req.ImageArea,
		BackgroundColor: req.BackgroundColor,
	}

	// 转换热区数据
	modelReq.HotAreas = make([]model.HotArea, len(req.HotAreas))
	for i, area := range req.HotAreas {
		modelReq.HotAreas[i] = model.HotArea{
			Width:     area.Width,
			Height:    area.Height,
			Unit:      area.Unit,
			X:         area.X,
			Y:         area.Y,
			EventType: area.EventType,
			Id:        area.Id,
		}
	}

	// 调用服务层更新创意
	_, err = service.Creative().Update(ctx, modelReq)
	if err != nil {
		return nil, err
	}

	// 删除创意缓存
	creativeKey := fmt.Sprintf("ad_creative:%d", req.Id)
	_, err = g.Redis().Del(ctx, creativeKey)
	if err != nil {
		g.Log().Errorf(ctx, "删除创意缓存失败: %v", err)
	}

	// 返回响应
	return &v1.CreativeUpdateRes{}, nil
}

// Delete 删除创意
func (c *Controller) Delete(ctx context.Context, req *v1.CreativeDeleteReq) (res *v1.CreativeDeleteRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 调用服务层删除创意
	_, err = service.Creative().Delete(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	// 返回响应
	return &v1.CreativeDeleteRes{}, nil
}
