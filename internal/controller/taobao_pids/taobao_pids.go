package taobao_pids

import (
	"context"
	"regexp"
	"strconv"
	"strings"

	v1 "ad-pro-v2/api/v1"
	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/model/entity"
	"ad-pro-v2/internal/service"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/net/ghttp"
)

// Controller 淘联链接控制器
type Controller struct{}

// List 获取淘联链接列表
func (c *Controller) List(ctx context.Context, req *v1.TaobaoPidsListReq) (res *v1.TaobaoPidsListRes, err error) {
	res = &v1.TaobaoPidsListRes{}

	// 构建查询条件
	filter := &model.TaobaoPidsFilter{
		Page:     req.Page,
		PageSize: req.Limit,
		Keyword:  req.Keyword,
		IsUsed:   req.IsUsed,
		ActType:  req.ActType,
		Sort:     req.Sort,
	}

	// 调用服务层获取数据
	items, total, usedCount, unusedCount, err := service.TaobaoPids().GetList(ctx, filter)
	if err != nil {
		return nil, err
	}

	// 填充返回数据
	res.Items = items
	res.Total = total
	res.UsedCount = usedCount
	res.UnusedCount = unusedCount

	return res, nil
}

// extractMemberIdFromPid 从PID中提取会员ID
func extractMemberIdFromPid(pid string) string {
	// 使用正则表达式匹配 mm_数字_数字_数字 格式
	re := regexp.MustCompile(`mm_(\d+)_\d+_\d+`)
	matches := re.FindStringSubmatch(pid)
	if len(matches) >= 2 {
		return matches[1]
	}
	// 如果格式不匹配，也尝试从 _ 分割
	parts := strings.Split(pid, "_")
	if len(parts) >= 2 {
		return parts[1]
	}
	return ""
}

// getAccountNameByMemberId 根据会员ID获取账户名称
func getAccountNameByMemberId(memberId string) string {
	// 建立会员ID与账户名称的映射关系
	accountMap := map[string]string{
		"**********": "上海壹棵树@alimama(**********)",
		"**********": "长沙易欢科技@alimama(**********)",
		"**********": "杭州旻天科技2021(**********)",
		"*********":  "西典科技",
	}

	// 查找并返回对应的账户名称，如果不存在则返回空
	if name, exists := accountMap[memberId]; exists {
		return name
	}
	// 如果找不到匹配，则返回格式化的默认名称
	return "未知账户(" + memberId + ")"
}

// Create 创建淘联链接
func (c *Controller) Create(ctx context.Context, req *v1.TaobaoPidCreateReq) (res *v1.TaobaoPidCreateRes, err error) {
	res = &v1.TaobaoPidCreateRes{}

	// 从PID中提取会员ID
	memberIdStr := extractMemberIdFromPid(req.Pid)
	if memberIdStr == "" {
		return nil, gerror.New("无法从PID中提取会员ID")
	}

	// 根据会员ID确定账户名称
	accountName := getAccountNameByMemberId(memberIdStr)

	// 将会员ID字符串转换为uint64
	memberId, err := strconv.ParseUint(memberIdStr, 10, 64)
	if err != nil {
		return nil, gerror.New("会员ID格式错误")
	}

	// 构建数据模型，使用提取的会员ID和对应的账户名称
	data := &entity.TaobaoPids{
		AccountName: accountName,
		MemberId:    memberId,
		ZoneName:    req.ZoneName,
		Pid:         req.Pid,
		ActType:     req.ActType,
	}

	// 调用服务层创建数据
	id, err := service.TaobaoPids().Create(ctx, data)
	if err != nil {
		return nil, err
	}

	res.Id = id
	return res, nil
}

// Update 更新淘联链接
func (c *Controller) Update(ctx context.Context, req *v1.TaobaoPidUpdateReq) (res *v1.TaobaoPidUpdateRes, err error) {
	res = &v1.TaobaoPidUpdateRes{}

	// 从HTTP请求中获取路径参数
	r := ghttp.RequestFromCtx(ctx)
	idStr := r.GetRouter("id").String()
	if idStr == "" {
		return nil, gerror.New("invalid id")
	}
	id := r.GetRouter("id").Uint64()

	// 从PID中提取会员ID
	memberIdStr := extractMemberIdFromPid(req.Pid)
	if memberIdStr == "" {
		return nil, gerror.New("无法从PID中提取会员ID")
	}

	// 根据会员ID确定账户名称
	accountName := getAccountNameByMemberId(memberIdStr)

	// 将会员ID字符串转换为uint64
	memberId, err := strconv.ParseUint(memberIdStr, 10, 64)
	if err != nil {
		return nil, gerror.New("会员ID格式错误")
	}

	// 构建数据模型
	data := &entity.TaobaoPids{
		Id:          id,
		AccountName: accountName,
		MemberId:    memberId,
		ZoneName:    req.ZoneName,
		Pid:         req.Pid,
		ActType:     req.ActType,
	}

	// 调用服务层更新数据
	err = service.TaobaoPids().Update(ctx, data)
	if err != nil {
		return nil, err
	}

	return res, nil
}

// Delete 删除淘联链接
func (c *Controller) Delete(ctx context.Context, req *v1.TaobaoPidDeleteReq) (res *v1.TaobaoPidDeleteRes, err error) {
	res = &v1.TaobaoPidDeleteRes{}

	// 从HTTP请求中获取路径参数
	r := ghttp.RequestFromCtx(ctx)
	idStr := r.GetRouter("id").String()
	if idStr == "" {
		return nil, gerror.New("invalid id")
	}
	id := r.GetRouter("id").Uint64()

	// 调用服务层删除数据
	err = service.TaobaoPids().Delete(ctx, id)
	if err != nil {
		return nil, err
	}

	return res, nil
}
