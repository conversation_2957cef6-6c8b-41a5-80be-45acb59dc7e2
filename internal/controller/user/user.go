package user

import (
	v1 "ad-pro-v2/api/v1"
	"ad-pro-v2/internal/middleware"
	"ad-pro-v2/internal/service"
	"context"

	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/model"

	"github.com/gogf/gf/v2/errors/gerror"
)

// Controller 用户管理控制器
type Controller struct{}

// New 创建用户管理控制器
func New() *Controller {
	return &Controller{}
}

// GetUsers 获取用户列表
func (c *Controller) GetUsers(ctx context.Context, req *v1.GetUsersReq) (res *v1.GetUsersRes, err error) {
	// 权限检查
	if err = middleware.CheckPermission(ctx, "system.account.view"); err != nil {
		return nil, err
	}

	// 参数验证
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 20
	}

	// 调用Logic层
	return service.User.GetUsers(ctx, req)
}

// CreateUser 创建用户
func (c *Controller) CreateUser(ctx context.Context, req *v1.CreateUserReq) (res *v1.CreateUserRes, err error) {
	// 权限检查
	if err = middleware.CheckPermission(ctx, "system.account.create"); err != nil {
		return nil, err
	}

	// 参数验证已在API定义中通过tag完成

	// 调用Logic层
	return service.User.CreateUser(ctx, req)
}

// UpdateUser 更新用户
func (c *Controller) UpdateUser(ctx context.Context, req *v1.UpdateUserReq) (res *v1.UpdateUserRes, err error) {
	// 权限检查
	if err = middleware.CheckPermission(ctx, "system.account.edit"); err != nil {
		return nil, err
	}

	// 参数验证已在API定义中通过tag完成

	// 调用Logic层
	return service.User.UpdateUser(ctx, req)
}

// DeleteUser 删除用户
func (c *Controller) DeleteUser(ctx context.Context, req *v1.DeleteUserReq) (res *v1.DeleteUserRes, err error) {
	// 权限检查
	if err = middleware.CheckPermission(ctx, "system.account.delete"); err != nil {
		return nil, err
	}

	if req.Id <= 0 {
		return nil, gerror.New("用户ID不能为空")
	}

	// 调用Logic层
	return service.User.DeleteUser(ctx, req)
}

// LockUser 锁定用户
func (c *Controller) LockUser(ctx context.Context, req *v1.LockUserReq) (res *v1.LockUserRes, err error) {
	// 权限检查
	if err = middleware.CheckPermission(ctx, "system.account.edit"); err != nil {
		return nil, err
	}

	if req.Id <= 0 {
		return nil, gerror.New("用户ID不能为空")
	}
	if req.LockReason == "" {
		return nil, gerror.New("锁定原因不能为空")
	}

	// 调用Logic层
	return service.User.LockUser(ctx, req)
}

// UnlockUser 解锁用户
func (c *Controller) UnlockUser(ctx context.Context, req *v1.UnlockUserReq) (res *v1.UnlockUserRes, err error) {
	// 权限检查
	if err = middleware.CheckPermission(ctx, "system.account.edit"); err != nil {
		return nil, err
	}

	if req.Id <= 0 {
		return nil, gerror.New("用户ID不能为空")
	}

	// 调用Logic层
	return service.User.UnlockUser(ctx, req)
}

// ResetPassword 重置密码
func (c *Controller) ResetPassword(ctx context.Context, req *v1.ResetPasswordReq) (res *v1.ResetPasswordRes, err error) {
	// 权限检查
	if err = middleware.CheckPermission(ctx, "system.account.reset_password"); err != nil {
		return nil, err
	}

	if req.Id <= 0 {
		return nil, gerror.New("用户ID不能为空")
	}
	if req.NewPassword == "" {
		return nil, gerror.New("新密码不能为空")
	}

	// 调用Logic层
	return service.User.ResetPassword(ctx, req)
}

// GetUserOptions 获取用户选项
func (c *Controller) GetUserOptions(ctx context.Context, req *v1.GetUserOptionsReq) (res *v1.GetUserOptionsRes, err error) {
	// 权限检查
	if err = middleware.CheckPermission(ctx, "system.account.view"); err != nil {
		return nil, err
	}

	// 调用Logic层
	return service.User.GetUserOptions(ctx, req)
}

// MediaUserList 获取媒介用户列表（保留原有接口）
func (c *Controller) MediaUserList(ctx context.Context, req *model.MediaUserListReq) (res *model.MediaUserListRes, err error) {
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 只有非媒介角色可以查看媒介列表
	if userInfo.IsMedia() {
		return nil, model.ErrNotAuthorized
	}

	// 查询媒介用户列表
	var users []*model.MediaUser
	err = dao.Users.Ctx(ctx).
		Fields("id", "name as username", "real_name as realName").
		WhereIn("role", []int{1, 5}). // 媒介角色
		Where("status", 1).           // 正常状态
		Order("id DESC").
		Scan(&users)
	if err != nil {
		return nil, err
	}

	return &model.MediaUserListRes{
		List: users,
	}, nil
}
