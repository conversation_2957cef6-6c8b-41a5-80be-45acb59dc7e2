package plan_statistics

import (
	"context"
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/frame/g"

	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/service"
)

type sPlanStatistics struct{}

func init() {
	service.RegisterPlanStatistics(New())
}

// New 创建计划统计服务实例
func New() *sPlanStatistics {
	return &sPlanStatistics{}
}

// GetPlanPvUv 获取计划PV/UV数据
func (s *sPlanStatistics) GetPlanPvUv(ctx context.Context, planId int, date string) (map[string]int, error) {
	// 获取推广位ID列表
	pids, err := dao.PromotionZones.Ctx(ctx).
		Where("plan_id", planId).
		Where("zone_type", "ele").
		Array("pid")
	if err != nil {
		g.Log().Error(ctx, "获取推广位列表失败", err)
		return nil, err
	}

	// 如果没有推广位，返回零值
	if len(pids) == 0 {
		return map[string]int{
			"pv": 0,
			"uv": 0,
		}, nil
	}

	// 将pids转换为字符串数组
	pidStrings := make([]string, len(pids))
	for i, pid := range pids {
		pidStrings[i] = pid.String()
	}

	// 构建SQL查询
	query := fmt.Sprintf(`
		SELECT 
			IFNULL(SUM(click_pv), 0) as pv,
			IFNULL(SUM(click_uv), 0) as uv
		FROM byn_data.elm_ad_zone_reports
		WHERE pid IN ('%s')
		AND report_date = ?
	`, strings.Join(pidStrings, "','"))

	g.Log().Debug(ctx, "SQL query:", query, "date:", date)

	// 执行查询
	var result struct {
		PV int `json:"pv" db:"pv"`
		UV int `json:"uv" db:"uv"`
	}

	err = g.DB().Ctx(ctx).Raw(query, date).Scan(&result)
	if err != nil {
		g.Log().Error(ctx, "查询PV/UV数据失败", err)
		return nil, err
	}

	return map[string]int{
		"pv": result.PV,
		"uv": result.UV,
	}, nil
}
