package department

import (
	v1 "ad-pro-v2/api/v1"
	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/model/entity"
	"ad-pro-v2/internal/service"
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

type sDepartment struct{}

func init() {
	service.RegisterDepartment(New())
}

func New() service.IDepartment {
	return &sDepartment{}
}

// GetDepartments 获取部门列表
func (s *sDepartment) GetDepartments(ctx context.Context, req *v1.GetDepartmentsReq) (*v1.GetDepartmentsRes, error) {
	var departments []*entity.Departments

	// 构建查询条件
	query := dao.Departments.Ctx(ctx)

	// 添加筛选条件
	if req.ParentId != nil {
		query = query.Where(dao.Departments.Columns().ParentId, *req.ParentId)
	}
	if req.Status > 0 {
		query = query.Where(dao.Departments.Columns().Status, req.Status)
	}

	// 查询部门列表
	err := query.OrderAsc(dao.Departments.Columns().Sort).
		OrderAsc(dao.Departments.Columns().CreatedAt).
		Scan(&departments)
	if err != nil {
		return nil, gerror.Wrap(err, "获取部门列表失败")
	}

	// 转换为返回格式
	var list []*v1.DepartmentInfo
	for _, dept := range departments {
		deptInfo := &v1.DepartmentInfo{
			Id:          dept.Id,
			Name:        dept.Name,
			ParentId:    dept.ParentId,
			ManagerId:   dept.ManagerId,
			Description: dept.Description,
			Status:      dept.Status,
			Sort:        dept.Sort,
			CreatedAt:   dept.CreatedAt,
			UpdatedAt:   dept.UpdatedAt,
		}

		// 获取上级部门名称
		if dept.ParentId != nil {
			parent, err := dao.Departments.Ctx(ctx).Where(dao.Departments.Columns().Id, *dept.ParentId).One()
			if err == nil && parent != nil {
				deptInfo.ParentName = parent["name"].String()
			}
		}

		// 获取负责人姓名
		if dept.ManagerId != nil {
			manager, err := dao.Users.Ctx(ctx).Where(dao.Users.Columns().Id, *dept.ManagerId).One()
			if err == nil && manager != nil {
				deptInfo.ManagerName = manager["real_name"].String()
			}
		}

		// 获取用户数量
		userCount, err := dao.Users.Ctx(ctx).
			Where(dao.Users.Columns().Department, dept.Name).
			Where(dao.Users.Columns().Status, 1).
			Count()
		if err == nil {
			deptInfo.UserCount = userCount
		}

		list = append(list, deptInfo)
	}

	return &v1.GetDepartmentsRes{
		List: list,
	}, nil
}

// GetDepartmentTree 获取部门树
func (s *sDepartment) GetDepartmentTree(ctx context.Context, req *v1.GetDepartmentTreeReq) (*v1.GetDepartmentTreeRes, error) {
	// 获取所有部门
	var departments []*entity.Departments
	err := dao.Departments.Ctx(ctx).
		Where(dao.Departments.Columns().Status, 1).
		OrderAsc(dao.Departments.Columns().Sort).
		OrderAsc(dao.Departments.Columns().CreatedAt).
		Scan(&departments)
	if err != nil {
		return nil, gerror.Wrap(err, "获取部门列表失败")
	}

	// 转换为部门信息并构建树结构
	deptMap := make(map[uint64]*v1.DepartmentInfo)
	var rootDepts []*v1.DepartmentInfo

	// 先创建所有部门节点
	for _, dept := range departments {
		deptInfo := &v1.DepartmentInfo{
			Id:          dept.Id,
			Name:        dept.Name,
			ParentId:    dept.ParentId,
			ManagerId:   dept.ManagerId,
			Description: dept.Description,
			Status:      dept.Status,
			Sort:        dept.Sort,
			Children:    make([]*v1.DepartmentInfo, 0),
			CreatedAt:   dept.CreatedAt,
			UpdatedAt:   dept.UpdatedAt,
		}

		// 获取负责人姓名
		if dept.ManagerId != nil {
			manager, err := dao.Users.Ctx(ctx).Where(dao.Users.Columns().Id, *dept.ManagerId).One()
			if err == nil && manager != nil {
				deptInfo.ManagerName = manager["real_name"].String()
			}
		}

		// 获取用户数量
		userCount, err := dao.Users.Ctx(ctx).
			Where(dao.Users.Columns().Department, dept.Name).
			Where(dao.Users.Columns().Status, 1).
			Count()
		if err == nil {
			deptInfo.UserCount = userCount
		}

		deptMap[dept.Id] = deptInfo
	}

	// 构建树结构
	for _, deptInfo := range deptMap {
		if deptInfo.ParentId == nil {
			// 根部门
			rootDepts = append(rootDepts, deptInfo)
		} else {
			// 子部门，添加到父部门的children中
			if parent, exists := deptMap[*deptInfo.ParentId]; exists {
				parent.Children = append(parent.Children, deptInfo)
			}
		}
	}

	return &v1.GetDepartmentTreeRes{
		Tree: rootDepts,
	}, nil
}

// CreateDepartment 创建部门
func (s *sDepartment) CreateDepartment(ctx context.Context, req *v1.CreateDepartmentReq) (*v1.CreateDepartmentRes, error) {
	// 检查部门名称是否存在
	count, err := dao.Departments.Ctx(ctx).Where(dao.Departments.Columns().Name, req.Name).Count()
	if err != nil {
		return nil, gerror.Wrap(err, "检查部门名称失败")
	}
	if count > 0 {
		return nil, gerror.New("部门名称已存在")
	}

	// 验证上级部门是否存在且有效
	if req.ParentId != nil {
		parentCount, err := dao.Departments.Ctx(ctx).
			Where(dao.Departments.Columns().Id, *req.ParentId).
			Where(dao.Departments.Columns().Status, 1).
			Count()
		if err != nil {
			return nil, gerror.Wrap(err, "检查上级部门失败")
		}
		if parentCount == 0 {
			return nil, gerror.New("指定的上级部门不存在或已禁用")
		}
	}

	// 验证负责人是否存在且有效
	if req.ManagerId != nil {
		managerCount, err := dao.Users.Ctx(ctx).
			Where(dao.Users.Columns().Id, *req.ManagerId).
			Where(dao.Users.Columns().Status, 1).
			Count()
		if err != nil {
			return nil, gerror.Wrap(err, "检查负责人失败")
		}
		if managerCount == 0 {
			return nil, gerror.New("指定的负责人不存在或已离职")
		}
	}

	// 创建部门
	deptId, err := dao.Departments.Ctx(ctx).Data(g.Map{
		dao.Departments.Columns().Name:        req.Name,
		dao.Departments.Columns().ParentId:    req.ParentId,
		dao.Departments.Columns().ManagerId:   req.ManagerId,
		dao.Departments.Columns().Description: req.Description,
		dao.Departments.Columns().Sort:        req.Sort,
		dao.Departments.Columns().Status:      1, // 默认启用
		dao.Departments.Columns().CreatedAt:   gtime.Now(),
		dao.Departments.Columns().UpdatedAt:   gtime.Now(),
	}).InsertAndGetId()

	if err != nil {
		return nil, gerror.Wrap(err, "创建部门失败")
	}

	return &v1.CreateDepartmentRes{
		Id: gconv.Uint64(deptId),
	}, nil
}

// UpdateDepartment 更新部门
func (s *sDepartment) UpdateDepartment(ctx context.Context, req *v1.UpdateDepartmentReq) (*v1.UpdateDepartmentRes, error) {
	// 检查部门是否存在
	dept, err := dao.Departments.Ctx(ctx).Where(dao.Departments.Columns().Id, req.Id).One()
	if err != nil {
		return nil, gerror.Wrap(err, "查询部门失败")
	}
	if dept == nil {
		return nil, gerror.New("部门不存在")
	}

	// 检查部门名称是否被其他部门使用
	count, err := dao.Departments.Ctx(ctx).
		Where(dao.Departments.Columns().Name, req.Name).
		WhereNot(dao.Departments.Columns().Id, req.Id).
		Count()
	if err != nil {
		return nil, gerror.Wrap(err, "检查部门名称失败")
	}
	if count > 0 {
		return nil, gerror.New("部门名称已被其他部门使用")
	}

	// 验证上级部门是否存在且有效
	if req.ParentId != nil {
		// 不能设置自己为上级
		if *req.ParentId == req.Id {
			return nil, gerror.New("不能设置自己为上级部门")
		}

		parentCount, err := dao.Departments.Ctx(ctx).
			Where(dao.Departments.Columns().Id, *req.ParentId).
			Where(dao.Departments.Columns().Status, 1).
			Count()
		if err != nil {
			return nil, gerror.Wrap(err, "检查上级部门失败")
		}
		if parentCount == 0 {
			return nil, gerror.New("指定的上级部门不存在或已禁用")
		}

		// 检查是否会形成循环引用
		if s.checkCircularReference(ctx, req.Id, *req.ParentId) {
			return nil, gerror.New("不能设置子部门为上级部门，这会形成循环引用")
		}
	}

	// 验证负责人是否存在且有效
	if req.ManagerId != nil {
		managerCount, err := dao.Users.Ctx(ctx).
			Where(dao.Users.Columns().Id, *req.ManagerId).
			Where(dao.Users.Columns().Status, 1).
			Count()
		if err != nil {
			return nil, gerror.Wrap(err, "检查负责人失败")
		}
		if managerCount == 0 {
			return nil, gerror.New("指定的负责人不存在或已离职")
		}
	}

	// 更新部门
	_, err = dao.Departments.Ctx(ctx).
		Where(dao.Departments.Columns().Id, req.Id).
		Data(g.Map{
			dao.Departments.Columns().Name:        req.Name,
			dao.Departments.Columns().ParentId:    req.ParentId,
			dao.Departments.Columns().ManagerId:   req.ManagerId,
			dao.Departments.Columns().Description: req.Description,
			dao.Departments.Columns().Status:      req.Status,
			dao.Departments.Columns().Sort:        req.Sort,
			dao.Departments.Columns().UpdatedAt:   gtime.Now(),
		}).Update()

	if err != nil {
		return nil, gerror.Wrap(err, "更新部门失败")
	}

	return &v1.UpdateDepartmentRes{}, nil
}

// DeleteDepartment 删除部门
func (s *sDepartment) DeleteDepartment(ctx context.Context, req *v1.DeleteDepartmentReq) (*v1.DeleteDepartmentRes, error) {
	// 检查部门是否存在
	dept, err := dao.Departments.Ctx(ctx).Where(dao.Departments.Columns().Id, req.Id).One()
	if err != nil {
		return nil, gerror.Wrap(err, "查询部门失败")
	}
	if dept == nil {
		return nil, gerror.New("部门不存在")
	}

	// 检查是否有子部门
	childCount, err := dao.Departments.Ctx(ctx).Where(dao.Departments.Columns().ParentId, req.Id).Count()
	if err != nil {
		return nil, gerror.Wrap(err, "检查子部门失败")
	}
	if childCount > 0 {
		return nil, gerror.New("该部门还有子部门，不能删除")
	}

	// 检查是否有用户属于该部门
	userCount, err := dao.Users.Ctx(ctx).
		Where(dao.Users.Columns().Department, dept["name"].String()).
		Where(dao.Users.Columns().Status, 1).
		Count()
	if err != nil {
		return nil, gerror.Wrap(err, "检查部门用户失败")
	}
	if userCount > 0 {
		return nil, gerror.New("该部门还有用户，不能删除")
	}

	// 删除部门
	_, err = dao.Departments.Ctx(ctx).Where(dao.Departments.Columns().Id, req.Id).Delete()
	if err != nil {
		return nil, gerror.Wrap(err, "删除部门失败")
	}

	return &v1.DeleteDepartmentRes{}, nil
}

// GetDepartmentOptions 获取部门选项
func (s *sDepartment) GetDepartmentOptions(ctx context.Context, req *v1.GetDepartmentOptionsReq) (*v1.GetDepartmentOptionsRes, error) {
	// 获取所有启用的部门
	var departments []*entity.Departments
	query := dao.Departments.Ctx(ctx).Where(dao.Departments.Columns().Status, 1)

	// 如果需要排除特定部门及其子部门
	if req.ExcludeId != nil {
		excludeIds := s.getExcludeIds(ctx, *req.ExcludeId)
		if len(excludeIds) > 0 {
			query = query.WhereNotIn(dao.Departments.Columns().Id, excludeIds)
		}
	}

	err := query.OrderAsc(dao.Departments.Columns().Sort).
		OrderAsc(dao.Departments.Columns().CreatedAt).
		Scan(&departments)
	if err != nil {
		return nil, gerror.Wrap(err, "获取部门列表失败")
	}

	// 构建树形选项
	deptMap := make(map[uint64]*v1.DepartmentTreeOption)
	var rootOptions []*v1.DepartmentTreeOption

	// 先创建所有部门节点
	for _, dept := range departments {
		option := &v1.DepartmentTreeOption{
			Value:    dept.Id,
			Label:    dept.Name,
			Children: make([]*v1.DepartmentTreeOption, 0),
		}
		deptMap[dept.Id] = option
	}

	// 构建树结构
	for _, dept := range departments {
		option := deptMap[dept.Id]
		if dept.ParentId == nil {
			// 根部门
			rootOptions = append(rootOptions, option)
		} else {
			// 子部门，添加到父部门的children中
			if parent, exists := deptMap[*dept.ParentId]; exists {
				parent.Children = append(parent.Children, option)
			}
		}
	}

	return &v1.GetDepartmentOptionsRes{
		List: rootOptions,
	}, nil
}

// 检查循环引用
func (s *sDepartment) checkCircularReference(ctx context.Context, deptId, parentId uint64) bool {
	// 向上递归查找，看parentId的上级链中是否包含deptId
	currentId := parentId
	visited := make(map[uint64]bool)

	for currentId != 0 {
		// 防止无限循环
		if visited[currentId] {
			return true
		}
		visited[currentId] = true

		// 如果找到了deptId，说明形成循环
		if currentId == deptId {
			return true
		}

		// 查找当前部门的上级
		dept, err := dao.Departments.Ctx(ctx).
			Where(dao.Departments.Columns().Id, currentId).
			One()
		if err != nil || dept == nil {
			break
		}

		if dept[dao.Departments.Columns().ParentId].IsNil() {
			break
		}

		currentId = gconv.Uint64(dept[dao.Departments.Columns().ParentId])
	}

	return false
}

// 获取需要排除的部门ID列表（包括自己和所有子部门）
func (s *sDepartment) getExcludeIds(ctx context.Context, deptId uint64) []uint64 {
	var excludeIds []uint64
	excludeIds = append(excludeIds, deptId)

	// 递归获取所有子部门
	s.getChildIds(ctx, deptId, &excludeIds)

	return excludeIds
}

// 递归获取所有子部门ID
func (s *sDepartment) getChildIds(ctx context.Context, parentId uint64, ids *[]uint64) {
	var children []*entity.Departments
	err := dao.Departments.Ctx(ctx).
		Where(dao.Departments.Columns().ParentId, parentId).
		Scan(&children)
	if err != nil {
		return
	}

	for _, child := range children {
		*ids = append(*ids, child.Id)
		// 递归获取子部门的子部门
		s.getChildIds(ctx, child.Id, ids)
	}
}
