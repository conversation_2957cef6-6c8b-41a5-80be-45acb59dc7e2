package taobao_pids

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/gconv"

	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/model/entity"
	"ad-pro-v2/internal/service"
)

func init() {
	service.RegisterTaobaoPids(New())
}

// 实现ITaobaoPids接口
type sTaobaoPids struct{}

// New 创建淘联链接服务实例
func New() *sTaobaoPids {
	return &sTaobaoPids{}
}

// GetList 获取淘联链接列表
func (s *sTaobaoPids) GetList(ctx context.Context, filter *model.TaobaoPidsFilter) (items interface{}, total int, usedCount int, unusedCount int, err error) {
	m := dao.TaobaoPids.Ctx(ctx)
	// 统计总数
	usedCount, err = dao.TaobaoPids.Ctx(ctx).
		Where(dao.TaobaoPids.Columns().IsUsed, 1).
		Where(dao.TaobaoPids.Columns().DeletedAt, nil).
		Count()
	if err != nil {
		return nil, 0, 0, 0, err
	}
	unusedCount, err = dao.TaobaoPids.Ctx(ctx).
		Where(dao.TaobaoPids.Columns().IsUsed, 0).
		Where(dao.TaobaoPids.Columns().DeletedAt, nil).
		Count()
	if err != nil {
		return nil, 0, 0, 0, err
	}

	// 筛选条件
	if filter.IsUsed != -1 {
		m = m.Where(dao.TaobaoPids.Columns().IsUsed, filter.IsUsed)
	}
	if filter.Keyword != "" {
		m = m.WhereOr(dao.TaobaoPids.Columns().ZoneName+" LIKE ?", "%"+filter.Keyword+"%").
			WhereOr(dao.TaobaoPids.Columns().Pid+" LIKE ?", "%"+filter.Keyword+"%")
	}
	if filter.ActType > 0 {
		m = m.Where(dao.TaobaoPids.Columns().ActType, filter.ActType)
	}

	// 排序
	orderBy := "id ASC"
	if filter.Sort != "" {
		// 处理排序字段
		if filter.Sort[0] == '+' {
			orderBy = filter.Sort[1:] + " ASC"
		} else if filter.Sort[0] == '-' {
			orderBy = filter.Sort[1:] + " DESC"
		}
	}

	// 查询总数
	count, err := m.Count()
	if err != nil {
		return nil, 0, 0, 0, err
	}

	// 分页查询
	result, err := m.Page(filter.Page, filter.PageSize).Order(orderBy).All()
	if err != nil {
		return nil, 0, 0, 0, err
	}

	return result, count, usedCount, unusedCount, nil
}

// Create 创建淘联链接
func (s *sTaobaoPids) Create(ctx context.Context, data *entity.TaobaoPids) (id uint64, err error) {
	// 检查PID是否已存在
	count, err := dao.TaobaoPids.Ctx(ctx).
		Where(dao.TaobaoPids.Columns().Pid, data.Pid).
		Where(dao.TaobaoPids.Columns().DeletedAt, nil).
		Count()
	if err != nil {
		return 0, err
	}
	if count > 0 {
		return 0, gerror.New("该推广位ID已存在")
	}

	// 设置默认值
	if data.IsUsed == 0 {
		data.IsUsed = 0
	}

	result, err := dao.TaobaoPids.Ctx(ctx).Insert(data)
	if err != nil {
		return 0, err
	}

	newId, err := result.LastInsertId()
	if err != nil {
		return 0, err
	}

	return uint64(newId), nil
}

// Update 更新淘联链接
func (s *sTaobaoPids) Update(ctx context.Context, data *entity.TaobaoPids) error {
	// 检查记录是否存在
	record, err := dao.TaobaoPids.Ctx(ctx).
		Where(dao.TaobaoPids.Columns().Id, data.Id).
		Where(dao.TaobaoPids.Columns().DeletedAt, nil).
		One()
	if err != nil {
		return err
	}
	if record.IsEmpty() {
		return gerror.New("记录不存在")
	}

	// 检查是否已使用
	isUsed := gconv.Int(record[dao.TaobaoPids.Columns().IsUsed])
	if isUsed == 1 {
		return gerror.New("该推广位已被使用，无法修改")
	}

	// 检查PID是否与其他记录重复
	count, err := dao.TaobaoPids.Ctx(ctx).
		Where(dao.TaobaoPids.Columns().Pid, data.Pid).
		WhereNot(dao.TaobaoPids.Columns().Id, data.Id).
		Where(dao.TaobaoPids.Columns().DeletedAt, nil).
		Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return gerror.New("该推广位ID已存在")
	}

	// 更新数据，只更新允许修改的字段
	updateData := g.Map{
		dao.TaobaoPids.Columns().AccountName: data.AccountName,
		dao.TaobaoPids.Columns().ZoneName:    data.ZoneName,
		dao.TaobaoPids.Columns().Pid:         data.Pid,
		dao.TaobaoPids.Columns().ActType:     data.ActType,
	}

	_, err = dao.TaobaoPids.Ctx(ctx).
		Where(dao.TaobaoPids.Columns().Id, data.Id).
		Data(updateData).
		Update()

	return err
}

// Delete 删除淘联链接
func (s *sTaobaoPids) Delete(ctx context.Context, id uint64) error {
	// 检查记录是否存在
	record, err := dao.TaobaoPids.Ctx(ctx).
		Where(dao.TaobaoPids.Columns().Id, id).
		Where(dao.TaobaoPids.Columns().DeletedAt, nil).
		One()
	if err != nil {
		return err
	}
	if record.IsEmpty() {
		return gerror.New("记录不存在")
	}

	// 检查是否已使用
	isUsed := gconv.Int(record[dao.TaobaoPids.Columns().IsUsed])
	if isUsed == 1 {
		return gerror.New("该推广位已被使用，无法删除")
	}

	// 软删除
	_, err = dao.TaobaoPids.Ctx(ctx).
		Where(dao.TaobaoPids.Columns().Id, id).
		Delete()

	return err
}
