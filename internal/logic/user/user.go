package user

import (
	v1 "ad-pro-v2/api/v1"
	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/model/entity"
	"ad-pro-v2/internal/service"
	"context"

	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

type sUser struct{}

func init() {
	service.RegisterUser(New())
}

func New() service.IUser {
	return &sUser{}
}

// GetUsers 获取用户列表
func (s *sUser) GetUsers(ctx context.Context, req *v1.GetUsersReq) (*v1.GetUsersRes, error) {
	var (
		users []*entity.Users
		total int
		err   error
	)

	// 构建查询条件
	query := dao.Users.Ctx(ctx)

	// 添加筛选条件
	if req.Name != "" {
		query = query.WhereLike(dao.Users.Columns().Name, "%"+req.Name+"%")
	}
	if req.Role > 0 {
		query = query.Where(dao.Users.Columns().Role, req.Role)
	}
	if req.Status > 0 {
		query = query.Where(dao.Users.Columns().Status, req.Status)
	}
	if req.Department != "" {
		query = query.Where(dao.Users.Columns().Department, req.Department)
	}
	if req.IsLocked >= 0 {
		query = query.Where(dao.Users.Columns().IsLocked, req.IsLocked)
	}

	// 获取总数
	total, err = query.Count()
	if err != nil {
		return nil, gerror.Wrap(err, "获取用户总数失败")
	}

	// 分页查询
	err = query.Page(req.Page, req.PageSize).
		OrderDesc(dao.Users.Columns().CreatedAt).
		Scan(&users)
	if err != nil {
		return nil, gerror.Wrap(err, "获取用户列表失败")
	}

	// 转换为返回格式
	var list []*v1.UserInfo
	for _, user := range users {
		userInfo := &v1.UserInfo{
			Id:           user.Id,
			Name:         user.Name,
			RealName:     user.RealName,
			Email:        user.Email,
			Phone:        user.Phone,
			Department:   user.Department,
			Position:     user.Position,
			SupervisorId: user.SupervisorId,
			Role:         user.Role,
			RoleName:     s.getRoleName(user.Role),
			Status:       user.Status,
			IsLocked:     user.IsLocked,
			LockReason:   user.LockReason,
			Remark:       user.Remark,
			LastLoginAt:  user.LastLoginAt,
			LastLoginIp:  user.LastLoginIp,
			CreatedAt:    user.CreatedAt,
			UpdatedAt:    user.UpdatedAt,
		}

		// 获取上级姓名
		if user.SupervisorId != nil {
			supervisor, err := dao.Users.Ctx(ctx).Where(dao.Users.Columns().Id, *user.SupervisorId).One()
			if err == nil && supervisor != nil {
				userInfo.SupervisorName = supervisor["real_name"].String()
			}
		}

		list = append(list, userInfo)
	}

	return &v1.GetUsersRes{
		List:  list,
		Total: total,
		Page:  req.Page,
		Size:  req.PageSize,
	}, nil
}

// CreateUser 创建用户
func (s *sUser) CreateUser(ctx context.Context, req *v1.CreateUserReq) (*v1.CreateUserRes, error) {
	// 检查用户名是否存在
	count, err := dao.Users.Ctx(ctx).Where(dao.Users.Columns().Name, req.Name).Count()
	if err != nil {
		return nil, gerror.Wrap(err, "检查用户名失败")
	}
	if count > 0 {
		return nil, gerror.New("用户名已存在")
	}

	// 检查邮箱是否存在
	count, err = dao.Users.Ctx(ctx).Where(dao.Users.Columns().Email, req.Email).Count()
	if err != nil {
		return nil, gerror.Wrap(err, "检查邮箱失败")
	}
	if count > 0 {
		return nil, gerror.New("邮箱已存在")
	}

	// 验证上级是否存在且有效
	if req.SupervisorId != nil {
		supervisorCount, err := dao.Users.Ctx(ctx).
			Where(dao.Users.Columns().Id, *req.SupervisorId).
			Where(dao.Users.Columns().Status, 1).
			Count()
		if err != nil {
			return nil, gerror.Wrap(err, "检查上级用户失败")
		}
		if supervisorCount == 0 {
			return nil, gerror.New("指定的上级用户不存在或已离职")
		}
	}

	// 加密密码
	hashedPassword, err := gmd5.Encrypt(req.Password)
	if err != nil {
		return nil, gerror.Wrap(err, "密码加密失败")
	}

	// 创建用户
	userId, err := dao.Users.Ctx(ctx).Data(g.Map{
		dao.Users.Columns().Name:         req.Name,
		dao.Users.Columns().RealName:     req.RealName,
		dao.Users.Columns().Email:        req.Email,
		dao.Users.Columns().Phone:        req.Phone,
		dao.Users.Columns().Department:   req.Department,
		dao.Users.Columns().Position:     req.Position,
		dao.Users.Columns().SupervisorId: req.SupervisorId,
		dao.Users.Columns().Role:         req.Role,
		dao.Users.Columns().Password:     hashedPassword,
		dao.Users.Columns().Status:       1, // 默认在职
		dao.Users.Columns().IsLocked:     0, // 默认未锁定
		dao.Users.Columns().Remark:       req.Remark,
		dao.Users.Columns().CreatedAt:    gtime.Now(),
		dao.Users.Columns().UpdatedAt:    gtime.Now(),
	}).InsertAndGetId()

	if err != nil {
		return nil, gerror.Wrap(err, "创建用户失败")
	}

	return &v1.CreateUserRes{
		Id: gconv.Uint64(userId),
	}, nil
}

// UpdateUser 更新用户
func (s *sUser) UpdateUser(ctx context.Context, req *v1.UpdateUserReq) (*v1.UpdateUserRes, error) {
	// 检查用户是否存在
	user, err := dao.Users.Ctx(ctx).Where(dao.Users.Columns().Id, req.Id).One()
	if err != nil {
		return nil, gerror.Wrap(err, "查询用户失败")
	}
	if user == nil {
		return nil, gerror.New("用户不存在")
	}

	// 检查用户名是否被其他用户使用
	count, err := dao.Users.Ctx(ctx).
		Where(dao.Users.Columns().Name, req.Name).
		WhereNot(dao.Users.Columns().Id, req.Id).
		Count()
	if err != nil {
		return nil, gerror.Wrap(err, "检查用户名失败")
	}
	if count > 0 {
		return nil, gerror.New("用户名已被其他用户使用")
	}

	// 检查邮箱是否被其他用户使用
	count, err = dao.Users.Ctx(ctx).
		Where(dao.Users.Columns().Email, req.Email).
		WhereNot(dao.Users.Columns().Id, req.Id).
		Count()
	if err != nil {
		return nil, gerror.Wrap(err, "检查邮箱失败")
	}
	if count > 0 {
		return nil, gerror.New("邮箱已被其他用户使用")
	}

	// 验证上级是否存在且有效
	if req.SupervisorId != nil {
		// 不能设置自己为上级
		if *req.SupervisorId == req.Id {
			return nil, gerror.New("不能设置自己为直属上级")
		}

		supervisorCount, err := dao.Users.Ctx(ctx).
			Where(dao.Users.Columns().Id, *req.SupervisorId).
			Where(dao.Users.Columns().Status, 1).
			Count()
		if err != nil {
			return nil, gerror.Wrap(err, "检查上级用户失败")
		}
		if supervisorCount == 0 {
			return nil, gerror.New("指定的上级用户不存在或已离职")
		}

		// 检查是否会形成循环引用
		if s.checkCircularReference(ctx, req.Id, *req.SupervisorId) {
			return nil, gerror.New("不能设置下级用户为上级，这会形成循环引用")
		}
	}

	// 更新用户
	_, err = dao.Users.Ctx(ctx).
		Where(dao.Users.Columns().Id, req.Id).
		Data(g.Map{
			dao.Users.Columns().Name:         req.Name,
			dao.Users.Columns().RealName:     req.RealName,
			dao.Users.Columns().Email:        req.Email,
			dao.Users.Columns().Phone:        req.Phone,
			dao.Users.Columns().Department:   req.Department,
			dao.Users.Columns().Position:     req.Position,
			dao.Users.Columns().SupervisorId: req.SupervisorId,
			dao.Users.Columns().Role:         req.Role,
			dao.Users.Columns().Status:       req.Status,
			dao.Users.Columns().Remark:       req.Remark,
			dao.Users.Columns().UpdatedAt:    gtime.Now(),
		}).Update()

	if err != nil {
		return nil, gerror.Wrap(err, "更新用户失败")
	}

	return &v1.UpdateUserRes{}, nil
}

// DeleteUser 删除用户
func (s *sUser) DeleteUser(ctx context.Context, req *v1.DeleteUserReq) (*v1.DeleteUserRes, error) {
	// 检查用户是否存在
	user, err := dao.Users.Ctx(ctx).Where(dao.Users.Columns().Id, req.Id).One()
	if err != nil {
		return nil, gerror.Wrap(err, "查询用户失败")
	}
	if user == nil {
		return nil, gerror.New("用户不存在")
	}

	// 检查是否有其他用户以此用户为上级
	count, err := dao.Users.Ctx(ctx).Where(dao.Users.Columns().SupervisorId, req.Id).Count()
	if err != nil {
		return nil, gerror.Wrap(err, "检查下级用户失败")
	}
	if count > 0 {
		return nil, gerror.New("该用户还有下级用户，不能删除")
	}

	// 软删除：设置状态为离职
	_, err = dao.Users.Ctx(ctx).
		Where(dao.Users.Columns().Id, req.Id).
		Data(g.Map{
			dao.Users.Columns().Status:    2, // 设置为离职
			dao.Users.Columns().UpdatedAt: gtime.Now(),
		}).Update()

	if err != nil {
		return nil, gerror.Wrap(err, "删除用户失败")
	}

	return &v1.DeleteUserRes{}, nil
}

// LockUser 锁定用户
func (s *sUser) LockUser(ctx context.Context, req *v1.LockUserReq) (*v1.LockUserRes, error) {
	// 检查用户是否存在
	user, err := dao.Users.Ctx(ctx).Where(dao.Users.Columns().Id, req.Id).One()
	if err != nil {
		return nil, gerror.Wrap(err, "查询用户失败")
	}
	if user == nil {
		return nil, gerror.New("用户不存在")
	}

	// 更新锁定状态
	_, err = dao.Users.Ctx(ctx).
		Where(dao.Users.Columns().Id, req.Id).
		Data(g.Map{
			dao.Users.Columns().IsLocked:   1,
			dao.Users.Columns().LockReason: req.LockReason,
			dao.Users.Columns().UpdatedAt:  gtime.Now(),
		}).Update()

	if err != nil {
		return nil, gerror.Wrap(err, "锁定用户失败")
	}

	return &v1.LockUserRes{}, nil
}

// UnlockUser 解锁用户
func (s *sUser) UnlockUser(ctx context.Context, req *v1.UnlockUserReq) (*v1.UnlockUserRes, error) {
	// 检查用户是否存在
	user, err := dao.Users.Ctx(ctx).Where(dao.Users.Columns().Id, req.Id).One()
	if err != nil {
		return nil, gerror.Wrap(err, "查询用户失败")
	}
	if user == nil {
		return nil, gerror.New("用户不存在")
	}

	// 更新锁定状态
	_, err = dao.Users.Ctx(ctx).
		Where(dao.Users.Columns().Id, req.Id).
		Data(g.Map{
			dao.Users.Columns().IsLocked:   0,
			dao.Users.Columns().LockReason: "",
			dao.Users.Columns().UpdatedAt:  gtime.Now(),
		}).Update()

	if err != nil {
		return nil, gerror.Wrap(err, "解锁用户失败")
	}

	return &v1.UnlockUserRes{}, nil
}

// ResetPassword 重置密码
func (s *sUser) ResetPassword(ctx context.Context, req *v1.ResetPasswordReq) (*v1.ResetPasswordRes, error) {
	// 检查用户是否存在
	user, err := dao.Users.Ctx(ctx).Where(dao.Users.Columns().Id, req.Id).One()
	if err != nil {
		return nil, gerror.Wrap(err, "查询用户失败")
	}
	if user == nil {
		return nil, gerror.New("用户不存在")
	}

	// 加密新密码
	hashedPassword, err := gmd5.Encrypt(req.NewPassword)
	if err != nil {
		return nil, gerror.Wrap(err, "密码加密失败")
	}

	// 更新密码
	_, err = dao.Users.Ctx(ctx).
		Where(dao.Users.Columns().Id, req.Id).
		Data(g.Map{
			dao.Users.Columns().Password:            hashedPassword,
			dao.Users.Columns().ForceChangePassword: 1, // 强制下次登录修改密码
			dao.Users.Columns().PasswordChangedAt:   gtime.Now(),
			dao.Users.Columns().UpdatedAt:           gtime.Now(),
		}).Update()

	if err != nil {
		return nil, gerror.Wrap(err, "重置密码失败")
	}

	return &v1.ResetPasswordRes{}, nil
}

// GetUserOptions 获取用户选项
func (s *sUser) GetUserOptions(ctx context.Context, req *v1.GetUserOptionsReq) (*v1.GetUserOptionsRes, error) {
	// 角色选项
	roles := []*v1.RoleOption{
		{Value: 1, Label: "媒介"},
		{Value: 2, Label: "运营"},
		{Value: 3, Label: "管理层"},
		{Value: 4, Label: "投手"},
		{Value: 5, Label: "财务"},
	}

	// 部门选项（从用户表中获取去重的部门）
	var departments []*v1.DepartmentOption
	departmentList, err := dao.Users.Ctx(ctx).
		Fields(dao.Users.Columns().Department).
		Where(dao.Users.Columns().Department+" != ?", "").
		Group(dao.Users.Columns().Department).
		Array()
	if err != nil {
		return nil, gerror.Wrap(err, "获取部门列表失败")
	}

	for _, dept := range departmentList {
		if dept.String() != "" {
			departments = append(departments, &v1.DepartmentOption{
				Value: dept.String(),
				Label: dept.String(),
			})
		}
	}

	// 上级选项（在职用户）
	var supervisors []*v1.UserOption
	users, err := dao.Users.Ctx(ctx).
		Fields(dao.Users.Columns().Id, dao.Users.Columns().RealName).
		Where(dao.Users.Columns().Status, 1).
		OrderAsc(dao.Users.Columns().RealName).
		All()
	if err != nil {
		return nil, gerror.Wrap(err, "获取用户列表失败")
	}

	for _, user := range users {
		supervisors = append(supervisors, &v1.UserOption{
			Value: gconv.Uint64(user["id"]),
			Label: user["real_name"].String(),
		})
	}

	return &v1.GetUserOptionsRes{
		Roles:       roles,
		Departments: departments,
		Supervisors: supervisors,
	}, nil
}

// 获取角色名称
func (s *sUser) getRoleName(role int) string {
	roleMap := map[int]string{
		1: "媒介",
		2: "运营",
		3: "管理层",
		4: "投手",
		5: "财务",
	}
	if name, exists := roleMap[role]; exists {
		return name
	}
	return "未知角色"
}

// 检查循环引用
func (s *sUser) checkCircularReference(ctx context.Context, userId, supervisorId uint64) bool {
	// 向上递归查找，看supervisorId的上级链中是否包含userId
	currentId := supervisorId
	visited := make(map[uint64]bool)

	for currentId != 0 {
		// 防止无限循环
		if visited[currentId] {
			return true
		}
		visited[currentId] = true

		// 如果找到了userId，说明形成循环
		if currentId == userId {
			return true
		}

		// 查找当前用户的上级
		user, err := dao.Users.Ctx(ctx).
			Where(dao.Users.Columns().Id, currentId).
			One()
		if err != nil || user == nil {
			break
		}

		if user[dao.Users.Columns().SupervisorId].IsNil() {
			break
		}

		currentId = gconv.Uint64(user[dao.Users.Columns().SupervisorId])
	}

	return false
}
