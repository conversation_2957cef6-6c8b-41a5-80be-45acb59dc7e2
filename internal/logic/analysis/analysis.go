package analysis

import (
	v1 "ad-pro-v2/api/analysis/v1"
	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/service"
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

type sAnalysis struct{}

func init() {
	service.Analysis = New()
}

func New() *sAnalysis {
	return &sAnalysis{}
}

// getFilteredPlanIds 获取筛选后的计划ID列表
func (s *sAnalysis) getFilteredPlanIds(ctx context.Context, planId, mediaId, productId, userId int, mediaType string) ([]int64, error) {
	g.Log().Debug(ctx, "getFilteredPlanIds input:", g.Map{
		"planId":    planId,
		"mediaId":   mediaId,
		"productId": productId,
		"userId":    userId,
		"mediaType": mediaType,
	})

	// 如果指定了planId,直接返回
	if planId > 0 {
		return []int64{int64(planId)}, nil
	}

	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	g.Log().Debug(ctx, "Current user info:", userInfo)

	// 构建计划查询
	query := dao.AdSlotPlans.Ctx(ctx)

	// 处理用户ID筛选
	var queryUserId int
	if userId > 0 {
		queryUserId = userId
		g.Log().Debug(ctx, "Using provided user ID:", queryUserId)
	} else {
		if userInfo.Role == 2 || userInfo.Role == 3 {
			queryUserId = 0 // 管理员/运营不限制用户
			g.Log().Debug(ctx, "Admin/Operator role, not filtering by user")
		} else {
			queryUserId = userInfo.Id
			g.Log().Debug(ctx, "Using current user ID:", userInfo.Id)
		}
	}
	if queryUserId > 0 {
		query = query.Where("user_id", queryUserId)
		g.Log().Debug(ctx, "Added user filter:", queryUserId)
	}

	// 根据类型获取媒体ID列表
	var mediaIds []int
	var err error
	if mediaType == "1" {
		// 获取普通投放媒体ID
		mediaQuery := dao.AdMedia.Ctx(ctx).Where("cooperation_type", "traffic")
		values, err := mediaQuery.Array("id")
		if err != nil {
			g.Log().Error(ctx, "Failed to get traffic media IDs:", err)
			return nil, err
		}
		g.Log().Debug(ctx, "Traffic media values:", values)

		mediaIds = make([]int, len(values))
		for i, v := range values {
			mediaIds[i] = v.Int()
		}
		g.Log().Debug(ctx, "Converted traffic media IDs:", mediaIds)
	} else if mediaType == "2" {
		// 获取CPS合作媒体ID
		mediaQuery := dao.AdMedia.Ctx(ctx).Where("cooperation_type", "cps")
		values, err := mediaQuery.Array("id")
		if err != nil {
			g.Log().Error(ctx, "Failed to get CPS media IDs:", err)
			return nil, err
		}
		g.Log().Debug(ctx, "CPS media values:", values)

		mediaIds = make([]int, len(values))
		for i, v := range values {
			mediaIds[i] = v.Int()
		}
		g.Log().Debug(ctx, "Converted CPS media IDs:", mediaIds)
	} else if mediaType == "3" {
		// 获取灯火媒体ID
		mediaQuery := dao.AdMedia.Ctx(ctx).Where("cooperation_type", "dh")
		values, err := mediaQuery.Array("id")
		if err != nil {
			g.Log().Error(ctx, "Failed to get DH media IDs:", err)
			return nil, err
		}
		g.Log().Debug(ctx, "DH media values:", values)

		mediaIds = make([]int, len(values))
		for i, v := range values {
			mediaIds[i] = v.Int()
		}
		g.Log().Debug(ctx, "Converted DH media IDs:", mediaIds)
	} else {
		mediaQuery := dao.AdMedia.Ctx(ctx).WhereIn("cooperation_type", []string{"traffic", "cps", "dh"})
		values, err := mediaQuery.Array("id")
		if err != nil {
			g.Log().Error(ctx, "Failed to get DH media IDs:", err)
			return nil, err
		}
		g.Log().Debug(ctx, "DH media values:", values)

		mediaIds = make([]int, len(values))
		for i, v := range values {
			mediaIds[i] = v.Int()
		}
	}

	g.Log().Debug(ctx, "Final filtered media IDs:", mediaIds)

	// 只有在 mediaIds 非空时才添加 WhereIn 条件
	if len(mediaIds) > 0 {
		query = query.WhereIn("media_id", mediaIds)
		g.Log().Debug(ctx, "Added media_id IN condition:", mediaIds)
	} else {
		// 如果没有符合条件的媒体ID，返回空结果
		g.Log().Debug(ctx, "No matching media IDs found, returning empty result")
		return []int64{}, nil
	}

	// 添加其他筛选条件
	if mediaId > 0 {
		query = query.Where("media_id", mediaId)
		g.Log().Debug(ctx, "Added media filter:", mediaId)
	}
	if productId > 0 {
		query = query.Where("ad_product_id", productId)
		g.Log().Debug(ctx, "Added product filter:", productId)
	}

	// 获取计划ID列表
	planIds, err := query.Array("id")
	if err != nil {
		g.Log().Error(ctx, "Failed to get plan IDs:", err)
		return nil, err
	}

	g.Log().Debug(ctx, "Raw plan IDs result:", planIds)

	// 转换为int64切片
	result := make([]int64, len(planIds))
	for i, id := range planIds {
		result[i] = id.Int64()
	}

	g.Log().Debug(ctx, "Final converted plan IDs:", result)

	return result, nil
}

// GetProfitData 获取收益分析数据
func (s *sAnalysis) GetProfitData(ctx context.Context, req *v1.GetProfitDataReq) (res *v1.GetProfitDataRes, err error) {
	// 获取过滤后的计划ID列表
	planIds, err := s.getFilteredPlanIds(ctx, req.UserId, req.MediaId, req.PlanId, req.ProductId, req.Type)
	if err != nil {
		return nil, err
	}

	// 构建查询条件
	m := dao.AdOrders.Ctx(ctx).
		Where("ad_plan_id IN(?)", planIds).
		Where("leak = 0").
		Where("order_status IN(2,3,4)").
		Where("create_time BETWEEN ? AND ?", req.StartDate, req.EndDate)

	// 按日期分组统计
	records, err := m.Fields(
		"DATE(create_time) as date",
		"SUM(CASE WHEN order_type = 1 THEN commission ELSE 0 END) as eleme_commission",
		"SUM(CASE WHEN order_type = 2 THEN commission ELSE 0 END) as fliggy_commission",
		"IFNULL((SELECT SUM(cost) FROM ad_slot_plan_costs WHERE date = DATE(create_time) AND plan_id IN(?)), 0) as cost",
	).Group("DATE(create_time)").All()

	if err != nil {
		return nil, err
	}

	// 构造返回数据
	res = &v1.GetProfitDataRes{
		List: make([]v1.ProfitData, 0),
	}

	for _, record := range records {
		date := record["date"].String()
		elemeCommission := record["eleme_commission"].Float64()
		fliggyCommission := record["fliggy_commission"].Float64()
		cost := record["cost"].Float64()

		res.List = append(res.List, v1.ProfitData{
			Date:             date,
			ElemeCommission:  elemeCommission,
			FliggyCommission: fliggyCommission,
			Cost:             cost,
		})
	}

	return
}
