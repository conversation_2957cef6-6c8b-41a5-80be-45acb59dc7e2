package dashboard

import (
	v1 "ad-pro-v2/api/dashboard/v1"
	"strconv"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
)

// UserInfo 用户信息结构体 (临时定义，实际应该从consts包获取)
type UserInfo struct {
	Id   int    `json:"id"`
	Role int    `json:"role"`
	Name string `json:"name"`
}

// 用户角色常量
const (
	RoleMedia    = 1 // 媒介账号
	RoleOperator = 2 // 运营
	RoleAdmin    = 3 // 管理员
	RoleFinance  = 4 // 财务
)

// applyUserBusinessRules 应用用户相关的业务规则
func (s *sLogic) applyUserBusinessRules(req *v1.GetMetricsReq, userInfo *UserInfo) *v1.GetMetricsReq {
	processedReq := *req

	// 如果是媒介账号，只能查看自己的数据
	if userInfo.Role == RoleMedia {
		processedReq.UserId = userInfo.Id
	}

	// 如果用户没有指定UserId，且不是管理员/运营，则设置为当前用户
	if req.UserId == 0 && userInfo.Role != RoleAdmin && userInfo.Role != RoleOperator {
		processedReq.UserId = userInfo.Id
	}

	return &processedReq
}

// processMetricsResponse 处理指标响应数据
func (s *sLogic) processMetricsResponse(metrics *v1.GetMetricsRes, userInfo *UserInfo) *v1.GetMetricsRes {
	if metrics == nil {
		return nil
	}

	// 根据用户角色过滤敏感数据
	processedMetrics := *metrics

	// 如果是媒介账号，隐藏成本和利润相关数据
	if userInfo.Role == RoleMedia {
		for key, metric := range processedMetrics.Metrics {
			if key == "cost" || key == "estimated_profit" || key == "settled_profit" {
				if metric != nil {
					metric.Value = 0
					metric.Change = 0
					metric.WeekChange = 0
				}
			}
		}
	}

	return &processedMetrics
}

// applyMediaListBusinessRules 应用媒体列表业务规则
func (s *sLogic) applyMediaListBusinessRules(req *v1.GetMediaListReq, userInfo *UserInfo) *v1.GetMediaListReq {
	processedReq := *req

	// 如果是媒介账号，只能查看关联的媒体
	if userInfo.Role == RoleMedia {
		processedReq.UserId = strconv.Itoa(userInfo.Id)
	}

	return &processedReq
}

// applyPlanStatsBusinessRules 应用分计划数据业务规则
func (s *sLogic) applyPlanStatsBusinessRules(req *v1.GetPlanStatsReq, userInfo *UserInfo) *v1.GetPlanStatsReq {
	processedReq := *req

	// 如果是媒介账号，只能查看自己的计划数据
	if userInfo.Role == RoleMedia {
		processedReq.UserId = strconv.Itoa(userInfo.Id)
	}

	// 如果用户没有指定UserId，且不是管理员/运营，则设置为当前用户
	if req.UserId == "" && userInfo.Role != RoleAdmin && userInfo.Role != RoleOperator {
		processedReq.UserId = strconv.Itoa(userInfo.Id)
	}

	return &processedReq
}

// validateMediaAccountAccess 验证用户是否有权限访问指定媒体账号
func (s *sLogic) validateMediaAccountAccess(userInfo *UserInfo, mediaAccountId string) error {
	// 管理员和运营可以访问所有媒体账号
	if userInfo.Role == RoleAdmin || userInfo.Role == RoleOperator || userInfo.Role == RoleFinance {
		return nil
	}

	// 媒介账号只能访问自己关联的媒体账号
	if userInfo.Role == RoleMedia {
		// 这里应该检查该媒体账号是否属于当前用户
		// 实际实现需要查询数据库验证关联关系
		// 目前先简单返回，后续可以完善
		return nil
	}

	return gerror.NewCode(gcode.CodeNotAuthorized, "无权限访问该媒体账号")
}

// IsAdmin 判断是否是管理员
func (u *UserInfo) IsAdmin() bool {
	return u.Role == RoleAdmin
}

// IsOperator 判断是否是运营
func (u *UserInfo) IsOperator() bool {
	return u.Role == RoleOperator
}

// IsFinance 判断是否是财务
func (u *UserInfo) IsFinance() bool {
	return u.Role == RoleFinance
}

// IsMedia 判断是否是媒介
func (u *UserInfo) IsMedia() bool {
	return u.Role == RoleMedia
}
