package dashboard

import (
	v1 "ad-pro-v2/api/dashboard/v1"
	dashboardService "ad-pro-v2/internal/service/dashboard"
	"context"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
)

// ILogic 仪表盘业务逻辑接口
type ILogic interface {
	GetFilterOptions(ctx context.Context, req *v1.GetFilterOptionsReq) (*v1.GetFilterOptionsRes, error)
	GetMetrics(ctx context.Context, req *v1.GetMetricsReq) (*v1.GetMetricsRes, error)
	GetTrendData(ctx context.Context, req *v1.GetTrendDataReq) (*v1.GetTrendDataRes, error)
	GetRegionData(ctx context.Context, req *v1.GetRegionDataReq) (*v1.GetRegionDataRes, error)
	GetOrderTypeData(ctx context.Context, req *v1.GetOrderTypeDataReq) (*v1.GetOrderTypeDataRes, error)
	GetMediaList(ctx context.Context, req *v1.GetMediaListReq) (*v1.GetMediaListRes, error)
	GetPlans(ctx context.Context, req *v1.GetPlansReq) (*v1.GetPlansRes, error)
	GetProducts(ctx context.Context, req *v1.GetProductsReq) (*v1.GetProductsRes, error)
	GetPlanStats(ctx context.Context, req *v1.GetPlanStatsReq) (*v1.GetPlanStatsRes, error)
}

type sLogic struct{}

var insLogic = sLogic{}

// 获取 dashboard 服务实例
var dashboardSvc = dashboardService.GetInstance()

// Logic 获取仪表盘业务逻辑实例
func Logic() ILogic {
	return &insLogic
}

// getUserFromContext 从上下文获取用户信息 (临时实现)
func (s *sLogic) getUserFromContext(ctx context.Context) *UserInfo {
	// 这是一个临时实现，实际应该从context或session中获取用户信息
	// 你需要根据项目的实际用户管理方式来实现这个方法

	// 临时返回一个测试用户，实际使用时需要替换
	return &UserInfo{
		Id:   1,
		Role: RoleAdmin, // 临时设置为管理员角色
		Name: "测试用户",
	}
}

// GetFilterOptions 获取仪表盘筛选项数据
func (s *sLogic) GetFilterOptions(ctx context.Context, req *v1.GetFilterOptionsReq) (*v1.GetFilterOptionsRes, error) {
	// 业务逻辑：根据用户角色返回不同的筛选选项
	userInfo := s.getUserFromContext(ctx)
	if userInfo == nil {
		return nil, gerror.NewCode(gcode.CodeNotAuthorized, "用户未登录")
	}

	// 调用新的service层获取数据
	return dashboardSvc.GetFilterOptions(ctx)
}

// GetMetrics 获取指标数据
func (s *sLogic) GetMetrics(ctx context.Context, req *v1.GetMetricsReq) (*v1.GetMetricsRes, error) {
	// 1. 业务规则验证
	if err := s.validateMetricsRequest(req); err != nil {
		return nil, err
	}

	// 2. 获取用户信息并应用业务规则
	userInfo := s.getUserFromContext(ctx)
	if userInfo == nil {
		return nil, gerror.NewCode(gcode.CodeNotAuthorized, "用户未登录")
	}

	// 3. 应用用户角色相关的业务规则
	processedReq := s.applyUserBusinessRules(req, userInfo)

	// 4. 调用新的Service层获取数据
	metrics, err := dashboardSvc.GetMetrics(ctx, processedReq)
	if err != nil {
		return nil, gerror.WrapCode(gcode.CodeInternalError, err, "获取指标数据失败")
	}

	// 5. 业务数据处理和转换
	return s.processMetricsResponse(metrics, userInfo), nil
}

// GetTrendData 获取趋势数据
func (s *sLogic) GetTrendData(ctx context.Context, req *v1.GetTrendDataReq) (*v1.GetTrendDataRes, error) {
	// 业务规则验证
	if err := s.validateTrendRequest(req); err != nil {
		return nil, err
	}

	// 获取用户信息
	userInfo := s.getUserFromContext(ctx)
	if userInfo == nil {
		return nil, gerror.NewCode(gcode.CodeNotAuthorized, "用户未登录")
	}

	// 调用新的service层
	return dashboardSvc.GetTrendData(ctx, req)
}

// GetRegionData 获取区域数据
func (s *sLogic) GetRegionData(ctx context.Context, req *v1.GetRegionDataReq) (*v1.GetRegionDataRes, error) {
	// 权限检查
	userInfo := s.getUserFromContext(ctx)
	if userInfo == nil {
		return nil, gerror.NewCode(gcode.CodeNotAuthorized, "用户未登录")
	}

	return dashboardSvc.GetRegionData(ctx, req)
}

// GetOrderTypeData 获取订单类型数据
func (s *sLogic) GetOrderTypeData(ctx context.Context, req *v1.GetOrderTypeDataReq) (*v1.GetOrderTypeDataRes, error) {
	// 权限检查
	userInfo := s.getUserFromContext(ctx)
	if userInfo == nil {
		return nil, gerror.NewCode(gcode.CodeNotAuthorized, "用户未登录")
	}

	return dashboardSvc.GetOrderTypeData(ctx, req)
}

// GetMediaList 获取媒体列表
func (s *sLogic) GetMediaList(ctx context.Context, req *v1.GetMediaListReq) (*v1.GetMediaListRes, error) {
	// 业务规则：根据用户角色和权限过滤媒体列表
	userInfo := s.getUserFromContext(ctx)
	if userInfo == nil {
		return nil, gerror.NewCode(gcode.CodeNotAuthorized, "用户未登录")
	}

	// 应用用户角色过滤规则
	processedReq := s.applyMediaListBusinessRules(req, userInfo)

	return dashboardSvc.GetMediaList(ctx, processedReq)
}

// GetPlans 获取投放计划列表
func (s *sLogic) GetPlans(ctx context.Context, req *v1.GetPlansReq) (*v1.GetPlansRes, error) {
	// 参数验证
	if req.MediaAccountId == "" {
		return nil, gerror.NewCode(gcode.CodeValidationFailed, "媒体账号ID不能为空")
	}

	// 权限检查
	userInfo := s.getUserFromContext(ctx)
	if userInfo == nil {
		return nil, gerror.NewCode(gcode.CodeNotAuthorized, "用户未登录")
	}

	// 业务规则：检查用户是否有权限查看该媒体账号的计划
	if err := s.validateMediaAccountAccess(userInfo, req.MediaAccountId); err != nil {
		return nil, err
	}

	// 转换媒体账号ID
	mediaAccountId, err := s.parseMediaAccountId(req.MediaAccountId)
	if err != nil {
		return nil, gerror.WrapCode(gcode.CodeValidationFailed, err, "无效的媒体账号ID")
	}

	return dashboardSvc.GetPlans(ctx, mediaAccountId)
}

// GetProducts 获取产品列表
func (s *sLogic) GetProducts(ctx context.Context, req *v1.GetProductsReq) (*v1.GetProductsRes, error) {
	// 权限检查
	userInfo := s.getUserFromContext(ctx)
	if userInfo == nil {
		return nil, gerror.NewCode(gcode.CodeNotAuthorized, "用户未登录")
	}

	return dashboardSvc.GetProducts(ctx)
}

// GetPlanStats 获取分计划数据
func (s *sLogic) GetPlanStats(ctx context.Context, req *v1.GetPlanStatsReq) (*v1.GetPlanStatsRes, error) {
	// 业务规则验证
	if err := s.validatePlanStatsRequest(req); err != nil {
		return nil, err
	}

	// 权限检查
	userInfo := s.getUserFromContext(ctx)
	if userInfo == nil {
		return nil, gerror.NewCode(gcode.CodeNotAuthorized, "用户未登录")
	}

	// 应用用户角色过滤规则
	processedReq := s.applyPlanStatsBusinessRules(req, userInfo)

	return dashboardSvc.GetPlanStats(ctx, processedReq)
}
