package dashboard

import (
	v1 "ad-pro-v2/api/dashboard/v1"
	"strconv"
	"time"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
)

// validateMetricsRequest 验证指标请求参数
func (s *sLogic) validateMetricsRequest(req *v1.GetMetricsReq) error {
	// 验证日期格式
	if req.StartDate != "" {
		if _, err := time.Parse("2006-01-02", req.StartDate); err != nil {
			return gerror.NewCode(gcode.CodeValidationFailed, "开始日期格式错误，应为：YYYY-MM-DD")
		}
	}

	if req.EndDate != "" {
		if _, err := time.Parse("2006-01-02", req.EndDate); err != nil {
			return gerror.NewCode(gcode.CodeValidationFailed, "结束日期格式错误，应为：YYYY-MM-DD")
		}
	}

	// 验证日期范围
	if req.StartDate != "" && req.EndDate != "" {
		start, _ := time.Parse("2006-01-02", req.StartDate)
		end, _ := time.Parse("2006-01-02", req.EndDate)
		if end.Before(start) {
			return gerror.NewCode(gcode.CodeValidationFailed, "结束日期不能早于开始日期")
		}

		// 验证日期范围不超过90天
		if end.Sub(start).Hours() > 90*24 {
			return gerror.NewCode(gcode.CodeValidationFailed, "查询日期范围不能超过90天")
		}
	}

	// 验证媒体ID
	if req.MediaId != "" {
		if _, err := strconv.ParseInt(req.MediaId, 10, 64); err != nil {
			return gerror.NewCode(gcode.CodeValidationFailed, "媒体ID格式错误")
		}
	}

	// 验证计划ID
	if req.PlanId != "" {
		if _, err := strconv.ParseInt(req.PlanId, 10, 64); err != nil {
			return gerror.NewCode(gcode.CodeValidationFailed, "计划ID格式错误")
		}
	}

	// 验证产品ID
	if req.ProductId != "" {
		if _, err := strconv.ParseInt(req.ProductId, 10, 64); err != nil {
			return gerror.NewCode(gcode.CodeValidationFailed, "产品ID格式错误")
		}
	}

	// 验证类型
	if req.Type != "" && req.Type != "1" && req.Type != "2" && req.Type != "3" {
		return gerror.NewCode(gcode.CodeValidationFailed, "类型参数错误，支持值：1(普通投放)、2(CPS合作)、3(灯火)")
	}

	// 验证大类
	if req.Category != "" && req.Category != "alipay" && req.Category != "wechat" && req.Category != "other" {
		return gerror.NewCode(gcode.CodeValidationFailed, "大类参数错误，支持值：alipay、wechat、other")
	}

	return nil
}

// validateTrendRequest 验证趋势数据请求参数
func (s *sLogic) validateTrendRequest(req *v1.GetTrendDataReq) error {
	// 验证指标类型
	validMetrics := map[string]bool{
		"click_pv":             true,
		"click_uv":             true,
		"orders":               true,
		"estimated_commission": true,
		"settled_commission":   true,
		"cost":                 true,
		"estimated_profit":     true,
		"settled_profit":       true,
	}

	if req.Metric != "" && !validMetrics[req.Metric] {
		return gerror.NewCode(gcode.CodeValidationFailed, "不支持的指标类型")
	}

	// 验证日期格式
	if req.StartDate != "" {
		if _, err := time.Parse("2006-01-02", req.StartDate); err != nil {
			return gerror.NewCode(gcode.CodeValidationFailed, "开始日期格式错误")
		}
	}

	if req.EndDate != "" {
		if _, err := time.Parse("2006-01-02", req.EndDate); err != nil {
			return gerror.NewCode(gcode.CodeValidationFailed, "结束日期格式错误")
		}
	}

	return nil
}

// validatePlanStatsRequest 验证分计划数据请求参数
func (s *sLogic) validatePlanStatsRequest(req *v1.GetPlanStatsReq) error {
	// 验证必要参数
	if req.StartDate == "" {
		return gerror.NewCode(gcode.CodeValidationFailed, "开始日期不能为空")
	}

	if req.EndDate == "" {
		return gerror.NewCode(gcode.CodeValidationFailed, "结束日期不能为空")
	}

	// 验证日期格式
	start, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		return gerror.NewCode(gcode.CodeValidationFailed, "开始日期格式错误")
	}

	end, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		return gerror.NewCode(gcode.CodeValidationFailed, "结束日期格式错误")
	}

	// 验证日期范围
	if end.Before(start) {
		return gerror.NewCode(gcode.CodeValidationFailed, "结束日期不能早于开始日期")
	}

	// 验证日期范围不超过31天
	if end.Sub(start).Hours() > 31*24 {
		return gerror.NewCode(gcode.CodeValidationFailed, "分计划数据查询日期范围不能超过31天")
	}

	return nil
}

// parseMediaAccountId 解析媒体账号ID
func (s *sLogic) parseMediaAccountId(mediaAccountIdStr string) (int64, error) {
	mediaAccountId, err := strconv.ParseInt(mediaAccountIdStr, 10, 64)
	if err != nil {
		return 0, gerror.NewCode(gcode.CodeValidationFailed, "媒体账号ID格式错误")
	}

	if mediaAccountId <= 0 {
		return 0, gerror.NewCode(gcode.CodeValidationFailed, "媒体账号ID必须大于0")
	}

	return mediaAccountId, nil
}
