package auth

import (
	"context"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"

	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/model/entity"
	"ad-pro-v2/internal/service"
)

type sAuth struct{}

// JWT相关配置
var (
	// JwtSecret JWT密钥
	JwtSecret = []byte("adpro_secret_key")
	// TokenExpireDuration token过期时间(24小时)
	TokenExpireDuration = 24 * time.Hour
)

// CustomClaims 自定义JWT claims
type CustomClaims struct {
	jwt.RegisteredClaims
	UserId uint64 `json:"userId"`
	Email  string `json:"email"`
}

func init() {
	service.RegisterAuth(New())
}

// New 创建认证服务
func New() service.IAuth {
	return &sAuth{}
}

// Login 实现登录
func (s *sAuth) Login(ctx context.Context, email, password string) (user *entity.Users, token string, err error) {
	user = &entity.Users{}
	// 查询用户
	err = dao.Users.Ctx(ctx).Where(dao.Users.Columns().Email, email).Scan(&user)
	if err != nil {
		return nil, "", err
	}
	if user == nil || user.Id == 0 {
		return nil, "", gerror.New("用户不存在")
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		return nil, "", gerror.New("密码错误")
	}

	// 更新登录信息
	_, err = dao.Users.Ctx(ctx).Data(g.Map{
		dao.Users.Columns().LastLoginAt: gtime.Now(),
		dao.Users.Columns().LastLoginIp: g.RequestFromCtx(ctx).GetClientIp(),
	}).Where(dao.Users.Columns().Id, user.Id).Update()
	if err != nil {
		return nil, "", err
	}

	// 生成JWT token
	claims := CustomClaims{
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(TokenExpireDuration)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
		UserId: user.Id,
		Email:  user.Email,
	}

	// 生成token
	token, err = jwt.NewWithClaims(jwt.SigningMethodHS256, claims).SignedString(JwtSecret)
	if err != nil {
		return nil, "", err
	}

	return user, token, nil
}

// ChangePassword 实现修改密码
func (s *sAuth) ChangePassword(ctx context.Context, userId uint64, oldPassword, newPassword string) error {
	user := &entity.Users{}
	// 查询用户
	err := dao.Users.Ctx(ctx).Where(dao.Users.Columns().Id, userId).Scan(&user)
	if err != nil {
		return err
	}
	if user == nil || user.Id == 0 {
		return gerror.New("用户不存在")
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(oldPassword)); err != nil {
		return gerror.New("旧密码错误")
	}

	// 生成新密码的bcrypt哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), 12)
	if err != nil {
		return err
	}

	// 更新密码
	_, err = dao.Users.Ctx(ctx).Data(g.Map{
		dao.Users.Columns().Password:          string(hashedPassword),
		dao.Users.Columns().PasswordChangedAt: gtime.Now(),
		dao.Users.Columns().UpdatedAt:         gtime.Now(),
	}).Where(dao.Users.Columns().Id, userId).Update()
	if err != nil {
		return err
	}

	return nil
}
