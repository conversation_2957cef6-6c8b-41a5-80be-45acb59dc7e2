package service

import (
	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/model/entity"
	"context"
	"strconv"
	"strings"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/xuri/excelize/v2"
)

// ModelService Model服务接口定义
type ModelService interface {
	// Create 创建模型
	Create(ctx context.Context, input *entity.Model) (id uint64, err error)

	// Delete 删除模型
	Delete(ctx context.Context, id uint64) error

	// Update 更新模型
	Update(ctx context.Context, input *entity.Model) error

	// GetList 获取模型列表
	GetList(ctx context.Context, input *ModelListInput) (*ModelListOutput, error)

	// AddDecay 添加模型衰减记录
	AddDecay(ctx context.Context, input *entity.ModelDecay) (id uint64, err error)

	// GetDecayList 获取模型衰减记录列表
	GetDecayList(ctx context.Context, modelId uint64) ([]*entity.ModelDecay, error)

	// ImportModel 导入模型
	ImportModel(ctx context.Context, input *model.ModelImportInput) error
}

// ModelListInput 获取模型列表输入参数
type ModelListInput struct {
	Page     int    // 页码
	PageSize int    // 每页数量
	Name     string // 模型名称
}

// ModelListItem 模型列表项
type ModelListItem struct {
	Id        uint        `json:"id"`         // 模型ID
	Name      string      `json:"name"`       // 模型名称
	CreatedAt *gtime.Time `json:"created_at"` // 创建时间
}

// ModelListOutput 获取模型列表输出参数
type ModelListOutput struct {
	List  []*ModelListItem // 数据列表
	Total int              // 数据总数
	Page  int              // 当前页码
}

type modelService struct{}

var Model = &modelService{}

// Create 创建模型
func (s *modelService) Create(ctx context.Context, input *entity.Model) (id uint64, err error) {
	r, err := dao.Model.DB().Model(dao.Model.Table()).Ctx(ctx).Data(input).Insert()
	if err != nil {
		return 0, err
	}
	newId, err := r.LastInsertId()
	if err != nil {
		return 0, err
	}
	return uint64(newId), nil
}

// Delete 删除模型
func (s *modelService) Delete(ctx context.Context, id uint64) error {
	_, err := dao.Model.DB().Model(dao.Model.Table()).Ctx(ctx).Where(dao.Model.Columns().Id, id).Delete()
	return err
}

// Update 更新模型
func (s *modelService) Update(ctx context.Context, input *entity.Model) error {
	_, err := dao.Model.DB().Model(dao.Model.Table()).Ctx(ctx).Data(input).Where(dao.Model.Columns().Id, input.Id).Update()
	return err
}

// GetList 获取模型列表
func (s *modelService) GetList(ctx context.Context, input *ModelListInput) (*ModelListOutput, error) {
	m := dao.Model.DB().Model(dao.Model.Table()).Ctx(ctx)

	// 条件查询
	if input.Name != "" {
		m = m.WhereLike("name", "%"+input.Name+"%")
	}

	// 获取总数
	countModel := *m
	total, err := countModel.Fields("id").Count()
	if err != nil {
		return nil, err
	}

	// 选择需要的字段
	m = m.Fields("id, name, created_at")

	// 分页查询
	var list []*ModelListItem
	err = m.Page(input.Page, input.PageSize).Scan(&list)
	if err != nil {
		return nil, err
	}

	return &ModelListOutput{
		List:  list,
		Total: total,
		Page:  input.Page,
	}, nil
}

// AddDecay 添加模型衰减记录
func (s *modelService) AddDecay(ctx context.Context, input *entity.ModelDecay) (id uint64, err error) {
	r, err := dao.ModelDecay.DB().Model(dao.ModelDecay.Table()).Ctx(ctx).Data(input).Insert()
	if err != nil {
		return 0, err
	}
	newId, err := r.LastInsertId()
	if err != nil {
		return 0, err
	}
	return uint64(newId), nil
}

// GetDecayList 获取模型衰减记录列表
func (s *modelService) GetDecayList(ctx context.Context, modelId uint64) ([]*entity.ModelDecay, error) {
	var list []*entity.ModelDecay
	err := dao.ModelDecay.DB().Model(dao.ModelDecay.Table()).Ctx(ctx).
		Where(dao.ModelDecay.Columns().ModelId, modelId).
		Order(dao.ModelDecay.Columns().CreatedAt + " DESC").
		Scan(&list)
	return list, err
}

// ImportModel 导入模型
func (s *modelService) ImportModel(ctx context.Context, input *model.ModelImportInput) error {
	// 1. 获取上传文件
	file := input.Request.GetUploadFile("file")
	if file == nil {
		return gerror.New("请上传Excel文件")
	}

	// 2. 打开文件
	fileReader, err := file.Open()
	if err != nil {
		return err
	}
	defer fileReader.Close()

	// 3. 使用 excelize 直接从 Reader 读取 Excel
	f, err := excelize.OpenReader(fileReader)
	if err != nil {
		return err
	}
	defer f.Close()

	// 4. 获取第一个Sheet
	rows, err := f.GetRows(f.GetSheetName(0))
	if err != nil {
		return err
	}

	// 5. 解析Excel数据，准备衰减记录
	var decayRecords []float64
	for i, row := range rows {
		// 跳过表头
		if i == 0 {
			continue
		}

		// 确保至少有一列
		if len(row) == 0 {
			continue
		}

		// 解析百分比值
		percentStr := row[0]
		// 去掉百分号
		percentStr = strings.TrimSuffix(percentStr, "%")
		percent, err := strconv.ParseFloat(percentStr, 64)
		if err != nil {
			continue
		}

		// 跳过第二行的100%
		if i == 1 && percent >= 100 {
			continue
		}

		decayRecords = append(decayRecords, percent)
	}

	// 6. 开启事务处理数据库操作
	err = dao.Model.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 6.1 创建模型基本信息
		modelInfo := &entity.Model{
			Name: input.Name,
		}
		r, err := tx.Model(dao.Model.Table()).Ctx(ctx).Data(modelInfo).Insert()
		if err != nil {
			return err
		}

		newId, err := r.LastInsertId()
		if err != nil {
			return err
		}
		modelId := uint(newId)

		// 6.2 批量插入衰减记录
		var decayEntities []*entity.ModelDecay
		for _, percentValue := range decayRecords {
			decayEntities = append(decayEntities, &entity.ModelDecay{
				ModelId: modelId,
				Percent: percentValue,
			})
		}
		if len(decayEntities) > 0 {
			_, err = tx.Model(dao.ModelDecay.Table()).Ctx(ctx).Data(decayEntities).Insert()
			if err != nil {
				return err
			}
		}

		return nil
	})

	return err
}

// GetReport 获取模型报表数据
func (s *modelService) GetReport(ctx context.Context, input *model.ModelReportReq) (*model.ModelReportRes, error) {
	// Implementation of GetReport method
	return nil, nil // Placeholder return, actual implementation needed
}
