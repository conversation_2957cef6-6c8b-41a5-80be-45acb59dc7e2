package service

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"

	"ad-pro-v2/internal/model/entity"
)

// IAdProduct 投放产品服务接口
type IAdProduct interface {
	// List 获取投放产品列表
	List(ctx context.Context, page, size int) ([]entity.AdProducts, int, error)
	// GetList 获取产品列表
	GetList(ctx context.Context) ([]*entity.AdProducts, error)
}

type adProductService struct{}

var insAdProduct = adProductService{}

// AdProduct 获取投放产品服务实例
func AdProduct() IAdProduct {
	return &insAdProduct
}

// List 获取投放产品列表
func (s *adProductService) List(ctx context.Context, page, size int) ([]entity.AdProducts, int, error) {
	glog.Info(ctx, "开始获取投放产品列表", g.Map{
		"page": page,
		"size": size,
	})

	// 构建查询条件
	query := g.DB().Model("ad_products").Safe()

	// 获取总数
	count, err := query.Count()
	if err != nil {
		glog.Error(ctx, "获取投放产品总数失败", g.Map{
			"error": err.Error(),
		})
		return nil, 0, err
	}

	// 获取列表数据
	var products []entity.AdProducts
	if page > 0 && size > 0 {
		err = query.Page(page, size).Scan(&products)
	} else {
		err = query.Scan(&products)
	}
	if err != nil {
		glog.Error(ctx, "获取投放产品列表失败", g.Map{
			"error": err.Error(),
		})
		return nil, 0, err
	}

	return products, count, nil
}

// GetList 获取产品列表
func (s *adProductService) GetList(ctx context.Context) ([]*entity.AdProducts, error) {
	var products []*entity.AdProducts
	err := g.DB().Model("ad_products").Scan(&products)
	if err != nil {
		glog.Error(ctx, "获取产品列表失败", g.Map{
			"error": err.Error(),
		})
		return nil, err
	}
	return products, nil
}
