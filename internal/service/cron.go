package service

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"

	"ad-pro-v2/internal/consts"
	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/model"
	"ad-pro-v2/pkg/alipay"
	"ad-pro-v2/pkg/alipay/report"
)

// CronService 定时任务服务接口
type CronService interface {
	// StartHelloTask 启动Hello定时任务
	StartHelloTask(ctx context.Context) error
	// StartSyncPlatformDataTask 启动同步平台数据定时任务
	StartSyncPlatformDataTask(ctx context.Context) error
	// StartSyncXhsCostTask 启动同步小红书成本定时任务
	StartSyncXhsCostTask(ctx context.Context) error
	// StartPlanProfitNoticeTask 启动计划利润通知定时任务
	StartPlanProfitNoticeTask(ctx context.Context) error
	// GeneratePwdDailyReport 生成口令日报
	GeneratePwdDailyReport(ctx context.Context) error
}

var localCron CronService

// SetCron 设置定时任务服务实例
func SetCron(s CronService) {
	localCron = s
}

// Cron 获取定时任务服务实例
func Cron() CronService {
	return localCron
}

type cronService struct{}

// NewCronService 创建定时任务服务实例
func NewCronService() CronService {
	return &cronService{}
}

// StartPlanProfitNoticeTask 启动计划利润通知定时任务
func (s *cronService) StartPlanProfitNoticeTask(ctx context.Context) error {
	return AdSlotPlan().SendSettleProfitDingTalk(ctx, []uint64{
		192, 457, 199, 463, 491, 532, 531, 762, 761, 760, 793, 794, 830, 829,
	})
}

// StartHelloTask 启动Hello定时任务
func (s *cronService) StartHelloTask(ctx context.Context) error {
	return nil
}

// StartSyncPlatformDataTask 启动同步平台数据定时任务
func (s *cronService) StartSyncPlatformDataTask(ctx context.Context) error {
	// 1. 查询媒体数据
	mediaList, err := s.getMediaList(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "获取媒体列表失败: %v", err)
		return err
	}

	// 2. 遍历媒体数据
	for _, media := range mediaList {
		// 只处理灯火平台
		if media.PlatformConfig == nil || media.PlatformConfig.Platform != "denghuoplus" {
			continue
		}

		// 3. 获取代理数据
		agent, err := s.getAgent(ctx, uint64(media.AdAgentId))
		if err != nil {
			g.Log().Errorf(ctx, "获取代理数据失败, mediaId: %d, err: %v", media.Id, err)
			continue
		}

		if agent == nil {
			continue
		}

		// 检查代理状态和类型
		if agent.AuditStatus != "approved" {
			g.Log().Noticef(ctx, "代理审核状态不是已通过, mediaId: %d, agentId: %d", media.Id, agent.Id)
			continue
		}
		if agent.Type != consts.AgentTypeDelivery {
			g.Log().Noticef(ctx, "代理类型不是投放, mediaId: %d, agentId: %d", media.Id, agent.Id)
			continue
		}
		if agent.PlatformConfig == nil || agent.PlatformConfig.Platform != "denghuoplus" {
			g.Log().Noticef(ctx, "代理平台配置不是灯火, mediaId: %d, agentId: %d", media.Id, agent.Id)
			continue
		}

		now := time.Now()
		startDate := now.AddDate(0, 0, -6) // 6天前
		endDate := now

		// 4. 调用灯火接口获取数据
		err = s.syncDenghuoPlusData(ctx, media, agent, startDate, endDate)
		if err != nil {
			g.Log().Errorf(ctx, "同步灯火数据失败, mediaId: %d, agentId: %d, err: %v", media.Id, agent.Id, err)
			continue
		}
	}

	return nil
}

// getMediaList 获取媒体列表
func (s *cronService) getMediaList(ctx context.Context) ([]*model.Media, error) {
	// 直接通过 DAO 层查询数据，绕过权限检查
	list, err := dao.AdMedia.Ctx(ctx).Where(g.Map{
		"cooperation_type":   "dh",
		"audit_status":       "approved",
		"cooperation_status": "active",
	}).All()
	if err != nil {
		return nil, err
	}

	var media []*model.Media
	if err = list.Structs(&media); err != nil {
		return nil, err
	}

	return media, nil
}

// getAgent 获取代理数据
func (s *cronService) getAgent(ctx context.Context, agentId uint64) (*model.Agent, error) {
	return Agent().GetById(ctx, int(agentId))
}

// syncDenghuoPlusData 同步灯火数据
func (s *cronService) syncDenghuoPlusData(
	ctx context.Context,
	media *model.Media,
	agent *model.Agent,
	startDate time.Time,
	endDate time.Time,
) error {
	// 定义需要查询的维度
	adLevels := []string{"PLAN", "GROUP", "CREATIVE"}

	// 2. 遍历每个维度进行数据同步
	for _, level := range adLevels {
		pageSize := 100
		currentPage := 1
		totalCount := 0

		// 获取已存在的数据ID集合
		existingIds, err := s.getExistingIds(ctx, level)
		if err != nil {
			g.Log().Errorf(ctx, "获取已存在的%s数据失败: %v", level, err)
			continue
		}

		for {
			resp := &report.AdReportQueryResponse{}
			if strings.Contains(media.Name, "邦道") {
				principalTag := media.PlatformConfig.Info.MerchantMark
				if principalTag == "" {
					principalTag = "7952308912734f3598b6fc48fd6def94"
				}
				params := g.Map{
					"ad_level":      level,
					"query_type":    "DETAIL",
					"principal_tag": principalTag,
					"page_size":     pageSize,
					"current":       currentPage,
					"start_date":    startDate.Format("20060102"),
					"end_date":      endDate.Format("20060102"),
				}
				jsonParams, err := json.Marshal(params)
				if err != nil {
					g.Log().Errorf(ctx, "序列化参数失败: %v", err)
					continue
				}
				httpResp, err := http.Post(
					"https://test-napi.bangdao-tech.com/api/authority/admin/xdelm/data/query",
					"application/json",
					bytes.NewReader(jsonParams),
				)
				if err != nil {
					g.Log().Errorf(ctx, "调用绑定获取数据失败: %v", err)
					continue
				}
				defer httpResp.Body.Close()

				body, err := io.ReadAll(httpResp.Body)
				if err != nil {
					g.Log().Errorf(ctx, "读取邦道返回的响应失败: %v", err)
					continue
				}

				type Result struct {
					Code    string `json:"code"`
					Msg     string `json:"msg"`
					SubCode any    `json:"subCode"`
					SubMsg  any    `json:"subMsg"`
					Data    string `json:"body"`
				}

				var result Result
				err = json.Unmarshal(body, &result)
				if err != nil {
					g.Log().Errorf(ctx, "反序列化邦道返回的响应失败: %v", err)
					continue
				}
				if result.Code != "10000" {
					g.Log().Errorf(
						ctx,
						"请求失败业务错误: %v，返回值：%v",
						result.Msg,
						string(body),
					)
					continue
				}

				err = json.Unmarshal([]byte(result.Data), resp)
				if err != nil {
					g.Log().Errorf(ctx, "反序列化响应失败: %v", err)
					continue
				}

			} else {
				req := report.NewAdReportQueryRequest(startDate, endDate)
				req.BizToken = agent.PlatformConfig.Info["token"].(string)
				req.AlipayPid = agent.PlatformConfig.Info["pid"].(string)
				req.AdLevel = level
				req.QueryType = "DETAIL"
				req.PrincipalTag = media.PlatformConfig.Info.MerchantMark
				req.PageSize = pageSize
				req.Current = currentPage

				// 调用接口获取数据
				config := &alipay.Config{
					AppId:      media.PlatformConfig.Info.AppId,
					PrivateKey: media.PlatformConfig.Info.PrivateKey,
					PublicKey:  media.PlatformConfig.Info.PublicKey,
					IsProd:     true,
				}
				client := alipay.NewClient(config)
				err := client.Execute(req.GetMethod(), req, resp)
				if err != nil {
					g.Log().Errorf(ctx, "获取%s维度数据失败: %v", level, err)
					break
				}
			}

			// 3. 保存数据
			for _, item := range resp.Response.Items {
				var belongId uint64
				var err error

				// 检查基础数据是否已存在
				if _, exists := existingIds[item.DataID]; !exists {
					// 只有不存在时才插入基础数据
					switch level {
					case "PLAN":
						belongId, err = s.upsertPlan(ctx, media, agent, &item)
					case "GROUP":
						belongId, err = s.upsertGroup(ctx, media, agent, &item)
					case "CREATIVE":
						belongId, err = s.upsertCreative(ctx, media, agent, &item)
					}

					if err != nil {
						g.Log().Errorf(ctx, "保存%s数据失败: %v", level, err)
						continue
					}
				} else {
					// 如果基础数据已存在,直接获取ID
					belongId = s.getExistingId(ctx, level, item.DataID)
				}

				if belongId == 0 {
					g.Log().Errorf(ctx, "获取%s数据ID失败, dataId: %s", level, item.DataID)
					continue
				}

				// 保存报表数据(每次都更新)
				err = s.upsertReport(ctx, belongId, strings.ToLower(level), &item)
				if err != nil {
					g.Log().Errorf(ctx, "保存%s报表数据失败: %v", level, err)
					continue
				}

				// 保存转化数据(每次都更新)
				err = s.upsertConversions(ctx, belongId, strings.ToLower(level), &item)
				if err != nil {
					g.Log().Errorf(ctx, "保存%s转化数据失败: %v", level, err)
					continue
				}
			}

			// 更新总数和判断是否需要继续分页
			if resp.Response.Total > 0 {
				totalCount = resp.Response.Total
			}

			// 判断是否还有下一页
			if len(resp.Response.Items) < pageSize || currentPage*pageSize >= totalCount {
				g.Log().Infof(ctx, "完成同步%s维度数据, 总数: %d", level, totalCount)
				break
			}

			currentPage++
		}
	}

	return nil
}

// getExistingIds 获取已存在的数据ID集合
func (s *cronService) getExistingIds(ctx context.Context, level string) (map[string]struct{}, error) {
	var result map[string]struct{} = make(map[string]struct{})

	var list gdb.Result
	var err error

	switch level {
	case "PLAN":
		list, err = dao.PlatformPlans.Ctx(ctx).
			Fields("data_id").
			Where("platform_type", "denghuoplus").
			All()
	case "GROUP":
		list, err = dao.PlatformGroups.Ctx(ctx).
			Fields("data_id").
			Where("platform_type", "denghuoplus").
			All()
	case "CREATIVE":
		list, err = dao.PlatformCreatives.Ctx(ctx).
			Fields("data_id").
			Where("platform_type", "denghuoplus").
			All()
	}

	if err != nil {
		return nil, err
	}

	// 将结果转换为map
	for _, record := range list {
		result[record["data_id"].String()] = struct{}{}
	}

	return result, nil
}

// getExistingId 获取已存在数据的ID
func (s *cronService) getExistingId(ctx context.Context, level string, dataId string) uint64 {
	var record gdb.Record
	var err error

	switch level {
	case "PLAN":
		record, err = dao.PlatformPlans.Ctx(ctx).
			Fields("id").
			Where(g.Map{
				"platform_type": "denghuoplus",
				"data_id":       dataId,
			}).
			One()
	case "GROUP":
		record, err = dao.PlatformGroups.Ctx(ctx).
			Fields("id").
			Where(g.Map{
				"platform_type": "denghuoplus",
				"data_id":       dataId,
			}).
			One()
	case "CREATIVE":
		record, err = dao.PlatformCreatives.Ctx(ctx).
			Fields("id").
			Where(g.Map{
				"platform_type": "denghuoplus",
				"data_id":       dataId,
			}).
			One()
	}

	if err != nil || record.IsEmpty() {
		return 0
	}

	return uint64(record["id"].Int64())
}

// upsertPlan 更新或插入计划数据
func (s *cronService) upsertPlan(ctx context.Context, media *model.Media, agent *model.Agent, item *report.Item) (uint64, error) {
	data := g.Map{
		"agent_id":           agent.Id,
		"media_id":           media.Id,
		"platform_type":      "denghuoplus",
		"data_id":            item.DataID,
		"name":               item.PlanName,
		"principal_account":  item.PrincipalAlipayAccount,
		"principal_name":     item.PrincipalName,
		"market_target_name": item.MarketTargetName,
		"scene_name":         item.SceneName,
	}

	_, err := dao.PlatformPlans.Ctx(ctx).
		Where(g.Map{
			"platform_type": "denghuoplus",
			"data_id":       item.DataID,
		}).
		Save(data)
	if err != nil {
		return 0, err
	}

	// 获取ID
	var plan *model.PlatformPlan
	err = dao.PlatformPlans.Ctx(ctx).
		Where(g.Map{
			"platform_type": "denghuoplus",
			"data_id":       item.DataID,
		}).
		Scan(&plan)
	if err != nil {
		return 0, err
	}

	return uint64(plan.Id), nil
}

// upsertReport 更新或插入报表数据
func (s *cronService) upsertReport(ctx context.Context, belongId uint64, belongType string, item *report.Item) error {
	data := g.Map{
		"belong_id":   belongId,
		"belong_type": belongType,
		"biz_date":    item.BizDate,
		"impression":  item.Impression,
		"click":       item.Click,
		"cost":        item.Cost,
	}

	_, err := dao.PlatformReports.Ctx(ctx).
		Where(g.Map{
			"belong_id":   belongId,
			"belong_type": belongType,
			"biz_date":    item.BizDate,
		}).
		Save(data)

	return err
}

// upsertConversions 更新或插入转化数据
func (s *cronService) upsertConversions(ctx context.Context, belongId uint64, belongType string, item *report.Item) error {
	for _, conv := range item.ConversionDataList {
		data := g.Map{
			"belong_id":            belongId,
			"belong_type":          belongType,
			"plan_id":              belongId, // 这里belongId就是planId
			"biz_date":             item.BizDate,
			"conversion_type":      conv.ConversionType,
			"conversion_type_name": conv.ConversionTypeName,
			"conversion_result":    conv.ConversionResult,
		}

		_, err := dao.PlatformConversions.Ctx(ctx).
			Where(g.Map{
				"belong_id":       belongId,
				"belong_type":     belongType,
				"biz_date":        item.BizDate,
				"conversion_type": conv.ConversionType,
			}).
			Save(data)

		if err != nil {
			return err
		}
	}

	return nil
}

// upsertGroup 更新或插入广告组数据
func (s *cronService) upsertGroup(ctx context.Context, media *model.Media, agent *model.Agent, item *report.Item) (uint64, error) {
	// 先查询计划表获取真实的plan_id
	var plan *model.PlatformPlan
	err := dao.PlatformPlans.Ctx(ctx).
		Where(g.Map{
			"platform_type": "denghuoplus",
			"data_id":       item.PlanID,
		}).
		Scan(&plan)
	if err != nil {
		return 0, err
	}

	// 如果找不到对应的计划，则跳过
	if plan == nil {
		g.Log().Noticef(ctx, "找不到对应的计划数据, planId: %s", item.PlanID)
		return 0, nil
	}

	data := g.Map{
		"agent_id":           agent.Id,
		"media_id":           media.Id,
		"platform_type":      "denghuoplus",
		"data_id":            item.DataID,
		"name":               item.GroupName,
		"plan_id":            plan.Id, // 使用查询到的真实plan_id
		"principal_account":  item.PrincipalAlipayAccount,
		"principal_name":     item.PrincipalName,
		"market_target_name": item.MarketTargetName,
		"scene_name":         item.SceneName,
	}

	_, err = dao.PlatformGroups.Ctx(ctx).
		Where(g.Map{
			"platform_type": "denghuoplus",
			"data_id":       item.DataID,
		}).
		Save(data)
	if err != nil {
		return 0, err
	}

	var group *model.PlatformGroup
	err = dao.PlatformGroups.Ctx(ctx).
		Where(g.Map{
			"platform_type": "denghuoplus",
			"data_id":       item.DataID,
		}).
		Scan(&group)
	if err != nil {
		return 0, err
	}

	return uint64(group.Id), nil
}

// upsertCreative 更新或插入广告创意数据
func (s *cronService) upsertCreative(ctx context.Context, media *model.Media, agent *model.Agent, item *report.Item) (uint64, error) {
	// 先查询计划表获取真实的plan_id
	var plan *model.PlatformPlan
	err := dao.PlatformPlans.Ctx(ctx).
		Where(g.Map{
			"platform_type": "denghuoplus",
			"data_id":       item.PlanID,
		}).
		Scan(&plan)
	if err != nil {
		return 0, err
	}

	// 如果找不到对应的计划，则跳过
	if plan == nil {
		g.Log().Noticef(ctx, "找不到对应的计划数据, planId: %s", item.PlanID)
		return 0, nil
	}

	// 查询广告组表获取真实的group_id
	var group *model.PlatformGroup
	err = dao.PlatformGroups.Ctx(ctx).
		Where(g.Map{
			"platform_type": "denghuoplus",
			"data_id":       item.GroupID,
		}).
		Scan(&group)
	if err != nil {
		return 0, err
	}

	// 如果找不到对应的广告组，则跳过
	if group == nil {
		g.Log().Noticef(ctx, "找不到对应的广告组数据, groupId: %s", item.GroupID)
		return 0, nil
	}

	data := g.Map{
		"agent_id":           agent.Id,
		"media_id":           media.Id,
		"platform_type":      "denghuoplus",
		"data_id":            item.DataID,
		"name":               item.CreativeName,
		"plan_id":            plan.Id,  // 使用查询到的真实plan_id
		"group_id":           group.Id, // 使用查询到的真实group_id
		"principal_account":  item.PrincipalAlipayAccount,
		"principal_name":     item.PrincipalName,
		"market_target_name": item.MarketTargetName,
		"scene_name":         item.SceneName,
	}

	_, err = dao.PlatformCreatives.Ctx(ctx).
		Where(g.Map{
			"platform_type": "denghuoplus",
			"data_id":       item.DataID,
		}).
		Save(data)
	if err != nil {
		return 0, err
	}

	var creative *model.PlatformCreative
	err = dao.PlatformCreatives.Ctx(ctx).
		Where(g.Map{
			"platform_type": "denghuoplus",
			"data_id":       item.DataID,
		}).
		Scan(&creative)
	if err != nil {
		return 0, err
	}

	return uint64(creative.Id), nil
}

// StartSyncXhsCostTask 启动同步小红书成本定时任务
func (s *cronService) StartSyncXhsCostTask(ctx context.Context) error {
	return PromotionReport.ImportXhsCost(ctx)
}

func (s *cronService) GeneratePwdDailyReport(ctx context.Context) error {
	return AdSlotPlan().GeneratePwdDailyReport(ctx, time.Now().Add(-3*24*time.Hour), time.Now())
}
