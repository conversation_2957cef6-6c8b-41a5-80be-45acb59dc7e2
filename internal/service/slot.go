package service

import (
	"context"
	"fmt"
	"strconv"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/model"
)

type ISlot interface {
	GetList(ctx context.Context, condition g.Map, page, size int) (*model.SlotListRes, error)
	GetById(ctx context.Context, id int) (*model.Slot, error)
	Create(ctx context.Context, req *model.SlotCreateReq) (*model.SlotCreateRes, error)
	Update(ctx context.Context, req *model.SlotUpdateReq) (*model.SlotUpdateRes, error)
	Delete(ctx context.Context, id int) (*model.SlotDeleteRes, error)
	Audit(ctx context.Context, req *model.SlotAuditReq) (*model.SlotAuditRes, error)
}

type slotService struct{}

var insSlot = slotService{}

func Slot() ISlot {
	return &insSlot
}

// GetList 获取资源位列表
func (s *slotService) GetList(ctx context.Context, condition g.Map, page, size int) (*model.SlotListRes, error) {
	// 获取当前用户信息
	userInfo := Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 打印原始的condition
	g.Log().Debug(ctx, "Original condition:", condition)

	// 构建查询条件
	whereCondition := g.Map{}

	// 如果不是管理员/运营/财务，只能查看自己的资源位
	if !userInfo.IsAdmin() && !userInfo.IsOperator() && !userInfo.IsFinance() {
		whereCondition["user_id"] = userInfo.Id
		g.Log().Debug(ctx, "Added user_id from auth:", userInfo.Id)
	}

	// 添加筛选条件
	for k, v := range condition {
		g.Log().Debug(ctx, "Processing condition key:", k, "value:", v)
	}

	if v, ok := condition["userid"]; ok {
		g.Log().Debug(ctx, "Found userid in condition:", v)
		if v != nil && v != 0 {
			whereCondition["user_id"] = v
			g.Log().Debug(ctx, "Added user_id to whereCondition:", v)
		}
		delete(condition, "userid")
	}

	if v, ok := condition["name"]; ok && v != "" {
		whereCondition["name LIKE ?"] = "%" + v.(string) + "%"
	}
	if v, ok := condition["type"]; ok && v != "" {
		whereCondition["type"] = v
	}
	if v, ok := condition["media_id"]; ok && v != nil && v != 0 {
		whereCondition["media_id"] = v
		delete(condition, "mediaid")
	}
	if v, ok := condition["auditstatus"]; ok && v != "" {
		whereCondition["audit_status"] = v
		delete(condition, "auditstatus")
	}

	// 打印最终的查询条件
	g.Log().Debug(ctx, "Final whereCondition:", whereCondition)

	// 获取总数
	total, err := dao.AdSlots.Ctx(ctx).Where(whereCondition).Count()
	if err != nil {
		return nil, err
	}

	// 获取列表数据
	var slots []*model.Slot
	err = dao.AdSlots.Ctx(ctx).
		Where(whereCondition).
		Page(page, size).
		Order("id DESC").
		Scan(&slots)
	if err != nil {
		return nil, err
	}

	// 如果没有数据，直接返回
	if len(slots) == 0 {
		return &model.SlotListRes{
			List:  slots,
			Total: total,
			Page:  page,
			Size:  size,
		}, nil
	}

	// 获取用户ID列表
	userIds := make([]int, 0)
	mediaIds := make([]int, 0)
	for _, slot := range slots {
		userIds = append(userIds, slot.UserId)
		mediaIds = append(mediaIds, slot.MediaId)
	}

	// 获取用户信息
	var users []*model.User
	err = dao.Users.Ctx(ctx).
		Where("id", userIds).
		Fields("id,real_name").
		Scan(&users)
	if err != nil {
		return nil, err
	}

	// 获取媒体信息
	var medias []*model.Media
	err = dao.AdMedia.Ctx(ctx).
		Where("id", mediaIds).
		Fields("id,name").
		Scan(&medias)
	if err != nil {
		return nil, err
	}

	// 构建用户和媒体映射
	userMap := make(map[int]string)
	for _, user := range users {
		userMap[user.Id] = user.RealName
	}

	mediaMap := make(map[int]string)
	for _, media := range medias {
		mediaMap[media.Id] = media.Name
	}

	// 填充关联数据
	for _, slot := range slots {
		slot.UserRealName = userMap[slot.UserId]
		slot.MediaName = mediaMap[slot.MediaId]

		// 处理类型映射
		switch slot.Type {
		case model.SlotTypeWechatH5:
			slot.TypeText = "微信H5"
		case model.SlotTypeWechatMP:
			slot.TypeText = "微信小程序"
		case model.SlotTypeAlipayMP:
			slot.TypeText = "支付宝小程序"
		case model.SlotTypeAlipayH5:
			slot.TypeText = "支付宝H5"
		case model.SlotTypeOther:
			slot.TypeText = "其他"
		default:
			slot.TypeText = "未知"
		}
	}

	return &model.SlotListRes{
		List:  slots,
		Total: total,
		Page:  page,
		Size:  size,
	}, nil
}

// GetById 获取资源位详情
func (s *slotService) GetById(ctx context.Context, id int) (*model.Slot, error) {
	var slot *model.Slot
	err := dao.AdSlots.Ctx(ctx).Where("id", id).Scan(&slot)
	if err != nil {
		return nil, err
	}
	return slot, nil
}

// Create 创建资源位
func (s *slotService) Create(ctx context.Context, req *model.SlotCreateReq) (*model.SlotCreateRes, error) {
	// 获取当前用户信息
	userInfo := Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 验证媒体是否存在
	media, err := Media().GetById(ctx, req.MediaId)
	if err != nil {
		return nil, err
	}
	if media == nil {
		return nil, model.ErrNotFound
	}

	// 权限检查：管理员可以操作任何媒体，普通媒介只能操作自己的媒体
	if !userInfo.IsAdmin() && media.UserId != req.UserId {
		return nil, model.ErrNotAuthorized
	}

	// 验证媒体状态
	if media.AuditStatus != model.AuditStatusApproved {
		return nil, model.ErrInvalidParam
	}

	// 生成资源位编号
	today := gtime.Now().Format("Ymd")
	maxCode, err := dao.AdSlots.Ctx(ctx).
		Fields("code").
		Where("code LIKE ?", "S"+today+"%").
		Order("code DESC").
		Value()

	var sequence int
	if err != nil || maxCode.IsEmpty() {
		sequence = 1
	} else {
		// 解析序号
		code := maxCode.String()
		if len(code) < 5 {
			return nil, fmt.Errorf("资源位编号格式错误")
		}
		sequence, err = strconv.Atoi(code[len(code)-5:])
		if err != nil {
			return nil, err
		}
		sequence++
	}

	// 生成新编号
	code := fmt.Sprintf("S%s%05d", today, sequence)

	// 设置折扣率默认值
	var discountRate float64
	if media.CooperationType == "cps" {
		discountRate = 30.0
	} else if media.CooperationType == "traffic" && req.Type == model.SlotTypeWechatMP {
		discountRate = 30.0
	} else {
		discountRate = 0.0
	}

	var auditStatus = model.AuditStatusPending
	if media.CooperationType == "dh" || media.CooperationType == "other_delivery" {
		auditStatus = model.AuditStatusApproved
	}

	result, err := dao.AdSlots.Ctx(ctx).Insert(g.Map{
		"code":          code,
		"user_id":       req.UserId,
		"media_id":      req.MediaId,
		"media_name":    media.Name,
		"name":          req.Name,
		"type":          req.Type,
		"audit_status":  auditStatus,
		"leak_rate":     0.0,
		"discount_rate": discountRate,
		"remark":        req.Remark,
		"created_by":    req.UserId,
		"updated_by":    req.UserId,
	})
	if err != nil {
		return nil, err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	return &model.SlotCreateRes{Id: int(id)}, nil
}

// Update 更新资源位
func (s *slotService) Update(ctx context.Context, req *model.SlotUpdateReq) (*model.SlotUpdateRes, error) {
	// 获取当前用户信息
	userInfo := Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 验证媒体是否存在
	media, err := Media().GetById(ctx, req.MediaId)
	if err != nil {
		return nil, err
	}
	if media == nil {
		return nil, model.ErrNotFound
	}

	// 获取资源位信息
	slot, err := s.GetById(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	if slot == nil {
		return nil, model.ErrNotFound
	}

	// 权限检查：管理员可以操作任何媒体，普通媒介只能操作自己的媒体
	if !userInfo.IsAdmin() && media.UserId != slot.UserId {
		return nil, model.ErrNotAuthorized
	}

	// 验证媒体状态
	if media.AuditStatus != model.AuditStatusApproved {
		return nil, model.ErrInvalidParam
	}

	// 设置折扣率默认值
	var discountRate float64
	if media.CooperationType == "cps" {
		discountRate = 30.0
	} else if media.CooperationType == "traffic" && req.Type == model.SlotTypeWechatMP {
		discountRate = 10.0
	} else {
		discountRate = 0.0
	}

	_, err = dao.AdSlots.Ctx(ctx).Where("id", req.Id).Update(g.Map{
		"media_id":      req.MediaId,
		"media_name":    media.Name,
		"name":          req.Name,
		"type":          req.Type,
		"discount_rate": discountRate,
		"remark":        req.Remark,
		"updated_by":    slot.UserId,
	})
	if err != nil {
		return nil, err
	}

	return &model.SlotUpdateRes{}, nil
}

// Delete 删除资源位
func (s *slotService) Delete(ctx context.Context, id int) (*model.SlotDeleteRes, error) {
	_, err := dao.AdSlots.Ctx(ctx).Where("id", id).Delete()
	if err != nil {
		return nil, err
	}

	return &model.SlotDeleteRes{}, nil
}

// Audit 审核资源位
func (s *slotService) Audit(ctx context.Context, req *model.SlotAuditReq) (*model.SlotAuditRes, error) {
	_, err := dao.AdSlots.Ctx(ctx).Where("id", req.Id).Update(g.Map{
		"audit_status":  req.AuditStatus,
		"reject_reason": req.RejectReason,
		"remark":        req.Remark,
	})
	if err != nil {
		return nil, err
	}

	return &model.SlotAuditRes{}, nil
}
