package service

import (
	"context"
	"fmt"
	"strconv"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"ad-pro-v2/internal/consts"
	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/model"
)

type IAgent interface {
	GetList(ctx context.Context, condition g.Map, page, size int) (*model.AgentListRes, error)
	GetById(ctx context.Context, id int) (*model.Agent, error)
	Create(ctx context.Context, req *model.AgentCreateReq) (*model.AgentCreateRes, error)
	Update(ctx context.Context, req *model.AgentUpdateReq) (*model.AgentUpdateRes, error)
	Delete(ctx context.Context, id int) (*model.AgentDeleteRes, error)
	Audit(ctx context.Context, req *model.AgentAuditReq) (*model.AgentAuditRes, error)
}

type agentService struct{}

var insAgent = agentService{}

func Agent() IAgent {
	return &insAgent
}

// GetList 获取代理列表
func (s *agentService) GetList(ctx context.Context, condition g.Map, page, size int) (*model.AgentListRes, error) {
	// 获取当前用户信息
	userInfo := Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 打印原始的condition
	g.Log().Debug(ctx, "Original condition:", condition)

	// 构建查询条件
	whereCondition := g.Map{}

	// 如果不是管理员/运营/财务，只能查看自己的代理
	if !userInfo.IsAdmin() && !userInfo.IsOperator() && !userInfo.IsFinance() {
		whereCondition["user_id"] = userInfo.Id
		g.Log().Debug(ctx, "Added user_id from auth:", userInfo.Id)
	}

	// 添加筛选条件
	for k, v := range condition {
		g.Log().Debug(ctx, "Processing condition key:", k, "value:", v)
	}

	if v, ok := condition["userid"]; ok {
		g.Log().Debug(ctx, "Found userid in condition:", v)
		if v != nil && v != 0 {
			whereCondition["user_id"] = v
			g.Log().Debug(ctx, "Added user_id to whereCondition:", v)
		}
		delete(condition, "userid")
	}

	if v, ok := condition["name"]; ok && v != "" {
		whereCondition["name LIKE ?"] = "%" + v.(string) + "%"
	}
	if v, ok := condition["type"]; ok && v != "" {
		whereCondition["type"] = v
	}
	if v, ok := condition["auditstatus"]; ok && v != "" {
		whereCondition["audit_status"] = v
	}
	if v, ok := condition["cooperationstatus"]; ok && v != "" {
		whereCondition["cooperation_status"] = v
	}

	// 打印最终的查询条件
	g.Log().Debug(ctx, "Final whereCondition:", whereCondition)

	// 获取列表数据
	list, err := dao.AdAgents.Ctx(ctx).
		Where(whereCondition).
		Page(page, size).
		Order("id DESC").
		All()
	if err != nil {
		return nil, err
	}

	// 获取总数
	total, err := dao.AdAgents.Ctx(ctx).Where(whereCondition).Count()
	if err != nil {
		return nil, err
	}

	var agents []*model.Agent
	if err = list.Structs(&agents); err != nil {
		return nil, err
	}

	return &model.AgentListRes{
		List:  agents,
		Total: total,
		Page:  page,
		Size:  size,
	}, nil
}

// GetById 获取代理详情
func (s *agentService) GetById(ctx context.Context, id int) (*model.Agent, error) {
	var agent *model.Agent
	err := dao.AdAgents.Ctx(ctx).Where("id", id).Scan(&agent)
	if err != nil {
		return nil, err
	}
	return agent, nil
}

// Create 创建代理
func (s *agentService) Create(ctx context.Context, req *model.AgentCreateReq) (*model.AgentCreateRes, error) {
	// 验证代理类型
	if req.Type != consts.AgentTypeTraffic && req.Type != consts.AgentTypeDelivery {
		return nil, fmt.Errorf("无效的代理类型")
	}

	var platformConfig interface{} = nil
	// 只在投放类型时验证和设置平台配置
	if req.Type == consts.AgentTypeDelivery {
		if req.PlatformConfig == nil || req.PlatformConfig.Platform == "" {
			return nil, fmt.Errorf("请选择平台类型")
		}
		if req.PlatformConfig.Platform != "denghuoplus" {
			return nil, fmt.Errorf("目前仅支持灯火平台")
		}
		if req.PlatformConfig.Info == nil {
			return nil, fmt.Errorf("平台配置信息不能为空")
		}
		token, ok := req.PlatformConfig.Info["token"]
		if !ok || token == "" {
			return nil, fmt.Errorf("请输入Token")
		}
		pid, ok := req.PlatformConfig.Info["pid"]
		if !ok || pid == "" {
			return nil, fmt.Errorf("请输入PID")
		}
		platformConfig = req.PlatformConfig
	}

	var id int64
	// 开启事务
	err := dao.AdAgents.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 生成代理编号
		today := gtime.Now().Format("Ymd")
		maxCode, err := dao.AdAgents.Ctx(ctx).
			Fields("code").
			Where("code LIKE ?", "A"+today+"%").
			Order("code DESC").
			Value()

		var sequence int
		if err != nil || maxCode.IsEmpty() {
			sequence = 1
		} else {
			// 解析序号
			code := maxCode.String()
			if len(code) < 5 {
				return fmt.Errorf("代理编号格式错误")
			}
			sequence, err = strconv.Atoi(code[len(code)-5:])
			if err != nil {
				return err
			}
			sequence++
		}

		// 生成新编号
		code := fmt.Sprintf("A%s%05d", today, sequence)

		// 构建创建数据
		insertData := g.Map{
			"code":               code,
			"user_id":            req.UserId,
			"name":               req.Name,
			"type":               req.Type,
			"company_name":       req.CompanyName,
			"company_address":    req.CompanyAddress,
			"contact_name":       req.ContactName,
			"contact_phone":      req.ContactPhone,
			"audit_status":       "pending",     // 默认待审核
			"cooperation_status": "not_started", // 默认未开始
			"remarks":            req.Remarks,
			"platform_config":    platformConfig, // 使用前面处理好的平台配置
		}

		// 创建代理
		result, err := dao.AdAgents.Ctx(ctx).Insert(insertData)
		if err != nil {
			return err
		}

		id, err = result.LastInsertId()
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &model.AgentCreateRes{Id: int(id)}, nil
}

// Update 更新代理
func (s *agentService) Update(ctx context.Context, req *model.AgentUpdateReq) (*model.AgentUpdateRes, error) {
	// 验证代理类型
	if req.Type != consts.AgentTypeTraffic && req.Type != consts.AgentTypeDelivery {
		return nil, fmt.Errorf("无效的代理类型")
	}

	var platformConfig interface{} = nil
	// 只在投放类型时验证和设置平台配置
	if req.Type == consts.AgentTypeDelivery {
		if req.PlatformConfig == nil || req.PlatformConfig.Platform == "" {
			return nil, fmt.Errorf("请选择平台类型")
		}
		if req.PlatformConfig.Platform != "denghuoplus" {
			return nil, fmt.Errorf("目前仅支持灯火平台")
		}
		if req.PlatformConfig.Info == nil {
			return nil, fmt.Errorf("平台配置信息不能为空")
		}
		token, ok := req.PlatformConfig.Info["token"]
		if !ok || token == "" {
			return nil, fmt.Errorf("请输入Token")
		}
		pid, ok := req.PlatformConfig.Info["pid"]
		if !ok || pid == "" {
			return nil, fmt.Errorf("请输入PID")
		}
		platformConfig = req.PlatformConfig
	}

	// 构建更新数据
	updateData := g.Map{
		"name":            req.Name,
		"type":            req.Type,
		"company_name":    req.CompanyName,
		"company_address": req.CompanyAddress,
		"contact_name":    req.ContactName,
		"contact_phone":   req.ContactPhone,
		"remarks":         req.Remarks,
		"platform_config": platformConfig, // 使用前面处理好的平台配置
	}

	// 执行更新
	_, err := dao.AdAgents.Ctx(ctx).Where("id", req.Id).Update(updateData)
	if err != nil {
		return nil, err
	}

	return &model.AgentUpdateRes{}, nil
}

// Delete 删除代理
func (s *agentService) Delete(ctx context.Context, id int) (*model.AgentDeleteRes, error) {
	_, err := dao.AdAgents.Ctx(ctx).Where("id", id).Delete()
	if err != nil {
		return nil, err
	}

	return &model.AgentDeleteRes{}, nil
}

// Audit 审核代理
func (s *agentService) Audit(ctx context.Context, req *model.AgentAuditReq) (*model.AgentAuditRes, error) {
	_, err := dao.AdAgents.Ctx(ctx).Where("id", req.Id).Update(g.Map{
		"audit_status":  req.AuditStatus,
		"reject_reason": req.RejectReason,
	})
	if err != nil {
		return nil, err
	}

	return &model.AgentAuditRes{}, nil
}
