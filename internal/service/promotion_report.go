package service

import (
	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/model/entity"
	"context"
	"encoding/json"
	"fmt"
	"math"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/xuri/excelize/v2"
)

func init() {
	// 启动任务处理协程
	go func() {
		for {
			// 创建上下文
			ctx := context.Background()
			// 处理任务
			if err := PromotionReport.processImportTasks(ctx); err != nil {
				g.Log().Error(ctx, "Process import tasks error:", err)
			}
			// 每30秒检查一次任务
			time.Sleep(5 * time.Second)
		}
	}()
}

type IPromotionReport interface {
	// UpdateCost 更新推广成本
	UpdateCost(ctx context.Context, groupId int64, reportDate string, cost float64) error
	// GetCostList 获取推广成本列表
	GetCostList(ctx context.Context, req *model.PromotionReportListReq) (*model.PromotionReportListRes, error)
	// GetModelReport 获取模型报表数据
	GetModelReport(ctx context.Context, modelId uint, categoryId uint, groupId int64) (*model.ModelReportRes, error)
	// ImportCost 导入推广成本
	ImportCost(ctx context.Context, categoryId uint, file *ghttp.UploadFile) (bool, error)
	// ImportXhsCost 导入小红书成本
	ImportXhsCost(ctx context.Context) error
	// GetTaskList 获取任务列表
	GetTaskList(ctx context.Context) ([]*model.TaskListItem, error)
}

// 在变量 PromotionReport 中使用接口类型
var PromotionReport = servicePromotionReport{}

type servicePromotionReport struct{}

// UpdateCost 更新推广成本
func (s *servicePromotionReport) UpdateCost(ctx context.Context, groupId int64, reportDate string, cost float64) error {
	// 1. 先查询密码组是否存在
	var group *entity.PasswordGroup
	err := dao.PasswordGroups.Ctx(ctx).Where(dao.PasswordGroups.Columns().Id, groupId).Scan(&group)
	if err != nil {
		return err
	}
	if group == nil {
		return gerror.Newf("分组ID %d 不存在", groupId)
	}

	// 2. 更新报表数据
	_, err = dao.PromotionReport.Ctx(ctx).
		Where(g.Map{
			dao.PromotionReport.Columns().ReportDate: reportDate,
			dao.PromotionReport.Columns().GroupId:    groupId,
		}).
		Save(g.Map{
			dao.PromotionReport.Columns().Cost:       cost,
			dao.PromotionReport.Columns().ReportDate: reportDate,
			dao.PromotionReport.Columns().GroupId:    groupId,
			dao.PromotionReport.Columns().CategoryId: group.CategoryId,
		})
	if err != nil {
		return err
	}

	// 3. 获取密码组对应的PID列表
	var pids []string
	err = dao.Password.Ctx(ctx).
		Fields(dao.Password.Columns().Pid).
		Where(dao.Password.Columns().GroupId, groupId).
		Scan(&pids)
	if err != nil {
		return err
	}
	if len(pids) == 0 {
		return nil
	}

	// 4. 构造订单表名（按月份分表）
	if len(reportDate) != 8 {
		return gerror.New("报表日期格式错误")
	}
	tableName := "cps_orders_" + reportDate[:6]

	// 5. 统计订单数和佣金
	var result struct {
		TotalOrders     int     `json:"total_orders"`
		TotalCommission float64 `json:"total_commission"`
	}
	err = g.DB("warehouse").Ctx(ctx).Model(tableName).
		WhereIn("pid", pids).
		WhereIn("order_status", g.Slice{2, 3, 4}).
		Fields(
			"COUNT(1) as total_orders",
			"SUM(commission - third_service_fee + activity_fee - activity_service_fee) as total_commission",
		).
		Scan(&result)
	if err != nil {
		return err
	}

	// 6. 更新统计数据
	_, err = dao.PromotionReport.Ctx(ctx).
		Where(g.Map{
			dao.PromotionReport.Columns().ReportDate: reportDate,
			dao.PromotionReport.Columns().GroupId:    groupId,
		}).
		Data(g.Map{
			dao.PromotionReport.Columns().TotalOrders:     result.TotalOrders,
			dao.PromotionReport.Columns().TotalCommission: result.TotalCommission,
		}).
		Update()
	return err
}

// GetCostList 获取推广成本列表
func (s *servicePromotionReport) GetCostList(ctx context.Context, req *model.PromotionReportListReq) (*model.PromotionReportListRes, error) {
	var (
		m    = dao.PromotionReport.Ctx(ctx)
		list []model.PromotionReportListItem
	)

	// 1. 关联密码组表获取组名和平台信息
	m = m.LeftJoin(dao.PasswordGroups.Table(), dao.PasswordGroups.Table()+"."+dao.PasswordGroups.Columns().Id+"="+
		dao.PromotionReport.Table()+"."+dao.PromotionReport.Columns().GroupId)

	// 2. 添加查询条件
	if req.GroupId > 0 {
		m = m.Where(dao.PromotionReport.Table()+"."+dao.PromotionReport.Columns().GroupId, req.GroupId)
	}
	if req.CategoryId > 0 {
		m = m.Where(dao.PromotionReport.Table()+"."+dao.PromotionReport.Columns().CategoryId, req.CategoryId)
	}
	if req.StartDate != "" && req.EndDate != "" {
		m = m.WhereBetween(dao.PromotionReport.Table()+"."+dao.PromotionReport.Columns().ReportDate, req.StartDate, req.EndDate)
	}

	// 3. 查询总数
	total, err := m.Count()
	if err != nil {
		return nil, err
	}

	// 4. 查询数据
	err = m.Fields(
		dao.PromotionReport.Table()+"."+dao.PromotionReport.Columns().Id,
		dao.PromotionReport.Table()+"."+dao.PromotionReport.Columns().GroupId+" as group_id",
		dao.PasswordGroups.Table()+"."+dao.PasswordGroups.Columns().Name+" as group_name",
		dao.PromotionReport.Table()+"."+dao.PromotionReport.Columns().ReportDate,
		dao.PromotionReport.Table()+"."+dao.PromotionReport.Columns().CategoryId+" as category_id",
		dao.PromotionReport.Table()+"."+dao.PromotionReport.Columns().Cost,
		dao.PromotionReport.Table()+"."+dao.PromotionReport.Columns().UpdatedAt+" as updated_at",
	).
		Page(req.Page, req.Size).
		OrderDesc(dao.PromotionReport.Table() + "." + dao.PromotionReport.Columns().ReportDate).
		Scan(&list)

	if err != nil {
		return nil, err
	}

	// 5. 处理平台名称
	for i := range list {
		switch list[i].CategoryId {
		case 55:
			list[i].CategoryName = "小红书"
		case 173:
			list[i].CategoryName = "抖音"
		case 284:
			list[i].CategoryName = "闪购"
		default:
			list[i].CategoryName = "未知"
		}
	}

	return &model.PromotionReportListRes{
		List:  list,
		Page:  req.Page,
		Size:  req.Size,
		Total: total,
	}, nil
}

// GetModelReport 获取模型报表数据
func (s *servicePromotionReport) GetModelReport(ctx context.Context, modelId uint, categoryId uint, groupId int64) (*model.ModelReportRes, error) {
	// 1. 获取衰减记录
	var decayRecords []*entity.ModelDecay
	err := dao.ModelDecay.DB().Model(dao.ModelDecay.Table()).
		Where(dao.ModelDecay.Columns().ModelId, modelId).
		Order(dao.ModelDecay.Columns().Id + " ASC").
		Scan(&decayRecords)
	if err != nil {
		return nil, err
	}

	// 2. 获取报表数据
	var reports []*entity.PromotionReport
	m := dao.PromotionReport.DB().Model(dao.PromotionReport.Table())

	if groupId > 0 {
		// 如果指定了口令组ID，则按口令组ID查询
		m = m.Where(dao.PromotionReport.Columns().GroupId, groupId)
	} else if categoryId > 0 {
		// 如果指定了分类ID，则按分类ID查询
		m = m.Where(dao.PromotionReport.Columns().CategoryId, categoryId)
	}

	err = m.Order(dao.PromotionReport.Columns().ReportDate + " ASC").
		Scan(&reports)
	if err != nil {
		return nil, err
	}

	// 3. 构建日期到报表数据的映射
	reportMap := make(map[string]*entity.PromotionReport)
	var minDate, maxDate string
	for _, report := range reports {
		date := report.ReportDate
		if minDate == "" || date < minDate {
			minDate = date
		}
		if maxDate == "" || date > maxDate {
			maxDate = date
		}
		if existingReport, ok := reportMap[date]; ok {
			// 如果同一天有多条数据，则累加
			existingReport.TotalOrders += report.TotalOrders
			existingReport.Cost += report.Cost
			existingReport.TotalCommission += report.TotalCommission
		} else {
			reportMap[date] = report
		}
	}

	if minDate == "" || maxDate == "" {
		// 如果没有数据，返回空结果
		return &model.ModelReportRes{
			List: make([]model.ModelReportItem, 0),
		}, nil
	}

	// 4. 生成连续日期列表并填充数据
	var result []model.ModelReportItem
	startTime, _ := time.ParseInLocation("20060102", minDate, time.Local)
	endTime, _ := time.ParseInLocation("20060102", maxDate, time.Local)

	// 先生成连续的日期列表
	for d := startTime; d.Before(endTime) || d.Equal(endTime); d = d.AddDate(0, 0, 1) {
		date := d.Format("20060102")
		result = append(result, model.ModelReportItem{
			Date: date,
		})
	}

	// 填充报表数据
	for i := range result {
		date := result[i].Date
		if _, exists := reportMap[date]; !exists {
			// 如果这一天没有数据，所有值都设为0
			result[i].TotalOrders = 0
			result[i].Cost = 0
			result[i].TotalCommission = 0
			result[i].NewOrders = 0
			result[i].DailyCost = 0
			result[i].PaybackDays = -1
		} else {
			// 填充实际数据
			result[i].TotalOrders = reportMap[date].TotalOrders
			result[i].Cost = reportMap[date].Cost
			result[i].TotalCommission = reportMap[date].TotalCommission
		}

		report := result[i]

		// 计算单均佣金
		if report.TotalOrders > 0 {
			result[i].AverageCommission = report.TotalCommission / float64(report.TotalOrders)
		} else {
			result[i].AverageCommission = 0
		}

		if i == 0 {
			// 第一天的新订单数就是总订单数
			result[i].NewOrders = report.TotalOrders
			if result[i].NewOrders > 0 {
				result[i].DailyCost = report.Cost / float64(result[i].NewOrders)
			} else {
				result[i].DailyCost = 0
			}
		} else {
			// 计算历史订单在当天的衰减订单数
			var decayOrders int
			// 遍历之前的每一天，计算它们在当天产生的订单数
			for j := 0; j < i; j++ {
				prevDay := result[j]
				// 获取正确的衰减比例
				// 比如：第三天计算第一天的衰减时用第二天的衰减比例，计算第二天的衰减时用第一天的衰减比例
				decayIndex := i - j - 1 // 第几天的衰减比例
				if decayIndex < len(decayRecords) {
					// 计算第j天在第i天产生的订单数
					// 使用第decayIndex天的衰减比例
					decay := int(math.Round(float64(prevDay.NewOrders) * decayRecords[decayIndex].Percent / 100))
					decayOrders += decay
				}
			}

			// 当日新订单数 = 总订单数 - 历史订单衰减数
			newOrders := report.TotalOrders - decayOrders
			// if newOrders < 0 {
			// 	newOrders = 0
			// }
			result[i].NewOrders = newOrders
			if newOrders == 0 {
				result[i].DailyCost = 0
			} else {
				// 计算当日订单成本
				result[i].DailyCost = report.Cost / float64(newOrders)
			}
		}

		// 计算回本周期天数
		if result[i].DailyCost > 0 && result[i].AverageCommission > 0 {
			// 计算累计收益
			var totalRevenue float64 = result[i].AverageCommission
			var paybackDays int = 1

			// 遍历衰减记录，直到累计收益大于等于当日订单成本
			for j := 0; j < len(decayRecords); j++ {
				if totalRevenue >= result[i].DailyCost {
					break
				}
				// 将衰减比例转换为小数并累加收益
				decayPercent := decayRecords[j].Percent / 100
				totalRevenue += result[i].AverageCommission * decayPercent
				paybackDays++
			}

			// 如果遍历完所有衰减记录后仍未回本，则设置为-1表示无法回本
			if totalRevenue < result[i].DailyCost {
				result[i].PaybackDays = -1
			} else {
				result[i].PaybackDays = paybackDays
			}
		} else {
			result[i].PaybackDays = -1
		}

	}

	return &model.ModelReportRes{
		List: result,
	}, nil
}

// updateOrderDataBatch 批量更新订单数据
func (s *servicePromotionReport) updateOrderDataBatch(ctx context.Context, tx gdb.TX, groupId int64, startDate, endDate string) error {
	// 1. 获取密码组对应的PID列表
	type PidResult struct {
		Pid string `json:"pid"`
	}
	var pidResults []PidResult
	err := tx.Model(dao.Password.Table()).
		Fields(dao.Password.Columns().Pid).
		Where(dao.Password.Columns().GroupId, groupId).
		WhereNot(dao.Password.Columns().Pid, "").
		Scan(&pidResults)
	if err != nil {
		return err
	}

	// 将结果转换为 []string
	pids := make([]string, 0, len(pidResults))
	for _, result := range pidResults {
		if result.Pid != "" {
			pids = append(pids, result.Pid)
		}
	}

	if len(pids) == 0 {
		g.Log().Warning(ctx, "No valid PIDs found for group:", groupId)
		return nil
	}

	// 2. 按日期统计数据
	type DailyStats struct {
		TotalOrders     int     `json:"total_orders"`
		TotalCommission float64 `json:"total_commission"`
	}
	dailyData := make(map[string]DailyStats)

	// 4. 处理查询逻辑
	type QueryResult struct {
		OrderDate       string  `json:"order_date"`
		TotalOrders     int     `json:"total_orders"`
		TotalCommission float64 `json:"total_commission"`
	}

	// 定义查询函数
	queryOrders := func(tableName, startTime, endTime string) ([]QueryResult, error) {
		var results []QueryResult
		err := g.DB("warehouse").
			Model(tableName).
			Ctx(ctx).
			Fields(
				"DATE_FORMAT(create_time, '%Y%m%d') as order_date",
				"COUNT(1) as total_orders",
				"SUM(commission - third_service_fee + activity_fee - activity_service_fee) as total_commission",
			).
			Where("pid IN(?)", pids).
			Where("order_status IN(?)", g.Slice{2, 3, 4}).
			Where("create_time >= ?", startTime).
			Where("create_time <= ?", endTime).
			Group("DATE_FORMAT(create_time, '%Y%m%d')").
			Order("order_date ASC").
			Scan(&results)
		return results, err
	}

	// 判断是否跨月
	startMonth := startDate[:6]
	endMonth := endDate[:6]

	if startMonth == endMonth {
		// 同月查询
		tableName := "cps_orders_" + startMonth
		t, _ := time.ParseInLocation("20060102", startDate, time.Local)
		startTime := t.Format("2006-01-02") + " 00:00:00"
		t, _ = time.ParseInLocation("20060102", endDate, time.Local)
		endTime := t.Format("2006-01-02") + " 23:59:59"

		results, err := queryOrders(tableName, startTime, endTime)
		if err != nil {
			return err
		}

		// 保存结果
		for _, result := range results {
			dailyData[result.OrderDate] = DailyStats{
				TotalOrders:     result.TotalOrders,
				TotalCommission: result.TotalCommission,
			}
		}
	} else {
		// 跨月查询
		startTime, _ := time.ParseInLocation("200601", startMonth, time.Local)
		endTime, _ := time.ParseInLocation("200601", endMonth, time.Local)

		// 遍历每个月份
		for d := startTime; !d.After(endTime); d = d.AddDate(0, 1, 0) {
			monthStr := d.Format("200601")
			tableName := "cps_orders_" + monthStr

			// 构建查询时间范围
			var queryStartTime, queryEndTime string
			if monthStr == startMonth {
				// 起始月使用指定的开始日期
				t, _ := time.ParseInLocation("20060102", startDate, time.Local)
				queryStartTime = t.Format("2006-01-02") + " 00:00:00"
			} else {
				// 非起始月使用月初
				queryStartTime = d.Format("2006-01-02") + " 00:00:00"
			}

			if monthStr == endMonth {
				// 结束月使用指定的结束日期
				t, _ := time.ParseInLocation("20060102", endDate, time.Local)
				queryEndTime = t.Format("2006-01-02") + " 23:59:59"
			} else {
				// 非结束月使用月末
				queryEndTime = d.AddDate(0, 1, -1).Format("2006-01-02") + " 23:59:59"
			}

			results, err := queryOrders(tableName, queryStartTime, queryEndTime)
			if err != nil {
				return err
			}

			// 保存结果
			for _, result := range results {
				dailyData[result.OrderDate] = DailyStats{
					TotalOrders:     result.TotalOrders,
					TotalCommission: result.TotalCommission,
				}
			}
		}
	}

	// 5. 批量更新数据
	for date, data := range dailyData {
		// 1. 先查询密码组是否存在
		var group *entity.PasswordGroup
		err := dao.PasswordGroups.Ctx(ctx).Where(dao.PasswordGroups.Columns().Id, groupId).Scan(&group)
		if err != nil {
			return err
		}
		if group == nil {
			return gerror.Newf("分组ID %d 不存在", groupId)
		}

		_, err = tx.Model(dao.PromotionReport.Table()).
			Where(g.Map{
				dao.PromotionReport.Columns().ReportDate: date,
				dao.PromotionReport.Columns().GroupId:    groupId,
			}).
			Save(g.Map{
				dao.PromotionReport.Columns().ReportDate:      date,
				dao.PromotionReport.Columns().GroupId:         groupId,
				dao.PromotionReport.Columns().TotalOrders:     data.TotalOrders,
				dao.PromotionReport.Columns().TotalCommission: data.TotalCommission,
				dao.PromotionReport.Columns().CategoryId:      group.CategoryId,
			})
		if err != nil {
			return err
		}
	}

	return nil
}

// 全局导入锁
var importMutex sync.Mutex

// ImportCost 导入推广成本
func (s *servicePromotionReport) ImportCost(ctx context.Context, categoryId uint, file *ghttp.UploadFile) (bool, error) {
	// 1. 检查文件是否存在
	if file == nil {
		return false, gerror.New("请上传Excel文件")
	}

	// 2. 使用互斥锁实现导入限制
	if !importMutex.TryLock() {
		return false, gerror.New("当前有导入任务正在进行中，请稍后再试")
	}
	// 确保在函数结束时释放锁
	defer importMutex.Unlock()

	// 3. 打开文件
	fileReader, err := file.Open()
	if err != nil {
		return false, err
	}
	defer fileReader.Close()

	// 4. 使用 excelize 读取 Excel
	f, err := excelize.OpenReader(fileReader)
	if err != nil {
		return false, err
	}
	defer f.Close()

	// 5. 获取所有sheet名称
	sheets := f.GetSheetList()

	// 6. 存储所有需要验证的口令组名称
	var groupNames []string
	for _, sheet := range sheets {
		// 检查sheet名称格式是否符合要求
		if !strings.HasSuffix(sheet, "-总") {
			continue
		}
		// 提取口令组名称并去除前后空格
		groupName := strings.TrimSpace(strings.TrimSuffix(sheet, "-总"))
		if groupName != "" {
			groupNames = append(groupNames, groupName)
		}
	}

	if len(groupNames) == 0 {
		return false, gerror.New("Excel中没有找到符合格式的sheet页，sheet名称必须以'-总'结尾")
	}

	// 7. 验证所有口令组是否存在
	var groups []*entity.PasswordGroup
	err = dao.PasswordGroups.Ctx(ctx).
		Where(dao.PasswordGroups.Columns().Name+" IN(?)", groupNames).
		Where("category_id", categoryId).
		Scan(&groups)
	if err != nil {
		return false, err
	}

	// 创建口令组名称到ID的映射
	groupMap := make(map[string]int64)
	for _, group := range groups {
		groupMap[group.Name] = int64(group.Id)
	}

	// 检查是否有未入库的口令组
	var missingGroups []string
	for _, name := range groupNames {
		if _, exists := groupMap[name]; !exists {
			missingGroups = append(missingGroups, name)
		}
	}

	if len(missingGroups) > 0 {
		return false, gerror.Newf("以下口令组未入库，请先入库后再导入：%s", strings.Join(missingGroups, "、"))
	}

	// 用于跟踪是否创建了任务
	var hasTask bool

	// 8. 开启事务处理数据导入
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		currentYear := time.Now().Year()
		currentMonth := int(time.Now().Month())

		// 收集组信息
		var groupInfos []g.Map

		for sheetName, groupId := range groupMap {
			// 获取sheet数据
			rows, err := f.GetRows(sheetName + "-总")
			if err != nil {
				return gerror.Newf("读取sheet[%s]失败: %v", sheetName, err)
			}

			// 获取数据库中该组的最大日期
			// 增量同步逻辑
			var result struct {
				MaxDate string `json:"max_date"`
			}
			err = tx.Model(dao.PromotionReport.Table()).
				Where(dao.PromotionReport.Columns().GroupId, groupId).
				Fields("MAX(" + dao.PromotionReport.Columns().ReportDate + ") as max_date").
				Scan(&result)
			if err != nil {
				return err
			}
			//maxDate := result.MaxDate

			// 解析Excel中的所有日期和成本
			dailyCosts := make(map[string]float64)
			var minDate, maxExcelDate string

			// 跳过表头，处理所有数据行
			for i := 1; i < len(rows); i++ {
				row := rows[i]
				if len(row) < 2 {
					continue
				}

				// 解析日期
				dateStr := strings.TrimSpace(row[0])
				if dateStr == "" || strings.TrimSpace(dateStr) == "总计" {
					break
				}

				var year, month, day int
				// 尝试解析 Excel 数值日期
				if numDate, err := strconv.ParseFloat(dateStr, 64); err == nil {
					// Excel 的日期是从 1900-01-01 开始的天数
					// 将 Excel 数值转换为时间
					excelEpoch := time.Date(1899, time.December, 30, 0, 0, 0, 0, time.UTC)
					date := excelEpoch.Add(time.Duration(numDate) * 24 * time.Hour)
					year = date.Year()
					month = int(date.Month())
					day = date.Day()
				} else {
					// 如果不是数值，尝试解析 "MM月DD日" 格式
					parts := strings.Split(dateStr, "月")
					if len(parts) != 2 {
						fmt.Println("日期", dateStr)
						return gerror.Newf("sheet[%s]第%d行日期格式错误，应为'MM月DD日'格式或Excel日期格式", sheetName, i+1)
					}

					var err error
					month, err = strconv.Atoi(parts[0])
					if err != nil {
						return gerror.Newf("sheet[%s]第%d行月份格式错误: %v", sheetName, i+1, err)
					}

					day, err = strconv.Atoi(strings.TrimSuffix(parts[1], "日"))
					if err != nil {
						return gerror.Newf("sheet[%s]第%d行日期格式错误: %v", sheetName, i+1, err)
					}

					// 确定年份
					year = currentYear
					if month > currentMonth {
						year--
					}
				}

				// 构造报表日期
				reportDate := fmt.Sprintf("%d%02d%02d", year, month, day)

				// 如果日期超过今天了则忽略
				if reportDate > time.Now().Format("20060102") {
					continue
				}

				// 解析成本
				costStr := strings.TrimSpace(row[1])
				var cost float64
				if costStr == "" {
					cost = 0
				} else {
					cost, err = strconv.ParseFloat(costStr, 64)
					if err != nil {
						cost = 0
					}
				}

				dailyCosts[reportDate] = cost

				// 更新最小和最大日期
				if minDate == "" || reportDate < minDate {
					minDate = reportDate
				}
				if maxExcelDate == "" || reportDate > maxExcelDate {
					maxExcelDate = reportDate
				}
			}

			// 如果没有数据，跳过处理
			if minDate == "" || maxExcelDate == "" {
				continue
			}

			// 如果数据库中已有数据，且Excel中的最大日期小于等于数据库中的最大日期，则跳过
			//if maxDate != "" && maxExcelDate <= maxDate {
			//	continue
			//}

			// 获取5天前的日期
			daysAgo := time.Now().AddDate(0, 0, -5).Format("20060102")

			// 记录需要更新订单数据的日期范围
			var needUpdateStartDate, needUpdateEndDate string

			// 处理每个日期的数据
			for reportDate, cost := range dailyCosts {
				// 只处理10天内的数据
				if reportDate < daysAgo {
					continue
				}
				// 检查记录是否存在
				count, err := tx.Model(dao.PromotionReport.Table()).
					Where(g.Map{
						dao.PromotionReport.Columns().ReportDate: reportDate,
						dao.PromotionReport.Columns().GroupId:    groupId,
					}).
					Count()
				if err != nil {
					return err
				}

				// 判断是否需要更新数据
				needUpdate := false
				if count == 0 { // 记录不存在，需要新增
					needUpdate = true
				} else if reportDate >= daysAgo { // 记录存在且在5天内，需要更新
					needUpdate = true
				}

				if needUpdate {
					// 更新或插入报表数据
					_, err = tx.Model(dao.PromotionReport.Table()).
						Where(g.Map{
							dao.PromotionReport.Columns().ReportDate: reportDate,
							dao.PromotionReport.Columns().GroupId:    groupId,
						}).
						Save(g.Map{
							dao.PromotionReport.Columns().Cost:       cost,
							dao.PromotionReport.Columns().ReportDate: reportDate,
							dao.PromotionReport.Columns().GroupId:    groupId,
							dao.PromotionReport.Columns().CategoryId: categoryId,
						})
					if err != nil {
						return err
					}

					// 更新需要处理订单数据的日期范围
					if needUpdateStartDate == "" || reportDate < needUpdateStartDate {
						needUpdateStartDate = reportDate
					}
					if needUpdateEndDate == "" || reportDate > needUpdateEndDate {
						needUpdateEndDate = reportDate
					}
				}

			}

			// 如果有需要更新的数据，则更新订单数据
			if needUpdateStartDate != "" && needUpdateEndDate != "" {
				// 收集组信息,包含该组的更新日期范围
				groupInfos = append(groupInfos, g.Map{
					"group_id":   groupId,
					"sheet_name": sheetName,
					"start_date": needUpdateStartDate,
					"end_date":   needUpdateEndDate,
				})
			}
		}

		// 如果有需要更新的数据，创建一个总的任务
		if len(groupInfos) > 0 {
			paramsJson, err := json.Marshal(groupInfos)
			if err != nil {
				return err
			}

			// 生成任务名称：年月日+文件名（不含扩展名）
			fileName := strings.TrimSuffix(file.Filename, filepath.Ext(file.Filename))
			taskName := time.Now().Format("20060102") + "_" + fileName

			_, err = tx.Model(dao.Tasks.Table()).
				Data(g.Map{
					dao.Tasks.Columns().TaskType: 1, // 费用导入场景
					dao.Tasks.Columns().Params:   string(paramsJson),
					dao.Tasks.Columns().Status:   "pending",
					dao.Tasks.Columns().Name:     taskName,
				}).
				Insert()
			if err != nil {
				return err
			}
			hasTask = true
		}
		return nil
	})

	return hasTask, err
}

// ImportXhsCost 导入小红书成本
func (s *servicePromotionReport) ImportXhsCost(ctx context.Context) error {
	// 1. 查询小红书成本数据
	var results []struct {
		CreateTime string  `json:"create_time"`
		RealCost   float64 `json:"real_cost"`
		PwdName    string  `json:"pwd_name"`
	}

	// 设置最早日期为 2024-10-20
	minDate := time.Now().AddDate(0, 0, -10).Format(time.DateTime)
	maxDate := time.Now().Format(time.DateTime)

	err := g.DB().Ctx(ctx).Raw(`
		SELECT
			create_time,
			CASE
				WHEN type in (1,2) AND create_time < '2024-12-16' THEN costs * 0.909
				WHEN type in (1,2) AND create_time >= '2024-12-16' AND create_time < '2025-2-16' THEN costs * 0.87
				WHEN type in (1,2) AND create_time >= '2025-2-16' THEN costs * 0.877
				WHEN type = 3 THEN costs * 0.83
				WHEN type = 4 THEN costs * 0.87
				WHEN type = 5 THEN costs * 0.862
				WHEN type = 6 THEN costs * 0.885
                WHEN type = 7 THEN costs * 0.909
				ELSE 0
			END AS real_cost,
			CASE
				WHEN koulinggroup IS NULL OR koulinggroup = '' THEN
					(SELECT pwd_name
					FROM warehouse.elm_pwds
					WHERE SUBSTRING_INDEX(pid,"_",-1) = ad_zone_id
					LIMIT 1)
				ELSE koulinggroup
			END AS pwd_name
		FROM
			byn_data.cx_costs
		WHERE
			create_time >= ? and create_time <= ?
		ORDER BY create_time ASC
	`, minDate, maxDate).Scan(&results)

	if err != nil {
		return err
	}

	// 2. 按日期和口令组名称聚合数据
	type DailyCost struct {
		Cost float64
	}
	dailyCosts := make(map[string]map[string]DailyCost) // map[date]map[pwdName]DailyCost

	// 获取5天前的日期
	fiveDaysAgo := time.Now().AddDate(0, 0, -5).Format("20060102")
	// 转换最小日期为 YYYYMMDD 格式，用于后续过滤
	minDateYMD := "20241020"

	for _, result := range results {
		// 转换日期格式从 YYYY-MM-DD 到 YYYYMMDD
		t, err := time.Parse("2006-01-02", result.CreateTime[:10])
		if err != nil {
			g.Log().Warning(ctx, "Invalid date format:", result.CreateTime)
			continue
		}
		date := t.Format("20060102")

		// 确保日期不早于最小日期（虽然SQL已经过滤，这里做双重保障）
		if date < minDateYMD {
			continue
		}

		if dailyCosts[date] == nil {
			dailyCosts[date] = make(map[string]DailyCost)
		}

		// 累加同一天同一口令组的成本
		cost := dailyCosts[date][result.PwdName]
		cost.Cost += result.RealCost
		dailyCosts[date][result.PwdName] = cost
	}

	// 3. 开启事务处理数据导入
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 查询所有小红书分类的口令组
		var groups []*entity.PasswordGroup
		err := tx.Model(dao.PasswordGroups.Table()).
			Where(dao.PasswordGroups.Columns().CategoryId, 55). // 55 是小红书分类ID
			Scan(&groups)
		if err != nil {
			return err
		}

		// 创建口令组名称到ID的映射
		groupMap := make(map[string]int64)
		for _, group := range groups {
			groupMap[group.Name] = int64(group.Id)
		}

		// 批量更新数据
		// 按口令组聚合数据，记录每个口令组的最早和最晚日期
		type GroupDateRange struct {
			MinDate string
			MaxDate string
		}
		groupDateRanges := make(map[int64]GroupDateRange)

		for date, pwdCosts := range dailyCosts {
			for pwdName, cost := range pwdCosts {
				// 查找对应的口令组ID
				groupId, exists := groupMap[pwdName]
				if !exists {
					// 如果口令组不存在，记录日志并继续
					g.Log().Noticef(ctx, "Password group not found: %s", pwdName)
					continue
				}

				// 检查记录是否存在
				count, err := tx.Model(dao.PromotionReport.Table()).
					Where(g.Map{
						dao.PromotionReport.Columns().ReportDate: date,
						dao.PromotionReport.Columns().GroupId:    groupId,
					}).
					Count()
				if err != nil {
					return err
				}

				// 如果记录存在且日期在30天前，则跳过
				if count > 0 && date < fiveDaysAgo {
					continue
				}

				// 更新或插入报表数据
				_, err = tx.Model(dao.PromotionReport.Table()).
					Where(g.Map{
						dao.PromotionReport.Columns().ReportDate: date,
						dao.PromotionReport.Columns().GroupId:    groupId,
					}).
					Save(g.Map{
						dao.PromotionReport.Columns().Cost:       cost.Cost,
						dao.PromotionReport.Columns().ReportDate: date,
						dao.PromotionReport.Columns().GroupId:    groupId,
						dao.PromotionReport.Columns().CategoryId: 55, // 小红书分类ID
					})
				if err != nil {
					return err
				}

				// 只有新记录或5天内的记录才更新日期范围
				if count == 0 || date >= fiveDaysAgo {
					// 更新日期范围
					dateRange := groupDateRanges[groupId]
					if dateRange.MinDate == "" || date < dateRange.MinDate {
						dateRange.MinDate = date
					}
					if dateRange.MaxDate == "" || date > dateRange.MaxDate {
						dateRange.MaxDate = date
					}
					groupDateRanges[groupId] = dateRange
				}
			}
		}

		// 批量更新每个口令组的订单数据
		for groupId, dateRange := range groupDateRanges {
			// 如果日期范围为空，说明该组没有需要更新的数据
			if dateRange.MinDate == "" || dateRange.MaxDate == "" {
				continue
			}
			err = s.updateOrderDataBatch(ctx, tx, groupId, dateRange.MinDate, dateRange.MaxDate)
			if err != nil {
				return err
			}
		}

		return nil
	})

	return err
}

// GetTaskList 获取任务列表
func (s *servicePromotionReport) GetTaskList(ctx context.Context) ([]*model.TaskListItem, error) {
	var list []*model.TaskListItem
	err := dao.Tasks.Ctx(ctx).
		Fields(
			dao.Tasks.Columns().Id,
			dao.Tasks.Columns().Name,
			dao.Tasks.Columns().Status,
			dao.Tasks.Columns().CreatedAt,
		).
		Where(dao.Tasks.Columns().TaskType, 1). // 只查询费用导入场景的任务
		OrderDesc(dao.Tasks.Columns().Id).
		Limit(10).
		Scan(&list)
	return list, err
}

// processImportTasks 处理导入任务
func (s *servicePromotionReport) processImportTasks(ctx context.Context) error {
	// 查询待处理的任务
	var tasks []*entity.Tasks
	err := dao.Tasks.Ctx(ctx).
		Where(dao.Tasks.Columns().TaskType, 1). // 费用导入场景
		Where(dao.Tasks.Columns().Status, "pending").
		OrderAsc(dao.Tasks.Columns().Id).
		Scan(&tasks)
	if err != nil {
		return err
	}

	if len(tasks) == 0 {
		return nil
	}

	// 处理每个任务
	for _, task := range tasks {
		// 更新任务状态为处理中
		_, err = dao.Tasks.Ctx(ctx).
			Where(dao.Tasks.Columns().Id, task.Id).
			Data(g.Map{
				dao.Tasks.Columns().Status: "processing",
			}).
			Update()
		if err != nil {
			g.Log().Error(ctx, "Update task status error:", err)
			continue
		}

		// 解析任务参数
		var groupInfos []g.Map
		err = json.Unmarshal([]byte(task.Params), &groupInfos)
		if err != nil {
			g.Log().Error(ctx, "Parse task params error:", err)
			s.updateTaskStatus(ctx, uint64(task.Id), "failed", err.Error())
			continue
		}

		// 开启事务处理数据
		err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			// 处理每个组的数据
			for _, info := range groupInfos {
				groupId := info["group_id"].(float64)
				startDate := info["start_date"].(string)
				endDate := info["end_date"].(string)

				// 更新订单数据
				err = s.updateOrderDataBatch(ctx, tx, int64(groupId), startDate, endDate)
				if err != nil {
					return err
				}
			}
			return nil
		})

		if err != nil {
			g.Log().Error(ctx, "Process task error:", err)
			s.updateTaskStatus(ctx, uint64(task.Id), "failed", err.Error())
			continue
		}

		// 更新任务状态为完成
		s.updateTaskStatus(ctx, uint64(task.Id), "completed", "")
	}

	return nil
}

// updateTaskStatus 更新任务状态
func (s *servicePromotionReport) updateTaskStatus(ctx context.Context, taskId uint64, status string, errMsg string) {
	data := g.Map{
		dao.Tasks.Columns().Status: status,
	}
	if errMsg != "" {
		data["error_msg"] = errMsg // 使用字符串字段名
	}
	_, err := dao.Tasks.Ctx(ctx).
		Where(dao.Tasks.Columns().Id, taskId).
		Data(data).
		Update()
	if err != nil {
		g.Log().Error(ctx, "Update task status error:", err)
	}
}
