// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package service

import (
	"context"
	"ad-pro-v2/internal/model/entity"
)

// IAuth 认证服务接口
type IAuth interface {
	// Login 用户登录
	Login(ctx context.Context, email, password string) (user *entity.Users, token string, err error)
	// ChangePassword 修改密码
	ChangePassword(ctx context.Context, userId uint64, oldPassword, newPassword string) error
}

var (
	localAuth IAuth
)

// Auth 获取认证服务单例
func Auth() IAuth {
	if localAuth == nil {
		panic("implement not found for interface IAuth, forgot register?")
	}
	return localAuth
}

// RegisterAuth 注册认证服务实现
func RegisterAuth(i IAuth) {
	localAuth = i
} 