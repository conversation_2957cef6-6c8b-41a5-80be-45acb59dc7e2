package dashboard

import (
	v1 "ad-pro-v2/api/dashboard/v1"
	"ad-pro-v2/internal/dao"
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcache"
)

// IFilterService 筛选服务接口
type IFilterService interface {
	GetFilterOptions(ctx context.Context) (*v1.GetFilterOptionsRes, error)
	GetProducts(ctx context.Context) (*v1.GetProductsRes, error)
}

// FilterService 筛选服务实现
type FilterService struct {
	cache *gcache.Cache
}

// NewFilterService 创建新的筛选服务实例
func NewFilterService(cache *gcache.Cache) IFilterService {
	return &FilterService{
		cache: cache,
	}
}

// GetFilterOptions 获取筛选选项
func (s *FilterService) GetFilterOptions(ctx context.Context) (*v1.GetFilterOptionsRes, error) {
	g.Log().Info(ctx, "获取筛选选项开始")

	// 缓存键
	cacheKey := "dashboard:filter_options"

	// 尝试从缓存获取
	if cached, err := s.cache.Get(ctx, cacheKey); err == nil && !cached.IsNil() {
		g.Log().Info(ctx, "从缓存获取筛选选项")
		var res *v1.GetFilterOptionsRes
		if err := cached.Struct(&res); err == nil {
			return res, nil
		}
	}

	// 获取媒体列表
	mediaList, err := s.getMediaList(ctx)
	if err != nil {
		g.Log().Warning(ctx, "获取媒体列表失败:", err)
		mediaList = []*v1.MediaListItem{}
	}

	// 获取产品列表
	productList, err := s.getProductList(ctx)
	if err != nil {
		g.Log().Warning(ctx, "获取产品列表失败:", err)
		productList = []*v1.ProductItem{}
	}

	// 构建响应数据
	res := &v1.GetFilterOptionsRes{
		ShowMediaList: len(mediaList) > 0,
		MediaList:     mediaList,
		ProductList:   productList,
	}

	// 缓存结果 (5分钟)
	s.cache.Set(ctx, cacheKey, res, 300)

	g.Log().Info(ctx, "获取筛选选项完成", g.Map{
		"mediaCount":   len(mediaList),
		"productCount": len(productList),
	})

	return res, nil
}

// getMediaList 获取媒体列表
func (s *FilterService) getMediaList(ctx context.Context) ([]*v1.MediaListItem, error) {
	// 从数据库获取媒体列表
	medias, err := dao.AdMedia.Ctx(ctx).
		Where("audit_status", "approved").
		Fields("id", "name").
		Order("id DESC").
		Limit(100).
		All()

	if err != nil {
		return nil, err
	}

	// 转换为API格式
	var mediaList []*v1.MediaListItem
	for _, media := range medias {
		mediaList = append(mediaList, &v1.MediaListItem{
			Id:   media["id"].String(),
			Name: media["name"].String(),
		})
	}

	return mediaList, nil
}

// getProductList 获取产品列表
func (s *FilterService) getProductList(ctx context.Context) ([]*v1.ProductItem, error) {
	// 从数据库获取产品列表
	products, err := dao.AdProducts.Ctx(ctx).
		Where("status", "active").
		Fields("id", "name").
		Order("id DESC").
		Limit(1000).
		All()

	if err != nil {
		return nil, err
	}

	// 转换为API格式
	var productList []*v1.ProductItem
	for _, product := range products {
		productList = append(productList, &v1.ProductItem{
			Id:   product["id"].String(),
			Name: product["name"].String(),
		})
	}

	return productList, nil
}

// GetProducts 获取产品列表
func (s *FilterService) GetProducts(ctx context.Context) (*v1.GetProductsRes, error) {
	g.Log().Info(ctx, "获取产品列表开始")

	// 缓存键
	cacheKey := "dashboard:products"

	// 尝试从缓存获取
	if cached, err := s.cache.Get(ctx, cacheKey); err == nil && !cached.IsNil() {
		g.Log().Info(ctx, "从缓存获取产品列表")
		var res *v1.GetProductsRes
		if err := cached.Struct(&res); err == nil {
			return res, nil
		}
	}

	// 获取产品列表
	productList, err := s.getProductList(ctx)
	if err != nil {
		g.Log().Error(ctx, "获取产品列表失败:", err)
		return &v1.GetProductsRes{List: []*v1.ProductItem{}}, err
	}

	res := &v1.GetProductsRes{
		List: productList,
	}

	// 缓存结果 (10分钟)
	s.cache.Set(ctx, cacheKey, res, 600)

	g.Log().Info(ctx, "获取产品列表完成", g.Map{
		"count": len(productList),
	})

	return res, nil
}
