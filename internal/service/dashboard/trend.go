package dashboard

import (
	v1 "ad-pro-v2/api/dashboard/v1"
	"ad-pro-v2/internal/dao"
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcache"
)

// ITrendService 趋势服务接口
type ITrendService interface {
	GetTrendData(ctx context.Context, req *v1.GetTrendDataReq) (*v1.GetTrendDataRes, error)
}

// TrendService 趋势服务实现
type TrendService struct {
	cache *gcache.Cache
}

// NewTrendService 创建新的趋势服务实例
func NewTrendService(cache *gcache.Cache) ITrendService {
	return &TrendService{
		cache: cache,
	}
}

// GetTrendData 获取趋势数据
func (s *TrendService) GetTrendData(ctx context.Context, req *v1.GetTrendDataReq) (*v1.GetTrendDataRes, error) {
	g.Log().Info(ctx, "获取趋势数据开始", g.Map{
		"userId":    req.UserId,
		"mediaId":   req.MediaId,
		"planId":    req.PlanId,
		"productId": req.ProductId,
		"metric":    req.Metric,
		"startDate": req.StartDate,
		"endDate":   req.EndDate,
		"type":      req.Type,
		"category":  req.Category,
	})

	// 解析日期
	if req.StartDate == "" || req.EndDate == "" {
		// 默认最近7天
		now := time.Now()
		req.EndDate = now.Format("2006-01-02")
		req.StartDate = now.AddDate(0, 0, -6).Format("2006-01-02")
	}

	// 缓存键
	cacheKey := fmt.Sprintf("dashboard:trend:%s:%s:%s:%s:%s:%s:%s:%s:%s",
		req.UserId, req.MediaId, req.PlanId, req.ProductId,
		req.Metric, req.StartDate, req.EndDate, req.Type, req.Category)

	// 尝试从缓存获取
	if cached, err := s.cache.Get(ctx, cacheKey); err == nil && !cached.IsNil() {
		g.Log().Info(ctx, "从缓存获取趋势数据")
		var res *v1.GetTrendDataRes
		if err := cached.Struct(&res); err == nil {
			return res, nil
		}
	}

	// 获取当前数据
	currentData, timePoints, err := s.getTrendDataByPeriod(ctx, req, req.StartDate, req.EndDate)
	if err != nil {
		g.Log().Error(ctx, "获取当前趋势数据失败:", err)
		return nil, err
	}

	// 计算前期数据日期范围
	startTime, _ := time.Parse("2006-01-02", req.StartDate)
	endTime, _ := time.Parse("2006-01-02", req.EndDate)
	dayDiff := int(endTime.Sub(startTime).Hours() / 24)

	previousEndDate := startTime.AddDate(0, 0, -1).Format("2006-01-02")
	previousStartDate := startTime.AddDate(0, 0, -dayDiff-1).Format("2006-01-02")

	// 获取前期数据
	previousData, _, err := s.getTrendDataByPeriod(ctx, req, previousStartDate, previousEndDate)
	if err != nil {
		g.Log().Warning(ctx, "获取前期趋势数据失败:", err)
		previousData = make([]float64, len(timePoints))
	}

	// 计算同期数据日期范围 (上周同期)
	samePeriodStartDate := startTime.AddDate(0, 0, -7).Format("2006-01-02")
	samePeriodEndDate := endTime.AddDate(0, 0, -7).Format("2006-01-02")

	// 获取同期数据
	samePeriodData, _, err := s.getTrendDataByPeriod(ctx, req, samePeriodStartDate, samePeriodEndDate)
	if err != nil {
		g.Log().Warning(ctx, "获取同期趋势数据失败:", err)
		samePeriodData = make([]float64, len(timePoints))
	}

	// 构建响应
	res := &v1.GetTrendDataRes{
		TimePoints: timePoints,
		Current:    currentData,
		Previous:   previousData,
		SamePeriod: samePeriodData,
	}

	// 设置标签
	if req.StartDate == req.EndDate {
		// 单天分时数据的标签
		currentDate := req.StartDate
		previousDate := previousEndDate
		samePeriodDate := samePeriodEndDate

		res.Labels.Current = fmt.Sprintf("%s (当日)", currentDate)
		res.Labels.Previous = fmt.Sprintf("%s (前一日)", previousDate)
		res.Labels.SamePeriod = fmt.Sprintf("%s (上周同期)", samePeriodDate)
	} else {
		// 多天数据的标签
		res.Labels.Current = fmt.Sprintf("%s至%s", req.StartDate, req.EndDate)
		res.Labels.Previous = fmt.Sprintf("%s至%s", previousStartDate, previousEndDate)
		res.Labels.SamePeriod = fmt.Sprintf("%s至%s", samePeriodStartDate, samePeriodEndDate)
	}

	// 缓存结果 (5分钟)
	s.cache.Set(ctx, cacheKey, res, 300)

	g.Log().Info(ctx, "获取趋势数据完成", g.Map{
		"timePointsCount": len(timePoints),
		"currentSum":      s.sumFloat64Array(currentData),
		"previousSum":     s.sumFloat64Array(previousData),
		"samePeriodSum":   s.sumFloat64Array(samePeriodData),
	})

	return res, nil
}

// getTrendDataByPeriod 获取指定时期的趋势数据
func (s *TrendService) getTrendDataByPeriod(ctx context.Context, req *v1.GetTrendDataReq, startDate, endDate string) ([]float64, []string, error) {
	g.Log().Info(ctx, "趋势数据查询参数检查", g.Map{
		"startDate": startDate,
		"endDate":   endDate,
		"isSameDay": startDate == endDate,
	})

	// 检查是否是同一天，如果是则显示分时数据
	if startDate == endDate {
		g.Log().Info(ctx, "使用分时数据逻辑")
		return s.getHourlyDataForSingleDay(ctx, req, startDate)
	}

	// 多天数据使用原有的日统计逻辑
	g.Log().Info(ctx, "使用多天统计逻辑")
	return s.getDailyDataForMultiDays(ctx, req, startDate, endDate)
}

// getHourlyDataForSingleDay 获取单天的分时数据（优化版本）
func (s *TrendService) getHourlyDataForSingleDay(ctx context.Context, req *v1.GetTrendDataReq, date string) ([]float64, []string, error) {
	g.Log().Info(ctx, "开始获取分时数据", g.Map{
		"date":   date,
		"metric": req.Metric,
	})

	// 使用优化的订单表查询（方案一）
	return s.getHourlyDataFromOrdersTable(ctx, req, date)
}

// getHourlyDataFromOrdersTable 从订单表获取小时数据（优化版本）
func (s *TrendService) getHourlyDataFromOrdersTable(ctx context.Context, req *v1.GetTrendDataReq, date string) ([]float64, []string, error) {
	// 获取过滤后的计划ID列表，限制数量
	planIds, err := s.getFilteredPlanIds(ctx, req)
	if err != nil {
		return nil, nil, err
	}

	if len(planIds) == 0 {
		timePoints := s.generateHourlyTimePoints()
		emptyData := make([]float64, 24)
		return emptyData, timePoints, nil
	}

	// 严格限制计划数量以避免IN子句过长
	if len(planIds) > 50 {
		planIds = planIds[:50]
		g.Log().Warning(ctx, "计划数量过多，限制为前50个", g.Map{"originalCount": len(planIds)})
	}

	// 确定查询的表名
	tableName := s.getTableNameByDate(date)

	// 根据指标确定查询字段
	var fieldName string
	switch req.Metric {
	case "orders":
		fieldName = "COUNT(*)"
	case "estimated_commission":
		fieldName = "SUM(pre_commission)"
	case "settled_commission":
		fieldName = "SUM(commission)"
	case "order_amount":
		fieldName = "SUM(pay_price)"
	default:
		fieldName = "COUNT(*)"
	}

	// 使用批量查询方式，减少单次查询的复杂度
	return s.executeBatchHourlyQuery(ctx, tableName, fieldName, planIds, date)
}

// executeBatchHourlyQuery 执行批量小时查询
func (s *TrendService) executeBatchHourlyQuery(ctx context.Context, tableName, fieldName string, planIds []string, date string) ([]float64, []string, error) {
	timePoints := s.generateHourlyTimePoints()
	dataMap := make(map[int]float64)

	// 分批查询，每次查询6小时的数据
	batchSize := 6
	for i := 0; i < 24; i += batchSize {
		endHour := i + batchSize
		if endHour > 24 {
			endHour = 24
		}

		batchData, err := s.queryHourlyBatch(ctx, tableName, fieldName, planIds, date, i, endHour)
		if err != nil {
			g.Log().Error(ctx, "批量查询失败", g.Map{"startHour": i, "endHour": endHour, "error": err})
			// 继续处理其他批次，不因为一个批次失败而整体失败
			continue
		}

		// 合并结果
		for hour, value := range batchData {
			dataMap[hour] = value
		}
	}

	// 生成最终数据数组
	var data []float64
	for i := 0; i < 24; i++ {
		if value, exists := dataMap[i]; exists {
			data = append(data, value)
		} else {
			data = append(data, 0)
		}
	}

	g.Log().Info(ctx, "分时数据查询完成", g.Map{
		"totalHours":   len(data),
		"nonZeroHours": len(dataMap),
		"maxValue":     s.maxFloat64(data),
		"totalValue":   s.sumFloat64Array(data),
	})

	return data, timePoints, nil
}

// queryHourlyBatch 批量查询小时数据
func (s *TrendService) queryHourlyBatch(ctx context.Context, tableName, fieldName string, planIds []string, date string, startHour, endHour int) (map[int]float64, error) {
	// 构建时间范围
	startTime := fmt.Sprintf("%s %02d:00:00", date, startHour)
	endTime := fmt.Sprintf("%s %02d:59:59", date, endHour-1)

	// 构建计划ID的占位符
	placeholders := make([]string, len(planIds))
	args := make([]interface{}, 0, len(planIds)+2)
	args = append(args, startTime, endTime)

	for i, planId := range planIds {
		placeholders[i] = "?"
		args = append(args, planId)
	}

	// 构建优化的SQL查询 - 使用索引提示
	sqlQuery := fmt.Sprintf(`
		SELECT 
			HOUR(create_time) as hour, 
			%s as value 
		FROM %s USE INDEX (idx_plan_status_leak_time)
		WHERE create_time >= ? AND create_time <= ?
		AND order_status IN (2,3,4)
		AND leak = 0
		AND ad_plan_id IN (%s)
		GROUP BY HOUR(create_time)
		ORDER BY hour ASC
	`, fieldName, tableName, strings.Join(placeholders, ","))

	// 设置查询超时为5秒
	queryCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// 执行查询
	results, err := g.DB().GetAll(queryCtx, sqlQuery, args...)
	if err != nil {
		return nil, err
	}

	// 处理结果
	dataMap := make(map[int]float64)
	for _, result := range results {
		hour := result["hour"].Int()
		value := safeFloat64(result["value"])
		dataMap[hour] = value
	}

	return dataMap, nil
}

// getFilteredPlanIds 获取过滤后的计划ID列表
func (s *TrendService) getFilteredPlanIds(ctx context.Context, req *v1.GetTrendDataReq) ([]string, error) {
	query := dao.AdSlotPlans.Ctx(ctx)

	// 添加筛选条件
	if req.UserId != "" {
		query = query.Where("user_id", req.UserId)
	}

	if req.MediaId != "" {
		query = query.Where("media_id", req.MediaId)
	}

	if req.PlanId != "" {
		query = query.Where("id", req.PlanId)
	}

	if req.ProductId != "" {
		query = query.Where("ad_product_id", req.ProductId)
	}

	// 处理合作类型筛选
	if req.Type != "" && req.Type != "0" {
		var mediaIds []interface{}
		switch req.Type {
		case "1": // 普通投放
			values, _ := dao.AdMedia.Ctx(ctx).Where("cooperation_type", "traffic").Array("id")
			for _, v := range values {
				mediaIds = append(mediaIds, v)
			}
		case "2": // CPS合作
			values, _ := dao.AdMedia.Ctx(ctx).Where("cooperation_type", "cps").Array("id")
			for _, v := range values {
				mediaIds = append(mediaIds, v)
			}
		case "3": // 灯火
			values, _ := dao.AdMedia.Ctx(ctx).Where("cooperation_type", "dh").Array("id")
			for _, v := range values {
				mediaIds = append(mediaIds, v)
			}
		}
		if len(mediaIds) > 0 {
			query = query.Where("media_id IN (?)", mediaIds)
		}
	}

	// 处理渠道分类筛选
	if req.Category != "" {
		var slotTypes []string
		switch req.Category {
		case "alipay":
			slotTypes = []string{"alipay_h5", "alipay_mp"}
		case "wechat":
			slotTypes = []string{"wechat_h5", "wechat_mp"}
		case "other":
			slotTypes = []string{"other"}
		}

		if len(slotTypes) > 0 {
			slotIds, err := dao.AdSlots.Ctx(ctx).Where("type IN (?)", slotTypes).Array("id")
			if err == nil && len(slotIds) > 0 {
				query = query.Where("ad_slot_id IN (?)", slotIds)
			}
		}
	}

	// 限制返回数量并获取计划ID
	planIds, err := query.Limit(100).Array("id")
	if err != nil {
		return nil, err
	}

	// 转换为字符串数组
	var result []string
	for _, id := range planIds {
		result = append(result, id.String())
	}

	return result, nil
}

// getTableNameByDate 根据日期获取表名
func (s *TrendService) getTableNameByDate(date string) string {
	// 解析日期
	t, err := time.Parse("2006-01-02", date)
	if err != nil {
		return "ad_orders" // 默认表
	}

	// 生成分表名
	return fmt.Sprintf("ad_orders_%s", t.Format("200601"))
}

// getDailyDataForMultiDays 获取多天的日统计数据（原有逻辑）
func (s *TrendService) getDailyDataForMultiDays(ctx context.Context, req *v1.GetTrendDataReq, startDate, endDate string) ([]float64, []string, error) {
	// 构建查询条件
	query := dao.AdSlotPlanDailyStats.Ctx(ctx)
	query = query.Where("date BETWEEN ? AND ?", startDate, endDate)

	// 添加筛选条件
	if req.UserId != "" {
		query = query.Where("user_id", req.UserId)
	}
	if req.MediaId != "" {
		query = query.Where("media_id", req.MediaId)
	}
	if req.PlanId != "" {
		query = query.Where("plan_id", req.PlanId)
	}
	if req.ProductId != "" {
		query = query.Where("ad_product_id", req.ProductId)
	}

	// 根据类型筛选
	if req.Type != "" && req.Type != "0" {
		switch req.Type {
		case "1": // 普通投放
			values, err := dao.AdMedia.Ctx(ctx).Where("cooperation_type", "traffic").Array("id")
			if err == nil {
				query = query.WhereIn("media_id", values)
			}
		case "2": // CPS合作
			values, err := dao.AdMedia.Ctx(ctx).Where("cooperation_type", "cps").Array("id")
			if err == nil {
				query = query.WhereIn("media_id", values)
			}
		case "3": // 灯火
			values, err := dao.AdMedia.Ctx(ctx).Where("cooperation_type", "dh").Array("id")
			if err == nil {
				query = query.WhereIn("media_id", values)
			}
		}
	}

	// 根据category筛选
	if req.Category != "" {
		switch req.Category {
		case "alipay":
			// 通过关联ad_slots表的type字段筛选支付宝渠道
			slotIds, err := dao.AdSlots.Ctx(ctx).Where("type IN (?)", []string{"alipay_h5", "alipay_mp"}).Array("id")
			if err == nil {
				query = query.WhereIn("ad_slot_id", slotIds)
			}
		case "wechat":
			// 通过关联ad_slots表的type字段筛选微信渠道
			slotIds, err := dao.AdSlots.Ctx(ctx).Where("type IN (?)", []string{"wechat_h5", "wechat_mp"}).Array("id")
			if err == nil {
				query = query.WhereIn("ad_slot_id", slotIds)
			}
		case "other":
			// 其他渠道
			slotIds, err := dao.AdSlots.Ctx(ctx).Where("type", "other").Array("id")
			if err == nil {
				query = query.WhereIn("ad_slot_id", slotIds)
			}
		}
	}

	// 根据指标类型选择字段
	var fieldName string
	switch req.Metric {
	case "click_pv":
		fieldName = "elm_click_pv"
	case "click_uv":
		fieldName = "elm_click_uv"
	case "orders":
		fieldName = "bd_orders"
	case "estimated_commission":
		fieldName = "bd_revenue"
	case "settled_commission":
		fieldName = "bd_settle_revenue"
	case "cost":
		fieldName = "cost"
	case "estimated_profit":
		fieldName = "bd_profit"
	case "settled_profit":
		fieldName = "bd_settle_profit"
	default:
		fieldName = "bd_orders" // 默认订单量
	}

	// 按日期分组查询
	results, err := query.
		Fields(fmt.Sprintf("date, SUM(%s) as value", fieldName)).
		Group("date").
		Order("date ASC").
		All()

	if err != nil {
		return nil, nil, err
	}

	// 生成完整的时间点列表
	timePoints := s.generateTimePoints(startDate, endDate)
	dataMap := make(map[string]float64)

	// 填充查询结果到map
	for _, result := range results {
		date := result["date"].String()
		value := result["value"].Float64()
		dataMap[date] = value
	}

	// 按时间点顺序生成数据数组
	var data []float64
	for _, timePoint := range timePoints {
		if value, exists := dataMap[timePoint]; exists {
			data = append(data, value)
		} else {
			data = append(data, 0) // 没有数据的日期补0
		}
	}

	return data, timePoints, nil
}

// generateTimePoints 生成时间点列表
func (s *TrendService) generateTimePoints(startDate, endDate string) []string {
	var timePoints []string

	start, _ := time.Parse("2006-01-02", startDate)
	end, _ := time.Parse("2006-01-02", endDate)

	current := start
	for !current.After(end) {
		timePoints = append(timePoints, current.Format("2006-01-02"))
		current = current.AddDate(0, 0, 1)
	}

	return timePoints
}

// generateHourlyTimePoints 生成24小时时间点列表
func (s *TrendService) generateHourlyTimePoints() []string {
	var timePoints []string
	for i := 0; i < 24; i++ {
		timePoints = append(timePoints, fmt.Sprintf("%02d:00", i))
	}
	return timePoints
}

// sumFloat64Array 计算float64数组的和
func (s *TrendService) sumFloat64Array(arr []float64) float64 {
	sum := 0.0
	for _, v := range arr {
		sum += v
	}
	return sum
}

// maxFloat64 计算float64数组的最大值
func (s *TrendService) maxFloat64(arr []float64) float64 {
	max := arr[0]
	for _, v := range arr {
		if v > max {
			max = v
		}
	}
	return max
}

// checkTableExists 检查表是否存在
func (s *TrendService) checkTableExists(ctx context.Context, tableName string) (bool, error) {
	sql := "SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ? LIMIT 1"
	result, err := g.DB().GetOne(ctx, sql, tableName)
	if err != nil {
		return false, err
	}
	return !result.IsEmpty(), nil
}
