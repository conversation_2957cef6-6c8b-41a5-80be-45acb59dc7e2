package dashboard

import (
	v1 "ad-pro-v2/api/dashboard/v1"
	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/service"
	"context"
	"time"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcache"
)

// IMetricsService 指标服务接口
type IMetricsService interface {
	GetMetrics(ctx context.Context, req *v1.GetMetricsReq) (*v1.GetMetricsRes, error)
}

// MetricsService 指标服务实现
type MetricsService struct {
	cache *gcache.Cache
}

// NewMetricsService 创建新的指标服务实例
func NewMetricsService(cache *gcache.Cache) IMetricsService {
	return &MetricsService{
		cache: cache,
	}
}

// GetMetrics 获取指标数据
func (s *MetricsService) GetMetrics(ctx context.Context, req *v1.GetMetricsReq) (*v1.GetMetricsRes, error) {
	// 如果没有指定日期,使用当天
	if req.StartDate == "" || req.EndDate == "" {
		today := time.Now().Format("2006-01-02")
		if req.StartDate == "" {
			req.StartDate = today
		}
		if req.EndDate == "" {
			req.EndDate = today
		}
	}

	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)

	// 获取每日统计数据
	dailyStats, err := s.getDailyStats(ctx, userInfo.Id, req)
	if err != nil {
		g.Log().Error(ctx, "Failed to get daily stats:", err)
		// 使用默认值
		dailyStats = map[string]interface{}{
			"click_pv":          0,
			"click_uv":          0,
			"orders":            0,
			"estimated_revenue": 0.0,
			"settled_revenue":   0.0,
			"cost":              0.0,
			"estimated_profit":  0.0,
			"settled_profit":    0.0,
		}
	}

	// 计算指标数据
	metrics := s.calculateMetrics(dailyStats, req)

	// 计算环比和同比变化
	metrics = s.calculateChanges(ctx, metrics, req)

	// 构建响应
	res := &v1.GetMetricsRes{
		Metrics: metrics,
	}

	return res, nil
}

// getDailyStats 获取每日统计数据
func (s *MetricsService) getDailyStats(ctx context.Context, userId int, req *v1.GetMetricsReq) (map[string]interface{}, error) {
	// 构建查询条件
	query := dao.AdSlotPlanDailyStats.Ctx(ctx)
	query = query.Where("date BETWEEN ? AND ?", req.StartDate, req.EndDate)

	var queryUserId int
	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)
	if req.UserId > 0 {
		queryUserId = req.UserId
	} else {
		if userInfo.Role == 2 || userInfo.Role == 3 {
			queryUserId = 0
		} else {
			queryUserId = userInfo.Id
		}
	}
	if queryUserId > 0 {
		query = query.Where("user_id", queryUserId)
	}

	// 根据类型，添加筛选条件
	if req.Type == "1" {
		values, err := dao.AdMedia.Ctx(ctx).Where("cooperation_type", "traffic").Array("id")
		if err == nil {
			mediaIds := make([]int, len(values))
			for i, v := range values {
				mediaIds[i] = v.Int()
			}
			query = query.WhereIn("media_id", mediaIds)
		}
	} else if req.Type == "2" {
		values, err := dao.AdMedia.Ctx(ctx).Where("cooperation_type", "cps").Array("id")
		if err == nil {
			mediaIds := make([]int, len(values))
			for i, v := range values {
				mediaIds[i] = v.Int()
			}
			query = query.WhereIn("media_id", mediaIds)
		}
	} else if req.Type == "3" {
		values, err := dao.AdMedia.Ctx(ctx).Where("cooperation_type", "dh").Array("id")
		if err == nil {
			mediaIds := make([]int, len(values))
			for i, v := range values {
				mediaIds[i] = v.Int()
			}
			query = query.WhereIn("media_id", mediaIds)
		}
	}

	// 添加筛选条件
	if req.PlanId != "" {
		query = query.Where("plan_id", req.PlanId)
	}
	if req.MediaId != "" {
		query = query.Where("media_id", req.MediaId)
	}

	// 根据category筛选
	if req.Category != "" {
		switch req.Category {
		case "alipay":
			// 通过关联ad_slots表的type字段筛选支付宝渠道
			slotIds, err := dao.AdSlots.Ctx(ctx).Where("type IN (?)", []string{"alipay_h5", "alipay_mp"}).Array("id")
			if err == nil {
				query = query.WhereIn("ad_slot_id", slotIds)
			}
		case "wechat":
			// 通过关联ad_slots表的type字段筛选微信渠道
			slotIds, err := dao.AdSlots.Ctx(ctx).Where("type IN (?)", []string{"wechat_h5", "wechat_mp", "wechat_plugin"}).Array("id")
			if err == nil {
				query = query.WhereIn("ad_slot_id", slotIds)
			}
		case "other":
			// 其他渠道
			slotIds, err := dao.AdSlots.Ctx(ctx).Where("type", "other").Array("id")
			if err == nil {
				query = query.WhereIn("ad_slot_id", slotIds)
			}
		}
	}

	// 汇总数据
	type SummaryData struct {
		Orders           int     `json:"orders"`
		EstimatedRevenue float64 `json:"estimated_revenue"`
		SettledRevenue   float64 `json:"settled_revenue"`
		Cost             float64 `json:"cost"`
		EstimatedProfit  float64 `json:"estimated_profit"`
		SettledProfit    float64 `json:"settled_profit"`
		ElmClickPv       int     `json:"elm_click_pv"`
		ElmClickUv       int     `json:"elm_click_uv"`
	}

	// 声明结果变量
	var result SummaryData
	var err error
	err = query.Fields(`
		SUM(bd_orders) as orders,
		SUM(bd_revenue) as estimated_revenue,
		SUM(bd_settle_revenue) as settled_revenue,
		SUM(cost) as cost,
		SUM(bd_profit) as estimated_profit,
		SUM(bd_settle_profit) as settled_profit,
		SUM(elm_click_pv) as elm_click_pv,
		SUM(elm_click_uv) as elm_click_uv
	`).Scan(&result)

	if err != nil {
		return nil, gerror.WrapCode(gcode.CodeInternalError, err, "查询指标数据失败")
	}

	return map[string]interface{}{
		"orders":            result.Orders,
		"estimated_revenue": result.EstimatedRevenue,
		"settled_revenue":   result.SettledRevenue,
		"cost":              result.Cost,
		"estimated_profit":  result.EstimatedProfit,
		"settled_profit":    result.SettledProfit,
		"elm_click_pv":      result.ElmClickPv,
		"elm_click_uv":      result.ElmClickUv,
	}, nil
}

// calculateMetrics 计算指标数据
func (s *MetricsService) calculateMetrics(dailyStats map[string]interface{}, req *v1.GetMetricsReq) map[string]*v1.MetricItem {
	metrics := make(map[string]*v1.MetricItem)

	// 判断是否是同一天
	isSameDay := req.StartDate == req.EndDate

	// 判断是否是CPS合作类型
	isCpsType := req.Type == "2"

	// 页面访问次数 - 添加nil检查
	clickPv := 0
	if v, ok := dailyStats["elm_click_pv"]; ok && v != nil {
		if intValue, ok := v.(int); ok {
			clickPv = intValue
		}
	}
	metrics["click_pv"] = &v1.MetricItem{
		Label:    "页面访问次数",
		Value:    float64(clickPv),
		HasTrend: true,
	}

	// 页面访问人数 - 添加nil检查
	clickUv := 0
	if v, ok := dailyStats["elm_click_uv"]; ok && v != nil {
		if intValue, ok := v.(int); ok {
			clickUv = intValue
		}
	}
	metrics["click_uv"] = &v1.MetricItem{
		Label:    "页面访问人数",
		Value:    float64(clickUv),
		HasTrend: true,
	}

	// 订单量 - 添加nil检查
	orders := 0
	if v, ok := dailyStats["orders"]; ok && v != nil {
		if intValue, ok := v.(int); ok {
			orders = intValue
		}
	}
	metrics["orders"] = &v1.MetricItem{
		Label:    "订单量",
		Value:    float64(orders),
		HasTrend: true,
	}

	// 预估佣金 - 添加nil检查
	estimatedRevenue := 0.0
	if v, ok := dailyStats["estimated_revenue"]; ok && v != nil {
		if floatValue, ok := v.(float64); ok {
			estimatedRevenue = floatValue
		}
	}
	metrics["estimated_commission"] = &v1.MetricItem{
		Label:    "预估佣金",
		Value:    estimatedRevenue,
		HasTrend: !isSameDay, // 同一天时不支持趋势
	}

	// 结算佣金 - 添加nil检查
	settledRevenue := 0.0
	if v, ok := dailyStats["settled_revenue"]; ok && v != nil {
		if floatValue, ok := v.(float64); ok {
			settledRevenue = floatValue
		}
	}
	metrics["settled_commission"] = &v1.MetricItem{
		Label:    "结算佣金",
		Value:    settledRevenue,
		HasTrend: !isSameDay, // 同一天时不支持趋势
	}

	// 如果不是CPS合作类型且不是同一天,显示其他指标
	if !isCpsType && !isSameDay {
		// 成本
		cost := 0.0
		if v, ok := dailyStats["cost"]; ok && v != nil {
			if floatValue, ok := v.(float64); ok {
				cost = floatValue
			}
		}
		metrics["cost"] = &v1.MetricItem{
			Label:    "成本",
			Value:    cost,
			HasTrend: false,
		}

		// 预估利润
		estimatedProfit := 0.0
		if v, ok := dailyStats["estimated_profit"]; ok && v != nil {
			if floatValue, ok := v.(float64); ok {
				estimatedProfit = floatValue
			}
		}
		metrics["estimated_profit"] = &v1.MetricItem{
			Label:    "预估利润",
			Value:    estimatedProfit,
			HasTrend: false,
		}

		// 结算利润
		settledProfit := 0.0
		if v, ok := dailyStats["settled_profit"]; ok && v != nil {
			if floatValue, ok := v.(float64); ok {
				settledProfit = floatValue
			}
		}
		metrics["settled_profit"] = &v1.MetricItem{
			Label:    "结算利润",
			Value:    settledProfit,
			HasTrend: false,
		}
	}

	return metrics
}

// calculateChanges 计算环比和同比变化
func (s *MetricsService) calculateChanges(ctx context.Context, metrics map[string]*v1.MetricItem, req *v1.GetMetricsReq) map[string]*v1.MetricItem {
	// 计算日期差
	start, _ := time.Parse("2006-01-02", req.StartDate)
	end, _ := time.Parse("2006-01-02", req.EndDate)
	dateDiff := int(end.Sub(start).Hours() / 24)

	g.Log().Info(ctx, "Date calculation:", map[string]interface{}{
		"startDate": req.StartDate,
		"endDate":   req.EndDate,
		"dateDiff":  dateDiff,
		"isSameDay": start.Equal(end),
	})

	// 计算上个周期的日期范围
	previousEnd := start.AddDate(0, 0, -1)
	previousStart := previousEnd.AddDate(0, 0, -dateDiff)

	g.Log().Info(ctx, "Previous period calculation:", map[string]interface{}{
		"previousStart": previousStart.Format("2006-01-02"),
		"previousEnd":   previousEnd.Format("2006-01-02"),
	})

	// 获取上期数据
	previousMetrics, err := s.getStatsByDateRange(ctx, previousStart.Format("2006-01-02"), previousEnd.Format("2006-01-02"), req)
	if err != nil {
		g.Log().Error(ctx, "Failed to get previous metrics:", err)
		return metrics
	}

	g.Log().Info(ctx, "Previous metrics data:", previousMetrics)

	// 判断是否是单天数据
	isSingleDay := req.StartDate == req.EndDate
	g.Log().Info(ctx, "Is single day data:", isSingleDay)

	// 如果是单天数据，获取上周同期数据
	var lastWeekMetrics map[string]*v1.MetricItem
	if isSingleDay {
		lastWeekStart := start.AddDate(0, 0, -7)
		lastWeekEnd := end.AddDate(0, 0, -7)

		g.Log().Info(ctx, "Last week period:", map[string]interface{}{
			"lastWeekStart": lastWeekStart.Format("2006-01-02"),
			"lastWeekEnd":   lastWeekEnd.Format("2006-01-02"),
		})

		lastWeekMetrics, err = s.getStatsByDateRange(ctx, lastWeekStart.Format("2006-01-02"), lastWeekEnd.Format("2006-01-02"), req)
		if err != nil {
			g.Log().Error(ctx, "Failed to get last week metrics:", err)
			return metrics
		}

		g.Log().Info(ctx, "Last week metrics data:", lastWeekMetrics)
	}

	// 计算变化率
	for key, metric := range metrics {
		currentValue := metric.Value
		g.Log().Info(ctx, "Processing metric:", map[string]interface{}{
			"key":          key,
			"currentValue": currentValue,
		})

		// 计算环比变化
		if previousMetric, ok := previousMetrics[key]; ok && previousMetric != nil {
			g.Log().Info(ctx, "Calculating change:", map[string]interface{}{
				"key":           key,
				"currentValue":  currentValue,
				"previousValue": previousMetric.Value,
			})
			metric.Change = s.calculateChange(currentValue, previousMetric.Value)
			g.Log().Info(ctx, "Change result:", map[string]interface{}{
				"key":    key,
				"change": metric.Change,
			})
		} else {
			g.Log().Warning(ctx, "No previous data for metric:", key)
		}

		// 如果是单天数据，计算周同比变化
		if isSingleDay {
			if lastWeekMetric, ok := lastWeekMetrics[key]; ok && lastWeekMetric != nil {
				g.Log().Info(ctx, "Calculating week change:", map[string]interface{}{
					"key":           key,
					"currentValue":  currentValue,
					"lastWeekValue": lastWeekMetric.Value,
				})
				metric.WeekChange = s.calculateChange(currentValue, lastWeekMetric.Value)
				g.Log().Info(ctx, "Week change result:", map[string]interface{}{
					"key":        key,
					"weekChange": metric.WeekChange,
				})
			} else {
				g.Log().Warning(ctx, "No last week data for metric:", key)
			}
		}
	}

	return metrics
}

// calculateChange 计算变化率
func (s *MetricsService) calculateChange(current float64, previous float64) float64 {
	g.Log().Info(nil, "Calculating change - Current:", current, "Previous:", previous)
	if previous == 0 {
		if current > 0 {
			g.Log().Info(nil, "Previous is 0, current > 0, returning 1")
			return 1 // 从0增长到有值，返回100%增长
		}
		g.Log().Info(nil, "Previous is 0, current is 0, returning 0")
		return 0 // 0/0 的情况，返回0
	}
	result := (current - previous) / previous
	g.Log().Info(nil, "Change calculated:", result)
	return result
}

// getStatsByDateRange 获取指定日期范围的统计数据
func (s *MetricsService) getStatsByDateRange(ctx context.Context, startDate string, endDate string, req *v1.GetMetricsReq) (map[string]*v1.MetricItem, error) {
	customReq := &v1.GetMetricsReq{
		StartDate: startDate,
		EndDate:   endDate,
		MediaId:   req.MediaId,
		PlanId:    req.PlanId,
		ProductId: req.ProductId,
		UserId:    req.UserId,
		Type:      req.Type,
		Category:  req.Category,
	}

	// 获取当前用户信息
	userInfo := service.Context().GetUser(ctx)

	// 初始化默认每日统计数据
	dailyStats := map[string]interface{}{
		"click_pv":          0,
		"click_uv":          0,
		"orders":            0,
		"estimated_revenue": 0.0,
		"settled_revenue":   0.0,
		"cost":              0.0,
		"estimated_profit":  0.0,
		"settled_profit":    0.0,
	}

	// 获取每日统计数据
	dailyStatsResult, err := s.getDailyStats(ctx, userInfo.Id, customReq)
	if err == nil && dailyStatsResult != nil {
		dailyStats = dailyStatsResult
	} else {
		g.Log().Warning(ctx, "Using default daily stats due to error:", err)
	}

	// 计算指标数据
	metrics := s.calculateMetrics(dailyStats, req)

	g.Log().Info(ctx, "Calculated metrics for date range:", map[string]interface{}{
		"startDate": startDate,
		"endDate":   endDate,
		"metrics":   metrics,
	})

	return metrics, nil
}
