package dashboard

import (
	v1 "ad-pro-v2/api/dashboard/v1"
	"ad-pro-v2/internal/dao"
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcache"
)

// IStatsService 统计服务接口
type IStatsService interface {
	GetRegionData(ctx context.Context, req *v1.GetRegionDataReq) (*v1.GetRegionDataRes, error)
	GetOrderTypeData(ctx context.Context, req *v1.GetOrderTypeDataReq) (*v1.GetOrderTypeDataRes, error)
	GetPlanStats(ctx context.Context, req *v1.GetPlanStatsReq) (*v1.GetPlanStatsRes, error)
	GetFilteredPlanIds(ctx context.Context, planId, mediaId, productId, userId string, cooperationType string, category string) ([]string, error)
}

// StatsService 统计服务实现
type StatsService struct {
	cache *gcache.Cache
}

// NewStatsService 创建新的统计服务实例
func NewStatsService(cache *gcache.Cache) IStatsService {
	return &StatsService{
		cache: cache,
	}
}

// GetRegionData 获取区域数据
func (s *StatsService) GetRegionData(ctx context.Context, req *v1.GetRegionDataReq) (*v1.GetRegionDataRes, error) {
	g.Log().Info(ctx, "获取区域数据开始", g.Map{
		"metric":    req.Metric,
		"startDate": req.StartDate,
		"endDate":   req.EndDate,
		"userId":    req.UserId,
		"mediaId":   req.MediaId,
		"planId":    req.PlanId,
		"productId": req.ProductId,
		"type":      req.Type,
		"category":  req.Category,
	})

	// 设置默认日期
	if req.StartDate == "" || req.EndDate == "" {
		now := time.Now()
		req.EndDate = now.Format("2006-01-02")
		req.StartDate = now.AddDate(0, 0, -6).Format("2006-01-02")
	}

	// 获取过滤后的计划ID列表
	planIds, err := s.GetFilteredPlanIds(ctx, req.PlanId, req.MediaId, req.ProductId, req.UserId, req.Type, req.Category)
	if err != nil {
		g.Log().Error(ctx, "获取过滤计划ID失败:", err)
		return &v1.GetRegionDataRes{Regions: []v1.RegionData{}}, err
	}

	if len(planIds) == 0 {
		g.Log().Info(ctx, "没有找到符合条件的计划")
		return &v1.GetRegionDataRes{Regions: []v1.RegionData{}}, nil
	}

	// 根据指标选择字段和表
	var fieldName string
	var tableName string
	var timeField string

	switch req.Metric {
	case "orders":
		fieldName = "COUNT(*)"
		tableName = "ad_orders"
		timeField = "create_time"
	case "estimated_commission":
		fieldName = "SUM(pre_commission)"
		tableName = "ad_orders"
		timeField = "create_time"
	case "settled_commission":
		fieldName = "SUM(commission)"
		tableName = "ad_orders"
		timeField = "receive_time"
	case "order_amount":
		fieldName = "SUM(pay_price)"
		tableName = "ad_orders"
		timeField = "create_time"
	default:
		fieldName = "COUNT(*)"
		tableName = "ad_orders"
		timeField = "create_time"
	}

	// 构建查询
	query := g.DB().Model(tableName).Ctx(ctx)

	// 添加基础条件
	query = query.Where("ad_plan_id IN(?)", planIds)
	query = query.Where("leak = 0")
	query = query.Where("order_status IN(2,3,4)")
	query = query.Where(fmt.Sprintf("DATE(%s) BETWEEN ? AND ?", timeField), req.StartDate, req.EndDate)

	// 按城市分组查询
	results, err := query.
		Fields(fmt.Sprintf("COALESCE(NULLIF(city, ''), '未知') as city, %s as value", fieldName)).
		Group("city").
		Having("value > 0").
		Order("value DESC").
		Limit(50).
		All()

	if err != nil {
		g.Log().Error(ctx, "查询区域数据失败:", err)
		return &v1.GetRegionDataRes{Regions: []v1.RegionData{}}, err
	}

	// 转换结果
	var regions []v1.RegionData
	for _, result := range results {
		cityName := result["city"].String()
		value := safeFloat64(result["value"])

		if cityName != "" && value > 0 {
			regions = append(regions, v1.RegionData{
				Name:  cityName,
				Value: value,
			})
		}
	}

	res := &v1.GetRegionDataRes{
		Regions: regions,
	}

	g.Log().Info(ctx, "获取区域数据完成", g.Map{
		"count": len(regions),
	})

	return res, nil
}

// GetOrderTypeData 获取订单类型数据
func (s *StatsService) GetOrderTypeData(ctx context.Context, req *v1.GetOrderTypeDataReq) (*v1.GetOrderTypeDataRes, error) {
	g.Log().Info(ctx, "获取订单类型数据开始", g.Map{
		"metric":    req.Metric,
		"startDate": req.StartDate,
		"endDate":   req.EndDate,
	})

	// 设置默认日期
	if req.StartDate == "" || req.EndDate == "" {
		now := time.Now()
		req.EndDate = now.Format("2006-01-02")
		req.StartDate = now.AddDate(0, 0, -6).Format("2006-01-02")
	}

	// 构建查询条件
	query := dao.AdSlotPlanDailyStats.Ctx(ctx)
	query = query.Where("date BETWEEN ? AND ?", req.StartDate, req.EndDate)

	// 根据指标选择字段
	fieldName := s.getFieldNameByMetric(req.Metric)

	// 按订单类型分组查询
	results, err := query.
		Fields(fmt.Sprintf("COALESCE(order_type, '普通订单') as order_type, SUM(%s) as value", fieldName)).
		Group("order_type").
		Order("value DESC").
		All()

	if err != nil {
		g.Log().Error(ctx, "查询订单类型数据失败:", err)
		// 返回默认数据
		return &v1.GetOrderTypeDataRes{
			OrderTypes: []v1.OrderTypeData{
				{Name: "普通订单", Value: 0},
				{Name: "优惠订单", Value: 0},
				{Name: "活动订单", Value: 0},
			},
		}, nil
	}

	// 转换结果
	var types []v1.OrderTypeData
	for _, result := range results {
		types = append(types, v1.OrderTypeData{
			Name:  result["order_type"].String(),
			Value: result["value"].Float64(),
		})
	}

	// 如果没有数据，返回默认类型
	if len(types) == 0 {
		types = []v1.OrderTypeData{
			{Name: "普通订单", Value: 0},
			{Name: "优惠订单", Value: 0},
			{Name: "活动订单", Value: 0},
		}
	}

	res := &v1.GetOrderTypeDataRes{
		OrderTypes: types,
	}

	g.Log().Info(ctx, "获取订单类型数据完成", g.Map{
		"count": len(types),
	})

	return res, nil
}

// GetPlanStats 获取分计划数据
func (s *StatsService) GetPlanStats(ctx context.Context, req *v1.GetPlanStatsReq) (*v1.GetPlanStatsRes, error) {
	g.Log().Info(ctx, "获取分计划数据开始", g.Map{
		"startDate": req.StartDate,
		"endDate":   req.EndDate,
		"userId":    req.UserId,
		"mediaId":   req.MediaId,
		"planId":    req.PlanId,
		"productId": req.ProductId,
		"type":      req.Type,
		"category":  req.Category,
	})

	// 缓存键
	cacheKey := fmt.Sprintf("dashboard:plan_stats:%s:%s:%s:%s:%s:%s:%s:%s",
		req.StartDate, req.EndDate, req.UserId, req.MediaId,
		req.PlanId, req.ProductId, req.Type, req.Category)

	// 尝试从缓存获取
	if cached, err := s.cache.Get(ctx, cacheKey); err == nil && !cached.IsNil() {
		g.Log().Info(ctx, "从缓存获取分计划数据")
		var res *v1.GetPlanStatsRes
		if err := cached.Struct(&res); err == nil {
			return res, nil
		}
	}

	// 构建查询条件
	query := dao.AdSlotPlanDailyStats.Ctx(ctx)
	query = query.Where("date BETWEEN ? AND ?", req.StartDate, req.EndDate)

	// 添加筛选条件
	query = s.applyCommonFilters(query, req.UserId, req.MediaId, req.PlanId, req.ProductId, req.Type, req.Category)

	// 联表查询获取计划信息
	results, err := query.
		LeftJoin("ad_slot_plans p", "p.id = ad_slot_plan_daily_stats.plan_id").
		LeftJoin("ad_media m", "m.id = ad_slot_plan_daily_stats.media_id").
		Fields(`
			ad_slot_plan_daily_stats.plan_id,
			p.code as plan_code,
			m.name as media_name,
			ad_slot_plan_daily_stats.ad_product_id,
			SUM(ad_slot_plan_daily_stats.elm_click_pv) as clicks,
			SUM(ad_slot_plan_daily_stats.cost) as cost,
			SUM(ad_slot_plan_daily_stats.bd_orders) as orders,
			SUM(ad_slot_plan_daily_stats.bd_revenue) as revenue,
			SUM(ad_slot_plan_daily_stats.bd_profit) as profit,
			SUM(ad_slot_plan_daily_stats.bd_settle_profit) as settled_profit,
			SUM(ad_slot_plan_daily_stats.elm_click_pv) as pv,
			SUM(ad_slot_plan_daily_stats.elm_click_uv) as uv
		`).
		Group("ad_slot_plan_daily_stats.plan_id, ad_slot_plan_daily_stats.ad_product_id").
		Order("revenue DESC").
		Limit(1000).
		All()

	if err != nil {
		g.Log().Error(ctx, "查询分计划数据失败:", err)
		return &v1.GetPlanStatsRes{List: []*v1.PlanStatsItem{}}, err
	}

	// 转换结果
	var planStats []*v1.PlanStatsItem
	for _, result := range results {
		revenue := result["revenue"].Float64()
		cost := result["cost"].Float64()
		roi := 0.0
		if cost > 0 {
			roi = revenue / cost
		}

		planStats = append(planStats, &v1.PlanStatsItem{
			PlanId:        result["plan_id"].String(),
			PlanCode:      result["plan_code"].String(),
			MediaName:     result["media_name"].String(),
			ProductId:     result["ad_product_id"].String(),
			Clicks:        result["clicks"].Int(),
			Cost:          cost,
			Orders:        result["orders"].Int(),
			Revenue:       revenue,
			Profit:        result["profit"].Float64(),
			Roi:           roi,
			SettledProfit: result["settled_profit"].Float64(),
			Pv:            result["pv"].Int(),
			Uv:            result["uv"].Int(),
		})
	}

	res := &v1.GetPlanStatsRes{
		List: planStats,
	}

	// 缓存结果 (5分钟)
	s.cache.Set(ctx, cacheKey, res, 300)

	g.Log().Info(ctx, "获取分计划数据完成", g.Map{
		"count": len(planStats),
	})

	return res, nil
}

// GetFilteredPlanIds 获取过滤后的计划ID列表
func (s *StatsService) GetFilteredPlanIds(ctx context.Context, planId, mediaId, productId, userId string, cooperationType string, category string) ([]string, error) {
	g.Log().Info(ctx, "获取过滤后的计划ID列表开始", g.Map{
		"planId":          planId,
		"mediaId":         mediaId,
		"productId":       productId,
		"userId":          userId,
		"cooperationType": cooperationType,
		"category":        category,
	})

	// 构建查询条件
	query := dao.AdSlotPlans.Ctx(ctx)

	// 添加筛选条件
	if planId != "" {
		query = query.Where("id", planId)
	}
	if mediaId != "" {
		query = query.Where("media_id", mediaId)
	}
	if productId != "" {
		query = query.Where("ad_product_id", productId)
	}
	if userId != "" {
		query = query.Where("user_id", userId)
	}

	// 根据合作类型筛选
	if cooperationType != "" {
		// 需要联表查询媒体表
		query = query.LeftJoin("ad_media m", "m.id = ad_slot_plans.media_id")
		query = query.Where("m.cooperation_type", cooperationType)
	}

	// 根据category筛选
	if category != "" {
		switch category {
		case "alipay":
			// 通过关联ad_slots表的type字段筛选支付宝渠道
			slotIds, err := dao.AdSlots.Ctx(context.Background()).Where("type IN (?)", []string{"alipay_h5", "alipay_mp"}).Array("id")
			if err == nil {
				query = query.WhereIn("ad_slot_id", slotIds)
			}
		case "wechat":
			// 通过关联ad_slots表的type字段筛选微信渠道
			slotIds, err := dao.AdSlots.Ctx(context.Background()).Where("type IN (?)", []string{"wechat_h5", "wechat_mp"}).Array("id")
			if err == nil {
				query = query.WhereIn("ad_slot_id", slotIds)
			}
		case "other":
			// 其他渠道
			slotIds, err := dao.AdSlots.Ctx(context.Background()).Where("type", "other").Array("id")
			if err == nil {
				query = query.WhereIn("ad_slot_id", slotIds)
			}
		}
	}

	// 只获取投放中的计划
	query = query.Where("delivery_status", "running")

	// 执行查询
	results, err := query.Fields("id").All()
	if err != nil {
		g.Log().Error(ctx, "查询计划ID失败:", err)
		return []string{}, err
	}

	// 转换结果
	var planIds []string
	for _, result := range results {
		planIds = append(planIds, result["id"].String())
	}

	g.Log().Info(ctx, "获取过滤后的计划ID列表完成", g.Map{
		"count": len(planIds),
	})

	return planIds, nil
}

// applyCommonFilters 应用通用筛选条件
func (s *StatsService) applyCommonFilters(query *gdb.Model, userId, mediaId, planId, productId, filterType, category string) *gdb.Model {
	if userId != "" {
		query = query.Where("user_id", userId)
	}
	if mediaId != "" {
		query = query.Where("media_id", mediaId)
	}
	if planId != "" {
		query = query.Where("plan_id", planId)
	}
	if productId != "" {
		query = query.Where("ad_product_id", productId)
	}

	// 根据类型筛选
	if filterType != "" {
		switch filterType {
		case "1": // 普通投放
			values, err := dao.AdMedia.Ctx(context.Background()).Where("cooperation_type", "traffic").Array("id")
			if err == nil {
				query = query.WhereIn("media_id", values)
			}
		case "2": // CPS合作
			values, err := dao.AdMedia.Ctx(context.Background()).Where("cooperation_type", "cps").Array("id")
			if err == nil {
				query = query.WhereIn("media_id", values)
			}
		case "3": // 灯火
			values, err := dao.AdMedia.Ctx(context.Background()).Where("cooperation_type", "dh").Array("id")
			if err == nil {
				query = query.WhereIn("media_id", values)
			}
		}
	}

	// 根据category筛选
	if category != "" {
		switch category {
		case "alipay":
			// 通过关联ad_slots表的type字段筛选支付宝渠道
			slotIds, err := dao.AdSlots.Ctx(context.Background()).Where("type IN (?)", []string{"alipay_h5", "alipay_mp"}).Array("id")
			if err == nil {
				query = query.WhereIn("ad_slot_id", slotIds)
			}
		case "wechat":
			// 通过关联ad_slots表的type字段筛选微信渠道
			slotIds, err := dao.AdSlots.Ctx(context.Background()).Where("type IN (?)", []string{"wechat_h5", "wechat_mp"}).Array("id")
			if err == nil {
				query = query.WhereIn("ad_slot_id", slotIds)
			}
		case "other":
			// 其他渠道
			slotIds, err := dao.AdSlots.Ctx(context.Background()).Where("type", "other").Array("id")
			if err == nil {
				query = query.WhereIn("ad_slot_id", slotIds)
			}
		}
	}

	return query
}

// getFieldNameByMetric 根据指标名称获取字段名
func (s *StatsService) getFieldNameByMetric(metric string) string {
	switch metric {
	case "click_pv":
		return "elm_click_pv"
	case "click_uv":
		return "elm_click_uv"
	case "orders":
		return "bd_orders"
	case "estimated_commission":
		return "bd_revenue"
	case "settled_commission":
		return "bd_settle_revenue"
	case "cost":
		return "cost"
	case "estimated_profit":
		return "bd_profit"
	case "settled_profit":
		return "bd_settle_profit"
	default:
		return "bd_orders" // 默认订单量
	}
}

// ============ 辅助方法 ============

// safeFloat64 安全地转换interface{}到float64
func safeFloat64(value interface{}) float64 {
	switch v := value.(type) {
	case float64:
		return v
	case float32:
		return float64(v)
	case int64:
		return float64(v)
	case int32:
		return float64(v)
	case int:
		return float64(v)
	case []uint8: // MySQL返回的数字可能是字节数组
		if str := string(v); str != "" {
			if f, err := strconv.ParseFloat(str, 64); err == nil {
				return f
			}
		}
		return 0
	case string: // 字符串类型的数字
		if f, err := strconv.ParseFloat(v, 64); err == nil {
			return f
		}
		return 0
	case nil:
		return 0
	default:
		// 尝试转换为字符串再解析
		if str := fmt.Sprintf("%v", v); str != "" && str != "<nil>" {
			if f, err := strconv.ParseFloat(str, 64); err == nil {
				return f
			}
		}
		return 0
	}
}
