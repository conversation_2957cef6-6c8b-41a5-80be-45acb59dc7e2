package dashboard

import (
	v1 "ad-pro-v2/api/dashboard/v1"
	"ad-pro-v2/internal/dao"
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcache"
)

// OptimizedTrendService 优化版本的趋势分析服务
type OptimizedTrendService struct {
	cache *gcache.Cache
}

// NewOptimizedTrendService 创建优化版本的趋势分析服务
func NewOptimizedTrendService(cache *gcache.Cache) *OptimizedTrendService {
	return &OptimizedTrendService{
		cache: cache,
	}
}

// getOptimizedHourlyData 优化的小时数据查询
func (s *OptimizedTrendService) getOptimizedHourlyData(ctx context.Context, req *v1.GetTrendDataReq, date string) ([]float64, []string, error) {
	g.Log().Info(ctx, "开始优化的分时查询", g.Map{
		"date":   date,
		"metric": req.Metric,
	})

	// 1. 首先获取过滤后的计划ID列表，限制数量
	planIds, err := s.getFilteredPlanIdsOptimized(ctx, req)
	if err != nil {
		return nil, nil, err
	}

	if len(planIds) == 0 {
		g.Log().Info(ctx, "没有符合条件的计划，返回空数据")
		timePoints := s.generateHourlyTimePoints()
		emptyData := make([]float64, 24)
		return emptyData, timePoints, nil
	}

	// 2. 限制计划数量以避免IN子句过长
	if len(planIds) > 100 {
		planIds = planIds[:100]
		g.Log().Warning(ctx, "计划数量过多，限制为前100个", g.Map{"originalCount": len(planIds)})
	}

	// 3. 确定查询的表名
	tableName := s.getTableNameByDate(date)

	// 4. 根据指标确定查询字段
	var fieldName string
	switch req.Metric {
	case "orders":
		fieldName = "COUNT(*)"
	case "estimated_commission":
		fieldName = "SUM(pre_commission)"
	case "settled_commission":
		fieldName = "SUM(commission)"
	case "order_amount":
		fieldName = "SUM(pay_price)"
	default:
		fieldName = "COUNT(*)"
	}

	// 5. 使用预聚合方式避免HOUR()函数
	return s.executeOptimizedHourlyQuery(ctx, tableName, fieldName, planIds, date)
}

// getFilteredPlanIdsOptimized 优化的计划ID筛选
func (s *OptimizedTrendService) getFilteredPlanIdsOptimized(ctx context.Context, req *v1.GetTrendDataReq) ([]string, error) {
	query := dao.AdSlotPlans.Ctx(ctx)

	// 添加筛选条件
	if req.UserId != "" {
		query = query.Where("user_id", req.UserId)
	}

	if req.MediaId != "" {
		query = query.Where("media_id", req.MediaId)
	}

	if req.PlanId != "" {
		query = query.Where("id", req.PlanId)
	}

	if req.ProductId != "" {
		query = query.Where("ad_product_id", req.ProductId)
	}

	// 处理合作类型筛选
	if req.Type != "" && req.Type != "0" {
		var mediaIds []interface{}
		switch req.Type {
		case "1": // 普通投放
			values, _ := dao.AdMedia.Ctx(ctx).Where("cooperation_type", "traffic").Array("id")
			for _, v := range values {
				mediaIds = append(mediaIds, v)
			}
		case "2": // CPS合作
			values, _ := dao.AdMedia.Ctx(ctx).Where("cooperation_type", "cps").Array("id")
			for _, v := range values {
				mediaIds = append(mediaIds, v)
			}
		case "3": // 灯火
			values, _ := dao.AdMedia.Ctx(ctx).Where("cooperation_type", "dh").Array("id")
			for _, v := range values {
				mediaIds = append(mediaIds, v)
			}
		}
		if len(mediaIds) > 0 {
			query = query.Where("media_id IN (?)", mediaIds)
		}
	}

	// 处理渠道分类筛选
	if req.Category != "" {
		var slotTypes []string
		switch req.Category {
		case "alipay":
			slotTypes = []string{"alipay_h5", "alipay_mp"}
		case "wechat":
			slotTypes = []string{"wechat_h5", "wechat_mp"}
		case "other":
			slotTypes = []string{"other"}
		}

		if len(slotTypes) > 0 {
			slotIds, err := dao.AdSlots.Ctx(ctx).Where("type IN (?)", slotTypes).Array("id")
			if err == nil && len(slotIds) > 0 {
				query = query.Where("ad_slot_id IN (?)", slotIds)
			}
		}
	}

	// 限制返回数量并获取计划ID
	planIds, err := query.Limit(100).Array("id")
	if err != nil {
		return nil, err
	}

	// 转换为字符串数组
	var result []string
	for _, id := range planIds {
		result = append(result, id.String())
	}

	return result, nil
}

// executeOptimizedHourlyQuery 执行优化的小时查询
func (s *OptimizedTrendService) executeOptimizedHourlyQuery(ctx context.Context, tableName, fieldName string, planIds []string, date string) ([]float64, []string, error) {
	// 使用批量查询方式，每次查询几个小时的数据
	timePoints := s.generateHourlyTimePoints()
	dataMap := make(map[int]float64)

	// 分批查询，每次查询4小时的数据
	batchSize := 4
	for i := 0; i < 24; i += batchSize {
		endHour := i + batchSize
		if endHour > 24 {
			endHour = 24
		}

		batchData, err := s.queryHourlyBatch(ctx, tableName, fieldName, planIds, date, i, endHour)
		if err != nil {
			g.Log().Error(ctx, "批量查询失败", g.Map{"startHour": i, "endHour": endHour, "error": err})
			continue
		}

		// 合并结果
		for hour, value := range batchData {
			dataMap[hour] = value
		}
	}

	// 生成最终数据数组
	var data []float64
	for i := 0; i < 24; i++ {
		if value, exists := dataMap[i]; exists {
			data = append(data, value)
		} else {
			data = append(data, 0)
		}
	}

	return data, timePoints, nil
}

// queryHourlyBatch 批量查询小时数据
func (s *OptimizedTrendService) queryHourlyBatch(ctx context.Context, tableName, fieldName string, planIds []string, date string, startHour, endHour int) (map[int]float64, error) {
	// 构建时间范围
	startTime := fmt.Sprintf("%s %02d:00:00", date, startHour)
	endTime := fmt.Sprintf("%s %02d:59:59", date, endHour-1)

	// 构建计划ID的占位符
	placeholders := make([]string, len(planIds))
	args := make([]interface{}, 0, len(planIds)+2)
	args = append(args, startTime, endTime)

	for i, planId := range planIds {
		placeholders[i] = "?"
		args = append(args, planId)
	}

	// 构建优化的SQL查询
	sqlQuery := fmt.Sprintf(`
		SELECT 
			HOUR(create_time) as hour, 
			%s as value 
		FROM %s 
		WHERE create_time >= ? AND create_time <= ?
		AND order_status IN (2,3,4)
		AND leak = 0
		AND ad_plan_id IN (%s)
		GROUP BY HOUR(create_time)
		ORDER BY hour ASC
	`, fieldName, tableName, strings.Join(placeholders, ","))

	// 设置查询超时
	queryCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// 执行查询
	results, err := g.DB().GetAll(queryCtx, sqlQuery, args...)
	if err != nil {
		return nil, err
	}

	// 处理结果
	dataMap := make(map[int]float64)
	for _, result := range results {
		hour := result["hour"].Int()
		value := s.safeFloat64Optimized(result["value"])
		dataMap[hour] = value
	}

	return dataMap, nil
}

// getTableNameByDate 根据日期获取表名
func (s *OptimizedTrendService) getTableNameByDate(date string) string {
	// 解析日期
	t, err := time.Parse("2006-01-02", date)
	if err != nil {
		return "ad_orders" // 默认表
	}

	// 生成分表名
	return fmt.Sprintf("ad_orders_%s", t.Format("200601"))
}

// generateHourlyTimePoints 生成24小时时间点
func (s *OptimizedTrendService) generateHourlyTimePoints() []string {
	var timePoints []string
	for i := 0; i < 24; i++ {
		timePoints = append(timePoints, fmt.Sprintf("%02d:00", i))
	}
	return timePoints
}

// getCachedHourlyData 使用缓存的优化查询
func (s *OptimizedTrendService) getCachedHourlyData(ctx context.Context, req *v1.GetTrendDataReq, date string) ([]float64, []string, error) {
	// 生成缓存键
	cacheKey := fmt.Sprintf("hourly_data:%s:%s:%s:%s:%s:%s",
		date, req.Metric, req.UserId, req.MediaId, req.PlanId, req.ProductId)

	// 尝试从缓存获取
	cached, err := s.cache.Get(ctx, cacheKey)
	if err == nil && cached != nil {
		if data, ok := cached.Val().(map[string]interface{}); ok {
			if values, ok := data["values"].([]float64); ok {
				if timePoints, ok := data["timePoints"].([]string); ok {
					g.Log().Info(ctx, "从缓存获取小时数据", g.Map{"cacheKey": cacheKey})
					return values, timePoints, nil
				}
			}
		}
	}

	// 缓存未命中，执行查询
	values, timePoints, err := s.getOptimizedHourlyData(ctx, req, date)
	if err != nil {
		return nil, nil, err
	}

	// 存入缓存，缓存5分钟
	cacheData := map[string]interface{}{
		"values":     values,
		"timePoints": timePoints,
	}
	s.cache.Set(ctx, cacheKey, cacheData, 5*time.Minute)

	return values, timePoints, nil
}

// safeFloat64Optimized 安全地转换interface{}到float64
func (s *OptimizedTrendService) safeFloat64Optimized(value interface{}) float64 {
	switch v := value.(type) {
	case float64:
		return v
	case float32:
		return float64(v)
	case int64:
		return float64(v)
	case int32:
		return float64(v)
	case int:
		return float64(v)
	case []uint8: // MySQL返回的数字可能是字节数组
		if str := string(v); str != "" {
			if f, err := strconv.ParseFloat(str, 64); err == nil {
				return f
			}
		}
		return 0
	case string: // 字符串类型的数字
		if f, err := strconv.ParseFloat(v, 64); err == nil {
			return f
		}
		return 0
	case nil:
		return 0
	default:
		return 0
	}
}
