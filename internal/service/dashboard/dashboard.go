package dashboard

import (
	v1 "ad-pro-v2/api/dashboard/v1"
	"context"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/os/gcache"
)

// DashboardService 仪表盘服务接口
type DashboardService interface {
	// 筛选相关
	GetFilterOptions(ctx context.Context) (*v1.GetFilterOptionsRes, error)

	// 指标相关
	GetMetrics(ctx context.Context, req *v1.GetMetricsReq) (*v1.GetMetricsRes, error)

	// 趋势相关
	GetTrendData(ctx context.Context, req *v1.GetTrendDataReq) (*v1.GetTrendDataRes, error)

	// 区域相关
	GetRegionData(ctx context.Context, req *v1.GetRegionDataReq) (*v1.GetRegionDataRes, error)

	// 订单类型相关
	GetOrderTypeData(ctx context.Context, req *v1.GetOrderTypeDataReq) (*v1.GetOrderTypeDataRes, error)

	// 媒体相关
	GetMediaList(ctx context.Context, req *v1.GetMediaListReq) (*v1.GetMediaListRes, error)
	GetPlans(ctx context.Context, mediaAccountId int64) (*v1.GetPlansRes, error)

	// 产品相关
	GetProducts(ctx context.Context) (*v1.GetProductsRes, error)

	// 统计相关
	GetPlanStats(ctx context.Context, req *v1.GetPlanStatsReq) (*v1.GetPlanStatsRes, error)

	// 工具方法
	GetFilteredPlanIds(ctx context.Context, planId, mediaId, productId, userId int, mediaType string) ([]int64, error)
}

// Service implements DashboardService interface
type Service struct {
	cache *gcache.Cache
}

var (
	instance *Service
)

// NewService creates a new dashboard service instance
func NewService(cache *gcache.Cache) *Service {
	return &Service{
		cache: cache,
	}
}

// GetInstance returns the singleton instance of dashboard service
func GetInstance() DashboardService {
	if instance == nil {
		instance = NewService(gcache.New())
	}
	return instance
}

// InitService initializes the dashboard service
func InitService(ctx context.Context) error {
	instance = NewService(gcache.New())
	return nil
}

// Implementation of DashboardService interface methods
func (s *Service) GetFilterOptions(ctx context.Context) (*v1.GetFilterOptionsRes, error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented, "服务正在迁移中，暂时不可用")
}

func (s *Service) GetMetrics(ctx context.Context, req *v1.GetMetricsReq) (*v1.GetMetricsRes, error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented, "服务正在迁移中，暂时不可用")
}

func (s *Service) GetTrendData(ctx context.Context, req *v1.GetTrendDataReq) (*v1.GetTrendDataRes, error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented, "服务正在迁移中，暂时不可用")
}

func (s *Service) GetRegionData(ctx context.Context, req *v1.GetRegionDataReq) (*v1.GetRegionDataRes, error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented, "服务正在迁移中，暂时不可用")
}

func (s *Service) GetOrderTypeData(ctx context.Context, req *v1.GetOrderTypeDataReq) (*v1.GetOrderTypeDataRes, error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented, "服务正在迁移中，暂时不可用")
}

func (s *Service) GetMediaList(ctx context.Context, req *v1.GetMediaListReq) (*v1.GetMediaListRes, error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented, "服务正在迁移中，暂时不可用")
}

func (s *Service) GetPlans(ctx context.Context, mediaAccountId int64) (*v1.GetPlansRes, error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented, "服务正在迁移中，暂时不可用")
}

func (s *Service) GetProducts(ctx context.Context) (*v1.GetProductsRes, error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented, "服务正在迁移中，暂时不可用")
}

func (s *Service) GetPlanStats(ctx context.Context, req *v1.GetPlanStatsReq) (*v1.GetPlanStatsRes, error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented, "服务正在迁移中，暂时不可用")
}

func (s *Service) GetFilteredPlanIds(ctx context.Context, planId, mediaId, productId, userId int, mediaType string) ([]int64, error) {
	return nil, gerror.NewCode(gcode.CodeNotImplemented, "服务正在迁移中，暂时不可用")
}
