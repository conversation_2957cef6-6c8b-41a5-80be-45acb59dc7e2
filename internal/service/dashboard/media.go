package dashboard

import (
	v1 "ad-pro-v2/api/dashboard/v1"
	"ad-pro-v2/internal/dao"
	"context"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcache"
)

// IMediaService 媒体服务接口
type IMediaService interface {
	GetMediaList(ctx context.Context, req *v1.GetMediaListReq) (*v1.GetMediaListRes, error)
	GetPlans(ctx context.Context, mediaAccountId int64) (*v1.GetPlansRes, error)
}

// MediaService 媒体服务实现
type MediaService struct {
	cache *gcache.Cache
}

// NewMediaService 创建新的媒体服务实例
func NewMediaService(cache *gcache.Cache) IMediaService {
	return &MediaService{
		cache: cache,
	}
}

// GetMediaList 获取媒体列表
func (s *MediaService) GetMediaList(ctx context.Context, req *v1.GetMediaListReq) (*v1.GetMediaListRes, error) {
	g.Log().Info(ctx, "获取媒体列表开始", g.Map{
		"userId":   req.UserId,
		"type":     req.Type,
		"category": req.Category,
	})

	// 构建查询条件
	query := dao.AdMedia.Ctx(ctx)

	// 根据用户ID筛选
	if req.UserId != "" {
		query = query.Where("user_id", req.UserId)
	}

	// 根据类型筛选
	if req.Type != "" {
		switch req.Type {
		case "1": // 普通投放
			query = query.Where("cooperation_type", "traffic")
		case "2": // CPS合作
			query = query.Where("cooperation_type", "cps")
		case "3": // 灯火
			query = query.Where("cooperation_type", "dh")
		}
	}

	// 根据大类筛选
	if req.Category != "" {
		switch req.Category {
		case "alipay": // 支付宝
			query = query.WhereLike("types", "%alipay%")
		case "wechat": // 微信
			query = query.WhereLike("types", "%wechat%")
		case "other": // 其他
			query = query.WhereLike("types", "%other%")
		}
	}

	// 只获取审核通过的媒体
	query = query.Where("audit_status", "approved")

	// 执行查询
	medias, err := query.
		Fields("id", "name").
		Order("id DESC").
		Limit(1000).
		All()

	if err != nil {
		g.Log().Error(ctx, "查询媒体列表失败:", err)
		return &v1.GetMediaListRes{List: []*v1.MediaListItem{}}, err
	}

	// 转换为API格式
	var mediaList []*v1.MediaListItem
	for _, media := range medias {
		mediaList = append(mediaList, &v1.MediaListItem{
			Id:   media["id"].String(),
			Name: media["name"].String(),
		})
	}

	res := &v1.GetMediaListRes{
		List: mediaList,
	}

	g.Log().Info(ctx, "获取媒体列表完成", g.Map{
		"count": len(mediaList),
	})

	return res, nil
}

// GetPlans 获取投放计划列表
func (s *MediaService) GetPlans(ctx context.Context, mediaAccountId int64) (*v1.GetPlansRes, error) {
	g.Log().Info(ctx, "获取投放计划列表开始", g.Map{
		"mediaAccountId": mediaAccountId,
	})

	// 缓存键
	cacheKey := fmt.Sprintf("dashboard:plans:%d", mediaAccountId)

	// 尝试从缓存获取
	if cached, err := s.cache.Get(ctx, cacheKey); err == nil && !cached.IsNil() {
		g.Log().Info(ctx, "从缓存获取投放计划列表")
		var res *v1.GetPlansRes
		if err := cached.Struct(&res); err == nil {
			return res, nil
		}
	}

	// 从数据库获取计划列表
	plans, err := dao.AdSlotPlans.Ctx(ctx).
		Where("media_id", mediaAccountId).
		Where("delivery_status", "running"). // 只获取投放中的计划
		Fields("id", "name", "code").
		Order("id DESC").
		Limit(1000).
		All()

	if err != nil {
		g.Log().Error(ctx, "查询投放计划列表失败:", err)
		return &v1.GetPlansRes{List: []*v1.PlanItem{}}, err
	}

	// 转换为API格式
	var planList []*v1.PlanItem
	for _, plan := range plans {
		planName := plan["name"].String()
		if planName == "" {
			planName = plan["code"].String() // 如果名称为空，使用编码
		}

		planList = append(planList, &v1.PlanItem{
			Id:   plan["id"].String(),
			Name: planName,
		})
	}

	res := &v1.GetPlansRes{
		List: planList,
	}

	// 缓存结果 (5分钟)
	s.cache.Set(ctx, cacheKey, res, 300)

	g.Log().Info(ctx, "获取投放计划列表完成", g.Map{
		"count": len(planList),
	})

	return res, nil
}
