package service

import (
	v1 "ad-pro-v2/api/v1"
	"context"
)

// IUser 用户管理服务接口
type IUser interface {
	// GetUsers 获取用户列表
	GetUsers(ctx context.Context, req *v1.GetUsersReq) (*v1.GetUsersRes, error)

	// CreateUser 创建用户
	CreateUser(ctx context.Context, req *v1.CreateUserReq) (*v1.CreateUserRes, error)

	// UpdateUser 更新用户
	UpdateUser(ctx context.Context, req *v1.UpdateUserReq) (*v1.UpdateUserRes, error)

	// DeleteUser 删除用户
	DeleteUser(ctx context.Context, req *v1.DeleteUserReq) (*v1.DeleteUserRes, error)

	// LockUser 锁定用户
	LockUser(ctx context.Context, req *v1.LockUserReq) (*v1.LockUserRes, error)

	// UnlockUser 解锁用户
	UnlockUser(ctx context.Context, req *v1.UnlockUserReq) (*v1.UnlockUserRes, error)

	// ResetPassword 重置密码
	ResetPassword(ctx context.Context, req *v1.ResetPasswordReq) (*v1.ResetPasswordRes, error)

	// GetUserOptions 获取用户选项
	GetUserOptions(ctx context.Context, req *v1.GetUserOptionsReq) (*v1.GetUserOptionsRes, error)
}

// User 用户管理服务
var User IUser

// RegisterUser 注册用户服务
func RegisterUser(i IUser) {
	User = i
}
