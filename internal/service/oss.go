package service

import (
	"context"
	"io"
	"net/http"
	"path"
	"strings"
	"time"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/util/guid"
)

// OssService OSS服务接口定义
type OssService interface {
	// UploadFile 上传文件
	UploadFile(ctx context.Context, file *ghttp.UploadFile) (url string, err error)

	// UploadImage 上传图片
	UploadImage(ctx context.Context, file *ghttp.UploadFile) (url string, err error)
}

// 实现OssService接口
type ossService struct{}

// OSS 全局OSS服务实例
var OSS = &ossService{}

// UploadFile 上传文件
func (s *ossService) UploadFile(ctx context.Context, file *ghttp.UploadFile) (url string, err error) {
	// 获取OSS配置
	endpoint := g.Cfg().MustGet(ctx, "oss.endpoint").String()
	accessKeyID := g.Cfg().MustGet(ctx, "oss.access_key_id").String()
	accessKeySecret := g.Cfg().MustGet(ctx, "oss.access_key_secret").String()
	bucketName := g.Cfg().MustGet(ctx, "oss.bucket_name").String()

	// 创建OSSClient实例
	client, err := oss.New(endpoint, accessKeyID, accessKeySecret)
	if err != nil {
		return "", err
	}

	// 获取存储空间
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		return "", err
	}

	// 打开文件
	fileHandle, err := file.Open()
	if err != nil {
		return "", err
	}
	defer fileHandle.Close()

	// 生成唯一文件名
	ext := path.Ext(file.Filename)
	objectName := "files/" + time.Now().Format("20060102") + "/" + guid.S() + ext

	// 上传文件
	err = bucket.PutObject(objectName, fileHandle)
	if err != nil {
		return "", err
	}

	// 返回文件URL
	return "https://" + bucketName + "." + endpoint + "/" + objectName, nil
}

// UploadImage 上传图片
func (s *ossService) UploadImage(ctx context.Context, file *ghttp.UploadFile) (url string, err error) {
	// 验证是否为图片
	if !isImage(file) {
		return "", gerror.New("只支持上传图片文件")
	}

	// 获取OSS配置
	endpoint := g.Cfg().MustGet(ctx, "oss.endpoint").String()
	accessKeyID := g.Cfg().MustGet(ctx, "oss.access_key_id").String()
	accessKeySecret := g.Cfg().MustGet(ctx, "oss.access_key_secret").String()
	bucketName := g.Cfg().MustGet(ctx, "oss.bucket_name").String()

	// 创建OSSClient实例
	client, err := oss.New(endpoint, accessKeyID, accessKeySecret)
	if err != nil {
		return "", err
	}

	// 获取存储空间
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		return "", err
	}

	// 打开文件
	fileHandle, err := file.Open()
	if err != nil {
		return "", err
	}
	defer fileHandle.Close()

	// 生成唯一文件名
	ext := path.Ext(file.Filename)
	objectName := "images/" + time.Now().Format("20060102") + "/" + guid.S() + ext

	// 上传文件
	err = bucket.PutObject(objectName, fileHandle)
	if err != nil {
		return "", err
	}

	// 返回文件URL
	return "https://img.dac6.cn/" + objectName, nil
}

// isImage 判断文件是否为图片
func isImage(file *ghttp.UploadFile) bool {
	// 检查文件扩展名
	ext := gfile.ExtName(file.Filename)
	validExts := map[string]bool{
		"jpg":  true,
		"jpeg": true,
		"png":  true,
		"gif":  true,
		"bmp":  true,
		"webp": true,
	}

	if _, ok := validExts[ext]; !ok {
		return false
	}

	// 打开文件并检查文件头
	f, err := file.Open()
	if err != nil {
		return false
	}
	defer f.Close()

	// 读取文件头
	buf := make([]byte, 512)
	if _, err := io.ReadFull(f, buf); err != nil {
		return false
	}

	// 检查MIME类型
	contentType := http.DetectContentType(buf)
	return strings.HasPrefix(contentType, "image/")
}
