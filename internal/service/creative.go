package service

import (
	"context"
	"encoding/json"
	"time"

	"github.com/gogf/gf/v2/frame/g"

	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/model"
)

type ICreative interface {
	GetList(ctx context.Context, req *model.CreativeListReq) (*model.CreativeListRes, error)
	GetById(ctx context.Context, id uint64) (*model.Creative, error)
	Create(ctx context.Context, req *model.CreativeCreateReq) (*model.CreativeCreateRes, error)
	Update(ctx context.Context, req *model.CreativeUpdateReq) (*model.CreativeUpdateRes, error)
	Delete(ctx context.Context, id uint64) (*model.CreativeDeleteRes, error)
}

type creativeService struct{}

var insCreative = creativeService{}

func Creative() ICreative {
	return &insCreative
}

// GetList 获取创意列表
func (s *creativeService) GetList(ctx context.Context, req *model.CreativeListReq) (*model.CreativeListRes, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	// 获取列表数据
	list, total, err := dao.AdCreatives.GetList(ctx, req.Name, req.Page, req.Size)
	if err != nil {
		return nil, err
	}

	// 转换数据
	var creatives []*model.Creative
	for _, v := range list {
		var creative model.Creative
		creative.Id = v["id"].Uint64()
		creative.Name = v["name"].String()
		creative.ImageUrl = v["image_url"].String()
		creative.ImageArea = v["image_area"].String()
		creative.BackgroundColor = v["background_color"].String()
		creative.CreatedAt = v["created_at"].GTime().String()
		creative.UpdatedAt = v["updated_at"].GTime().String()

		// 解析热区JSON
		if !v["hot_areas"].IsEmpty() {
			var hotAreas []model.HotArea
			if err := json.Unmarshal([]byte(v["hot_areas"].String()), &hotAreas); err == nil {
				creative.HotAreas = hotAreas
			}
		}

		creatives = append(creatives, &creative)
	}

	return &model.CreativeListRes{
		List:  creatives,
		Total: total,
		Page:  req.Page,
		Size:  req.Size,
	}, nil
}

// GetById 获取创意详情
func (s *creativeService) GetById(ctx context.Context, id uint64) (*model.Creative, error) {
	// 查询数据
	data, err := dao.AdCreatives.Ctx(ctx).Where("id", id).One()
	if err != nil {
		return nil, err
	}
	if data.IsEmpty() {
		return nil, model.ErrInvalidParam.WithMsg("创意不存在")
	}

	// 转换数据
	creative := &model.Creative{
		Id:              data["id"].Uint64(),
		Name:            data["name"].String(),
		ImageUrl:        data["image_url"].String(),
		ImageArea:       data["image_area"].String(),
		BackgroundColor: data["background_color"].String(),
		CreatedAt:       data["created_at"].GTime().String(),
		UpdatedAt:       data["updated_at"].GTime().String(),
	}

	// 解析热区JSON
	if !data["hot_areas"].IsEmpty() {
		var hotAreas []model.HotArea
		if err := json.Unmarshal([]byte(data["hot_areas"].String()), &hotAreas); err == nil {
			creative.HotAreas = hotAreas
		}
	}

	return creative, nil
}

// Create 创建创意
func (s *creativeService) Create(ctx context.Context, req *model.CreativeCreateReq) (*model.CreativeCreateRes, error) {
	// 验证热区数据
	for i, area := range req.HotAreas {
		if area.Width <= 0 || area.Height <= 0 {
			return nil, model.ErrInvalidParam.WithMsg("热区宽高必须大于0")
		}
		if area.Unit == "" {
			req.HotAreas[i].Unit = "px" // 设置默认单位
		}
	}

	// 转换热区为JSON
	hotAreasJson, err := json.Marshal(req.HotAreas)
	if err != nil {
		return nil, err
	}

	// 准备插入数据
	data := g.Map{
		"name":             req.Name,
		"image_url":        req.ImageUrl,
		"image_area":       req.ImageArea,
		"background_color": req.BackgroundColor,
		"hot_areas":        string(hotAreasJson),
		"created_at":       time.Now(),
		"updated_at":       time.Now(),
	}

	// 插入数据
	result, err := dao.AdCreatives.Ctx(ctx).Data(data).Insert()
	if err != nil {
		return nil, err
	}

	// 获取插入ID
	id, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	return &model.CreativeCreateRes{Id: uint64(id)}, nil
}

// Update 更新创意
func (s *creativeService) Update(ctx context.Context, req *model.CreativeUpdateReq) (*model.CreativeUpdateRes, error) {
	// 检查创意是否存在
	exists, err := dao.AdCreatives.Ctx(ctx).Where("id", req.Id).Count()
	if err != nil {
		return nil, err
	}
	if exists == 0 {
		return nil, model.ErrInvalidParam.WithMsg("创意不存在")
	}

	// 验证热区数据
	for i, area := range req.HotAreas {
		if area.Width <= 0 || area.Height <= 0 {
			return nil, model.ErrInvalidParam.WithMsg("热区宽高必须大于0")
		}
		if area.Unit == "" {
			req.HotAreas[i].Unit = "px" // 设置默认单位
		}
	}

	// 转换热区为JSON
	hotAreasJson, err := json.Marshal(req.HotAreas)
	if err != nil {
		return nil, err
	}

	// 准备更新数据
	data := g.Map{
		"name":             req.Name,
		"image_url":        req.ImageUrl,
		"image_area":       req.ImageArea,
		"background_color": req.BackgroundColor,
		"hot_areas":        string(hotAreasJson),
		"updated_at":       time.Now(),
	}

	// 更新数据
	_, err = dao.AdCreatives.Ctx(ctx).Data(data).Where("id", req.Id).Update()
	if err != nil {
		return nil, err
	}

	return &model.CreativeUpdateRes{}, nil
}

// Delete 删除创意
func (s *creativeService) Delete(ctx context.Context, id uint64) (*model.CreativeDeleteRes, error) {
	// 检查创意是否存在
	exists, err := dao.AdCreatives.Ctx(ctx).Where("id", id).Count()
	if err != nil {
		return nil, err
	}
	if exists == 0 {
		return nil, model.ErrInvalidParam.WithMsg("创意不存在")
	}

	// 删除数据
	_, err = dao.AdCreatives.Ctx(ctx).Where("id", id).Delete()
	if err != nil {
		return nil, err
	}

	return &model.CreativeDeleteRes{}, nil
}
