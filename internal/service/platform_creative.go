package service

import (
	v1 "ad-pro-v2/api/platform_creative/v1"
	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/model/entity"
	"context"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

type IPlatformCreative interface {
	List(ctx context.Context, req *v1.ListReq) (res *v1.ListRes, err error)
	Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error)
	Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error)
	Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error)
}

type platformCreativeImpl struct{}

var platformCreative = platformCreativeImpl{}

func PlatformCreative() IPlatformCreative {
	return platformCreative
}

// List 获取广告创意列表
func (s platformCreativeImpl) List(ctx context.Context, req *v1.ListReq) (res *v1.ListRes, err error) {
	res = &v1.ListRes{}

	// 获取当前登录用户
	user := Context().GetUser(ctx)
	if user == nil {
		return nil, model.ErrNotAuthorized
	}

	// 构建基础查询条件
	baseModel := g.Model("platform_creatives c").
		LeftJoin("platform_plans p", "p.id=c.plan_id").
		LeftJoin("ad_media m", "m.id=p.media_id").
		LeftJoin("ad_agents a", "a.id=m.ad_agent_id").
		Safe()

	// 如果不是管理层，只能看到属于自己媒体的数据
	if user.Role != 3 {
		baseModel = baseModel.Where("p.media_id IN (?)", g.Model("ad_media").Where("user_id", user.Id).Fields("id"))
	}

	// 添加平台类型筛选
	if req.PlatformType != "" {
		baseModel = baseModel.Where("p.platform_type", req.PlatformType)
	}

	// 添加创意名称模糊搜索
	if req.Keyword != "" {
		baseModel = baseModel.Where("(c.name LIKE ? OR c.data_id = ?)", "%"+req.Keyword+"%", req.Keyword)
	}

	// 添加营销目标筛选
	if req.MarketTargetName != "" {
		baseModel = baseModel.Where("p.market_target_name", req.MarketTargetName)
	}

	// 添加广告组关键词筛选
	if req.GroupKeyword != "" {
		baseModel = baseModel.Where("c.group_id IN (?)", g.Model("platform_groups").Where("(name LIKE ? OR data_id = ?)", "%"+req.GroupKeyword+"%", req.GroupKeyword).Fields("id"))
	}

	// 添加计划关键词筛选
	if req.PlanKeyword != "" {
		baseModel = baseModel.Where("(p.name LIKE ? OR p.data_id = ?)", "%"+req.PlanKeyword+"%", req.PlanKeyword)
	}

	// 获取总数
	res.Total, err = baseModel.Fields("c.id").Count()
	if err != nil {
		return nil, err
	}

	// 获取列表数据
	var list []struct {
		Id           uint64    `json:"id"`       // ID
		Name         string    `json:"name"`     // 名称
		GroupId      int64     `json:"group_id"` // 广告组ID
		PladId       int64     `json:"plan_id"`  // 计划ID
		PlanName     string    `json:"plan_name"`
		CreatedAt    time.Time `json:"created_at"`    // 创建时间
		UpdatedAt    time.Time `json:"updated_at"`    // 更新时间
		MediaName    string    `json:"media_name"`    // 媒体名称
		AgentName    string    `json:"agent_name"`    // 代理名称
		DataId       string    `json:"data_id"`       // 平台数据ID
		PlatformType string    `json:"platform_type"` // 平台类型
		Remark       string    `json:"remark"`        // 备注
	}

	err = baseModel.Fields("c.*,p.name as plan_name, m.name as media_name, a.name as agent_name").
		Page(req.Page, req.PageSize).
		Order("c.id DESC").
		Scan(&list)
	if err != nil {
		return nil, err
	}

	// 转换为响应结构
	res.List = make([]v1.PlatformCreativeItem, len(list))
	for i, v := range list {
		res.List[i] = v1.PlatformCreativeItem{
			ID:           v.Id,
			Name:         v.Name,
			GroupId:      v.GroupId,
			MediaName:    v.MediaName,
			AgentName:    v.AgentName,
			DataId:       v.DataId,
			PlatformType: v.PlatformType,
			CreatedAt:    v.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:    v.UpdatedAt.Format("2006-01-02 15:04:05"),
			PlanName:     v.PlanName,
			Remark:       v.Remark,
		}
	}

	return
}

// Create 创建广告创意
func (s platformCreativeImpl) Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error) {
	res = &v1.CreateRes{}

	// 创建广告创意
	creative := &entity.PlatformCreative{
		Name:        req.Name,
		GroupId:     req.GroupId,
		Title:       req.Title,
		Description: req.Description,
		ImageUrl:    req.ImageUrl,
		VideoUrl:    req.VideoUrl,
		LandingUrl:  req.LandingUrl,
		Status:      req.Status,
	}

	// 插入数据
	result, err := g.Model("platform_creatives").Data(creative).Insert()
	if err != nil {
		return nil, err
	}

	// 获取插入ID
	id, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	res.Id = id
	return
}

// Update 更新广告创意
func (s platformCreativeImpl) Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error) {
	res = &v1.UpdateRes{}

	// 获取当前登录用户
	user := Context().GetUser(ctx)
	if user == nil {
		return nil, model.ErrNotAuthorized
	}

	// 构建查询条件
	baseModel := g.Model("platform_creatives c").
		LeftJoin("platform_plans p", "p.id=c.plan_id").
		LeftJoin("ad_media m", "m.id=p.media_id").
		Where("c.id", req.Id)

	// 如果不是管理层，只能编辑属于自己媒体的数据
	if user.Role != 3 {
		baseModel = baseModel.Where("m.user_id", user.Id)
	}

	// 检查是否有权限编辑
	count, err := baseModel.Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, model.ErrNotAuthorized
	}

	// 更新数据
	_, err = g.Model("platform_creatives").
		Data(g.Map{
			"remark": req.Remark,
		}).
		Where("id", req.Id).
		Update()

	return
}

// Delete 删除广告创意
func (s platformCreativeImpl) Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error) {
	res = &v1.DeleteRes{}

	// 删除数据
	_, err = g.Model("platform_creatives").
		Where("id", req.Id).
		Delete()

	return
}
