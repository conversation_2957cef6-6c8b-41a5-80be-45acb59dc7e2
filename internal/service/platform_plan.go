package service

import (
	v1 "ad-pro-v2/api/platform_plan/v1"
	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/model/entity"
	"context"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

type IPlatformPlan interface {
	List(ctx context.Context, req *v1.ListReq) (res *v1.ListRes, err error)
	Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error)
	Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error)
	Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error)
	UpdatePlanType(ctx context.Context, id uint, planType string) error
	GetMarketTargets(ctx context.Context, platformType string) ([]string, error)
}

type platformPlanImpl struct{}

var platformPlan = platformPlanImpl{}

func PlatformPlan() IPlatformPlan {
	return platformPlan
}

// List 获取平台计划列表
func (s platformPlanImpl) List(ctx context.Context, req *v1.ListReq) (res *v1.ListRes, err error) {
	res = &v1.ListRes{}

	// 获取当前登录用户
	user := Context().GetUser(ctx)
	if user == nil {
		return nil, model.ErrNotAuthorized
	}

	// 构建基础查询条件
	baseModel := g.Model("platform_plans p").
		LeftJoin("ad_media m", "m.id=p.media_id").
		LeftJoin("ad_agents a", "a.id=p.agent_id").
		Where("m.deleted_at IS NULL").
		Where("a.deleted_at IS NULL").
		Safe()

	// 如果不是管理层，只能看到属于自己媒体的数据
	if user.Role != 3 {
		baseModel = baseModel.Where("p.media_id IN (?)",
			g.Model("ad_media").Where("user_id", user.Id).Fields("id"))
	}

	// 添加查询条件
	if req.PlatformType != "" {
		baseModel = baseModel.Where("p.platform_type", req.PlatformType)
	}

	if req.PlanName != "" {
		baseModel = baseModel.WhereLike("p.name", "%"+req.PlanName+"%")
	}

	if req.AgentId > 0 {
		baseModel = baseModel.Where("p.agent_id", req.AgentId)
	}

	if req.MediaId > 0 {
		baseModel = baseModel.Where("p.media_id", req.MediaId)
	}

	if req.MarketTargetName != "" {
		baseModel = baseModel.Where("p.market_target_name", req.MarketTargetName)
	}

	// 关键词支持计划名称模糊查询和平台ID精确匹配
	if req.Keyword != "" {
		baseModel = baseModel.Where("(p.name LIKE ? OR p.data_id = ?)", "%"+req.Keyword+"%", req.Keyword)
	}

	// 获取总数
	res.Total, err = baseModel.Fields("p.id").Count()
	if err != nil {
		return nil, err
	}

	// 获取列表数据
	var list []struct {
		*entity.PlatformPlan
		MediaName string `json:"media_name"`
		AgentName string `json:"agent_name"`
	}
	err = baseModel.Fields("p.*, m.name as media_name, a.name as agent_name").
		Page(req.Page, req.PageSize).
		Order("p.id DESC").
		Scan(&list)
	if err != nil {
		return nil, err
	}

	// 转换为响应结构
	res.List = make([]v1.PlatformPlanItem, len(list))
	for i, v := range list {
		res.List[i] = v1.PlatformPlanItem{
			ID:               v.Id,
			DataId:           v.DataId,
			Name:             v.Name,
			MediaName:        v.MediaName,
			AgentName:        v.AgentName,
			PlatformType:     v.PlatformType,
			MarketTargetName: v.MarketTargetName,
			PlanType:         v.PlanType,
			CreatedAt:        v.CreatedAt.Format("Y-m-d H:i:s"),
			UpdatedAt:        v.UpdatedAt.Format("Y-m-d H:i:s"),
		}
	}

	return
}

// Create 创建平台计划
func (s platformPlanImpl) Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error) {
	res = &v1.CreateRes{}

	// 创建计划
	plan := &entity.PlatformPlan{
		Name:         req.Name,
		Remark:       req.Description,
		AdCreativeID: req.AdCreativeID,
	}

	// 插入数据
	result, err := g.Model("platform_plans").Data(plan).Insert()
	if err != nil {
		return nil, err
	}

	// 获取插入ID
	id, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	res.Id = id
	return
}

// Update 更新平台计划
func (s platformPlanImpl) Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error) {
	res = &v1.UpdateRes{}

	// 获取当前登录用户
	user := Context().GetUser(ctx)
	if user == nil {
		return nil, model.ErrNotAuthorized
	}

	// 构建基础查询条件
	baseModel := g.Model("platform_plans").Where("id", req.Id)

	// 如果不是管理层(role=3)，只能修改自己代理商下的数据
	if user.Role != 3 {
		baseModel = baseModel.Where("agent_id IN (?)",
			g.Model("ad_agents").Where("user_id", user.Id).Fields("id"))
	}

	// 检查是否有权限修改该计划
	count, err := baseModel.Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, model.ErrNotAuthorized
	}

	// 更新数据
	_, err = baseModel.Data(g.Map{
		"name":           req.Name,
		"remark":         req.Description,
		"ad_creative_id": req.AdCreativeID,
		"updated_at":     time.Now(),
	}).Update()

	return
}

// Delete 删除平台计划
func (s platformPlanImpl) Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error) {
	res = &v1.DeleteRes{}

	// 删除数据
	_, err = g.Model("platform_plans").
		Where("id", req.Id).
		Delete()

	return
}

// UpdatePlanType 更新计划类型
func (s platformPlanImpl) UpdatePlanType(ctx context.Context, id uint, planType string) error {
	glog.Info(ctx, "开始更新计划类型", g.Map{
		"id":        id,
		"plan_type": planType,
	})

	_, err := g.DB().Model("platform_plans").
		Where("id", id).
		Data(g.Map{
			"plan_type":  planType,
			"updated_at": time.Now(),
		}).
		Update()

	if err != nil {
		glog.Error(ctx, "更新计划类型失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	return nil
}

// GetMarketTargets 获取市场目标列表
func (s platformPlanImpl) GetMarketTargets(ctx context.Context, platformType string) ([]string, error) {
	glog.Info(ctx, "开始获取市场目标列表", g.Map{
		"platform_type": platformType,
	})

	array, err := g.DB().Model("platform_plans").
		Where("platform_type", platformType).
		Where("market_target_name != ''"). // 排除空值
		Fields("DISTINCT market_target_name").
		Array()
	if err != nil {
		glog.Error(ctx, "获取市场目标列表失败", g.Map{
			"error": err.Error(),
		})
		return nil, err
	}

	// 转换为字符串数组
	targets := make([]string, len(array))
	for i, v := range array {
		targets[i] = v.String()
	}

	return targets, nil
}
