package service

import (
	v1 "ad-pro-v2/api/v1"
	"context"
)

// IDepartment 部门管理服务接口
type IDepartment interface {
	// GetDepartments 获取部门列表
	GetDepartments(ctx context.Context, req *v1.GetDepartmentsReq) (*v1.GetDepartmentsRes, error)

	// GetDepartmentTree 获取部门树
	GetDepartmentTree(ctx context.Context, req *v1.GetDepartmentTreeReq) (*v1.GetDepartmentTreeRes, error)

	// CreateDepartment 创建部门
	CreateDepartment(ctx context.Context, req *v1.CreateDepartmentReq) (*v1.CreateDepartmentRes, error)

	// UpdateDepartment 更新部门
	UpdateDepartment(ctx context.Context, req *v1.UpdateDepartmentReq) (*v1.UpdateDepartmentRes, error)

	// DeleteDepartment 删除部门
	DeleteDepartment(ctx context.Context, req *v1.DeleteDepartmentReq) (*v1.DeleteDepartmentRes, error)

	// GetDepartmentOptions 获取部门选项
	GetDepartmentOptions(ctx context.Context, req *v1.GetDepartmentOptionsReq) (*v1.GetDepartmentOptionsRes, error)
}

// Department 部门管理服务
var Department IDepartment

// RegisterDepartment 注册部门服务
func RegisterDepartment(i IDepartment) {
	Department = i
}
