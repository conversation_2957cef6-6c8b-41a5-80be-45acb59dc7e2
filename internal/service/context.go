package service

import (
	"context"

	"ad-pro-v2/internal/model"
)

type IContext interface {
	GetUser(ctx context.Context) *model.User
}

type contextService struct{}

var insContext = contextService{}

func Context() IContext {
	return &insContext
}

// GetUser 获取当前用户信息
func (s *contextService) GetUser(ctx context.Context) *model.User {
	value := ctx.Value("user")
	if value == nil {
		return nil
	}

	if localUser, ok := value.(*model.User); ok {
		return localUser
	}

	return nil
}
