package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"golang.org/x/crypto/bcrypt"

	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/model"
)

type IMedia interface {
	GetList(ctx context.Context, condition g.Map, page, size int) (*model.MediaListRes, error)
	GetById(ctx context.Context, id int) (*model.Media, error)
	Create(ctx context.Context, req *model.MediaCreateReq) (*model.MediaCreateRes, error)
	Update(ctx context.Context, req *model.MediaUpdateReq) (*model.MediaUpdateRes, error)
	Delete(ctx context.Context, id int) (*model.MediaDeleteRes, error)
	Audit(ctx context.Context, req *model.MediaAuditReq) (*model.MediaAuditRes, error)
	GetMediaAccountByUserId(ctx context.Context, userId uint64) (*model.MediaAccount, error)
	GetAllMediaWithAccounts(ctx context.Context) ([]*model.Media, error)
	GetAccountsByMediaId(ctx context.Context, mediaId uint64) ([]*model.MediaAccount, error)
	GetAccountsByMediaIds(ctx context.Context, mediaIds []uint64) ([]*model.MediaAccount, error)
}

type mediaService struct{}

var insMedia = mediaService{}

func Media() IMedia {
	return &insMedia
}

// GetList 获取媒体列表
func (s *mediaService) GetList(ctx context.Context, condition g.Map, page, size int) (*model.MediaListRes, error) {
	m := dao.AdMedia.Ctx(ctx)

	// 获取当前用户信息
	userInfo := Context().GetUser(ctx)
	if userInfo == nil {
		return nil, model.ErrNotAuthorized
	}

	// 构建查询条件
	whereCondition := g.Map{}

	// 如果不是管理员/运营/财务，只能查看自己的媒体
	if !userInfo.IsAdmin() && !userInfo.IsOperator() && !userInfo.IsFinance() {
		whereCondition["user_id"] = userInfo.Id
	}

	// 添加筛选条件
	if v, ok := condition["userId"]; ok {
		whereCondition["user_id"] = v
	}

	if v, ok := condition["name"]; ok && v != "" {
		whereCondition["name LIKE ?"] = "%" + v.(string) + "%"
	}
	if v, ok := condition["types"]; ok && v != nil {
		if types, ok := v.([]string); ok && len(types) > 0 {
			// 对于媒体类型筛选，使用LIKE查询，因为types字段存储为JSON字符串
			// 只处理第一个类型，因为前端目前只发送一个类型
			mediaType := types[0]
			whereCondition["types LIKE ?"] = "%" + mediaType + "%"
		}
	}
	if v, ok := condition["industry"]; ok && v != "" {
		whereCondition["industry"] = v
	}
	if v, ok := condition["auditStatus"]; ok && v != "" {
		whereCondition["audit_status"] = v
	}
	if v, ok := condition["cooperationStatus"]; ok && v != "" {
		whereCondition["cooperation_status"] = v
	}
	if v, ok := condition["cooperationType"]; ok && v != "" {
		whereCondition["cooperation_type"] = v
	}

	// 获取总数
	total, err := m.Where(whereCondition).Count()
	if err != nil {
		return nil, err
	}

	// 获取列表数据
	list, err := m.Where(whereCondition).
		Fields("ad_media.*,u.real_name as user_real_name").
		LeftJoin("users u", "u.id=ad_media.user_id").
		Order("id DESC").
		Page(page, size).
		All()
	if err != nil {
		return nil, err
	}

	var media []*model.Media
	if err = list.Structs(&media); err != nil {
		return nil, err
	}

	return &model.MediaListRes{
		List:  media,
		Total: total,
		Page:  page,
		Size:  size,
	}, nil
}

// GetById 获取媒体详情
func (s *mediaService) GetById(ctx context.Context, id int) (*model.Media, error) {
	var media *model.Media
	err := dao.AdMedia.Ctx(ctx).Where("id", id).Scan(&media)
	if err != nil {
		return nil, err
	}
	return media, nil
}

// 生成媒体编码
func (s *mediaService) generateMediaCode(ctx context.Context) (string, error) {
	today := time.Now().Format("20060102")

	// 查询今天最大编码
	maxCode, err := dao.AdMedia.Ctx(ctx).
		Fields("code").
		Where("code LIKE ?", "M"+today+"%").
		Order("code DESC").
		Value()

	var sequence int
	if err != nil || maxCode.IsEmpty() {
		sequence = 1
	} else {
		// 提取序号部分
		code := maxCode.String()
		seqStr := code[len(code)-5:]
		sequence, _ = strconv.Atoi(seqStr)
		sequence++
	}

	// 生成新编码
	return fmt.Sprintf("M%s%05d", today, sequence), nil
}

// Create 创建媒体
func (s *mediaService) Create(ctx context.Context, req *model.MediaCreateReq) (*model.MediaCreateRes, error) {
	// 生成媒体编码
	code, err := s.generateMediaCode(ctx)
	if err != nil {
		return nil, err
	}

	// 如果是CPS合作类型,需要加密密码
	if req.CooperationType == "cps" && req.Password != "" {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), 12)
		if err != nil {
			return nil, err
		}
		req.Password = string(hashedPassword)
	}

	// 验证平台配置
	if req.CooperationType == "dh" && req.PlatformConfig != nil {
		if req.PlatformConfig.Platform != "denghuoplus" {
			return nil, model.ErrInvalidParam.WithMsg("目前仅支持灯火平台")
		}
	}

	var auditStatus = model.AuditStatusPending
	if req.CooperationType == "dh" || req.CooperationType == "other_delivery" {
		auditStatus = model.AuditStatusApproved
	}

	data := g.Map{
		"code":               code,
		"user_id":            req.UserId,
		"name":               req.Name,
		"types":              req.Types,
		"industry":           req.Industry,
		"custom_industry":    req.CustomIndustry,
		"daily_activity":     req.DailyActivity,
		"transaction_volume": req.TransactionVolume,
		"company_name":       req.CompanyName,
		"company_address":    req.CompanyAddress,
		"contact_name":       req.ContactName,
		"contact_phone":      req.ContactPhone,
		"cooperation_type":   req.CooperationType,
		"account":            req.Account,
		"password":           req.Password,
		"audit_status":       auditStatus,
		"cooperation_status": req.CooperationStatus,
		"remark":             req.Remark,
		"ad_agent_id":        req.AdAgentId,
		"return_rate":        req.ReturnRate,
	}

	// 添加平台配置
	if req.PlatformConfig != nil {
		data["platform_config"] = req.PlatformConfig
	}

	result, err := dao.AdMedia.Ctx(ctx).Insert(data)
	if err != nil {
		return nil, err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	return &model.MediaCreateRes{Id: int(id)}, nil
}

// Update 更新媒体
func (s *mediaService) Update(ctx context.Context, req *model.MediaUpdateReq) (*model.MediaUpdateRes, error) {
	data := g.Map{
		"name":               req.Name,
		"types":              req.Types,
		"industry":           req.Industry,
		"custom_industry":    req.CustomIndustry,
		"daily_activity":     req.DailyActivity,
		"transaction_volume": req.TransactionVolume,
		"company_name":       req.CompanyName,
		"company_address":    req.CompanyAddress,
		"contact_name":       req.ContactName,
		"contact_phone":      req.ContactPhone,
		"cooperation_type":   req.CooperationType,
		"account":            req.Account,
		"remark":             req.Remark,
		"ad_agent_id":        req.AdAgentId,
		"return_rate":        req.ReturnRate,
	}

	// 如果是CPS合作类型且更新了密码,需要加密新密码
	if req.CooperationType == "cps" && req.Password != "" {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), 12)
		if err != nil {
			return nil, err
		}
		data["password"] = string(hashedPassword)
	}

	// 平台配置
	if req.PlatformConfig != nil {
		data["platform_config"] = req.PlatformConfig
	}

	_, err := dao.AdMedia.Ctx(ctx).Where("id", req.Id).Update(data)
	if err != nil {
		return nil, err
	}

	return &model.MediaUpdateRes{}, nil
}

// Delete 删除媒体
func (s *mediaService) Delete(ctx context.Context, id int) (*model.MediaDeleteRes, error) {
	_, err := dao.AdMedia.Ctx(ctx).Where("id", id).Delete()
	if err != nil {
		return nil, err
	}

	return &model.MediaDeleteRes{}, nil
}

// Audit 审核媒体
func (s *mediaService) Audit(ctx context.Context, req *model.MediaAuditReq) (*model.MediaAuditRes, error) {
	data := g.Map{
		"audit_status": req.AuditStatus,
		"remark":       req.Remark,
	}
	if req.AuditStatus == "rejected" {
		data["reject_reason"] = req.RejectReason
	}
	_, err := dao.AdMedia.Ctx(ctx).Where("id", req.Id).Update(data)
	if err != nil {
		return nil, err
	}

	return &model.MediaAuditRes{}, nil
}

// GetMediaAccountByUserId 根据用户ID获取媒体账号信息
func (s *mediaService) GetMediaAccountByUserId(ctx context.Context, userId uint64) (*model.MediaAccount, error) {
	return dao.AdMedia.GetAccountByUserId(ctx, userId)
}

// GetAllMediaWithAccounts 获取所有媒体及其账号信息
func (s *mediaService) GetAllMediaWithAccounts(ctx context.Context) ([]*model.Media, error) {
	return dao.AdMedia.GetAllWithAccounts(ctx)
}

// GetAccountsByMediaId 获取媒体下的所有账号
func (s *mediaService) GetAccountsByMediaId(ctx context.Context, mediaId uint64) ([]*model.MediaAccount, error) {
	return dao.AdMedia.GetAccountsByMediaId(ctx, mediaId)
}

// GetAccountsByMediaIds 批量获取多个媒体下的所有账号
func (s *mediaService) GetAccountsByMediaIds(ctx context.Context, mediaIds []uint64) ([]*model.MediaAccount, error) {
	return dao.AdMedia.GetAccountsByMediaIds(ctx, mediaIds)
}
