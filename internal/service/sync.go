package service

import (
	"ad-pro-v2/internal/consts"
	"context"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// SyncService 同步服务接口
type SyncService interface {
	// SyncDenghuoPlusData 同步灯火数据
	SyncDenghuoPlusData(ctx context.Context, mediaId, agentId uint64, startDate, endDate time.Time) error
}

var localSync SyncService

// SetSync 设置同步服务实例
func SetSync(s SyncService) {
	localSync = s
}

// Sync 获取同步服务实例
func Sync() SyncService {
	return localSync
}

type syncService struct{}

// NewSyncService 创建同步服务实例
func NewSyncService() SyncService {
	return &syncService{}
}

// SyncDenghuoPlusData 同步灯火数据
func (s *syncService) SyncDenghuoPlusData(ctx context.Context, mediaId, agentId uint64, startDate, endDate time.Time) error {
	// 1. 获取媒体信息
	media, err := Media().GetById(ctx, int(mediaId))
	if err != nil {
		return gerror.Wrap(err, "获取媒体信息失败")
	}
	if media == nil {
		return gerror.New("媒体不存在")
	}

	if media.PlatformConfig == nil || media.PlatformConfig.Platform != "denghuoplus" {
		return gerror.New("媒体平台配置不是灯火")
	}

	// 2. 获取代理信息
	agent, err := Agent().GetById(ctx, int(agentId))
	if err != nil {
		return gerror.Wrap(err, "获取代理信息失败")
	}
	if agent == nil {
		return gerror.New("代理不存在")
	}

	// 检查代理状态和类型
	if agent.AuditStatus != "approved" {
		g.Log().Noticef(ctx, "代理审核状态不是已通过, mediaId: %d, agentId: %d", media.Id, agent.Id)
		return gerror.New("代理审核状态不是已通过")
	}
	if agent.Type != consts.AgentTypeDelivery {
		g.Log().Noticef(ctx, "代理类型不是投放, mediaId: %d, agentId: %d", media.Id, agent.Id)
		return gerror.New("代理类型不是投放")
	}
	if agent.PlatformConfig == nil || agent.PlatformConfig.Platform != "denghuoplus" {
		g.Log().Noticef(ctx, "代理平台配置不是灯火, mediaId: %d, agentId: %d", media.Id, agent.Id)
		return gerror.New("代理平台配置不是灯火")
	}

	// 3. 调用同步方法
	cronSvc := Cron().(*cronService)
	return cronSvc.syncDenghuoPlusData(ctx, media, agent, startDate, endDate)
}
