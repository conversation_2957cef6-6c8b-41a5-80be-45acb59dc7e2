package service

import (
	"context"
)

// IPlanStatistics 计划统计服务接口
type IPlanStatistics interface {
	// GetPlanPvUv 获取计划PV/UV数据
	GetPlanPvUv(ctx context.Context, planId int, date string) (map[string]int, error)
}

var (
	localPlanStatistics IPlanStatistics
)

// PlanStatistics 获取计划统计服务实例
func PlanStatistics() IPlanStatistics {
	if localPlanStatistics == nil {
		panic("implement not found for interface IPlanStatistics, forgot register?")
	}
	return localPlanStatistics
}

// RegisterPlanStatistics 注册计划统计服务实例
func RegisterPlanStatistics(i IPlanStatistics) {
	localPlanStatistics = i
}
