package service

import (
	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

type passwordImpl struct{}

var (
	localPassword IPassword = new(passwordImpl)
)

// RegisterPassword registers password service implementation to global service.
func RegisterPassword(i IPassword) {
	localPassword = i
}

// Password returns the singleton instance of password service.
func Password() IPassword {
	return localPassword
}

// Password service interface
type IPassword interface {
	// CreateWithGroup creates passwords with group
	CreateWithGroup(ctx context.Context, groupName string, categoryId int, passwords []PasswordInfo) error
	// List returns the list of password groups with their passwords
	List(ctx context.Context, page, pageSize int, groupName string, categoryId int64) (list []*GroupInfo, total int, err error)
	// OriList returns the list of original passwords from warehouse.elm_pwds based on category
	OriList(ctx context.Context, page, pageSize int, categoryIds []int) (list []*OriPasswordInfo, total int, err error)
	// UpdateGroup updates password group and its passwords
	Update(ctx context.Context, groupId int, groupName string, categoryId int, passwords []PasswordInfo) error
	// Delete deletes a password group and its associated passwords
	Delete(ctx context.Context, groupId int) error
}

// PasswordInfo defines the password information for creation
type PasswordInfo struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
	Pid  string `json:"pid"`
}

// OriPasswordInfo defines the original password information
type OriPasswordInfo struct {
	Name string `json:"name" `
	Pid  string `json:"pid" `
}

// GroupInfo defines the group information with passwords
type GroupInfo struct {
	Id         int             `json:"id"    `
	Name       string          `json:"name" `
	CategoryId int             `json:"categoryId" `
	CreatedAt  *gtime.Time     `json:"createdAt" `
	UpdatedAt  *gtime.Time     `json:"updatedAt" `
	Passwords  []*PasswordInfo `json:"passwords" `
}

// CreateWithGroup creates passwords with group
func (s *passwordImpl) CreateWithGroup(ctx context.Context, groupName string, categoryId int, passwords []PasswordInfo) error {
	// 开启事务
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 1. 检查组名是否已存在
		var group *entity.PasswordGroup
		err := dao.PasswordGroups.Ctx(ctx).Where("name", groupName).Scan(&group)
		if err != nil {
			return err
		}
		if group != nil {
			return gerror.Newf("组名 '%s' 已存在", groupName)
		}

		// 2. 创建新的口令组
		result, err := dao.PasswordGroups.Ctx(ctx).Data(g.Map{
			"name":        groupName,
			"category_id": categoryId,
		}).Insert()
		if err != nil {
			return err
		}
		lastId, err := result.LastInsertId()
		if err != nil {
			return err
		}
		groupId := int(lastId)

		// 3. 批量创建口令
		if len(passwords) == 0 {
			return gerror.New("passwords cannot be empty")
		}

		var passwordEntities []g.Map
		for _, p := range passwords {
			passwordEntities = append(passwordEntities, g.Map{
				"pid":      p.Pid,
				"name":     p.Name,
				"group_id": groupId,
			})
		}

		_, err = dao.Password.Ctx(ctx).Data(passwordEntities).Insert()
		return err
	})
}

// List returns the list of password groups with their passwords
func (s *passwordImpl) List(ctx context.Context, page, pageSize int, groupName string, categoryId int64) (list []*GroupInfo, total int, err error) {
	var (
		m = dao.PasswordGroups.Ctx(ctx)
	)

	// 1. 构建查询条件
	if groupName != "" {
		m = m.WhereLike("password_groups.name", "%"+groupName+"%")
	}
	if categoryId > 0 {
		m = m.Where("password_groups.category_id", categoryId)
	}

	// 2. 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 3. 查询分页数据
	var groups []*entity.PasswordGroup
	err = m.Page(page, pageSize).OrderDesc("id").Scan(&groups)
	if err != nil {
		return nil, 0, err
	}

	// 如果没有组数据，直接返回
	if len(groups) == 0 {
		return []*GroupInfo{}, total, nil
	}

	// 4. 提取所有组ID并初始化结果slice
	var groupIds []int
	list = make([]*GroupInfo, len(groups))
	groupMap := make(map[int]*GroupInfo)

	for i, group := range groups {
		groupIds = append(groupIds, group.Id)
		list[i] = &GroupInfo{
			Id:         group.Id,
			Name:       group.Name,
			CategoryId: group.CategoryId,
			CreatedAt:  group.CreatedAt,
			UpdatedAt:  group.UpdatedAt,
			Passwords:  make([]*PasswordInfo, 0),
		}
		groupMap[group.Id] = list[i]
	}

	// 5. 一次性查询所有组的密码
	var passwords []*entity.Password
	err = dao.Password.Ctx(ctx).
		WhereIn("group_id", groupIds).
		Scan(&passwords)
	if err != nil {
		return nil, 0, err
	}

	// 6. 在内存中将密码分配到对应的组
	for _, pwd := range passwords {
		if groupInfo, ok := groupMap[pwd.GroupId]; ok {
			groupInfo.Passwords = append(groupInfo.Passwords, &PasswordInfo{
				Name: pwd.Name,
				Pid:  pwd.Pid,
				Id:   int64(pwd.Id),
			})
		}
	}

	return list, total, nil
}

// OriList returns the list of original passwords from warehouse.elm_pwds based on category
func (s *passwordImpl) OriList(ctx context.Context, page, pageSize int, categoryIds []int) (list []*OriPasswordInfo, total int, err error) {
	// 1. 递归查询所有子分类ID
	allCategoryIds, err := s.getAllChildCategories(ctx, categoryIds)
	if err != nil {
		return nil, 0, err
	}

	if len(allCategoryIds) == 0 {
		return []*OriPasswordInfo{}, 0, nil
	}

	// 2. 使用所有分类ID查询口令
	sql := `
		SELECT 
			e.pwd_name, 
			e.pid
		FROM 
			warehouse.elm_pwds e
		JOIN 
			byn_data.elm_adzone_category_rels r ON SUBSTRING_INDEX(e.pid, '_', -1) COLLATE utf8mb4_general_ci = r.adzone COLLATE utf8mb4_general_ci
		WHERE 
			r.category_id IN (?)
	`

	// 计算总数
	countSql := `
		SELECT 
			COUNT(1) as total
		FROM 
			warehouse.elm_pwds e
		JOIN 
			byn_data.elm_adzone_category_rels r ON SUBSTRING_INDEX(e.pid, '_', -1) COLLATE utf8mb4_general_ci = r.adzone COLLATE utf8mb4_general_ci
		WHERE 
			r.category_id IN (?)
	`

	var totalCount struct {
		Total int `json:"total"`
	}

	err = g.DB().GetScan(ctx, &totalCount, countSql, allCategoryIds)
	if err != nil {
		return nil, 0, err
	}
	total = totalCount.Total

	// 如果没有数据，直接返回
	if total == 0 {
		return []*OriPasswordInfo{}, 0, nil
	}

	// 添加分页
	sql += " LIMIT ?, ?"
	offset := (page - 1) * pageSize

	// 执行查询
	var results []struct {
		PwdName string `json:"pwd_name"`
		Pid     string `json:"pid"`
	}

	err = g.DB().GetScan(ctx, &results, sql, allCategoryIds, offset, pageSize)
	if err != nil {
		return nil, 0, err
	}

	// 转换为响应结构
	list = make([]*OriPasswordInfo, 0, len(results))
	for _, r := range results {
		list = append(list, &OriPasswordInfo{
			Name: r.PwdName,
			Pid:  r.Pid,
		})
	}

	return list, total, nil
}

// getAllChildCategories 递归获取所有子分类ID
func (s *passwordImpl) getAllChildCategories(ctx context.Context, parentIds []int) ([]int, error) {
	if len(parentIds) == 0 {
		return nil, nil
	}

	var result []int
	result = append(result, parentIds...)

	for {
		var children []struct {
			Id int `json:"id"`
		}

		// 查询直接子分类
		err := g.DB().GetScan(ctx, &children, `
			SELECT id 
			FROM byn_data.elm_adzone_categories 
			WHERE parent_id IN (?) 
			AND deleted_at IS NULL
		`, parentIds)

		if err != nil {
			return nil, err
		}

		// 如果没有子分类了，退出循环
		if len(children) == 0 {
			break
		}

		// 提取子分类ID
		var childIds []int
		for _, child := range children {
			childIds = append(childIds, child.Id)
		}

		// 将子分类ID添加到结果中
		result = append(result, childIds...)

		// 继续查找下一层
		parentIds = childIds
	}

	return result, nil
}

// UpdateGroup updates password group and its passwords
func (s *passwordImpl) Update(ctx context.Context, groupId int, groupName string, categoryId int, passwords []PasswordInfo) error {
	// 开启事务
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 1. 检查组名是否已存在
		var group *entity.PasswordGroup
		err := dao.PasswordGroups.Ctx(ctx).Where("id", groupId).Scan(&group)
		if err != nil {
			return err
		}
		if group == nil {
			return gerror.Newf("组ID '%d' 不存在", groupId)
		}

		// 2. 更新组名和分类
		_, err = dao.PasswordGroups.Ctx(ctx).Data(g.Map{
			"name":        groupName,
			"category_id": categoryId,
		}).Where("id", groupId).Update()
		if err != nil {
			return err
		}

		// 3. 删除该组下的所有密码记录
		_, err = dao.Password.Ctx(ctx).Where("group_id", groupId).Delete()
		if err != nil {
			return err
		}

		// 4. 批量创建新的口令
		if len(passwords) == 0 {
			return gerror.New("passwords cannot be empty")
		}

		var passwordEntities []g.Map
		for _, p := range passwords {
			passwordEntities = append(passwordEntities, g.Map{
				"pid":      p.Pid,
				"name":     p.Name,
				"group_id": groupId,
			})
		}

		_, err = dao.Password.Ctx(ctx).Data(passwordEntities).Insert()
		return err
	})
}

// Delete deletes a password group and its associated passwords
func (s *passwordImpl) Delete(ctx context.Context, groupId int) error {
	// 开启事务
	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 1. 检查组名是否已存在
		var group *entity.PasswordGroup
		err := dao.PasswordGroups.Ctx(ctx).Where("id", groupId).Scan(&group)
		if err != nil {
			return err
		}
		if group == nil {
			return gerror.Newf("组ID '%d' 不存在", groupId)
		}

		// 2. 删除该组下的所有密码记录
		_, err = dao.Password.Ctx(ctx).Where("group_id", groupId).Delete()
		if err != nil {
			return err
		}

		// 3. 删除该组
		_, err = dao.PasswordGroups.Ctx(ctx).Where("id", groupId).Delete()
		return err
	})
}
