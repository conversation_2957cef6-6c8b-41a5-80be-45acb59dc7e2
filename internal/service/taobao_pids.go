package service

import (
	"context"

	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/model/entity"
)

// ITaobaoPids 淘联链接服务接口
type ITaobaoPids interface {
	// GetList 获取列表数据
	GetList(ctx context.Context, filter *model.TaobaoPidsFilter) (items interface{}, total int, usedCount int, unusedCount int, err error)

	// Create 创建淘联链接
	Create(ctx context.Context, data *entity.TaobaoPids) (id uint64, err error)

	// Update 更新淘联链接
	Update(ctx context.Context, data *entity.TaobaoPids) error

	// Delete 删除淘联链接
	Delete(ctx context.Context, id uint64) error
}

var (
	localTaobaoPids ITaobaoPids
)

// TaobaoPids 获取淘联链接服务
func TaobaoPids() ITaobaoPids {
	if localTaobaoPids == nil {
		panic("implement not found for interface ITaobaoPids, forgot register?")
	}
	return localTaobaoPids
}

// RegisterTaobaoPids 注册淘联链接服务
func RegisterTaobaoPids(i ITaobaoPids) {
	localTaobaoPids = i
}
