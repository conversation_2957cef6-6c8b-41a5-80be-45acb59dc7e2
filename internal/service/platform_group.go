package service

import (
	v1 "ad-pro-v2/api/platform_group/v1"
	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/model/entity"
	"context"

	"github.com/gogf/gf/v2/frame/g"
)

type IPlatformGroup interface {
	List(ctx context.Context, req *v1.ListReq) (res *v1.ListRes, err error)
	Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error)
	Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error)
	Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error)
}

type platformGroupImpl struct{}

var platformGroup = platformGroupImpl{}

func PlatformGroup() IPlatformGroup {
	return platformGroup
}

// List 获取广告组列表
func (s platformGroupImpl) List(ctx context.Context, req *v1.ListReq) (res *v1.ListRes, err error) {
	res = &v1.ListRes{}

	// 获取当前登录用户
	user := Context().GetUser(ctx)
	if user == nil {
		return nil, model.ErrNotAuthorized
	}

	// 构建基础查询条件
	baseModel := g.Model("platform_groups g").
		LeftJoin("platform_plans p", "p.id=g.plan_id").
		LeftJoin("ad_media m", "m.id=p.media_id").
		LeftJoin("ad_agents a", "a.id=m.ad_agent_id").
		Safe()

	// 权限控制:
	// 1. 如果是管理员(role=3)，可以查看所有数据
	// 2. 如果不是管理员，只能查看自己媒体的数据
	if user.Role != 3 {
		baseModel = baseModel.Where("p.media_id IN (?)",
			g.Model("ad_media").Where("user_id", user.Id).Fields("id"))
	}

	// 添加计划关键词筛选
	if req.PlanKeyword != "" {
		baseModel = baseModel.Where("(p.name LIKE ? OR p.data_id = ?)", "%"+req.PlanKeyword+"%", req.PlanKeyword)
	}

	// 添加平台类型筛选
	if req.PlatformType != "" {
		baseModel = baseModel.Where("p.platform_type", req.PlatformType)
	}

	// 添加营销目标筛选
	if req.MarketTargetName != "" {
		baseModel = baseModel.Where("p.market_target_name", req.MarketTargetName)
	}

	// 添加关键词搜索
	if req.Keyword != "" {
		baseModel = baseModel.Where("(g.name LIKE ? OR g.data_id = ?)", "%"+req.Keyword+"%", req.Keyword)
	}

	// 获取总数
	res.Total, err = baseModel.Fields("g.id").Count()
	if err != nil {
		return nil, err
	}

	// 获取列表数据
	var list []struct {
		Id           uint64 `json:"id"`
		Name         string `json:"name"`          // 单元名称
		PlanName     string `json:"plan_name"`     // 计划名称
		MediaName    string `json:"media_name"`    // 媒体名称
		AgentName    string `json:"agent_name"`    // 代理名称
		Description  string `json:"remark"`        // 备注信息
		DataId       string `json:"data_id"`       // 数据ID
		PlatformType string `json:"platform_type"` // 平台类型
	}

	err = baseModel.Fields("g.id, g.name, g.data_id, p.name as plan_name, m.name as media_name, a.name as agent_name, g.remark, g.platform_type").
		Page(req.Page, req.PageSize).
		Order("g.id DESC").
		Scan(&list)
	if err != nil {
		return nil, err
	}

	// 转换为响应结构
	res.List = make([]v1.PlatformGroupItem, len(list))
	for i, v := range list {
		res.List[i] = v1.PlatformGroupItem{
			ID:           v.Id,
			Name:         v.Name,
			PlanName:     v.PlanName,
			MediaName:    v.MediaName,
			AgentName:    v.AgentName,
			Description:  v.Description,
			DataId:       v.DataId,
			PlatformType: v.PlatformType,
		}
	}

	return
}

// Create 创建广告组
func (s platformGroupImpl) Create(ctx context.Context, req *v1.CreateReq) (res *v1.CreateRes, err error) {
	res = &v1.CreateRes{}

	// 创建广告组
	group := &entity.PlatformGroup{
		Name:         req.Name,
		PlanId:       req.PlanId,
		Budget:       req.Budget,
		DailyBudget:  req.DailyBudget,
		Status:       req.Status,
		DeliveryMode: req.DeliveryMode,
	}

	// 插入数据
	result, err := g.Model("platform_groups").Data(group).Insert()
	if err != nil {
		return nil, err
	}

	// 获取插入ID
	id, err := result.LastInsertId()
	if err != nil {
		return nil, err
	}

	res.Id = id
	return
}

// Update 更新广告组
func (s platformGroupImpl) Update(ctx context.Context, req *v1.UpdateReq) (res *v1.UpdateRes, err error) {
	res = &v1.UpdateRes{}

	// 获取当前登录用户
	user := Context().GetUser(ctx)
	if user == nil {
		return nil, model.ErrNotAuthorized
	}

	// 构建基础查询
	baseModel := g.Model("platform_groups g").
		LeftJoin("platform_plans p", "p.id=g.plan_id").
		LeftJoin("ad_media m", "m.id=p.media_id").
		Safe()

	// 权限控制:
	// 1. 如果是管理员(role=3)，可以编辑所有数据
	// 2. 如果不是管理员，只能编辑自己媒体的数据
	if user.Role != 3 {
		baseModel = baseModel.Where("p.media_id IN (?)",
			g.Model("ad_media").Where("user_id", user.Id).Fields("id"))
	}

	// 检查是否有权限编辑该广告组
	count, err := baseModel.Where("g.id", req.Id).Count()
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, model.ErrNotAuthorized
	}

	// 更新数据
	_, err = g.Model("platform_groups").
		Data(g.Map{
			"remark": req.Remark,
		}).
		Where("id", req.Id).
		Update()

	return
}

// Delete 删除广告组
func (s platformGroupImpl) Delete(ctx context.Context, req *v1.DeleteReq) (res *v1.DeleteRes, err error) {
	res = &v1.DeleteRes{}

	// 删除数据
	_, err = g.Model("platform_groups").
		Where("id", req.Id).
		Delete()

	return
}
