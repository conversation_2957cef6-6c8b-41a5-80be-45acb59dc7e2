package service

import (
	v1 "ad-pro-v2/api/ad_slot_plan/v1"
	"ad-pro-v2/pkg"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"time"

	"ad-pro-v2/internal/constants"
	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/model/entity"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

// IAdSlotPlan 投放计划服务接口
type IAdSlotPlan interface {
	// List 获取投放计划列表
	List(ctx context.Context, page, size int, filters map[string]interface{}) ([]model.AdSlotPlan, int, error)

	// Get 获取投放计划详情
	Get(ctx context.Context, id uint64) (*model.AdSlotPlan, error)

	// Create 创建投放计划
	Create(ctx context.Context, plan *model.AdSlotPlan) error

	// Update 更新投放计划
	Update(ctx context.Context, plan *model.AdSlotPlan) error

	// Delete 删除投放计划
	Delete(ctx context.Context, id uint64) error

	// Approve 审核通过
	Approve(ctx context.Context, id uint64, userID uint64) error

	// Reject 审核拒绝
	Reject(ctx context.Context, id uint64, userID uint64, reason string) error

	// UpdateDeliveryMode 更新投放策略
	UpdateDeliveryMode(ctx context.Context, id uint64, mode int, userID uint64) error

	// GeneratePromotionLink 生成推广链接
	GeneratePromotionLink(ctx context.Context, id uint64, productID uint64) error

	// GenerateShortUrl 生成短链接
	GenerateShortUrl(ctx context.Context, id uint64) error

	// UpdatePromotionLink 更新推广链接
	UpdatePromotionLink(ctx context.Context, id uint64, link string, userID uint64) error

	// UpdateShortUrl 更新短链接
	UpdateShortUrl(ctx context.Context, id uint64, url string, userID uint64) error

	// UpdateMergeLinks 更新融合链接
	UpdateMergeLinks(ctx context.Context, id uint64, mergeLinks interface{}) error

	// GetPlansByMediaAccountId 获取媒体账号下的投放计划列表
	GetPlansByMediaAccountId(ctx context.Context, mediaAccountId uint64) ([]*model.AdSlotPlan, error)

	// GetPlansByMediaAccountIds 批量获取多个媒体账号下的投放计划列表
	GetPlansByMediaAccountIds(ctx context.Context, mediaAccountIds []uint64) ([]*model.AdSlotPlan, error)

	// GetMergeLinksByCode 根据编号获取融合链接
	GetMergeLinksByCode(ctx context.Context, code string) (interface{}, error)

	// SendSettleProfitDingTalk 发送结算佣金钉钉通知
	SendSettleProfitDingTalk(ctx context.Context, planIds []uint64) error

	GetPlatformObjectData(ctx context.Context, in *v1.GetPlatformObjectDataReq) (any, error)

	GeneratePlanReportData(ctx context.Context, start, end time.Time) ([]*PlanAllStats, error)

	GeneratePwdDailyReport(ctx context.Context, start, end time.Time) error
}

type adSlotPlanService struct{}

var insAdSlotPlan = adSlotPlanService{}

// AdSlotPlan 获取投放计划服务实例
func AdSlotPlan() IAdSlotPlan {
	return &insAdSlotPlan
}

// List 获取投放计划列表
func (s *adSlotPlanService) List(ctx context.Context, page, size int, filters map[string]interface{}) ([]model.AdSlotPlan, int, error) {
	glog.Info(ctx, "开始获取投放计划列表", g.Map{
		"page":    page,
		"size":    size,
		"filters": filters,
	})

	// 构建查询条件
	query := g.DB().Model("ad_slot_plans").Safe()

	// 预加载关联数据
	query = query.WithAll()

	// 应用过滤条件
	if v, ok := filters["code"].(string); ok && v != "" {
		query = query.WhereLike("code", "%"+v+"%")
	}
	if v, ok := filters["media_id"]; ok {
		query = query.Where("media_id", v)
	}
	if v, ok := filters["ad_slot_id"]; ok {
		query = query.Where("ad_slot_id", v)
	}
	if v, ok := filters["user_id"]; ok {
		query = query.Where("user_id", v)
	}

	// 获取总数
	count, err := query.Count()
	if err != nil {
		glog.Error(ctx, "获取投放计划总数失败", g.Map{
			"error": err.Error(),
		})
		return nil, 0, err
	}

	// 获取列表数据
	var plans []model.AdSlotPlan
	err = query.Page(page, size).OrderDesc("id").Scan(&plans)
	if err != nil {
		glog.Error(ctx, "获取投放计划列表失败", g.Map{
			"error": err.Error(),
		})
		return nil, 0, err
	}

	// 处理状态字段
	for i := range plans {
		// 设置状态文本
		plans[i].AuditStatusText = plans[i].GetAuditStatusText()
		plans[i].DeliveryStatusText = plans[i].GetDeliveryStatusText()

		// 加载关联数据
		if plans[i].AdSlotID > 0 {
			var slot entity.AdSlots
			err = g.DB().Model("ad_slots").Where("id", plans[i].AdSlotID).Scan(&slot)
			if err == nil {
				plans[i].AdSlot = &slot
			}
		}

		if plans[i].AdProductID > 0 {
			var product entity.AdProducts
			err = g.DB().Model("ad_products").Where("id", plans[i].AdProductID).Scan(&product)
			if err == nil {
				plans[i].AdProduct = &product
			}
		}

		// 加载媒介（负责人）信息
		if plans[i].UserId > 0 {
			var user struct {
				Name     string `json:"name"`
				RealName string `json:"real_name"`
			}
			err = g.DB().Model("users").Where("id", plans[i].UserId).Scan(&user)
			if err == nil {
				// 优先使用真实姓名，如果没有则使用用户名
				if user.RealName != "" {
					plans[i].UserName = user.RealName
				} else if user.Name != "" {
					plans[i].UserName = user.Name
				}
			}
		}
	}

	return plans, count, nil
}

// Get 获取投放计划详情
func (s *adSlotPlanService) Get(ctx context.Context, id uint64) (*model.AdSlotPlan, error) {
	glog.Info(ctx, "开始获取投放计划详情", g.Map{
		"id": id,
	})

	var result model.AdSlotPlan

	// 获取基本数据
	err := g.DB().Model("ad_slot_plans").Where("id", id).Scan(&result)
	if err != nil {
		glog.Error(ctx, "获取投放计划失败", g.Map{
			"error": err.Error(),
		})
		return nil, err
	}

	if result.ID == 0 {
		return nil, fmt.Errorf("plan not found: %d", id)
	}

	// 加载关联数据
	if result.AdSlotID > 0 {
		var slot entity.AdSlots
		err = g.DB().Model("ad_slots").Where("id", result.AdSlotID).Scan(&slot)
		if err == nil {
			result.AdSlot = &slot
		}
	}

	if result.AdProductID > 0 {
		var product entity.AdProducts
		err = g.DB().Model("ad_products").Where("id", result.AdProductID).Scan(&product)
		if err == nil {
			result.AdProduct = &product
		}
	}

	// 加载推广位列表
	var zones []*entity.PromotionZones
	err = g.DB().Model("promotion_zones").Where("plan_id", result.ID).Scan(&zones)
	if err == nil {
		result.PromotionZones = zones
	}

	var rels []*entity.PlanRelateRels
	err = g.DB().Model("plan_relate_rels").Where("plan_id", result.ID).Scan(&rels)
	if err == nil {
		result.Rels = rels
	}

	// 设置状态文本
	result.AuditStatusText = constants.PlanAuditStatusMap[result.AuditStatus]
	result.DeliveryStatusText = constants.PlanDeliveryStatusMap[result.DeliveryStatus]

	return &result, nil
}

// Create 创建投放计划
func (s *adSlotPlanService) Create(ctx context.Context, plan *model.AdSlotPlan) error {
	glog.Info(ctx, "开始创建投放计划", g.Map{
		"plan": plan,
	})

	// 获取广告位信息以获取 media_id
	var adSlot entity.AdSlots
	err := g.DB().Model("ad_slots").Where("id", plan.AdSlotID).Scan(&adSlot)
	if err != nil {
		glog.Error(ctx, "获取广告位信息失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	// 从上下文获取当前用户信息
	user := g.RequestFromCtx(ctx).GetCtxVar("user").Val().(*model.User)
	if user == nil {
		return fmt.Errorf("user not found in context")
	}

	media, err := Media().GetById(ctx, int(adSlot.MediaId))
	if err != nil {
		glog.Error(ctx, "获取媒体信息失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	// 设置初始状态
	if media.CooperationType == "dh" || media.CooperationType == "other_delivery" {
		plan.AuditStatus = constants.PLAN_AUDIT_STATUS_APPROVED
		plan.DeliveryStatus = constants.PLAN_DELIVERY_STATUS_CONFIGURING
	} else {
		plan.AuditStatus = constants.PLAN_AUDIT_STATUS_PENDING
		plan.DeliveryStatus = constants.PLAN_DELIVERY_STATUS_INIT
	}
	plan.CreatedAt = time.Now()
	plan.UpdatedAt = time.Now()
	plan.MediaId = adSlot.MediaId
	plan.UserId = uint64(user.Id)

	// 获取最后一个计划的ID
	var lastPlan model.AdSlotPlan
	err = g.DB().Model("ad_slot_plans").OrderDesc("id").Scan(&lastPlan)
	if err != nil && err != sql.ErrNoRows {
		glog.Error(ctx, "获取最后一个计划失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	// 生成计划编号: P + 5位数字
	nextId := uint64(1)
	if lastPlan.ID > 0 {
		nextId = lastPlan.ID + 1
	}
	plan.Code = fmt.Sprintf("P%05d", nextId)

	// 处理测试计划的特殊逻辑
	if plan.Type == constants.PLAN_TYPE_TEST {
		// 强制启用结束时间
		plan.IsEndDateEnabled = true

		// 如果没有设置结束时间，自动设置为开始时间后3天
		if plan.EndDate == nil {
			endDate := plan.StartDate.Add(72 * time.Hour) // 3天
			plan.EndDate = &endDate
		}
	}

	// 插入数据
	result, err := g.DB().Model("ad_slot_plans").Data(plan).Insert()
	if err != nil {
		glog.Error(ctx, "创建投放计划失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	if len(plan.Rels) > 0 {
		id, err := result.LastInsertId()
		if err != nil {
			return err
		}
		for _, rel := range plan.Rels {
			rel.PlanId = id
		}
		_, err = g.DB().Model("plan_relate_rels").Data(plan.Rels).Insert()
		if err != nil {
			glog.Error(ctx, "投放计划与其他关联失败", g.Map{
				"error": err.Error(),
			})
			return err
		}
	}
	return nil
}

// Update 更新投放计划
func (s *adSlotPlanService) Update(ctx context.Context, plan *model.AdSlotPlan) error {
	glog.Info(ctx, "开始更新投放计划", g.Map{
		"plan": plan,
	})

	plan.UpdatedAt = time.Now()

	_, err := g.DB().Model("ad_slot_plans").
		Data(plan).
		Where("id", plan.ID).
		Update()

	if err != nil {
		glog.Error(ctx, "更新投放计划失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	_, err = g.DB().Model("plan_relate_rels").Where("plan_id", plan.ID).Delete()
	if err != nil {
		glog.Error(ctx, "删除投放计划关联失败", g.Map{
			"error": err.Error(),
		})
		return err
	}
	if len(plan.Rels) > 0 {
		_, err = g.DB().Model("plan_relate_rels").Data(plan.Rels).Insert()
		if err != nil {
			glog.Error(ctx, "投放计划与其他关联失败", g.Map{
				"error": err.Error(),
			})
			return err
		}
	}

	return nil
}

// Delete 删除投放计划
func (s *adSlotPlanService) Delete(ctx context.Context, id uint64) error {
	glog.Info(ctx, "开始删除投放计划", g.Map{
		"id": id,
	})

	_, err := g.DB().Model("ad_slot_plans").Where("id", id).Delete()
	if err != nil {
		glog.Error(ctx, "删除投放计划失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	return nil
}

// Approve 审核通过
func (s *adSlotPlanService) Approve(ctx context.Context, id uint64, userID uint64) error {
	glog.Info(ctx, "开始审核通过投放计划", g.Map{
		"id":      id,
		"user_id": userID,
	})

	// 开启事务
	err := g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 获取计划信息
		var plan model.AdSlotPlan
		err := tx.Model("ad_slot_plans").Where("id", id).Scan(&plan)
		if err != nil {
			return err
		}

		if plan.ID == 0 {
			return fmt.Errorf("plan not found: %d", id)
		}

		// 检查状态
		if plan.AuditStatus != constants.PLAN_AUDIT_STATUS_PENDING {
			return fmt.Errorf("invalid audit status: %s", plan.AuditStatus)
		}

		now := time.Now()

		// 更新审核状态
		_, err = tx.Model("ad_slot_plans").Data(g.Map{
			"audit_status":    constants.PLAN_AUDIT_STATUS_APPROVED,
			"delivery_status": constants.PLAN_DELIVERY_STATUS_CONFIGURING,
			"updated_at":      now,
		}).Where("id", id).Update()
		if err != nil {
			return err
		}

		// 记录操作日志
		operationParams, _ := json.Marshal(g.Map{
			"old_status": constants.PLAN_AUDIT_STATUS_PENDING,
			"new_status": constants.PLAN_AUDIT_STATUS_APPROVED,
		})

		_, err = tx.Model("ad_slot_plan_operation_logs").Insert(g.Map{
			"plan_id":          id,
			"user_id":          userID,
			"action":           constants.PLAN_ACTION_APPROVE,
			"operation_desc":   "审核通过",
			"operation_params": operationParams,
			"old_status":       constants.PLAN_AUDIT_STATUS_PENDING,
			"new_status":       constants.PLAN_AUDIT_STATUS_APPROVED,
			"remark":           "审核通过",
			"created_at":       now,
			"updated_at":       now,
		})
		return err
	})

	if err != nil {
		glog.Error(ctx, "审核通过投放计划失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	return nil
}

// Reject 审核拒绝
func (s *adSlotPlanService) Reject(ctx context.Context, id uint64, userID uint64, reason string) error {
	glog.Info(ctx, "开始审核拒绝投放计划", g.Map{
		"id":      id,
		"user_id": userID,
		"reason":  reason,
	})

	// 开启事务
	err := g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 获取计划信息
		var plan model.AdSlotPlan
		err := tx.Model("ad_slot_plans").Where("id", id).Scan(&plan)
		if err != nil {
			return err
		}

		if plan.ID == 0 {
			return fmt.Errorf("plan not found: %d", id)
		}

		// 检查状态
		if plan.AuditStatus != constants.PLAN_AUDIT_STATUS_PENDING {
			return fmt.Errorf("invalid audit status: %s", plan.AuditStatus)
		}

		now := time.Now()

		// 更新计划
		_, err = tx.Model("ad_slot_plans").Data(g.Map{
			"audit_status":  constants.PLAN_AUDIT_STATUS_REJECTED,
			"reject_reason": reason,
			"updated_at":    now,
		}).Where("id", id).Update()
		if err != nil {
			return err
		}

		// 记录操作日志
		operationParams, _ := json.Marshal(g.Map{
			"old_status": constants.PLAN_AUDIT_STATUS_PENDING,
			"new_status": constants.PLAN_AUDIT_STATUS_REJECTED,
			"reason":     reason,
		})

		_, err = tx.Model("ad_slot_plan_operation_logs").Insert(g.Map{
			"plan_id":          id,
			"user_id":          userID,
			"action":           constants.PLAN_ACTION_REJECT,
			"operation_desc":   "审核拒绝",
			"operation_params": operationParams,
			"old_status":       constants.PLAN_AUDIT_STATUS_PENDING,
			"new_status":       constants.PLAN_AUDIT_STATUS_REJECTED,
			"remark":           fmt.Sprintf("审核拒绝：%s", reason),
			"created_at":       now,
			"updated_at":       now,
		})
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		glog.Error(ctx, "审核拒绝投放计划失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	return nil
}

// UpdateDeliveryMode 更新投放策略
func (s *adSlotPlanService) UpdateDeliveryMode(ctx context.Context, id uint64, mode int, userID uint64) error {
	glog.Info(ctx, "开始更新投放策略", g.Map{
		"id":      id,
		"mode":    mode,
		"user_id": userID,
	})

	// 开启事务
	err := g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		// 获取计划信息
		var plan model.AdSlotPlan
		err := tx.Model("ad_slot_plans").Where("id", id).Scan(&plan)
		if err != nil {
			return err
		}

		if plan.ID == 0 {
			return fmt.Errorf("plan not found: %d", id)
		}

		// 检查状态
		if plan.DeliveryStatus == constants.PLAN_DELIVERY_STATUS_COMPLETED {
			return fmt.Errorf("plan already completed")
		}

		now := time.Now()

		// 更新计划
		_, err = tx.Model("ad_slot_plans").Data(g.Map{
			"delivery_mode": mode,
			"updated_at":    now,
		}).Where("id", id).Update()
		if err != nil {
			return err
		}

		// 记录操作日志
		operationParams, _ := json.Marshal(g.Map{
			"old_mode": plan.DeliveryMode,
			"new_mode": mode,
		})

		_, err = tx.Model("ad_slot_plan_operation_logs").Insert(g.Map{
			"plan_id":          id,
			"user_id":          userID,
			"action":           constants.PLAN_ACTION_UPDATE_DELIVERY_MODE,
			"operation_desc":   "更新投放策略",
			"operation_params": operationParams,
			"old_status":       plan.DeliveryMode,
			"new_status":       mode,
			"remark": fmt.Sprintf("更新投放策略：%s -> %s",
				constants.PlanDeliveryModeMap[plan.DeliveryMode],
				constants.PlanDeliveryModeMap[mode]),
			"created_at": now,
			"updated_at": now,
		})
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		glog.Error(ctx, "更新投放策略失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	return nil
}

// GeneratePromotionLink 生成推广链接
func (s *adSlotPlanService) GeneratePromotionLink(ctx context.Context, id uint64, productID uint64) error {
	glog.Info(ctx, "开始生成推广链接", g.Map{
		"id":         id,
		"product_id": productID,
	})

	// 获取计划信息
	var plan model.AdSlotPlan
	err := g.DB().Model("ad_slot_plans").Where("id", id).Scan(&plan)
	if err != nil {
		glog.Error(ctx, "获取计划信息失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	if plan.ID == 0 {
		return fmt.Errorf("plan not found: %d", id)
	}

	if productID == 0 {
		productID = plan.AdProductID
	}

	// 构建请求参数
	reqData := g.Map{
		"plan_id":    id,
		"product_id": productID,
	}

	// 根据环境选择不同的域名
	baseURL := "http://adpro.biyingniao.com"
	// 发送请求
	reqURL := fmt.Sprintf("%s/api/promotion/link", baseURL)
	r, err := g.Client().Post(ctx, reqURL, reqData)
	if err != nil {
		glog.Error(ctx, "调用推广链接生成接口失败", g.Map{
			"error": err.Error(),
			"url":   reqURL,
			"data":  reqData,
		})
		return err
	}
	defer r.Close()

	// 解析响应为通用格式
	var resp struct {
		Code    int                    `json:"code"`
		Message string                 `json:"message"`
		Data    map[string]interface{} `json:"data"`
	}

	if err := json.NewDecoder(r.Body).Decode(&resp); err != nil {
		glog.Error(ctx, "解析推广链接响应失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	if resp.Code != 0 {
		return fmt.Errorf("generate promotion link failed: %s", resp.Message)
	}

	if resp.Data == nil {
		return fmt.Errorf("invalid response data: empty data")
	}

	// 更新计划的推广链接信息
	if productID == 0 {
		_, err = g.DB().Model("ad_slot_plans").
			Where("id", id).
			Data(g.Map{
				"promotion_link":   resp.Data["promotion_link"],
				"promotion_qrcode": resp.Data["promotion_qrcode"],
				"promotion_params": resp.Data["promotion_params"],
				"updated_at":       time.Now(),
			}).
			Update()

		if err != nil {
			glog.Error(ctx, "更新推广链接失败", g.Map{
				"error": err.Error(),
			})
			return err
		}

	}

	return nil
}

// GenerateShortUrl 生成短链接
func (s *adSlotPlanService) GenerateShortUrl(ctx context.Context, id uint64) error {
	glog.Info(ctx, "开始生成短链接", g.Map{
		"id": id,
	})

	// 获取计划信息
	var plan model.AdSlotPlan
	err := g.DB().Model("ad_slot_plans").Where("id", id).Scan(&plan)
	if err != nil {
		glog.Error(ctx, "获取计划信息失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	if plan.ID == 0 {
		return fmt.Errorf("plan not found: %d", id)
	}

	if plan.PromotionLink == "" {
		return fmt.Errorf("promotion link is empty")
	}

	// TODO: 调用短链接生成服务
	shortUrl := "http://example.com/xxx" // 这里需要替换为实际的短链接生成逻辑

	// 更新计划
	_, err = g.DB().Model("ad_slot_plans").Data(g.Map{
		"short_url":  shortUrl,
		"updated_at": time.Now(),
	}).Where("id", id).Update()

	if err != nil {
		glog.Error(ctx, "更新短链接失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	return nil
}

// UpdatePromotionLink 更新推广链接
func (s *adSlotPlanService) UpdatePromotionLink(ctx context.Context, id uint64, link string, userID uint64) error {
	glog.Info(ctx, "开始更新推广链接", g.Map{
		"id":      id,
		"link":    link,
		"user_id": userID,
	})

	// 获取当前计划
	plan, err := s.Get(ctx, id)
	if err != nil {
		return err
	}

	// 验证审核状态
	if plan.AuditStatus != constants.PLAN_AUDIT_STATUS_APPROVED {
		return gerror.New("只有审核通过的计划才能更新推广链接")
	}

	// 从上下文获取用户信息
	userValue := g.RequestFromCtx(ctx).GetCtxVar("user")
	if userValue.IsNil() {
		return gerror.New("user not found in context")
	}

	user := new(model.User)
	if err := userValue.Scan(user); err != nil {
		glog.Error(ctx, "解析用户信息失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	// 验证权限
	if !user.IsAdmin() && plan.UserId != uint64(user.Id) {
		return gerror.New("permission denied: only creator or admin can update promotion link")
	}

	// 更新推广链接
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err = tx.Model("ad_slot_plans").Where("id", id).Update(g.Map{
			"promotion_link": link,
		})
		return err
	})
	if err != nil {
		glog.Error(ctx, "更新推广链接失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	// 记录操作日志
	operationParams, _ := json.Marshal(g.Map{
		"old_link": plan.PromotionLink,
		"new_link": link,
	})

	_, err = g.DB().Model("ad_slot_plan_operation_logs").Insert(g.Map{
		"plan_id":          id,
		"user_id":          userID,
		"action":           constants.PLAN_ACTION_UPDATE_STATUS,
		"operation_desc":   "更新推广链接",
		"operation_params": operationParams,
		"old_status":       plan.DeliveryStatus,
		"new_status":       plan.DeliveryStatus,
		"remark":           "手动更新推广链接",
		"created_at":       time.Now(),
		"updated_at":       time.Now(),
	})
	if err != nil {
		glog.Error(ctx, "记录操作日志失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	return nil
}

// UpdateShortUrl 更新短链接
func (s *adSlotPlanService) UpdateShortUrl(ctx context.Context, id uint64, url string, userID uint64) error {
	glog.Info(ctx, "开始更新短链接", g.Map{
		"id":      id,
		"url":     url,
		"user_id": userID,
	})

	// 获取当前计划
	plan, err := s.Get(ctx, id)
	if err != nil {
		return err
	}

	// 验证审核状态
	if plan.AuditStatus != constants.PLAN_AUDIT_STATUS_APPROVED {
		return gerror.New("只有审核通过的计划才能更新短链接")
	}

	// 从上下文获取用户信息
	userValue := g.RequestFromCtx(ctx).GetCtxVar("user")
	if userValue.IsNil() {
		return gerror.New("user not found in context")
	}

	user := new(model.User)
	if err := userValue.Scan(user); err != nil {
		glog.Error(ctx, "解析用户信息失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	// 验证权限
	if !user.IsAdmin() && plan.UserId != uint64(user.Id) {
		return gerror.New("permission denied: only creator or admin can update short url")
	}

	// 更新短链接
	err = g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err = tx.Model("ad_slot_plans").Where("id", id).Update(g.Map{
			"short_url": url,
		})
		return err
	})
	if err != nil {
		glog.Error(ctx, "更新短链接失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	// 记录操作日志
	operationParams, _ := json.Marshal(g.Map{
		"old_url": plan.ShortUrl,
		"new_url": url,
	})

	_, err = g.DB().Model("ad_slot_plan_operation_logs").Insert(g.Map{
		"plan_id":          id,
		"user_id":          userID,
		"action":           constants.PLAN_ACTION_UPDATE_STATUS,
		"operation_desc":   "更新短链接",
		"operation_params": operationParams,
		"old_status":       plan.DeliveryStatus,
		"new_status":       plan.DeliveryStatus,
		"remark":           "手动更新短链接",
		"created_at":       time.Now(),
		"updated_at":       time.Now(),
	})
	if err != nil {
		glog.Error(ctx, "记录操作日志失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	return nil
}

// GetPlansByMediaAccountId 获取媒体账号下的投放计划列表
func (s *adSlotPlanService) GetPlansByMediaAccountId(ctx context.Context, mediaAccountId uint64) ([]*model.AdSlotPlan, error) {
	glog.Info(ctx, "开始获取媒体账号下的投放计划列表", g.Map{
		"mediaAccountId": mediaAccountId,
	})

	var plans []*model.AdSlotPlan
	err := g.DB().Model("ad_slot_plans").
		Where("media_id = ?", mediaAccountId).
		Scan(&plans)
	if err != nil {
		glog.Error(ctx, "获取媒体账号下的投放计划列表失败", g.Map{
			"error": err.Error(),
		})
		return nil, err
	}
	return plans, nil
}

// GetPlansByMediaAccountIds 批量获取多个媒体账号下的投放计划列表
func (s *adSlotPlanService) GetPlansByMediaAccountIds(ctx context.Context, mediaAccountIds []uint64) ([]*model.AdSlotPlan, error) {
	glog.Info(ctx, "开始批量获取媒体账号下的投放计划列表", g.Map{
		"mediaAccountIds": mediaAccountIds,
	})

	var plans []*model.AdSlotPlan
	err := g.DB().Model("ad_slot_plans").
		Where("media_id IN(?) AND deleted_at IS NULL", mediaAccountIds).
		Scan(&plans)
	if err != nil {
		glog.Error(ctx, "批量获取媒体账号下的投放计划列表失败", g.Map{
			"error": err.Error(),
		})
		return nil, err
	}
	return plans, nil
}

// UpdateMergeLinks 更新融合链接
func (s *adSlotPlanService) UpdateMergeLinks(ctx context.Context, id uint64, mergeLinks interface{}) error {
	glog.Info(ctx, "开始更新融合链接", g.Map{
		"id":         id,
		"mergeLinks": mergeLinks,
	})

	// 判断计划是否存在并获取其code
	plan, err := s.Get(ctx, id)
	if err != nil {
		glog.Error(ctx, "获取投放计划失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	// 获取计划编号用于清除缓存
	planCode := plan.Code

	// 将MergeLinks转换为JSON
	mergeLinksJson, err := json.Marshal(mergeLinks)
	if err != nil {
		glog.Error(ctx, "MergeLinks转JSON失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	// 更新merge_links字段
	_, err = g.DB().Model("ad_slot_plans").
		Where("id", id).
		Data(g.Map{
			"merge_links": mergeLinksJson,
			"updated_at":  time.Now(),
		}).
		Update()

	if err != nil {
		glog.Error(ctx, "更新融合链接失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	// 清除缓存
	cacheKey := fmt.Sprintf("merge_links:%s", planCode)
	g.DB().GetCache().Remove(ctx, cacheKey)
	glog.Info(ctx, "已清除融合链接缓存", g.Map{
		"code":      planCode,
		"cache_key": cacheKey,
	})

	glog.Info(ctx, "更新融合链接成功", g.Map{
		"id": id,
	})

	return nil
}

// GetMergeLinksByCode 根据编号获取融合链接
func (s *adSlotPlanService) GetMergeLinksByCode(ctx context.Context, code string) (interface{}, error) {
	glog.Info(ctx, "获取融合链接信息", g.Map{
		"code": code,
	})

	// 根据code前缀判断数据来源
	prefix := code[0:1]

	// 从内存缓存中获取
	cacheKey := fmt.Sprintf("merge_links:%s", code)
	value, err := g.DB().GetCache().GetOrSetFuncLock(ctx, cacheKey, func(ctx context.Context) (interface{}, error) {
		// 根据前缀选择不同的数据源
		if prefix == "P" {
			// 原有逻辑 - 从ad_slot_plans获取
			var plan entity.AdSlotPlan
			err := g.DB().Model("ad_slot_plans").Where("code", code).Fields("merge_links").Scan(&plan)
			if err != nil {
				glog.Error(ctx, "获取融合链接失败", g.Map{
					"error": err.Error(),
				})
				return nil, err
			}

			if len(plan.MergeLinks) == 0 {
				return nil, fmt.Errorf("merge links not found for code: %s", code)
			}

			// 解析merge_links字段
			var mergeLinks interface{}
			err = json.Unmarshal(plan.MergeLinks, &mergeLinks)
			if err != nil {
				glog.Error(ctx, "解析融合链接失败", g.Map{
					"error": err.Error(),
				})
				return nil, err
			}

			return mergeLinks, nil
		} else if prefix == "J" || prefix == "L" {
			// 从promotion_pages获取
			var page struct {
				URL1   string `json:"url1"`
				URL2   string `json:"url2"`
				Qrcode string `json:"qrcode"`
				Status int    `json:"status"`
			}

			err := g.DB().Model("byn_data.promotion_pages").
				Where("code", code).
				Where("status", 1).
				Fields("url1,url2,qrcode,status").
				Scan(&page)

			if err != nil {
				glog.Error(ctx, "获取推广页面失败", g.Map{
					"error": err.Error(),
				})
				return nil, err
			}

			if page.Status != 1 {
				return nil, fmt.Errorf("promotion page not active for code: %s", code)
			}

			// 构造与merge_links相同格式的返回数据
			mergeLinks := map[string]interface{}{
				"url1":   page.URL1,
				"url2":   page.URL2,
				"qrcode": page.Qrcode,
			}

			return mergeLinks, nil
		}

		return nil, fmt.Errorf("invalid code prefix: %s", prefix)
	}, 3600*time.Second)

	if err != nil {
		return nil, err
	}

	return value, nil
}

// SendSettleProfitDingTalk 发送结算佣金钉钉通知
func (s *adSlotPlanService) SendSettleProfitDingTalk(ctx context.Context, planIds []uint64) error {
	glog.Info(ctx, "开始发送结算佣金钉钉通知", g.Map{
		"plan_ids": planIds,
	})

	// 查询计划信息
	var plans []model.AdSlotPlan
	err := g.DB().Model("ad_slot_plans").
		Where("id IN(?)", planIds).
		Fields("id, code").
		Scan(&plans)
	if err != nil {
		glog.Error(ctx, "获取计划信息失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	// 查询结算佣金信息
	var stats []gdb.Record
	err = g.DB().Model("ad_slot_plan_daily_stats").
		Where("plan_id IN(?) AND date = DATE_FORMAT(CURDATE(), '%Y-%m-%d')", planIds).
		Fields("plan_id, SUM(bd_settle_profit) as total_profit").
		Group("plan_id").
		Scan(&stats)
	if err != nil {
		glog.Error(ctx, "获取结算佣金信息失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	// 构建计划ID到结算佣金的映射
	profitMap := make(map[uint64]float64)
	for _, stat := range stats {
		planId := stat["plan_id"].Uint64()
		totalProfit := stat["total_profit"].Float64()
		profitMap[planId] = totalProfit
	}

	// 构建通知消息
	var messageBuilder strings.Builder
	for _, plan := range plans {
		profit := profitMap[plan.ID]
		messageBuilder.WriteString(fmt.Sprintf("%s，结算佣金：%.2f元\n", plan.Code, profit))
	}

	// 构建钉钉请求，正式
	webhook := "https://oapi.dingtalk.com/robot/send?access_token=7667a0baac31e4dc7f4573deb494cf4e415ef52d847ca3365f76357453f90a1b"
	secret := "SEC8d658796a2f21c8cb184e89f5df3918d72baab2bdd59247ee8ab5c4ffdf8d9c5"
	// 构建钉钉请求，测试
	// webhook := "https://oapi.dingtalk.com/robot/send?access_token=8fe94c3cba9ca877b17a151c0b4255db39aedd5616c8920984a255f9b195b34f"
	// secret := "SEC23ee56c51b0ad86d7778c3608948b28bb47f6e367e5e3923983fc20eb290a069"

	// 计算签名
	timestamp := time.Now().UnixMilli()
	// 按照文档要求，签名原始字符串是"timestamp\nsecret"，注意是时间戳+换行符+密钥
	stringToSign := fmt.Sprintf("%d\n%s", timestamp, secret)

	// 使用 HmacSHA256 算法计算签名
	mac := hmac.New(sha256.New, []byte(secret))
	mac.Write([]byte(stringToSign))
	sign := url.QueryEscape(base64.StdEncoding.EncodeToString(mac.Sum(nil)))

	// 构建请求URL
	webhookURL := fmt.Sprintf("%s&timestamp=%d&sign=%s", webhook, timestamp, sign)

	// 构建请求体
	reqBody := g.Map{
		"msgtype": "text",
		"text": g.Map{
			"content": messageBuilder.String(),
		},
	}

	// 发送请求
	client := g.Client()
	resp, err := client.Header(map[string]string{
		"Content-Type": "application/json",
	}).Post(ctx, webhookURL, reqBody)
	if err != nil {
		glog.Error(ctx, "发送钉钉通知失败", g.Map{
			"error": err.Error(),
		})
		return err
	}
	defer resp.Close()

	// 解析响应
	var result struct {
		Errcode int    `json:"errcode"`
		Errmsg  string `json:"errmsg"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		glog.Error(ctx, "解析钉钉响应失败", g.Map{
			"error": err.Error(),
		})
		return err
	}

	if result.Errcode != 0 {
		return fmt.Errorf("钉钉通知发送失败: %s", result.Errmsg)
	}

	glog.Info(ctx, "结算佣金钉钉通知发送成功", g.Map{
		"plan_ids": planIds,
	})

	return nil
}

func (s *adSlotPlanService) GetPlatformObjectData(ctx context.Context, in *v1.GetPlatformObjectDataReq) (any, error) {
	var (
		err  error
		data any
	)
	switch in.Type {
	case 1: // 计划
		var list []struct {
			Id   int64  `json:"id"`
			Name string `json:"name"`
		}
		err = g.DB().Model("platform_plans").Scan(&list)
		data = list
	case 2: // 口令
		var list []struct {
			Id   int64  `json:"id"`
			Name string `json:"name"`
		}
		err = g.DB().Model("ad_search_words").Scan(&list)
		data = list
	default:
		err = fmt.Errorf("invalid type: %d", in.Type)
	}
	return data, err
}

type PlanStats struct {
	MediaId      int64  `json:"media_id"`
	MediaName    string `json:"media_name"`
	PlanId       int64  `json:"plan_id"`
	PlanName     string `json:"plan_name"`
	MaterialType string `json:"material_type"`
	Day          string `json:"day"`
	// 广告
	AdImpression int64   `json:"ad_impression"`  // 曝光量
	AdClick      int64   `json:"ad_click"`       // 点击量
	AdCost       float64 `json:"ad_cost"`        // 广告消耗
	AdActualCost float64 `json:"ad_actual_cost"` // 实际消耗，计算：AdCost * 返点
	AdCtr        float64 `json:"ad_ctr"`         // CTR，计算：AdClick / AdImpression
	AdCpc        float64 `json:"ad_cpc"`         // CPC单价，计算：AdCost / AdClick
	//cps
	CpsUv              int64   `json:"cps_uv"`               // 锁佣UV
	CpsUvPrice         float64 `json:"cps_uv_price"`         // uv价值，计算：CpsOrderCommission / CpsUv
	CpsUvCost          float64 `json:"cps_uv_cost"`          // uv成本，计算：AdActualCost / CpsUv
	CpsClickTransRate  float64 `json:"cps_click_trans_rate"` // 点击转化率，计算：CpsUv / AdClick
	CpsOrderNum        int64   `json:"cps_order_num"`        // 订单数
	CpsOrderCommission float64 `json:"cps_order_commission"` // 订单佣金
	CpsRoi             float64 `json:"cps_roi"`              // 锁佣回收（roi），计算：CpsOrderCommission / AdActualCost
}

type PlanAllStats struct {
	Items []*PlanStats `json:"items"`
	// 口令
	PwdUv              int64   `json:"pwd_uv"`
	PwdSearchTransRate float64 `json:"pwd_search_trans_rate"` // 搜索转化率，计算：PwdUv / sum(Items.AdClick)
	PwdOrderNum        int64   `json:"pwd_order_num"`         // 订单数
	PwdOrderCommission float64 `json:"pwd_order_commission"`  // 订单佣金
	PwdRoi             float64 `json:"pwd_roi"`               // 口令回收（roi），计算：PwdOrderCommission / sum(Items.AdActualCost)
	// 汇总
	StatsCommission      float64 `json:"stats_commission"`       // 当日总佣金（cps+口令），计算：PwdOrderCommission + sum(Items.CpsOrderCommission)
	StatsRecycle         float64 `json:"stats_recycle"`          // 当日总回收，计算：StatsCommission / sum(Items.AdActualCost)
	StatsCost            float64 `json:"stats_cost"`             // 实际总消耗
	StatsTotalCommission float64 `json:"stats_total_commission"` // 总佣金
	StatsTotalRecycle    float64 `json:"stats_total_recycle"`    // 总回收，计算：StatsTotalCommission / StatsCost
	// 当日数据
	TotalAdActualCost       float64 `json:"total_ad_actual_cost"`       // 实际消耗，计算：sum(Items.AdActualCost)
	TotalAdCost             float64 `json:"total_ad_cost"`              // 广告消耗，计算：sum(Items.AdCost)
	TotalAdImpression       int64   `json:"total_ad_impression"`        // 曝光量，计算：sum(Items.AdImpression)
	TotalAdClick            int64   `json:"total_ad_click"`             // 点击量，计算：sum(Items.AdClick)
	TotalCtr                float64 `json:"total_ctr"`                  // CTR，计算：TotalAdClick / TotalAdImpression
	TotalCpsOrderCommission float64 `json:"total_cps_order_commission"` // CPS(总佣金)，计算：sum(Items.CpsOrderCommission)
	TotalCpsOrderNum        int64   `json:"total_cps_order_num"`        // CPS(订单)，计算：sum(Items.CpsOrderNum)
	TotalCpsRol             float64 `json:"total_cps_rol"`              // CPS（roi），计算：TotalCpsOrderCommission / TotalAdActualCost
}

func (s *adSlotPlanService) GeneratePlanReportData(ctx context.Context, start, end time.Time) ([]*PlanAllStats, error) {
	if end.Unix() > start.Unix()+86400*7 {
		return nil, fmt.Errorf("时间范围不能超过7天")
	}
	var plans []*model.AdSlotPlan
	err := g.DB().Model("ad_slot_plans").Scan(&plans)
	if err != nil {
		return nil, fmt.Errorf("获取计划失败: %s", err.Error())
	}
	res := make([]*PlanAllStats, 0)
	if len(plans) == 0 {
		return res, nil
	}

	materialTypeMap := map[int8]string{
		1: "图文",
		2: "视频",
	}
	planIds, planMap, planMediaMap, pids, planPidMap, pidPlanIdsMap := make([]int64, 0), make(map[int64]*model.AdSlotPlan), make(map[int64]*model.Media), make([]string, 0), make(map[int64]string), make(map[string][]int64)
	{
		mediaIds := make([]int64, 0)
		for _, plan := range plans {
			planIds = append(planIds, int64(plan.ID))
			planMap[int64(plan.ID)] = plan
			mediaIds = append(mediaIds, int64(plan.MediaId))
			planMediaMap[int64(plan.ID)] = &model.Media{
				Id: int(plan.MediaId),
			}
		}
		if len(mediaIds) > 0 {
			var mediaList []*model.Media
			err = g.DB().Model("ad_media").Where("id IN(?)", mediaIds).Scan(&mediaList)
			if err != nil {
				return nil, fmt.Errorf("获取媒体失败: %s", err.Error())
			}
			for id, info := range planMediaMap {
				for _, media := range mediaList {
					if media.Id == info.Id {
						planMediaMap[id] = media
						break
					}
				}
			}
		}

		var rels []*entity.PlanRelateRels
		err = g.DB().Model("plan_relate_rels").Where("plan_id IN ? and relate_type = 2", planIds).Scan(&rels)
		if err != nil {
			return nil, fmt.Errorf("获取关联失败: %s", err.Error())
		}
		planPwdMap, pwdIds := make(map[int64]int64), make([]int64, 0)
		for _, rel := range rels {
			if _, ok := planPwdMap[rel.PlanId]; !ok {
				planPwdMap[rel.PlanId] = rel.RelateId
				pwdIds = append(pwdIds, rel.RelateId)
			}
		}
		if len(pwdIds) > 0 {
			var pwds []*AdSearchWord
			err = g.DB().Model("ad_search_words").Where("id IN ?", pwdIds).Scan(&pwds)
			if err != nil {
				return nil, fmt.Errorf("获取口令失败: %s", err.Error())
			}
			for _, pwd := range pwds {
				pids = append(pids, pwd.Pid)
			}
			for id1, id2 := range planPwdMap {
				for _, pwd := range pwds {
					if id2 == pwd.Id {
						planPidMap[id1] = pwd.Pid
						break
					}
				}
			}
			for id1, id2 := range planPidMap {
				if _, ok := pidPlanIdsMap[id2]; !ok {
					pidPlanIdsMap[id2] = make([]int64, 0)
				}
				pidPlanIdsMap[id2] = append(pidPlanIdsMap[id2], id1)
			}
		}
	}

	planDayStatsMap := make(map[string]*PlanStats)
	{
		var adStats []*struct {
			PlanId     int64  `json:"plan_id"`
			BizDate    string `json:"biz_date"`
			Impression int64  `json:"impression"`
			Click      int64  `json:"click"`
			Cost       int64  `json:"cost"`
		}
		adSql := `
select plan_id, biz_date, sum(impression) impression, sum(click) click, sum(cost) cost 
from (
	select a.impression, a.click, a.cost, a,biz_date, b.plan_id 
	from platform_reports a 
	left join plan_relate_rels b on b.relate_type = 1 and b.relate_id = b.belong_id 
	where a.biz_date between ? and ? and a.belong_type = "plan" and b.plan_id in ?
) t 
group by plan_id, biz_date
`
		adStatsErr := g.DB().Ctx(ctx).Raw(adSql, start.Format("20060102"), end.Format("20060102"), planIds).Scan(&adStats)
		if adStatsErr != nil {
			return nil, fmt.Errorf("获取广告统计失败: %s", adStatsErr.Error())
		}
		for _, stat := range adStats {
			day := fmt.Sprintf("%s-%s-%s", stat.BizDate[0:5], stat.BizDate[4:6], stat.BizDate[6:])
			key := fmt.Sprintf("%s_%d", day, stat.PlanId)
			if _, ok := planDayStatsMap[key]; !ok {
				planDayStatsMap[key] = &PlanStats{
					MediaId:      int64(planMediaMap[stat.PlanId].Id),
					MediaName:    planMediaMap[stat.PlanId].Name,
					PlanId:       stat.PlanId,
					PlanName:     planMap[stat.PlanId].Code,
					MaterialType: materialTypeMap[planMap[stat.PlanId].MaterialType],
					Day:          day,
				}
			}
			planDayStatsMap[key].AdImpression = stat.Impression
			planDayStatsMap[key].AdClick = stat.Click
			planDayStatsMap[key].AdCost = pkg.Fen2Yuan(stat.Cost)
			planDayStatsMap[key].AdActualCost = planDayStatsMap[key].AdCost * planMediaMap[stat.PlanId].ReturnRate / 100
			planDayStatsMap[key].AdCtr = float64(planDayStatsMap[key].AdClick) / float64(planDayStatsMap[key].AdImpression)
			planDayStatsMap[key].AdCpc = planDayStatsMap[key].AdCost / float64(planDayStatsMap[key].AdClick)
		}
	}

	{
		var cpsStats []*struct {
			PlanId     int64   `json:"plan_id"`
			Date       string  `json:"date"`
			Uv         int64   `json:"uv"`
			Num        int64   `json:"num"`
			Commission float64 `json:"commission"`
		}
		cpsStatsErr := g.DB().Ctx(ctx).Raw("select plan_id, date, elm_click_uv uv, admin_revenue commission, admin_orders num from ad_slot_plan_daily_stats where date between ? and ? and plan_id in ?", start.Format(time.DateOnly), end.Format(time.DateOnly), planIds).Scan(&cpsStats)
		if cpsStatsErr != nil {
			return nil, fmt.Errorf("获取Cps统计失败: %s", cpsStatsErr.Error())
		}
		for _, stat := range cpsStats {
			key := fmt.Sprintf("%s_%d", stat.Date, stat.PlanId)
			if _, ok := planDayStatsMap[key]; !ok {
				planDayStatsMap[key] = &PlanStats{
					MediaId:      int64(planMediaMap[stat.PlanId].Id),
					MediaName:    planMediaMap[stat.PlanId].Name,
					PlanId:       stat.PlanId,
					PlanName:     planMap[stat.PlanId].Code,
					MaterialType: materialTypeMap[planMap[stat.PlanId].MaterialType],
					Day:          stat.Date,
				}
			}
			planDayStatsMap[key].CpsUv = stat.Uv
			planDayStatsMap[key].CpsOrderNum = stat.Num                                                       // 订单数
			planDayStatsMap[key].CpsOrderCommission = stat.Commission                                         // 订单佣金
			planDayStatsMap[key].CpsUvPrice = stat.Commission / float64(stat.Uv)                              // uv价值，计算：CpsOrderCommission / CpsUv
			planDayStatsMap[key].CpsUvCost = planDayStatsMap[key].AdActualCost / float64(stat.Uv)             // uv成本，计算：AdActualCost / CpsUv
			planDayStatsMap[key].CpsClickTransRate = float64(stat.Uv) / float64(planDayStatsMap[key].AdClick) // 点击转化率，计算：CpsUv / AdClick
			planDayStatsMap[key].CpsRoi = stat.Commission / planDayStatsMap[key].AdActualCost                 // 锁佣回收（roi），计算：CpsOrderCommission / AdActualCost
		}
	}

	pwdDayStatsMap := make(map[string]*AdSearchWordDailyStats)
	{
		var pwdStats []*AdSearchWordDailyStats
		err = g.DB().Ctx(ctx).Raw("select * from ad_search_word_daily_stats where date between ? and ? and pid in ?", start.Format(time.DateOnly), end.Format(time.DateOnly), pids).Scan(&pwdStats)
		if err != nil {
			return nil, fmt.Errorf("获取口令统计失败: %s", err.Error())
		}
		for _, stat := range pwdStats {
			pwdDayStatsMap[fmt.Sprintf("%s_%s", stat.Date, stat.Pid)] = stat
		}
	}

	for i := 0; ; i++ {
		day := start.Add(time.Duration(i*24) * time.Hour)
		if day.Unix() > end.Unix() {
			break
		}
		item := &PlanAllStats{
			Items: make([]*PlanStats, 0),
		}

		// 口令组的方式
		for fPid, fPlanIds := range pidPlanIdsMap {
			for _, planId := range fPlanIds {
				if pidInfo, ok := planDayStatsMap[fmt.Sprintf("%s_%d", day.Format(time.DateOnly), planId)]; ok {
					item.Items = append(item.Items, pidInfo)
				} else {
					item.Items = append(item.Items, &PlanStats{
						MediaId:      int64(planMediaMap[planId].Id),
						MediaName:    planMediaMap[planId].Name,
						PlanId:       planId,
						PlanName:     planMap[planId].Code,
						MaterialType: materialTypeMap[planMap[planId].MaterialType],
						Day:          day.Format(time.DateOnly),
					})
				}
			}
			if info, ok := pwdDayStatsMap[fmt.Sprintf("%s_%s", day.Format(time.DateOnly), fPid)]; ok {
				item.PwdUv = info.ClickUv
				item.PwdOrderNum = info.ActualOrderNum
				item.PwdOrderCommission = info.ActualSettle
			}
		}

		// 非口令组的方式
		for _, planId := range planIds {
			if _, ok1 := planPidMap[planId]; !ok1 {
				if pidInfo, ok2 := planDayStatsMap[fmt.Sprintf("%s_%d", day.Format(time.DateOnly), planId)]; ok2 {
					item.Items = append(item.Items, pidInfo)
				} else {
					item.Items = append(item.Items, &PlanStats{
						MediaId:      int64(planMediaMap[planId].Id),
						MediaName:    planMediaMap[planId].Name,
						PlanId:       planId,
						PlanName:     planMap[planId].Code,
						MaterialType: materialTypeMap[planMap[planId].MaterialType],
						Day:          day.Format(time.DateOnly),
					})
				}
			}
		}

		// 各项指标计算
		sumItemAdImpression, sumItemAdClick, sumItemAdCost, sumItemAdActualCost, sumItemCpsOrderCommission, sumItemCpsOrderNum := int64(0), int64(0), float64(0), float64(0), float64(0), int64(0)
		for _, pItem := range item.Items {
			sumItemAdImpression += pItem.AdImpression
			sumItemAdClick += pItem.AdClick
			sumItemAdCost += pItem.AdCost
			sumItemAdActualCost += pItem.AdActualCost
			sumItemCpsOrderCommission += pItem.CpsOrderCommission
			sumItemCpsOrderNum += pItem.CpsOrderNum
		}
		item.PwdSearchTransRate = float64(item.PwdUv) / float64(sumItemAdClick)      // 搜索转化率，计算：PwdUv / sum(Items.AdClick)
		item.PwdRoi = item.PwdOrderCommission / sumItemAdActualCost                  // 口令回收（roi），计算：PwdOrderCommission / sum(Items.AdActualCost)
		item.StatsCommission = item.PwdOrderCommission + sumItemCpsOrderCommission   // 当日总佣金（cps+口令），计算：PwdOrderCommission + sum(Items.CpsOrderCommission)
		item.StatsRecycle = item.StatsCommission / sumItemAdActualCost               // 当日总回收，计算：StatsCommission / sum(Items.AdActualCost)
		item.StatsCost = 0                                                           // TODO 实际总消耗 ?
		item.StatsTotalCommission = 0                                                // TODO 总佣金 ?
		item.StatsTotalRecycle = item.StatsTotalCommission / item.StatsCost          // 总回收，计算：StatsTotalCommission / StatsCost
		item.TotalAdActualCost = sumItemAdActualCost                                 // 实际消耗，计算：sum(Items.AdActualCost)
		item.TotalAdCost = sumItemAdCost                                             // 广告消耗，计算：sum(Items.AdCost)
		item.TotalAdImpression = sumItemAdImpression                                 // 曝光量，计算：sum(Items.AdImpression)
		item.TotalAdClick = sumItemAdClick                                           // 点击量，计算：sum(Items.AdClick)
		item.TotalCtr = float64(item.TotalAdClick) / float64(item.TotalAdImpression) // CTR，计算：TotalAdClick / TotalAdImpression
		item.TotalCpsOrderCommission = sumItemCpsOrderCommission                     // CPS(总佣金)，计算：sum(Items.CpsOrderCommission)
		item.TotalCpsOrderNum = sumItemCpsOrderNum                                   // CPS(订单)，计算：sum(Items.CpsOrderNum)
		item.TotalCpsRol = item.TotalCpsOrderCommission / item.TotalAdActualCost     // CPS（roi），计算：TotalCpsOrderCommission / TotalAdActualCost

		res = append(res, item)
	}
	return res, nil
}

type AdSearchWord struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
	Pid  string `json:"pid"`
}

type AdSearchWordDailyStats struct {
	PwdId          int64   `json:"pwd_id"`
	Pid            string  `json:"pid"`
	Date           string  `json:"date"`
	Pv             int64   `json:"pv"`
	Uv             int64   `json:"uv"`
	ClickPv        int64   `json:"click_pv"`
	ClickUv        int64   `json:"click_uv"`
	OrderNum       int64   `json:"order_num"`
	Income         float64 `json:"income"`
	Settle         float64 `json:"settle"`
	ActualOrderNum int64   `json:"actual_order_num"`
	ActualIncome   float64 `json:"actual_income"`
	ActualSettle   float64 `json:"actual_settle"`
}

func (s *adSlotPlanService) GeneratePwdDailyReport(ctx context.Context, start, end time.Time) error {
	var pwds []*AdSearchWord
	err := g.DB().Model("ad_search_words").Scan(&pwds)
	if err != nil {
		return fmt.Errorf("获取口令失败: %s", err.Error())
	}
	if len(pwds) == 0 {
		return nil
	}

	pids, pidMap := make([]string, 0), make(map[string]int64)
	for _, pwd := range pwds {
		pids = append(pids, pwd.Pid)
		pidMap[pwd.Pid] = pwd.Id
	}

	pidDailyMap := make(map[string]*AdSearchWordDailyStats)
	for i := 0; ; i++ {
		day := start.Add(time.Duration(i*24) * time.Hour)
		if day.Unix() > end.Unix() {
			break
		}

		{
			var pwdUvStats []*struct {
				Pid      string  `json:"pid"`
				Pv       int64   `json:"pv"`
				Uv       int64   `json:"uv"`
				ClickPv  int64   `json:"click_pv"`
				ClickUv  int64   `json:"click_uv"`
				OrderNum int64   `json:"order_num"`
				Income   float64 `json:"income"`
				Settle   float64 `json:"settle"`
			}
			err = g.DB().Ctx(ctx).Raw("select * from byn_data.elm_ad_zone_reports where report_date = ? and pid in ?", day.Format(time.DateOnly), pids).Scan(&pwdUvStats)
			if err == nil {
				for _, stat := range pwdUvStats {
					key := fmt.Sprintf("%s_%s", day.Format(time.DateOnly), stat.Pid)
					if _, ok := pidDailyMap[key]; !ok {
						pidDailyMap[key] = &AdSearchWordDailyStats{
							PwdId: pidMap[stat.Pid],
							Pid:   stat.Pid,
							Date:  day.Format(time.DateOnly),
						}
					}
					pidDailyMap[key].Pv = stat.Pv
					pidDailyMap[key].Uv = stat.Uv
					pidDailyMap[key].ClickPv = stat.ClickPv
					pidDailyMap[key].ClickUv = stat.ClickUv
					pidDailyMap[key].OrderNum = stat.OrderNum
					pidDailyMap[key].Income = stat.Income
					pidDailyMap[key].Settle = stat.Settle
				}
			}
		}

		{
			var pwdOrderStats []*struct {
				Pid      string  `json:"pid"`
				OrderNum int64   `json:"order_num"`
				Income   float64 `json:"income"`
				Settle   float64 `json:"settle"`
			}
			cpsOrderName := fmt.Sprintf("cps_orders_%s", day.Format("200601"))
			pwdOrderSql := `
select pid, count(1) order_num, sum(pre_commission) income, sum(commission) settle from warehouse.` + cpsOrderName + ` where supplier_id = 104 and create_time between ? and ? and pid in ? and order_status in (2,3,4) group by pid
`
			err = g.DB().Ctx(ctx).Raw(pwdOrderSql, day.Format(time.DateOnly)+" 00:00:00", day.Format(time.DateOnly)+" 23:59:59", pids).Scan(&pwdOrderStats)
			if err == nil {
				for _, stat := range pwdOrderStats {
					key := fmt.Sprintf("%s_%s", day.Format(time.DateOnly), stat.Pid)
					if _, ok := pidDailyMap[key]; !ok {
						pidDailyMap[key] = &AdSearchWordDailyStats{
							PwdId: pidMap[stat.Pid],
							Pid:   stat.Pid,
							Date:  day.Format(time.DateOnly),
						}
					}
					pidDailyMap[key].ActualOrderNum = stat.OrderNum
					pidDailyMap[key].ActualIncome = stat.Income
					pidDailyMap[key].ActualSettle = stat.Settle
				}
			}
		}
	}

	var stats []*AdSearchWordDailyStats
	for _, pid := range pids {
		for i := 0; ; i++ {
			day := start.Add(time.Duration(i*24) * time.Hour)
			if day.Unix() > end.Unix() {
				break
			}
			if item, ok := pidDailyMap[fmt.Sprintf("%s_%s", day.Format(time.DateOnly), pid)]; ok {
				stats = append(stats, item)
			} else {
				stats = append(stats, &AdSearchWordDailyStats{
					PwdId: pidMap[pid],
					Pid:   pid,
					Date:  day.Format(time.DateOnly),
				})
			}
		}
	}

	return g.DB().Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, e := tx.Model("ad_search_word_daily_stats").Where("pid in ? and date between ? and ?", pids, start.Format(time.DateOnly), end.Format(time.DateOnly)).Delete()
		if e != nil {
			return e
		}
		_, e = tx.Model("ad_search_word_daily_stats").Data(stats).Insert()
		if e != nil {
			return e
		}
		return nil
	})
}
