package router

import (
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"

	"ad-pro-v2/internal/controller/ad_product"
	"ad-pro-v2/internal/controller/ad_slot_plan"
	"ad-pro-v2/internal/controller/agent"
	"ad-pro-v2/internal/controller/analysis"
	"ad-pro-v2/internal/controller/auth"
	"ad-pro-v2/internal/controller/creative"
	"ad-pro-v2/internal/controller/dashboard"
	"ad-pro-v2/internal/controller/department"
	"ad-pro-v2/internal/controller/media"
	"ad-pro-v2/internal/controller/model"
	"ad-pro-v2/internal/controller/password"
	"ad-pro-v2/internal/controller/plan_statistics"
	"ad-pro-v2/internal/controller/platform_creative"
	"ad-pro-v2/internal/controller/platform_group"
	"ad-pro-v2/internal/controller/platform_plan"
	"ad-pro-v2/internal/controller/promotion_report"
	"ad-pro-v2/internal/controller/slot"
	"ad-pro-v2/internal/controller/sync"
	"ad-pro-v2/internal/controller/taobao_pids"
	"ad-pro-v2/internal/controller/upload"
	"ad-pro-v2/internal/controller/user"
	"ad-pro-v2/internal/middleware"
)

// RegisterRouter 注册路由
func RegisterRouter(s *ghttp.Server) {
	// 全局中间件
	s.Use(ghttp.MiddlewareHandlerResponse, func(r *ghttp.Request) {

		// 如果 api 路径是 /api/v1/get-adpage 或者 /api/v1/get-adlink 则进行消耗时间的打印，精确到毫秒
		if r.Request.RequestURI == "/api/v1/get-adpage" || r.Request.RequestURI == "/api/v1/get-adlink" {
			startTime := time.Now()
			r.Middleware.Next()
			duration := time.Since(startTime)
			g.Log().Infof(r.Context(), "请求 %s 耗时 %dms", r.Request.RequestURI, duration.Milliseconds())
		} else {
			r.Middleware.Next()
		}
	})

	// 不需要认证的路由组
	s.Group("/api/v1", func(group *ghttp.RouterGroup) {
		group.POST("/auth/login", new(auth.Controller).Login)

		// 计划统计
		group.Group("/plan-statistics", func(group *ghttp.RouterGroup) {
			group.GET("/get-pv-uv", new(plan_statistics.Controller).GetPlanPvUv)
		})

		// 允许跨域访问的公开接口
		group.Group("/public", func(group *ghttp.RouterGroup) {
			// 添加CORS中间件
			group.Middleware(func(r *ghttp.Request) {
				r.Response.CORSDefault()
				r.Middleware.Next()
			})

			// 获取融合链接接口
			group.GET("/ad-slot-plans/get-merge-links", new(ad_slot_plan.Controller).GetMergeLinks)
		})

		// 小程序插件获取广告链接
		group.POST("/get-adlink", new(ad_slot_plan.Controller).GetAdLink)
		group.POST("/get-adpage", new(ad_slot_plan.Controller).GetAdPage)
	})

	// 需要认证的路由组
	s.Group("/api/v1", func(group *ghttp.RouterGroup) {
		group.Middleware(middleware.Auth)

		// 认证相关
		group.GET("/auth/info", new(auth.Controller).Info)
		group.POST("/auth/logout", new(auth.Controller).Logout)

		// 用户管理
		group.Group("/users", func(group *ghttp.RouterGroup) {
			userCtrl := new(user.Controller)
			group.GET("", userCtrl.GetUsers)
			group.POST("", userCtrl.CreateUser)
			group.PUT("/:id", userCtrl.UpdateUser)
			group.DELETE("/:id", userCtrl.DeleteUser)
			group.POST("/:id/lock", userCtrl.LockUser)
			group.POST("/:id/unlock", userCtrl.UnlockUser)
			group.POST("/:id/password/reset", userCtrl.ResetPassword)
			group.GET("/options", userCtrl.GetUserOptions)
			// 保留原有的媒介用户列表接口
			group.GET("/media-users", userCtrl.MediaUserList)
		})

		// 部门管理
		group.Group("/departments", func(group *ghttp.RouterGroup) {
			deptCtrl := new(department.Controller)
			group.GET("", deptCtrl.GetDepartments)
			group.GET("/tree", deptCtrl.GetDepartmentTree)
			group.POST("", deptCtrl.CreateDepartment)
			group.PUT("/:id", deptCtrl.UpdateDepartment)
			group.DELETE("/:id", deptCtrl.DeleteDepartment)
			group.GET("/options", deptCtrl.GetDepartmentOptions)
		})

		// 同步
		group.Group("/sync", func(group *ghttp.RouterGroup) {
			group.POST("/denghuo-plus", new(sync.Controller).DenghuoPlus)
		})

		// 代理管理
		group.Group("/agents", func(group *ghttp.RouterGroup) {
			group.GET("/list", new(agent.Controller).List)
			group.GET("/detail", new(agent.Controller).Detail)
			group.POST("/create", new(agent.Controller).Create)
			group.PUT("/update", new(agent.Controller).Update)
			group.DELETE("/delete", new(agent.Controller).Delete)
			group.PUT("/audit", new(agent.Controller).Audit)
		})

		// 媒体管理
		group.Group("/media", func(group *ghttp.RouterGroup) {
			group.GET("/list", new(media.Controller).List)
			group.GET("/detail", new(media.Controller).Detail)
			group.POST("/create", new(media.Controller).Create)
			group.PUT("/update", new(media.Controller).Update)
			group.DELETE("/delete", new(media.Controller).Delete)
			group.PUT("/audit", new(media.Controller).Audit)
		})

		// 资源位管理
		group.Group("/slots", func(group *ghttp.RouterGroup) {
			group.GET("/list", new(slot.Controller).List)
			group.GET("/detail", new(slot.Controller).Detail)
			group.POST("/create", new(slot.Controller).Create)
			group.PUT("/update", new(slot.Controller).Update)
			group.DELETE("/delete", new(slot.Controller).Delete)
			group.PUT("/audit", new(slot.Controller).Audit)
		})

		// 上传管理
		group.Group("/upload", func(group *ghttp.RouterGroup) {
			controller := new(upload.Controller)
			group.POST("", controller.Upload)
			group.POST("/image", controller.UploadImage)
		})

		// 投放计划管理
		group.Group("/ad-slot-plans", func(group *ghttp.RouterGroup) {
			group.GET("/list", new(ad_slot_plan.Controller).List)
			group.GET("/get", new(ad_slot_plan.Controller).Get)
			group.POST("/create", new(ad_slot_plan.Controller).Create)
			group.POST("/update", new(ad_slot_plan.Controller).Update)
			group.DELETE("/delete", new(ad_slot_plan.Controller).Delete)
			group.POST("/approve", new(ad_slot_plan.Controller).Approve)
			group.POST("/reject", new(ad_slot_plan.Controller).Reject)
			group.POST("/update-delivery-mode", new(ad_slot_plan.Controller).UpdateDeliveryMode)
			group.GET("/generate-promotion-link", new(ad_slot_plan.Controller).GeneratePromotionLink)
			group.GET("/generate-short-url", new(ad_slot_plan.Controller).GenerateShortUrl)
			group.POST("/update-promotion-link", new(ad_slot_plan.Controller).UpdatePromotionLink)
			group.POST("/update-short-url", new(ad_slot_plan.Controller).UpdateShortUrl)
			group.POST("/update-merge-links", new(ad_slot_plan.Controller).UpdateMergeLinks)
			group.GET("/platform_object_data", new(ad_slot_plan.Controller).GetPlatformObjectData)
		})

		// 投放产品管理
		group.Group("/ad-products", func(group *ghttp.RouterGroup) {
			group.GET("/list", new(ad_product.Controller).List)
		})

		// 仪表盘
		group.Group("/dashboard", func(group *ghttp.RouterGroup) {
			group.GET("/media-list", new(dashboard.Controller).GetMediaList)
			group.GET("/plans", new(dashboard.Controller).GetPlans)
			group.GET("/products", new(dashboard.Controller).GetProducts)
			group.GET("/metrics", new(dashboard.Controller).GetMetrics)
			group.GET("/trend", new(dashboard.Controller).GetTrendData)
			group.GET("/region", new(dashboard.Controller).GetRegionData)
			group.GET("/order-type", new(dashboard.Controller).GetOrderTypeData)
			group.GET("/plan-stats", new(dashboard.Controller).GetPlanStats)
		})

		// 数据分析
		group.Group("/analysis", func(group *ghttp.RouterGroup) {
			group.GET("/profit", new(analysis.Controller).GetProfitData)
		})

		// 口令管理
		group.Group("/password", func(group *ghttp.RouterGroup) {
			ctrl := password.NewController()
			group.POST("/create", ctrl.Create)
			group.GET("/list", ctrl.List)
			group.GET("/oriList", ctrl.OriList)
			group.PUT("/:id", ctrl.Update)
			group.DELETE("/:id", ctrl.Delete)
		})

		// 投放管理
		group.Group("/delivery", func(group *ghttp.RouterGroup) {
			group.Group("/plans", func(group *ghttp.RouterGroup) {
				group.GET("/list", new(platform_plan.Controller).List)
				group.GET("/market-targets", new(platform_plan.Controller).MarketTargets)
				group.PUT("/:id", new(platform_plan.Controller).UpdatePlanType)
			})
			// 广告组管理
			group.Group("/groups", func(group *ghttp.RouterGroup) {
				group.GET("/list", new(platform_group.Controller).List)
				group.POST("/create", new(platform_group.Controller).Create)
				group.PUT("/:id", new(platform_group.Controller).Update)
				group.DELETE("/delete", new(platform_group.Controller).Delete)
			})

			// 广告创意管理
			group.Group("/creatives", func(group *ghttp.RouterGroup) {
				group.GET("/list", new(platform_creative.Controller).List)
				group.POST("/create", new(platform_creative.Controller).Create)
				group.PUT("/:id", new(platform_creative.Controller).Update)
				group.DELETE("/delete", new(platform_creative.Controller).Delete)
			})

			// 报表
			group.Group("/reports", func(group *ghttp.RouterGroup) {
				group.Group("/cost", func(group *ghttp.RouterGroup) {
					group.POST("/update", new(promotion_report.Controller).UpdateCost)
					group.GET("/list", new(promotion_report.Controller).GetCostList)
					group.POST("/import", new(promotion_report.Controller).ImportCost)
					group.GET("/tasks", new(promotion_report.Controller).GetTaskList)
				})
				group.GET("/list", new(promotion_report.Controller).GetModelReport)
			})
			// 模型管理
			group.Group("/models", func(group *ghttp.RouterGroup) {
				controller := &model.Controller{}
				group.GET("/list", controller.List)
				group.POST("/create", controller.Create)
				group.POST("/update", controller.Update)
				group.POST("/delete", controller.Delete)
				group.POST("/import", controller.Import)
			})
		})

		// 淘联链接管理
		group.Group("/taobao-pids", func(group *ghttp.RouterGroup) {
			controller := new(taobao_pids.Controller)
			group.GET("/list", controller.List)
			group.POST("/create", controller.Create)
			group.PUT("/:id", controller.Update)
			group.DELETE("/:id", controller.Delete)
		})

		// 创意管理
		group.Group("/ad-creatives", func(group *ghttp.RouterGroup) {
			controller := new(creative.Controller)
			group.GET("/list", controller.List)
			group.GET("/detail", controller.Detail)
			group.POST("/create", controller.Create)
			group.PUT("/update", controller.Update)
			group.DELETE("/delete", controller.Delete)
		})

		// 注册同步相关接口
		group.Bind(
			new(sync.Controller),
		)
	})
}
