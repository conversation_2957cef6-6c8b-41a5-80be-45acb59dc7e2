// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdOrders202411Dao is an internal type for wrapping the internal DAO implementation.
type internalAdOrders202411Dao = *internal.AdOrders202411Dao

// adOrders202411Dao is the data access object for the table ad_orders_202411.
// You can define custom methods on it to extend its functionality as needed.
type adOrders202411Dao struct {
	internalAdOrders202411Dao
}

var (
	// AdOrders202411 is a globally accessible object for table ad_orders_202411 operations.
	AdOrders202411 = adOrders202411Dao{
		internal.NewAdOrders202411Dao(),
	}
)

// Add your custom methods and functionality below.
