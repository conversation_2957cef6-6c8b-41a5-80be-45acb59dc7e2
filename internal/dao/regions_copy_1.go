// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalRegionsCopy1Dao is an internal type for wrapping the internal DAO implementation.
type internalRegionsCopy1Dao = *internal.RegionsCopy1Dao

// regionsCopy1Dao is the data access object for the table regions_copy1.
// You can define custom methods on it to extend its functionality as needed.
type regionsCopy1Dao struct {
	internalRegionsCopy1Dao
}

var (
	// RegionsCopy1 is a globally accessible object for table regions_copy1 operations.
	RegionsCopy1 = regionsCopy1Dao{
		internal.NewRegionsCopy1Dao(),
	}
)

// Add your custom methods and functionality below.
