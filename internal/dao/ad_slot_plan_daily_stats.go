// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdSlotPlanDailyStatsDao is an internal type for wrapping the internal DAO implementation.
type internalAdSlotPlanDailyStatsDao = *internal.AdSlotPlanDailyStatsDao

// adSlotPlanDailyStatsDao is the data access object for the table ad_slot_plan_daily_stats.
// You can define custom methods on it to extend its functionality as needed.
type adSlotPlanDailyStatsDao struct {
	internalAdSlotPlanDailyStatsDao
}

var (
	// AdSlotPlanDailyStats is a globally accessible object for table ad_slot_plan_daily_stats operations.
	AdSlotPlanDailyStats = adSlotPlanDailyStatsDao{
		internal.NewAdSlotPlanDailyStatsDao(),
	}
)

// Add your custom methods and functionality below.
