// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalSyncStatsLogsDao is an internal type for wrapping the internal DAO implementation.
type internalSyncStatsLogsDao = *internal.SyncStatsLogsDao

// syncStatsLogsDao is the data access object for the table sync_stats_logs.
// You can define custom methods on it to extend its functionality as needed.
type syncStatsLogsDao struct {
	internalSyncStatsLogsDao
}

var (
	// SyncStatsLogs is a globally accessible object for table sync_stats_logs operations.
	SyncStatsLogs = syncStatsLogsDao{
		internal.NewSyncStatsLogsDao(),
	}
)

// Add your custom methods and functionality below.
