// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdMediaWithdrawsDao is an internal type for wrapping the internal DAO implementation.
type internalAdMediaWithdrawsDao = *internal.AdMediaWithdrawsDao

// adMediaWithdrawsDao is the data access object for the table ad_media_withdraws.
// You can define custom methods on it to extend its functionality as needed.
type adMediaWithdrawsDao struct {
	internalAdMediaWithdrawsDao
}

var (
	// AdMediaWithdraws is a globally accessible object for table ad_media_withdraws operations.
	AdMediaWithdraws = adMediaWithdrawsDao{
		internal.NewAdMediaWithdrawsDao(),
	}
)

// Add your custom methods and functionality below.
