// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdSlotPlanDailyCityStatsDao is an internal type for wrapping the internal DAO implementation.
type internalAdSlotPlanDailyCityStatsDao = *internal.AdSlotPlanDailyCityStatsDao

// adSlotPlanDailyCityStatsDao is the data access object for the table ad_slot_plan_daily_city_stats.
// You can define custom methods on it to extend its functionality as needed.
type adSlotPlanDailyCityStatsDao struct {
	internalAdSlotPlanDailyCityStatsDao
}

var (
	// AdSlotPlanDailyCityStats is a globally accessible object for table ad_slot_plan_daily_city_stats operations.
	AdSlotPlanDailyCityStats = adSlotPlanDailyCityStatsDao{
		internal.NewAdSlotPlanDailyCityStatsDao(),
	}
)

// Add your custom methods and functionality below.
