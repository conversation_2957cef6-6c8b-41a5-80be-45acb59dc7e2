// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdProductsDao is an internal type for wrapping the internal DAO implementation.
type internalAdProductsDao = *internal.AdProductsDao

// adProductsDao is the data access object for the table ad_products.
// You can define custom methods on it to extend its functionality as needed.
type adProductsDao struct {
	internalAdProductsDao
}

var (
	// AdProducts is a globally accessible object for table ad_products operations.
	AdProducts = adProductsDao{
		internal.NewAdProductsDao(),
	}
)

// Add your custom methods and functionality below.
