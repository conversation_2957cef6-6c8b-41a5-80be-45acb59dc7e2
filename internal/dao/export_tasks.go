// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalExportTasksDao is an internal type for wrapping the internal DAO implementation.
type internalExportTasksDao = *internal.ExportTasksDao

// exportTasksDao is the data access object for the table export_tasks.
// You can define custom methods on it to extend its functionality as needed.
type exportTasksDao struct {
	internalExportTasksDao
}

var (
	// ExportTasks is a globally accessible object for table export_tasks operations.
	ExportTasks = exportTasksDao{
		internal.NewExportTasksDao(),
	}
)

// Add your custom methods and functionality below.
