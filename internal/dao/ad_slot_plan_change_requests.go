// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdSlotPlanChangeRequestsDao is an internal type for wrapping the internal DAO implementation.
type internalAdSlotPlanChangeRequestsDao = *internal.AdSlotPlanChangeRequestsDao

// adSlotPlanChangeRequestsDao is the data access object for the table ad_slot_plan_change_requests.
// You can define custom methods on it to extend its functionality as needed.
type adSlotPlanChangeRequestsDao struct {
	internalAdSlotPlanChangeRequestsDao
}

var (
	// AdSlotPlanChangeRequests is a globally accessible object for table ad_slot_plan_change_requests operations.
	AdSlotPlanChangeRequests = adSlotPlanChangeRequestsDao{
		internal.NewAdSlotPlanChangeRequestsDao(),
	}
)

// Add your custom methods and functionality below.
