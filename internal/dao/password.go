// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// passwordDao is the data access object for password.
type passwordDao struct {
	*internal.PasswordDao
}

var (
	// Password is the global instance for passwordDao
	Password = &passwordDao{
		PasswordDao: internal.NewPasswordDao(),
	}
)

// Model creates and returns a new model based on current dao
func (dao *passwordDao) Model(ctx context.Context) *gdb.Model {
	return g.DB().Model(dao.Table()).Safe().Ctx(ctx)
}

// Ctx is a chaining function, which creates and returns a new DB that is a shallow copy
// of current DB object and with given context in it.
// Note that this returned DB object can be used only once, so do not assign it to
// a global or package variable for long using.
func (dao *passwordDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.Model(ctx)
}

// DO returns the Data Object of current DAO
func (dao *passwordDao) DO() internal.PasswordDO {
	return internal.PasswordDO{}
}

// Add your custom methods below.
