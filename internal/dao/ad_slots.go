// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdSlotsDao is an internal type for wrapping the internal DAO implementation.
type internalAdSlotsDao = *internal.AdSlotsDao

// adSlotsDao is the data access object for the table ad_slots.
// You can define custom methods on it to extend its functionality as needed.
type adSlotsDao struct {
	internalAdSlotsDao
}

var (
	// AdSlots is a globally accessible object for table ad_slots operations.
	AdSlots = adSlotsDao{
		internal.NewAdSlotsDao(),
	}
)

// Add your custom methods and functionality below.
