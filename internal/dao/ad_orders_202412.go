// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdOrders202412Dao is an internal type for wrapping the internal DAO implementation.
type internalAdOrders202412Dao = *internal.AdOrders202412Dao

// adOrders202412Dao is the data access object for the table ad_orders_202412.
// You can define custom methods on it to extend its functionality as needed.
type adOrders202412Dao struct {
	internalAdOrders202412Dao
}

var (
	// AdOrders202412 is a globally accessible object for table ad_orders_202412 operations.
	AdOrders202412 = adOrders202412Dao{
		internal.NewAdOrders202412Dao(),
	}
)

// Add your custom methods and functionality below.
