// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdMediaBalanceLogsDao is an internal type for wrapping the internal DAO implementation.
type internalAdMediaBalanceLogsDao = *internal.AdMediaBalanceLogsDao

// adMediaBalanceLogsDao is the data access object for the table ad_media_balance_logs.
// You can define custom methods on it to extend its functionality as needed.
type adMediaBalanceLogsDao struct {
	internalAdMediaBalanceLogsDao
}

var (
	// AdMediaBalanceLogs is a globally accessible object for table ad_media_balance_logs operations.
	AdMediaBalanceLogs = adMediaBalanceLogsDao{
		internal.NewAdMediaBalanceLogsDao(),
	}
)

// Add your custom methods and functionality below.
