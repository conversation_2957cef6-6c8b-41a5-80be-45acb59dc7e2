// =================================================================================
// This file is auto-generated by GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalPlatformConversionsDao is an internal type for wrapping internal DAO implementation.
type internalPlatformConversionsDao = *internal.PlatformConversionsDao

// platformConversionsDao is the data access object for table platform_conversions.
// You can define custom methods on it to extend its functionality as you wish.
type platformConversionsDao struct {
	internalPlatformConversionsDao
}

var (
	// PlatformConversions is the globally public accessible object for table platform_conversions operations.
	PlatformConversions = platformConversionsDao{
		internal.NewPlatformConversionsDao(),
	}
)
