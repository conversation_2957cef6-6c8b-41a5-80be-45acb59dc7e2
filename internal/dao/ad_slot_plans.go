// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
	"ad-pro-v2/internal/model/entity"
	"context"
)

// internalAdSlotPlansDao is an internal type for wrapping the internal DAO implementation.
type internalAdSlotPlansDao = *internal.AdSlotPlansDao

// adSlotPlansDao is the data access object for the table ad_slot_plans.
// You can define custom methods on it to extend its functionality as needed.
type adSlotPlansDao struct {
	internalAdSlotPlansDao
}

var (
	// AdSlotPlans is a globally accessible object for table ad_slot_plans operations.
	AdSlotPlans = adSlotPlansDao{
		internal.NewAdSlotPlansDao(),
	}
)

// Add your custom methods and functionality below.

// GetPlansByMediaAccountId 获取媒体账号下的投放计划列表
func (d *adSlotPlansDao) GetPlansByMediaAccountId(ctx context.Context, mediaAccountId int64) ([]*entity.AdSlotPlan, error) {
	var plans []*entity.AdSlotPlan
	err := d.DB().Model(d.Table()).
		Where("media_id = ? AND deleted_at IS NULL", mediaAccountId).
		Scan(&plans)
	if err != nil {
		return nil, err
	}
	return plans, nil
}

// GetPlansByMediaAccountIdAndUserId 获取媒体账号下指定用户的投放计划列表
func (d *adSlotPlansDao) GetPlansByMediaAccountIdAndUserId(ctx context.Context, mediaAccountId int64, userId int) ([]*entity.AdSlotPlan, error) {
	var plans []*entity.AdSlotPlan
	err := d.DB().Model(d.Table()).
		Where("media_id = ? AND user_id = ? AND deleted_at IS NULL", mediaAccountId, userId).
		Scan(&plans)
	if err != nil {
		return nil, err
	}
	return plans, nil
}
