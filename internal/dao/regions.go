// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalRegionsDao is an internal type for wrapping the internal DAO implementation.
type internalRegionsDao = *internal.RegionsDao

// regionsDao is the data access object for the table regions.
// You can define custom methods on it to extend its functionality as needed.
type regionsDao struct {
	internalRegionsDao
}

var (
	// Regions is a globally accessible object for table regions operations.
	Regions = regionsDao{
		internal.NewRegionsDao(),
	}
)

// Add your custom methods and functionality below.
