// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalModelDecaysDao is an internal type for wrapping the internal DAO implementation.
type internalModelDecaysDao = *internal.ModelDecaysDao

// modelDecaysDao is the data access object for the table model_decays.
// You can define custom methods on it to extend its functionality as needed.
type modelDecaysDao struct {
	internalModelDecaysDao
}

var (
	// ModelDecays is a globally accessible object for table model_decays operations.
	ModelDecays = modelDecaysDao{
		internal.NewModelDecaysDao(),
	}
)

// Add your custom methods and functionality below.
