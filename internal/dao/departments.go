// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalDepartmentsDao is an internal type for wrapping the internal DAO implementation.
type internalDepartmentsDao = *internal.DepartmentsDao

// departmentsDao is the data access object for the table departments.
// You can define custom methods on it to extend its functionality as needed.
type departmentsDao struct {
	internalDepartmentsDao
}

var (
	// Departments is a globally accessible object for table departments operations.
	Departments = departmentsDao{
		internal.NewDepartmentsDao(),
	}
)

// Add your custom methods and functionality below.
