// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// internalPasswordGroupsDao is an internal type for wrapping the internal DAO implementation.
type internalPasswordGroupsDao = *internal.PasswordGroupsDao

// passwordGroupsDao is the data access object for the table password_groups.
// You can define custom methods on it to extend its functionality as needed.
type passwordGroupsDao struct {
	internalPasswordGroupsDao
}

var (
	// PasswordGroups is a globally accessible object for table password_groups operations.
	PasswordGroups = passwordGroupsDao{
		internal.NewPasswordGroupsDao(),
	}
)

// Add your custom methods and functionality below.

// passwordGroupDao is the data access object for password group
type passwordGroupDao struct {
	db      *gdb.Model
	table   string
	columns internal.PasswordGroupColumns
}

var (
	// PasswordGroup is the global instance for passwordGroupDao
	PasswordGroup = &passwordGroupDao{
		table:   internal.PasswordGroup,
		columns: internal.PasswordGroupTable,
	}
)

// Model creates and returns a new model based on current dao
func (dao *passwordGroupDao) Model(ctx context.Context) *gdb.Model {
	return g.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Columns returns the columns of table password_group
func (dao *passwordGroupDao) Columns() internal.PasswordGroupColumns {
	return dao.columns
}

// Table returns the table name of password_group
func (dao *passwordGroupDao) Table() string {
	return dao.table
}

// Ctx is a chaining function, which creates and returns a new DB that is a shallow copy
// of current DB object and with given context in it.
// Note that this returned DB object can be used only once, so do not assign it to
// a global or package variable for long using.
func (dao *passwordGroupDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.Model(ctx)
}

// DO returns the Data Object of current DAO
func (dao *passwordGroupDao) DO() internal.PasswordGroupDO {
	return internal.PasswordGroupDO{}
}
