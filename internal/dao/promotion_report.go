// =================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// promotionReportDao is the data access object for table promotion_report.
// You can define custom methods on it to extend its functionality as you wish.
type promotionReportDao struct {
	*internal.PromotionReportDao
}

var (
	// PromotionReport is globally public accessible object for table promotion_report operations.
	PromotionReport = promotionReportDao{
		internal.NewPromotionReportDao(),
	}
)

// Fill with your ideas below. 