// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdProductsCopy1Dao is an internal type for wrapping the internal DAO implementation.
type internalAdProductsCopy1Dao = *internal.AdProductsCopy1Dao

// adProductsCopy1Dao is the data access object for the table ad_products_copy1.
// You can define custom methods on it to extend its functionality as needed.
type adProductsCopy1Dao struct {
	internalAdProductsCopy1Dao
}

var (
	// AdProductsCopy1 is a globally accessible object for table ad_products_copy1 operations.
	AdProductsCopy1 = adProductsCopy1Dao{
		internal.NewAdProductsCopy1Dao(),
	}
)

// Add your custom methods and functionality below.
