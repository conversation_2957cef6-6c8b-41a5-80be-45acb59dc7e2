// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdMediaSubsidyRulesDao is an internal type for wrapping the internal DAO implementation.
type internalAdMediaSubsidyRulesDao = *internal.AdMediaSubsidyRulesDao

// adMediaSubsidyRulesDao is the data access object for the table ad_media_subsidy_rules.
// You can define custom methods on it to extend its functionality as needed.
type adMediaSubsidyRulesDao struct {
	internalAdMediaSubsidyRulesDao
}

var (
	// AdMediaSubsidyRules is a globally accessible object for table ad_media_subsidy_rules operations.
	AdMediaSubsidyRules = adMediaSubsidyRulesDao{
		internal.NewAdMediaSubsidyRulesDao(),
	}
)

// Add your custom methods and functionality below.
