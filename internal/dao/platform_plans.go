// =================================================================================
// This file is auto-generated by GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalPlatformPlansDao is an internal type for wrapping internal DAO implementation.
type internalPlatformPlansDao = *internal.PlatformPlansDao

// platformPlansDao is the data access object for table platform_plans.
// You can define custom methods on it to extend its functionality as you wish.
type platformPlansDao struct {
	internalPlatformPlansDao
}

var (
	// PlatformPlans is the globally public accessible object for table platform_plans operations.
	PlatformPlans = platformPlansDao{
		internal.NewPlatformPlansDao(),
	}
)
