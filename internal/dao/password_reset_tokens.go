// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalPasswordResetTokensDao is an internal type for wrapping the internal DAO implementation.
type internalPasswordResetTokensDao = *internal.PasswordResetTokensDao

// passwordResetTokensDao is the data access object for the table password_reset_tokens.
// You can define custom methods on it to extend its functionality as needed.
type passwordResetTokensDao struct {
	internalPasswordResetTokensDao
}

var (
	// PasswordResetTokens is a globally accessible object for table password_reset_tokens operations.
	PasswordResetTokens = passwordResetTokensDao{
		internal.NewPasswordResetTokensDao(),
	}
)

// Add your custom methods and functionality below.
