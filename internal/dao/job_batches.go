// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalJobBatchesDao is an internal type for wrapping the internal DAO implementation.
type internalJobBatchesDao = *internal.JobBatchesDao

// jobBatchesDao is the data access object for the table job_batches.
// You can define custom methods on it to extend its functionality as needed.
type jobBatchesDao struct {
	internalJobBatchesDao
}

var (
	// JobBatches is a globally accessible object for table job_batches operations.
	JobBatches = jobBatchesDao{
		internal.NewJobBatchesDao(),
	}
)

// Add your custom methods and functionality below.
