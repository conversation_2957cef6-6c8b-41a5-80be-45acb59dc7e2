// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalPermissionsDao is an internal type for wrapping the internal DAO implementation.
type internalPermissionsDao = *internal.PermissionsDao

// permissionsDao is the data access object for the table permissions.
// You can define custom methods on it to extend its functionality as needed.
type permissionsDao struct {
	internalPermissionsDao
}

var (
	// Permissions is a globally accessible object for table permissions operations.
	Permissions = permissionsDao{
		internal.NewPermissionsDao(),
	}
)

// Add your custom methods and functionality below.
