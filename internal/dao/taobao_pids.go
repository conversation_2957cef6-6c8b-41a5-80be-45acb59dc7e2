// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalTaobaoPidsDao is an internal type for wrapping the internal DAO implementation.
type internalTaobaoPidsDao = *internal.TaobaoPidsDao

// taobaoPidsDao is the data access object for the table taobao_pids.
// You can define custom methods on it to extend its functionality as needed.
type taobaoPidsDao struct {
	internalTaobaoPidsDao
}

var (
	// TaobaoPids is a globally accessible object for table taobao_pids operations.
	TaobaoPids = taobaoPidsDao{
		internal.NewTaobaoPidsDao(),
	}
)

// Add your custom methods and functionality below.
