// =================================================================================
// This file is auto-generated by GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalPlatformCreativesDao is an internal type for wrapping internal DAO implementation.
type internalPlatformCreativesDao = *internal.PlatformCreativesDao

// platformCreativesDao is the data access object for table platform_creatives.
// You can define custom methods on it to extend its functionality as you wish.
type platformCreativesDao struct {
	internalPlatformCreativesDao
}

var (
	// PlatformCreatives is the globally public accessible object for table platform_creatives operations.
	PlatformCreatives = platformCreativesDao{
		internal.NewPlatformCreativesDao(),
	}
)
