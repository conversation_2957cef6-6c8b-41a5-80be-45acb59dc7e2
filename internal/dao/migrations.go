// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalMigrationsDao is an internal type for wrapping the internal DAO implementation.
type internalMigrationsDao = *internal.MigrationsDao

// migrationsDao is the data access object for the table migrations.
// You can define custom methods on it to extend its functionality as needed.
type migrationsDao struct {
	internalMigrationsDao
}

var (
	// Migrations is a globally accessible object for table migrations operations.
	Migrations = migrationsDao{
		internal.NewMigrationsDao(),
	}
)

// Add your custom methods and functionality below.
