// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdSlotPlanAuditLogsDao is an internal type for wrapping the internal DAO implementation.
type internalAdSlotPlanAuditLogsDao = *internal.AdSlotPlanAuditLogsDao

// adSlotPlanAuditLogsDao is the data access object for the table ad_slot_plan_audit_logs.
// You can define custom methods on it to extend its functionality as needed.
type adSlotPlanAuditLogsDao struct {
	internalAdSlotPlanAuditLogsDao
}

var (
	// AdSlotPlanAuditLogs is a globally accessible object for table ad_slot_plan_audit_logs operations.
	AdSlotPlanAuditLogs = adSlotPlanAuditLogsDao{
		internal.NewAdSlotPlanAuditLogsDao(),
	}
)

// Add your custom methods and functionality below.
