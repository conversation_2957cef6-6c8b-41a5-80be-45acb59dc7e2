// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdSlotMonitorLogsDao is an internal type for wrapping the internal DAO implementation.
type internalAdSlotMonitorLogsDao = *internal.AdSlotMonitorLogsDao

// adSlotMonitorLogsDao is the data access object for the table ad_slot_monitor_logs.
// You can define custom methods on it to extend its functionality as needed.
type adSlotMonitorLogsDao struct {
	internalAdSlotMonitorLogsDao
}

var (
	// AdSlotMonitorLogs is a globally accessible object for table ad_slot_monitor_logs operations.
	AdSlotMonitorLogs = adSlotMonitorLogsDao{
		internal.NewAdSlotMonitorLogsDao(),
	}
)

// Add your custom methods and functionality below.
