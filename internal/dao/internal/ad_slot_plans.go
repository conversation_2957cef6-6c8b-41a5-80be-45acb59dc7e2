// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdSlotPlansDao is the data access object for the table ad_slot_plans.
type AdSlotPlansDao struct {
	table   string             // table is the underlying table name of the DAO.
	group   string             // group is the database configuration group name of the current DAO.
	columns AdSlotPlansColumns // columns contains all the column names of Table for convenient usage.
}

// AdSlotPlansColumns defines and stores column names for the table ad_slot_plans.
type AdSlotPlansColumns struct {
	Id               string //
	LinkId           string // 历史系统关联ID
	Code             string // 计划编码
	Type             string // 计划类型：test-测试计划,formal-正式计划
	AdSlotId         string // 广告位ID
	MediaId          string //
	AdProductId      string // 投放产品ID
	AdCreativeId     string // 创意ID
	UserId           string // 用户ID
	Price            string // 价格
	AuditStatus      string // 审核状态：pending-待审核,approved-已通过,rejected-已拒绝
	DeliveryStatus   string // 投放状态：init-初始状态,configuring-配置中,wait-待开始,running-投放中,stopped-手动停投,stopped_auto-自动停投,completed-已完结
	DeliveryMode     string // 投放策略:1=普通投放,2=分日组合,3=分区域组合
	StartDate        string // 开始时间
	EndDate          string // 结束时间
	IsEndDateEnabled string // 结束时间是否生效
	PromotionLink    string // 推广链接
	PromotionParams  string // 推广参数
	PromotionQrcode  string // 推广二维码
	ShortUrl         string // 短链接
	RejectReason     string // 拒绝原因
	Remark           string // 备注
	CreatedBy        string // 创建人ID
	UpdatedBy        string // 更新人ID
	CreatedAt        string //
	UpdatedAt        string //
	DeletedAt        string //
	StopReason       string // 停止投放原因
	TestResult       string // 测试结论：success=测试成功，failed=测试失败
	TestFailedReason string // 测试失败原因
	TestResultAt     string // 测试结论时间
	TestResultBy     string // 测试结论操作人
}

// adSlotPlansColumns holds the columns for the table ad_slot_plans.
var adSlotPlansColumns = AdSlotPlansColumns{
	Id:               "id",
	LinkId:           "link_id",
	Code:             "code",
	Type:             "type",
	AdSlotId:         "ad_slot_id",
	MediaId:          "media_id",
	AdProductId:      "ad_product_id",
	AdCreativeId:     "ad_creative_id",
	UserId:           "user_id",
	Price:            "price",
	AuditStatus:      "audit_status",
	DeliveryStatus:   "delivery_status",
	DeliveryMode:     "delivery_mode",
	StartDate:        "start_date",
	EndDate:          "end_date",
	IsEndDateEnabled: "is_end_date_enabled",
	PromotionLink:    "promotion_link",
	PromotionParams:  "promotion_params",
	PromotionQrcode:  "promotion_qrcode",
	ShortUrl:         "short_url",
	RejectReason:     "reject_reason",
	Remark:           "remark",
	CreatedBy:        "created_by",
	UpdatedBy:        "updated_by",
	CreatedAt:        "created_at",
	UpdatedAt:        "updated_at",
	DeletedAt:        "deleted_at",
	StopReason:       "stop_reason",
	TestResult:       "test_result",
	TestFailedReason: "test_failed_reason",
	TestResultAt:     "test_result_at",
	TestResultBy:     "test_result_by",
}

// NewAdSlotPlansDao creates and returns a new DAO object for table data access.
func NewAdSlotPlansDao() *AdSlotPlansDao {
	return &AdSlotPlansDao{
		group:   "default",
		table:   "ad_slot_plans",
		columns: adSlotPlansColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdSlotPlansDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdSlotPlansDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdSlotPlansDao) Columns() AdSlotPlansColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdSlotPlansDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdSlotPlansDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdSlotPlansDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
