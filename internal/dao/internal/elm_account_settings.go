// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ElmAccountSettingsDao is the data access object for the table elm_account_settings.
type ElmAccountSettingsDao struct {
	table   string                    // table is the underlying table name of the DAO.
	group   string                    // group is the database configuration group name of the current DAO.
	columns ElmAccountSettingsColumns // columns contains all the column names of Table for convenient usage.
}

// ElmAccountSettingsColumns defines and stores column names for the table elm_account_settings.
type ElmAccountSettingsColumns struct {
	Id        string // ID
	Cookie    string // Cookie
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
}

// elmAccountSettingsColumns holds the columns for the table elm_account_settings.
var elmAccountSettingsColumns = ElmAccountSettingsColumns{
	Id:        "id",
	Cookie:    "cookie",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// NewElmAccountSettingsDao creates and returns a new DAO object for table data access.
func NewElmAccountSettingsDao() *ElmAccountSettingsDao {
	return &ElmAccountSettingsDao{
		group:   "rebate_new",
		table:   "elm_account_settings",
		columns: elmAccountSettingsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *ElmAccountSettingsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *ElmAccountSettingsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *ElmAccountSettingsDao) Columns() ElmAccountSettingsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *ElmAccountSettingsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *ElmAccountSettingsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *ElmAccountSettingsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
} 