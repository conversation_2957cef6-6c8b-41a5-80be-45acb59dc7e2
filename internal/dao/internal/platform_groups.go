// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// PlatformGroupsDao is the data access object for table platform_groups.
type PlatformGroupsDao struct {
	table   string               // table is the underlying table name of the DAO.
	group   string               // group is the database configuration group name of the current DAO.
	columns PlatformGroupColumns // columns contains all the column names of Table for convenient usage.
}

// PlatformGroupColumns defines and stores column names for table platform_groups.
type PlatformGroupColumns struct {
	Id                string // 主键
	AgentId          string // 代理ID
	MediaId          string // 媒体ID
	PlatformType     string // 平台类型
	DataId           string // 数据ID
	Name             string // 名称
	PlanId           string // 计划ID
	PrincipalAccount string // 负责人账号
	PrincipalName    string // 负责人名称
	MarketTargetName string // 营销目标名称
	SceneName        string // 场景名称
	CreatedAt        string // 创建时间
	UpdatedAt        string // 更新时间
}

// platformGroupColumns holds the columns for table platform_groups.
var platformGroupColumns = PlatformGroupColumns{
	Id:                "id",
	AgentId:          "agent_id",
	MediaId:          "media_id",
	PlatformType:     "platform_type",
	DataId:           "data_id",
	Name:             "name",
	PlanId:           "plan_id",
	PrincipalAccount: "principal_account",
	PrincipalName:    "principal_name",
	MarketTargetName: "market_target_name",
	SceneName:        "scene_name",
	CreatedAt:        "created_at",
	UpdatedAt:        "updated_at",
}

// NewPlatformGroupsDao creates and returns a new DAO object for table data access.
func NewPlatformGroupsDao() *PlatformGroupsDao {
	return &PlatformGroupsDao{
		group:   "default",
		table:   "platform_groups",
		columns: platformGroupColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *PlatformGroupsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current DAO.
func (dao *PlatformGroupsDao) Table() string {
	return dao.table
}

// Columns returns all column names of current DAO.
func (dao *PlatformGroupsDao) Columns() PlatformGroupColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current DAO.
func (dao *PlatformGroupsDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *PlatformGroupsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error if function f returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function. 