// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UsersDao is the data access object for the table users.
type UsersDao struct {
	table   string       // table is the underlying table name of the DAO.
	group   string       // group is the database configuration group name of the current DAO.
	columns UsersColumns // columns contains all the column names of Table for convenient usage.
}

// UsersColumns defines and stores column names for the table users.
type UsersColumns struct {
	Id                  string // 用户ID
	Name                string // 用户名
	RealName            string // 用户姓名
	Email               string // 邮箱
	EmailVerifiedAt     string // 邮箱验证时间
	Password            string // 密码
	Phone               string // 手机号
	Department          string // 部门
	Position            string // 职位
	SupervisorId        string // 直属上级ID
	Role                string // 角色 1-媒介 2-运营 3-管理层 4-投手 5-财务
	Status              string // 状态：1在职 2离职
	IsLocked            string // 是否锁定：0未锁定 1已锁定
	LockReason          string // 锁定原因
	Remark              string // 备注
	LastLoginAt         string // 最近登录时间
	LastLoginIp         string // 最近登录IP
	RememberToken       string // 记住密码令牌
	CreatedAt           string // 创建时间
	UpdatedAt           string // 更新时间
	ForceChangePassword string // 强制修改密码
	PasswordChangedAt   string // 密码修改时间
	DingUserId          string // 钉钉用户ID
	DingDeptId          string // 钉钉部门ID
}

// usersColumns holds the columns for the table users.
var usersColumns = UsersColumns{
	Id:                  "id",
	Name:                "name",
	RealName:            "real_name",
	Email:               "email",
	EmailVerifiedAt:     "email_verified_at",
	Password:            "password",
	Phone:               "phone",
	Department:          "department",
	Position:            "position",
	SupervisorId:        "supervisor_id",
	Role:                "role",
	Status:              "status",
	IsLocked:            "is_locked",
	LockReason:          "lock_reason",
	Remark:              "remark",
	LastLoginAt:         "last_login_at",
	LastLoginIp:         "last_login_ip",
	RememberToken:       "remember_token",
	CreatedAt:           "created_at",
	UpdatedAt:           "updated_at",
	ForceChangePassword: "force_change_password",
	PasswordChangedAt:   "password_changed_at",
	DingUserId:          "ding_user_id",
	DingDeptId:          "ding_dept_id",
}

// NewUsersDao creates and returns a new DAO object for table data access.
func NewUsersDao() *UsersDao {
	return &UsersDao{
		group:   "default",
		table:   "users",
		columns: usersColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UsersDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UsersDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UsersDao) Columns() UsersColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UsersDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UsersDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UsersDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
