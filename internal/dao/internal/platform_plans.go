// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// PlatformPlansDao is the data access object for table platform_plans.
type PlatformPlansDao struct {
	table   string               // table is the underlying table name of the DAO.
	group   string               // group is the database configuration group name of the current DAO.
	columns PlatformPlanColumns // columns contains all the column names of Table for convenient usage.
}

// PlatformPlanColumns defines and stores column names for table platform_plans.
type PlatformPlanColumns struct {
	Id                string // 主键
	AgentId          string // 代理ID
	MediaId          string // 媒体ID
	PlatformType     string // 平台类型
	DataId           string // 数据ID
	Name             string // 名称
	PrincipalAccount string // 负责人账号
	PrincipalName    string // 负责人名称
	MarketTargetName string // 营销目标名称
	SceneName        string // 场景名称
	CreatedAt        string // 创建时间
	UpdatedAt        string // 更新时间
}

// platformPlanColumns holds the columns for table platform_plans.
var platformPlanColumns = PlatformPlanColumns{
	Id:                "id",
	AgentId:          "agent_id",
	MediaId:          "media_id",
	PlatformType:     "platform_type",
	DataId:           "data_id",
	Name:             "name",
	PrincipalAccount: "principal_account",
	PrincipalName:    "principal_name",
	MarketTargetName: "market_target_name",
	SceneName:        "scene_name",
	CreatedAt:        "created_at",
	UpdatedAt:        "updated_at",
}

// NewPlatformPlansDao creates and returns a new DAO object for table data access.
func NewPlatformPlansDao() *PlatformPlansDao {
	return &PlatformPlansDao{
		group:   "default",
		table:   "platform_plans",
		columns: platformPlanColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *PlatformPlansDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current DAO.
func (dao *PlatformPlansDao) Table() string {
	return dao.table
}

// Columns returns all column names of current DAO.
func (dao *PlatformPlansDao) Columns() PlatformPlanColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current DAO.
func (dao *PlatformPlansDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *PlatformPlansDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error if function f returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function. 