// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdSlotPlanOperationLogsDao is the data access object for the table ad_slot_plan_operation_logs.
type AdSlotPlanOperationLogsDao struct {
	table   string                         // table is the underlying table name of the DAO.
	group   string                         // group is the database configuration group name of the current DAO.
	columns AdSlotPlanOperationLogsColumns // columns contains all the column names of Table for convenient usage.
}

// AdSlotPlanOperationLogsColumns defines and stores column names for the table ad_slot_plan_operation_logs.
type AdSlotPlanOperationLogsColumns struct {
	Id              string //
	PlanId          string // 投放计划ID
	UserId          string // 操作人ID
	Action          string // 操作类型：create-创建计划,start-开始投放,stop-手动停止投放,auto_stop-自动停止投放,complete-完成投放
	OldStatus       string // 原状态
	NewStatus       string // 新状态
	Remark          string // 备注
	CreatedAt       string //
	UpdatedAt       string //
	DeletedAt       string //
	OperationParams string // 操作参数
}

// adSlotPlanOperationLogsColumns holds the columns for the table ad_slot_plan_operation_logs.
var adSlotPlanOperationLogsColumns = AdSlotPlanOperationLogsColumns{
	Id:              "id",
	PlanId:          "plan_id",
	UserId:          "user_id",
	Action:          "action",
	OldStatus:       "old_status",
	NewStatus:       "new_status",
	Remark:          "remark",
	CreatedAt:       "created_at",
	UpdatedAt:       "updated_at",
	DeletedAt:       "deleted_at",
	OperationParams: "operation_params",
}

// NewAdSlotPlanOperationLogsDao creates and returns a new DAO object for table data access.
func NewAdSlotPlanOperationLogsDao() *AdSlotPlanOperationLogsDao {
	return &AdSlotPlanOperationLogsDao{
		group:   "default",
		table:   "ad_slot_plan_operation_logs",
		columns: adSlotPlanOperationLogsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdSlotPlanOperationLogsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdSlotPlanOperationLogsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdSlotPlanOperationLogsDao) Columns() AdSlotPlanOperationLogsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdSlotPlanOperationLogsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdSlotPlanOperationLogsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdSlotPlanOperationLogsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
