// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ImportHistoriesDao is the data access object for the table import_histories.
type ImportHistoriesDao struct {
	table   string                 // table is the underlying table name of the DAO.
	group   string                 // group is the database configuration group name of the current DAO.
	columns ImportHistoriesColumns // columns contains all the column names of Table for convenient usage.
}

// ImportHistoriesColumns defines and stores column names for the table import_histories.
type ImportHistoriesColumns struct {
	Id            string //
	UserId        string //
	Type          string //
	FileName      string //
	Status        string //
	TotalRows     string //
	SuccessRows   string //
	FailedRows    string //
	ErrorMessages string //
	CompletedAt   string //
	CreatedAt     string //
	UpdatedAt     string //
}

// importHistoriesColumns holds the columns for the table import_histories.
var importHistoriesColumns = ImportHistoriesColumns{
	Id:            "id",
	UserId:        "user_id",
	Type:          "type",
	FileName:      "file_name",
	Status:        "status",
	TotalRows:     "total_rows",
	SuccessRows:   "success_rows",
	FailedRows:    "failed_rows",
	ErrorMessages: "error_messages",
	CompletedAt:   "completed_at",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
}

// NewImportHistoriesDao creates and returns a new DAO object for table data access.
func NewImportHistoriesDao() *ImportHistoriesDao {
	return &ImportHistoriesDao{
		group:   "default",
		table:   "import_histories",
		columns: importHistoriesColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *ImportHistoriesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *ImportHistoriesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *ImportHistoriesDao) Columns() ImportHistoriesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *ImportHistoriesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *ImportHistoriesDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *ImportHistoriesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
