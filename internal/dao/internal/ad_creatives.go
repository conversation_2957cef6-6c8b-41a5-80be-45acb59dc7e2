// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdCreativesDao is the data access object for the table ad_creatives.
type AdCreativesDao struct {
	table   string             // table is the underlying table name of the DAO.
	group   string             // group is the database configuration group name of the current DAO.
	columns AdCreativesColumns // columns contains all the column names of Table for convenient usage.
}

// AdCreativesColumns defines and stores column names for the table ad_creatives.
type AdCreativesColumns struct {
	Id              string // 主键ID
	Name            string // 创意名称
	ImageUrl        string // 素材图片地址
	ImageArea       string // 图片区域宽度
	HotAreas        string // 热区，JSON字符串
	CreatedAt       string // 创建时间
	UpdatedAt       string // 更新时间
	BackgroundColor string //
}

// adCreativesColumns holds the columns for the table ad_creatives.
var adCreativesColumns = AdCreativesColumns{
	Id:              "id",
	Name:            "name",
	ImageUrl:        "image_url",
	ImageArea:       "image_area",
	HotAreas:        "hot_areas",
	CreatedAt:       "created_at",
	UpdatedAt:       "updated_at",
	BackgroundColor: "background_color",
}

// NewAdCreativesDao creates and returns a new DAO object for table data access.
func NewAdCreativesDao() *AdCreativesDao {
	return &AdCreativesDao{
		group:   "default",
		table:   "ad_creatives",
		columns: adCreativesColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdCreativesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdCreativesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdCreativesDao) Columns() AdCreativesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdCreativesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdCreativesDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdCreativesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
