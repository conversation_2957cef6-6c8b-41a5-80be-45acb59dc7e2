// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// RolesDao is the data access object for the table roles.
type RolesDao struct {
	table   string       // table is the underlying table name of the DAO.
	group   string       // group is the database configuration group name of the current DAO.
	columns RolesColumns // columns contains all the column names of Table for convenient usage.
}

// RolesColumns defines and stores column names for the table roles.
type RolesColumns struct {
	Id          string // 角色ID
	Name        string // 角色名称
	Code        string // 角色代码
	Description string // 角色描述
	Status      string // 状态：1启用 0禁用
	CreatedAt   string // 创建时间
	UpdatedAt   string // 更新时间
}

// rolesColumns holds the columns for the table roles.
var rolesColumns = RolesColumns{
	Id:          "id",
	Name:        "name",
	Code:        "code",
	Description: "description",
	Status:      "status",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

// NewRolesDao creates and returns a new DAO object for table data access.
func NewRolesDao() *RolesDao {
	return &RolesDao{
		group:   "default",
		table:   "roles",
		columns: rolesColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *RolesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *RolesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *RolesDao) Columns() RolesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *RolesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *RolesDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *RolesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
} 