// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdSlotsDao is the data access object for the table ad_slots.
type AdSlotsDao struct {
	table   string         // table is the underlying table name of the DAO.
	group   string         // group is the database configuration group name of the current DAO.
	columns AdSlotsColumns // columns contains all the column names of Table for convenient usage.
}

// AdSlotsColumns defines and stores column names for the table ad_slots.
type AdSlotsColumns struct {
	Id            string //
	Code          string //
	Type          string //
	Name          string //
	UserId        string //
	MediaId       string //
	MediaName     string //
	ScreenshotUrl string //
	VideoUrl      string //
	AuditStatus   string //
	RejectReason  string //
	LeakRate      string //
	DiscountRate  string //
	Remark        string //
	CreatedBy     string //
	UpdatedBy     string //
	CreatedAt     string //
	UpdatedAt     string //
	DeletedAt     string //
}

// adSlotsColumns holds the columns for the table ad_slots.
var adSlotsColumns = AdSlotsColumns{
	Id:            "id",
	Code:          "code",
	Type:          "type",
	Name:          "name",
	UserId:        "user_id",
	MediaId:       "media_id",
	MediaName:     "media_name",
	ScreenshotUrl: "screenshot_url",
	VideoUrl:      "video_url",
	AuditStatus:   "audit_status",
	RejectReason:  "reject_reason",
	LeakRate:      "leak_rate",
	DiscountRate:  "discount_rate",
	Remark:        "remark",
	CreatedBy:     "created_by",
	UpdatedBy:     "updated_by",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
	DeletedAt:     "deleted_at",
}

// NewAdSlotsDao creates and returns a new DAO object for table data access.
func NewAdSlotsDao() *AdSlotsDao {
	return &AdSlotsDao{
		group:   "default",
		table:   "ad_slots",
		columns: adSlotsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdSlotsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdSlotsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdSlotsDao) Columns() AdSlotsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdSlotsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdSlotsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdSlotsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
