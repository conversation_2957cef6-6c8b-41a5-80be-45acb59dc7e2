// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ModelDecaysDao is the data access object for the table model_decays.
type ModelDecaysDao struct {
	table   string             // table is the underlying table name of the DAO.
	group   string             // group is the database configuration group name of the current DAO.
	columns ModelDecaysColumns // columns contains all the column names of Table for convenient usage.
}

// ModelDecaysColumns defines and stores column names for the table model_decays.
type ModelDecaysColumns struct {
	Id        string // 主键ID
	ModelId   string // 模型ID
	Percent   string // 衰减百分比
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
}

// modelDecaysColumns holds the columns for the table model_decays.
var modelDecaysColumns = ModelDecaysColumns{
	Id:        "id",
	ModelId:   "model_id",
	Percent:   "percent",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// NewModelDecaysDao creates and returns a new DAO object for table data access.
func NewModelDecaysDao() *ModelDecaysDao {
	return &ModelDecaysDao{
		group:   "default",
		table:   "model_decays",
		columns: modelDecaysColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *ModelDecaysDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *ModelDecaysDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *ModelDecaysDao) Columns() ModelDecaysColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *ModelDecaysDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *ModelDecaysDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *ModelDecaysDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
