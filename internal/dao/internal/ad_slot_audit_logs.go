// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdSlotAuditLogsDao is the data access object for the table ad_slot_audit_logs.
type AdSlotAuditLogsDao struct {
	table   string                 // table is the underlying table name of the DAO.
	group   string                 // group is the database configuration group name of the current DAO.
	columns AdSlotAuditLogsColumns // columns contains all the column names of Table for convenient usage.
}

// AdSlotAuditLogsColumns defines and stores column names for the table ad_slot_audit_logs.
type AdSlotAuditLogsColumns struct {
	Id           string //
	SlotId       string // 广告位ID
	Event        string // 事件：create-创建审核,update-变更审核
	SubmitterId  string // 发起人ID
	AuditStatus  string // 审核状态：pending-待审核,approved-已通过,rejected-已拒绝
	AuditorId    string // 审核人ID
	AuditAt      string // 审核时间
	RejectReason string // 拒绝理由
	Remark       string // 备注
	AuditDetail  string // 审核详情
	CreatedAt    string //
	UpdatedAt    string //
	DeletedAt    string //
}

// adSlotAuditLogsColumns holds the columns for the table ad_slot_audit_logs.
var adSlotAuditLogsColumns = AdSlotAuditLogsColumns{
	Id:           "id",
	SlotId:       "slot_id",
	Event:        "event",
	SubmitterId:  "submitter_id",
	AuditStatus:  "audit_status",
	AuditorId:    "auditor_id",
	AuditAt:      "audit_at",
	RejectReason: "reject_reason",
	Remark:       "remark",
	AuditDetail:  "audit_detail",
	CreatedAt:    "created_at",
	UpdatedAt:    "updated_at",
	DeletedAt:    "deleted_at",
}

// NewAdSlotAuditLogsDao creates and returns a new DAO object for table data access.
func NewAdSlotAuditLogsDao() *AdSlotAuditLogsDao {
	return &AdSlotAuditLogsDao{
		group:   "default",
		table:   "ad_slot_audit_logs",
		columns: adSlotAuditLogsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdSlotAuditLogsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdSlotAuditLogsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdSlotAuditLogsDao) Columns() AdSlotAuditLogsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdSlotAuditLogsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdSlotAuditLogsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdSlotAuditLogsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
