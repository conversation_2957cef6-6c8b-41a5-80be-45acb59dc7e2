// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdSlotPlanPriceLogsDao is the data access object for the table ad_slot_plan_price_logs.
type AdSlotPlanPriceLogsDao struct {
	table   string                     // table is the underlying table name of the DAO.
	group   string                     // group is the database configuration group name of the current DAO.
	columns AdSlotPlanPriceLogsColumns // columns contains all the column names of Table for convenient usage.
}

// AdSlotPlanPriceLogsColumns defines and stores column names for the table ad_slot_plan_price_logs.
type AdSlotPlanPriceLogsColumns struct {
	Id         string //
	PlanId     string // 计划ID
	OldPrice   string // 变动前价格
	NewPrice   string // 当前价格
	OperatorId string // 操作人ID
	Remark     string // 变动原因
	CreatedAt  string //
	UpdatedAt  string //
}

// adSlotPlanPriceLogsColumns holds the columns for the table ad_slot_plan_price_logs.
var adSlotPlanPriceLogsColumns = AdSlotPlanPriceLogsColumns{
	Id:         "id",
	PlanId:     "plan_id",
	OldPrice:   "old_price",
	NewPrice:   "new_price",
	OperatorId: "operator_id",
	Remark:     "remark",
	CreatedAt:  "created_at",
	UpdatedAt:  "updated_at",
}

// NewAdSlotPlanPriceLogsDao creates and returns a new DAO object for table data access.
func NewAdSlotPlanPriceLogsDao() *AdSlotPlanPriceLogsDao {
	return &AdSlotPlanPriceLogsDao{
		group:   "default",
		table:   "ad_slot_plan_price_logs",
		columns: adSlotPlanPriceLogsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdSlotPlanPriceLogsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdSlotPlanPriceLogsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdSlotPlanPriceLogsDao) Columns() AdSlotPlanPriceLogsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdSlotPlanPriceLogsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdSlotPlanPriceLogsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdSlotPlanPriceLogsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
