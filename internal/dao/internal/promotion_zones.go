// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// PromotionZonesDao is the data access object for the table promotion_zones.
type PromotionZonesDao struct {
	table   string                // table is the underlying table name of the DAO.
	group   string                // group is the database configuration group name of the current DAO.
	columns PromotionZonesColumns // columns contains all the column names of Table for convenient usage.
}

// PromotionZonesColumns defines and stores column names for the table promotion_zones.
type PromotionZonesColumns struct {
	Id              string //
	PlanId          string // 投放计划ID
	AdProductId     string // 产品ID
	ZoneId          string // 推广位ID
	ZoneName        string // 推广位名称
	Pid             string // PID
	ZoneType        string // 推广位类型：ele-饿了么，fliggy-飞猪
	PromotionLink   string // 推广链接
	PromotionQrcode string // 推广二维码
	ZoneParams      string // 推广位参数
	PromotionParams string // 推广参数
	MergedPageUrl   string // 融合页面URL
	MergeParams     string // 融合参数
	CreatedBy       string // 创建人ID
	UpdatedBy       string // 更新人ID
	CreatedAt       string //
	UpdatedAt       string //
	DeletedAt       string //
}

// promotionZonesColumns holds the columns for the table promotion_zones.
var promotionZonesColumns = PromotionZonesColumns{
	Id:              "id",
	PlanId:          "plan_id",
	AdProductId:     "ad_product_id",
	ZoneId:          "zone_id",
	ZoneName:        "zone_name",
	Pid:             "pid",
	ZoneType:        "zone_type",
	PromotionLink:   "promotion_link",
	PromotionQrcode: "promotion_qrcode",
	ZoneParams:      "zone_params",
	PromotionParams: "promotion_params",
	MergedPageUrl:   "merged_page_url",
	MergeParams:     "merge_params",
	CreatedBy:       "created_by",
	UpdatedBy:       "updated_by",
	CreatedAt:       "created_at",
	UpdatedAt:       "updated_at",
	DeletedAt:       "deleted_at",
}

// NewPromotionZonesDao creates and returns a new DAO object for table data access.
func NewPromotionZonesDao() *PromotionZonesDao {
	return &PromotionZonesDao{
		group:   "default",
		table:   "promotion_zones",
		columns: promotionZonesColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *PromotionZonesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *PromotionZonesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *PromotionZonesDao) Columns() PromotionZonesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *PromotionZonesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *PromotionZonesDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *PromotionZonesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
