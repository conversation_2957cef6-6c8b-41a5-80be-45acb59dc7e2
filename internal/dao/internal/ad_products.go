// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdProductsDao is the data access object for the table ad_products.
type AdProductsDao struct {
	table   string            // table is the underlying table name of the DAO.
	group   string            // group is the database configuration group name of the current DAO.
	columns AdProductsColumns // columns contains all the column names of Table for convenient usage.
}

// AdProductsColumns defines and stores column names for the table ad_products.
type AdProductsColumns struct {
	Id          string //
	Code        string // 产品编号
	Name        string // 广告产品名称
	Image       string // 广告产品图片
	Description string // 广告产品描述
	Status      string // 状态：active-启用,inactive-停用
	CreatedBy   string // 创建人ID
	UpdatedBy   string // 更新人ID
	CreatedAt   string //
	UpdatedAt   string //
	DeletedAt   string //
}

// adProductsColumns holds the columns for the table ad_products.
var adProductsColumns = AdProductsColumns{
	Id:          "id",
	Code:        "code",
	Name:        "name",
	Image:       "image",
	Description: "description",
	Status:      "status",
	CreatedBy:   "created_by",
	UpdatedBy:   "updated_by",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
	DeletedAt:   "deleted_at",
}

// NewAdProductsDao creates and returns a new DAO object for table data access.
func NewAdProductsDao() *AdProductsDao {
	return &AdProductsDao{
		group:   "default",
		table:   "ad_products",
		columns: adProductsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdProductsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdProductsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdProductsDao) Columns() AdProductsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdProductsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdProductsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdProductsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
