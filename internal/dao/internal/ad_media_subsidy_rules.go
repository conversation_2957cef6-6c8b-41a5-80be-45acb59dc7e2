// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdMediaSubsidyRulesDao is the data access object for the table ad_media_subsidy_rules.
type AdMediaSubsidyRulesDao struct {
	table   string                     // table is the underlying table name of the DAO.
	group   string                     // group is the database configuration group name of the current DAO.
	columns AdMediaSubsidyRulesColumns // columns contains all the column names of Table for convenient usage.
}

// AdMediaSubsidyRulesColumns defines and stores column names for the table ad_media_subsidy_rules.
type AdMediaSubsidyRulesColumns struct {
	Id              string //
	Level           string // 等级:A,B,C,D,E
	MediaType       string // 媒体类型
	CooperationType string // 合作类型
	OrdersFrom      string // 订单数起始值
	OrdersTo        string // 订单数结束值
	SubsidyRate     string // 补贴比例
	CreatedAt       string //
	UpdatedAt       string //
	DeletedAt       string //
}

// adMediaSubsidyRulesColumns holds the columns for the table ad_media_subsidy_rules.
var adMediaSubsidyRulesColumns = AdMediaSubsidyRulesColumns{
	Id:              "id",
	Level:           "level",
	MediaType:       "media_type",
	CooperationType: "cooperation_type",
	OrdersFrom:      "orders_from",
	OrdersTo:        "orders_to",
	SubsidyRate:     "subsidy_rate",
	CreatedAt:       "created_at",
	UpdatedAt:       "updated_at",
	DeletedAt:       "deleted_at",
}

// NewAdMediaSubsidyRulesDao creates and returns a new DAO object for table data access.
func NewAdMediaSubsidyRulesDao() *AdMediaSubsidyRulesDao {
	return &AdMediaSubsidyRulesDao{
		group:   "default",
		table:   "ad_media_subsidy_rules",
		columns: adMediaSubsidyRulesColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdMediaSubsidyRulesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdMediaSubsidyRulesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdMediaSubsidyRulesDao) Columns() AdMediaSubsidyRulesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdMediaSubsidyRulesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdMediaSubsidyRulesDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdMediaSubsidyRulesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
