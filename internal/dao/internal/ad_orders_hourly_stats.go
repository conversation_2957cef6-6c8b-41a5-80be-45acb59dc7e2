// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdOrdersHourlyStatsDao is the data access object for the table ad_orders_hourly_stats.
type AdOrdersHourlyStatsDao struct {
	table   string                         // table is the underlying table name of the DAO.
	group   string                         // group is the database configuration group name of the current DAO.
	columns AdOrdersHourlyStatsColumns     // columns contains all the column names of Table for convenient usage.
}

// AdOrdersHourlyStatsColumns defines and stores column names for the table ad_orders_hourly_stats.
type AdOrdersHourlyStatsColumns struct {
	Id                   string // 主键ID
	Date                 string // 日期
	Hour                 string // 小时(0-23)
	AdPlanId             string // 计划ID
	AdMediaId            string // 媒体ID
	AdSlotId             string // 广告位ID
	Orders               string // 订单数
	EstimatedCommission  string // 预估佣金
	SettledCommission    string // 结算佣金
	OrderAmount          string // 订单金额
	CreatedAt            string // 创建时间
	UpdatedAt            string // 更新时间
}

// adOrdersHourlyStatsColumns holds the columns for the table ad_orders_hourly_stats.
var adOrdersHourlyStatsColumns = AdOrdersHourlyStatsColumns{
	Id:                   "id",
	Date:                 "date",
	Hour:                 "hour",
	AdPlanId:             "ad_plan_id",
	AdMediaId:            "ad_media_id",
	AdSlotId:             "ad_slot_id",
	Orders:               "orders",
	EstimatedCommission:  "estimated_commission",
	SettledCommission:    "settled_commission",
	OrderAmount:          "order_amount",
	CreatedAt:            "created_at",
	UpdatedAt:            "updated_at",
}

// NewAdOrdersHourlyStatsDao creates and returns a new DAO object for table data access.
func NewAdOrdersHourlyStatsDao() *AdOrdersHourlyStatsDao {
	return &AdOrdersHourlyStatsDao{
		group:   "default",
		table:   "ad_orders_hourly_stats",
		columns: adOrdersHourlyStatsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdOrdersHourlyStatsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdOrdersHourlyStatsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdOrdersHourlyStatsDao) Columns() AdOrdersHourlyStatsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdOrdersHourlyStatsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdOrdersHourlyStatsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdOrdersHourlyStatsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
} 