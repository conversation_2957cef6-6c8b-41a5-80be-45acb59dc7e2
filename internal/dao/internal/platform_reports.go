// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// PlatformReportsDao is the data access object for table platform_reports.
type PlatformReportsDao struct {
	table   string                // table is the underlying table name of the DAO.
	group   string                // group is the database configuration group name of the current DAO.
	columns PlatformReportColumns // columns contains all the column names of Table for convenient usage.
}

// PlatformReportColumns defines and stores column names for table platform_reports.
type PlatformReportColumns struct {
	Id         string // 主键
	BelongId   string // 关联ID
	BelongType string // 关联类型
	BizDate    string // 数据日期
	Impression string // 展现量
	Click      string // 点击量
	Cost       string // 消费金额(分)
	CreatedAt  string // 创建时间
	UpdatedAt  string // 更新时间
}

// platformReportColumns holds the columns for table platform_reports.
var platformReportColumns = PlatformReportColumns{
	Id:         "id",
	BelongId:   "belong_id",
	BelongType: "belong_type",
	BizDate:    "biz_date",
	Impression: "impression",
	Click:      "click",
	Cost:       "cost",
	CreatedAt:  "created_at",
	UpdatedAt:  "updated_at",
}

// NewPlatformReportsDao creates and returns a new DAO object for table data access.
func NewPlatformReportsDao() *PlatformReportsDao {
	return &PlatformReportsDao{
		group:   "default",
		table:   "platform_reports",
		columns: platformReportColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *PlatformReportsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current DAO.
func (dao *PlatformReportsDao) Table() string {
	return dao.table
}

// Columns returns all column names of current DAO.
func (dao *PlatformReportsDao) Columns() PlatformReportColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current DAO.
func (dao *PlatformReportsDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *PlatformReportsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error if function f returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function. 