// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdMediaMonthlyLevelsDao is the data access object for the table ad_media_monthly_levels.
type AdMediaMonthlyLevelsDao struct {
	table   string                      // table is the underlying table name of the DAO.
	group   string                      // group is the database configuration group name of the current DAO.
	columns AdMediaMonthlyLevelsColumns // columns contains all the column names of Table for convenient usage.
}

// AdMediaMonthlyLevelsColumns defines and stores column names for the table ad_media_monthly_levels.
type AdMediaMonthlyLevelsColumns struct {
	Id          string //
	MediaId     string // 媒体ID
	Month       string // 统计月份
	Orders      string // 月订单数
	DailyOrders string // 日均订单数
	Level       string // 等级
	SubsidyRate string // 补贴比例
	CreatedAt   string //
	UpdatedAt   string //
	DeletedAt   string //
}

// adMediaMonthlyLevelsColumns holds the columns for the table ad_media_monthly_levels.
var adMediaMonthlyLevelsColumns = AdMediaMonthlyLevelsColumns{
	Id:          "id",
	MediaId:     "media_id",
	Month:       "month",
	Orders:      "orders",
	DailyOrders: "daily_orders",
	Level:       "level",
	SubsidyRate: "subsidy_rate",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
	DeletedAt:   "deleted_at",
}

// NewAdMediaMonthlyLevelsDao creates and returns a new DAO object for table data access.
func NewAdMediaMonthlyLevelsDao() *AdMediaMonthlyLevelsDao {
	return &AdMediaMonthlyLevelsDao{
		group:   "default",
		table:   "ad_media_monthly_levels",
		columns: adMediaMonthlyLevelsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdMediaMonthlyLevelsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdMediaMonthlyLevelsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdMediaMonthlyLevelsDao) Columns() AdMediaMonthlyLevelsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdMediaMonthlyLevelsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdMediaMonthlyLevelsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdMediaMonthlyLevelsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
