package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// PasswordDO is the data object for password table
type PasswordDO struct {
	Id        uint   `json:"id"         description:"Primary key"`
	GroupId   uint   `json:"group_id"   description:"Group ID"`
	Pid       string `json:"pid"        description:"Password ID"`
	Name      string `json:"name"       description:"Password name"`
	CreatedAt string `json:"created_at" description:"Created time"`
	UpdatedAt string `json:"updated_at" description:"Updated time"`
}

// PasswordColumns defines the columns of password table
type PasswordColumns struct {
	Id        string // Primary key
	GroupId   string // Group ID
	Pid       string // Password ID
	Name      string // Password name
	CreatedAt string // Created time
	UpdatedAt string // Updated time
}

var (
	// Password is the table name
	Password = "passwords"
	// PasswordTable is the columns of password table
	PasswordTable = PasswordColumns{
		Id:        "id",
		GroupId:   "group_id",
		Pid:       "pid",
		Name:      "name",
		CreatedAt: "created_at",
		UpdatedAt: "updated_at",
	}
)

// PasswordDao is the manager for logic model data accessing and custom defined data operations functions management.
type PasswordDao struct {
	table   string          // Table name of the dao.
	group   string          // Database configuration group name of the dao.
	columns PasswordColumns // Columns makes it easier to write db operations.
}

// NewPasswordDao creates and returns a new DAO object for table data access.
func NewPasswordDao() *PasswordDao {
	return &PasswordDao{
		group:   "default",
		table:   Password,
		columns: PasswordTable,
	}
}

// DB retrieves and returns the underlying database connection object.
func (dao *PasswordDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *PasswordDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *PasswordDao) Columns() PasswordColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *PasswordDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *PasswordDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
func (dao *PasswordDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
