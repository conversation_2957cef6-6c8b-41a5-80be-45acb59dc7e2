// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdSlotPlanDailyOrderStatsDao is the data access object for the table ad_slot_plan_daily_order_stats.
type AdSlotPlanDailyOrderStatsDao struct {
	table   string                           // table is the underlying table name of the DAO.
	group   string                           // group is the database configuration group name of the current DAO.
	columns AdSlotPlanDailyOrderStatsColumns // columns contains all the column names of Table for convenient usage.
}

// AdSlotPlanDailyOrderStatsColumns defines and stores column names for the table ad_slot_plan_daily_order_stats.
type AdSlotPlanDailyOrderStatsColumns struct {
	Id                     string //
	DailyStatId            string //
	OrderType              string // 订单类型
	BdOrders               string // 媒介订单数
	BdSettleOrders         string // 媒介结算订单数
	AdminOrders            string // 管理员订单数
	AdminSettleOrders      string // 实际结算订单数
	BdRevenue              string // 媒介收入
	BdSettleRevenue        string // 媒介结算收入
	BdActivityFee          string // 媒介激励费用
	BdSettleActivityFee    string // 媒介结算激励
	AdminRevenue           string // 管理员收入
	AdminSettleRevenue     string // 实际结算收入
	AdminActivityFee       string // 实际激励费用
	AdminSettleActivityFee string // 实际结算��励
	CreatedAt              string //
	UpdatedAt              string //
}

// adSlotPlanDailyOrderStatsColumns holds the columns for the table ad_slot_plan_daily_order_stats.
var adSlotPlanDailyOrderStatsColumns = AdSlotPlanDailyOrderStatsColumns{
	Id:                     "id",
	DailyStatId:            "daily_stat_id",
	OrderType:              "order_type",
	BdOrders:               "bd_orders",
	BdSettleOrders:         "bd_settle_orders",
	AdminOrders:            "admin_orders",
	AdminSettleOrders:      "admin_settle_orders",
	BdRevenue:              "bd_revenue",
	BdSettleRevenue:        "bd_settle_revenue",
	BdActivityFee:          "bd_activity_fee",
	BdSettleActivityFee:    "bd_settle_activity_fee",
	AdminRevenue:           "admin_revenue",
	AdminSettleRevenue:     "admin_settle_revenue",
	AdminActivityFee:       "admin_activity_fee",
	AdminSettleActivityFee: "admin_settle_activity_fee",
	CreatedAt:              "created_at",
	UpdatedAt:              "updated_at",
}

// NewAdSlotPlanDailyOrderStatsDao creates and returns a new DAO object for table data access.
func NewAdSlotPlanDailyOrderStatsDao() *AdSlotPlanDailyOrderStatsDao {
	return &AdSlotPlanDailyOrderStatsDao{
		group:   "default",
		table:   "ad_slot_plan_daily_order_stats",
		columns: adSlotPlanDailyOrderStatsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdSlotPlanDailyOrderStatsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdSlotPlanDailyOrderStatsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdSlotPlanDailyOrderStatsDao) Columns() AdSlotPlanDailyOrderStatsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdSlotPlanDailyOrderStatsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdSlotPlanDailyOrderStatsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdSlotPlanDailyOrderStatsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
