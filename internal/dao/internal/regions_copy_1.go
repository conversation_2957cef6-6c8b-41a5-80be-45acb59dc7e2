// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// RegionsCopy1Dao is the data access object for the table regions_copy1.
type RegionsCopy1Dao struct {
	table   string              // table is the underlying table name of the DAO.
	group   string              // group is the database configuration group name of the current DAO.
	columns RegionsCopy1Columns // columns contains all the column names of Table for convenient usage.
}

// RegionsCopy1Columns defines and stores column names for the table regions_copy1.
type RegionsCopy1Columns struct {
	Id        string //
	ParentId  string // 父级ID
	Name      string // 地区名称
	Code      string // 地区编码
	Level     string // 层级：1=省份，2=城市，3=区县
	IsEnabled string // 是否启用
	Sort      string // 排序
	CreatedAt string //
	UpdatedAt string //
}

// regionsCopy1Columns holds the columns for the table regions_copy1.
var regionsCopy1Columns = RegionsCopy1Columns{
	Id:        "id",
	ParentId:  "parent_id",
	Name:      "name",
	Code:      "code",
	Level:     "level",
	IsEnabled: "is_enabled",
	Sort:      "sort",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// NewRegionsCopy1Dao creates and returns a new DAO object for table data access.
func NewRegionsCopy1Dao() *RegionsCopy1Dao {
	return &RegionsCopy1Dao{
		group:   "default",
		table:   "regions_copy1",
		columns: regionsCopy1Columns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *RegionsCopy1Dao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *RegionsCopy1Dao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *RegionsCopy1Dao) Columns() RegionsCopy1Columns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *RegionsCopy1Dao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *RegionsCopy1Dao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *RegionsCopy1Dao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
