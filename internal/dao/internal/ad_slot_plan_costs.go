// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdSlotPlanCostsDao is the data access object for the table ad_slot_plan_costs.
type AdSlotPlanCostsDao struct {
	table   string                 // table is the underlying table name of the DAO.
	group   string                 // group is the database configuration group name of the current DAO.
	columns AdSlotPlanCostsColumns // columns contains all the column names of Table for convenient usage.
}

// AdSlotPlanCostsColumns defines and stores column names for the table ad_slot_plan_costs.
type AdSlotPlanCostsColumns struct {
	Id           string //
	PlanId       string // 投放计划ID
	Date         string // 统计日期
	MediaId      string // 媒体ID
	AdSlotId     string // 广告位ID
	AdProductId  string // 产品ID
	UserId       string // 媒介ID
	Clicks       string // 点击量
	Cost         string // 成本
	Remark       string // 备注
	AuditStatus  string // 审核状态:pending=待审核,approved=已通过,rejected=已拒绝
	AuditBy      string // 审核人
	AuditAt      string // 审核时间
	RejectReason string // 拒绝原因
	CreatedBy    string // 创建人
	UpdatedBy    string // 更新人
	CreatedAt    string //
	UpdatedAt    string //
	DeletedAt    string //
}

// adSlotPlanCostsColumns holds the columns for the table ad_slot_plan_costs.
var adSlotPlanCostsColumns = AdSlotPlanCostsColumns{
	Id:           "id",
	PlanId:       "plan_id",
	Date:         "date",
	MediaId:      "media_id",
	AdSlotId:     "ad_slot_id",
	AdProductId:  "ad_product_id",
	UserId:       "user_id",
	Clicks:       "clicks",
	Cost:         "cost",
	Remark:       "remark",
	AuditStatus:  "audit_status",
	AuditBy:      "audit_by",
	AuditAt:      "audit_at",
	RejectReason: "reject_reason",
	CreatedBy:    "created_by",
	UpdatedBy:    "updated_by",
	CreatedAt:    "created_at",
	UpdatedAt:    "updated_at",
	DeletedAt:    "deleted_at",
}

// NewAdSlotPlanCostsDao creates and returns a new DAO object for table data access.
func NewAdSlotPlanCostsDao() *AdSlotPlanCostsDao {
	return &AdSlotPlanCostsDao{
		group:   "default",
		table:   "ad_slot_plan_costs",
		columns: adSlotPlanCostsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdSlotPlanCostsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdSlotPlanCostsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdSlotPlanCostsDao) Columns() AdSlotPlanCostsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdSlotPlanCostsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdSlotPlanCostsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdSlotPlanCostsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
