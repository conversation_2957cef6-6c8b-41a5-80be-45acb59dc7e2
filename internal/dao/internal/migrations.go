// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// MigrationsDao is the data access object for the table migrations.
type MigrationsDao struct {
	table   string            // table is the underlying table name of the DAO.
	group   string            // group is the database configuration group name of the current DAO.
	columns MigrationsColumns // columns contains all the column names of Table for convenient usage.
}

// MigrationsColumns defines and stores column names for the table migrations.
type MigrationsColumns struct {
	Id        string //
	Migration string //
	Batch     string //
}

// migrationsColumns holds the columns for the table migrations.
var migrationsColumns = MigrationsColumns{
	Id:        "id",
	Migration: "migration",
	Batch:     "batch",
}

// NewMigrationsDao creates and returns a new DAO object for table data access.
func NewMigrationsDao() *MigrationsDao {
	return &MigrationsDao{
		group:   "default",
		table:   "migrations",
		columns: migrationsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *MigrationsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *MigrationsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *MigrationsDao) Columns() MigrationsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *MigrationsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *MigrationsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *MigrationsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
