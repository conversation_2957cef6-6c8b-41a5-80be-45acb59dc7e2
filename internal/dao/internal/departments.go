// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// DepartmentsDao is the data access object for the table departments.
type DepartmentsDao struct {
	table   string             // table is the underlying table name of the DAO.
	group   string             // group is the database configuration group name of the current DAO.
	columns DepartmentsColumns // columns contains all the column names of Table for convenient usage.
}

// DepartmentsColumns defines and stores column names for the table departments.
type DepartmentsColumns struct {
	Id          string // 部门ID
	Name        string // 部门名称
	ParentId    string // 上级部门ID
	ManagerId   string // 部门负责人ID
	Description string // 部门描述
	Status      string // 状态：1启用 0禁用
	Sort        string // 排序
	CreatedAt   string // 创建时间
	UpdatedAt   string // 更新时间
}

// departmentsColumns holds the columns for the table departments.
var departmentsColumns = DepartmentsColumns{
	Id:          "id",
	Name:        "name",
	ParentId:    "parent_id",
	ManagerId:   "manager_id",
	Description: "description",
	Status:      "status",
	Sort:        "sort",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

// NewDepartmentsDao creates and returns a new DAO object for table data access.
func NewDepartmentsDao() *DepartmentsDao {
	return &DepartmentsDao{
		group:   "default",
		table:   "departments",
		columns: departmentsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *DepartmentsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *DepartmentsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *DepartmentsDao) Columns() DepartmentsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *DepartmentsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *DepartmentsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *DepartmentsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
} 