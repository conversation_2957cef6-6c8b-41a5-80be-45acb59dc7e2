// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserMonthlyTargetsDao is the data access object for the table user_monthly_targets.
type UserMonthlyTargetsDao struct {
	table   string                    // table is the underlying table name of the DAO.
	group   string                    // group is the database configuration group name of the current DAO.
	columns UserMonthlyTargetsColumns // columns contains all the column names of Table for convenient usage.
}

// UserMonthlyTargetsColumns defines and stores column names for the table user_monthly_targets.
type UserMonthlyTargetsColumns struct {
	Id           string //
	UserId       string //
	Year         string //
	Month        string //
	ProfitTarget string //
	ActualProfit string //
	Remark       string //
	CreatedBy    string //
	UpdatedBy    string //
	CreatedAt    string //
	UpdatedAt    string //
	DeletedAt    string //
}

// userMonthlyTargetsColumns holds the columns for the table user_monthly_targets.
var userMonthlyTargetsColumns = UserMonthlyTargetsColumns{
	Id:           "id",
	UserId:       "user_id",
	Year:         "year",
	Month:        "month",
	ProfitTarget: "profit_target",
	ActualProfit: "actual_profit",
	Remark:       "remark",
	CreatedBy:    "created_by",
	UpdatedBy:    "updated_by",
	CreatedAt:    "created_at",
	UpdatedAt:    "updated_at",
	DeletedAt:    "deleted_at",
}

// NewUserMonthlyTargetsDao creates and returns a new DAO object for table data access.
func NewUserMonthlyTargetsDao() *UserMonthlyTargetsDao {
	return &UserMonthlyTargetsDao{
		group:   "default",
		table:   "user_monthly_targets",
		columns: userMonthlyTargetsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserMonthlyTargetsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserMonthlyTargetsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserMonthlyTargetsDao) Columns() UserMonthlyTargetsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserMonthlyTargetsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserMonthlyTargetsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserMonthlyTargetsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
