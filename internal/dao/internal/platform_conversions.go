// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// PlatformConversionsDao is the data access object for table platform_conversions.
type PlatformConversionsDao struct {
	table   string                    // table is the underlying table name of the DAO.
	group   string                    // group is the database configuration group name of the current DAO.
	columns PlatformConversionColumns // columns contains all the column names of Table for convenient usage.
}

// PlatformConversionColumns defines and stores column names for table platform_conversions.
type PlatformConversionColumns struct {
	Id                 string // 主键
	BelongId           string // 关联ID
	BelongType         string // 关联类型
	PlanId             string // 计划ID
	BizDate            string // 数据日期
	ConversionType     string // 转化事件类型
	ConversionTypeName string // 转化事件类型名称
	ConversionResult   string // 转化事件结果
	CreatedAt          string // 创建时间
	UpdatedAt          string // 更新时间
}

// platformConversionColumns holds the columns for table platform_conversions.
var platformConversionColumns = PlatformConversionColumns{
	Id:                 "id",
	BelongId:           "belong_id",
	BelongType:         "belong_type",
	PlanId:             "plan_id",
	BizDate:            "biz_date",
	ConversionType:     "conversion_type",
	ConversionTypeName: "conversion_type_name",
	ConversionResult:   "conversion_result",
	CreatedAt:          "created_at",
	UpdatedAt:          "updated_at",
}

// NewPlatformConversionsDao creates and returns a new DAO object for table data access.
func NewPlatformConversionsDao() *PlatformConversionsDao {
	return &PlatformConversionsDao{
		group:   "default",
		table:   "platform_conversions",
		columns: platformConversionColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *PlatformConversionsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current DAO.
func (dao *PlatformConversionsDao) Table() string {
	return dao.table
}

// Columns returns all column names of current DAO.
func (dao *PlatformConversionsDao) Columns() PlatformConversionColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current DAO.
func (dao *PlatformConversionsDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *PlatformConversionsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error if function f returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function. 