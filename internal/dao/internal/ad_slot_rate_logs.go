// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdSlotRateLogsDao is the data access object for the table ad_slot_rate_logs.
type AdSlotRateLogsDao struct {
	table   string                // table is the underlying table name of the DAO.
	group   string                // group is the database configuration group name of the current DAO.
	columns AdSlotRateLogsColumns // columns contains all the column names of Table for convenient usage.
}

// AdSlotRateLogsColumns defines and stores column names for the table ad_slot_rate_logs.
type AdSlotRateLogsColumns struct {
	Id        string //
	AdSlotId  string // 广告位ID
	UserId    string // 操作人ID
	Field     string // 修改字段：leak_rate-泄漏率,discount_rate-折扣率
	OldValue  string // 原值
	NewValue  string // 新值
	Remark    string // 修改原因
	CreatedAt string //
	UpdatedAt string //
}

// adSlotRateLogsColumns holds the columns for the table ad_slot_rate_logs.
var adSlotRateLogsColumns = AdSlotRateLogsColumns{
	Id:        "id",
	AdSlotId:  "ad_slot_id",
	UserId:    "user_id",
	Field:     "field",
	OldValue:  "old_value",
	NewValue:  "new_value",
	Remark:    "remark",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// NewAdSlotRateLogsDao creates and returns a new DAO object for table data access.
func NewAdSlotRateLogsDao() *AdSlotRateLogsDao {
	return &AdSlotRateLogsDao{
		group:   "default",
		table:   "ad_slot_rate_logs",
		columns: adSlotRateLogsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdSlotRateLogsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdSlotRateLogsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdSlotRateLogsDao) Columns() AdSlotRateLogsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdSlotRateLogsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdSlotRateLogsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdSlotRateLogsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
