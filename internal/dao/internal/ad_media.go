// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdMediaDao is the data access object for the table ad_media.
type AdMediaDao struct {
	table   string         // table is the underlying table name of the DAO.
	group   string         // group is the database configuration group name of the current DAO.
	columns AdMediaColumns // columns contains all the column names of Table for convenient usage.
}

// AdMediaColumns defines and stores column names for the table ad_media.
type AdMediaColumns struct {
	Id                string //
	Code              string //
	AdAgentId         string //
	Name              string //
	AuditStatus       string //
	CooperationStatus string //
	CooperationType   string //
	Account           string //
	Password          string //
	LastLoginAt       string //
	Balance           string //
	Types             string //
	Industry          string //
	CustomIndustry    string //
	DailyActivity     string //
	TransactionVolume string //
	RegionCodes       string //
	CompanyName       string //
	CompanyAddress    string //
	ContactName       string //
	ContactPhone      string //
	RejectReason      string //
	UserId            string //
	Remark            string //
	CreatedBy         string //
	UpdatedBy         string //
	CreatedAt         string //
	UpdatedAt         string //
	DeletedAt         string //
	InstanceId        string //
	PlatformConfig    string // 平台配置 仅当 cooperationType=delivery 时可配置
}

// adMediaColumns holds the columns for the table ad_media.
var adMediaColumns = AdMediaColumns{
	Id:                "id",
	Code:              "code",
	AdAgentId:         "ad_agent_id",
	Name:              "name",
	AuditStatus:       "audit_status",
	CooperationStatus: "cooperation_status",
	CooperationType:   "cooperation_type",
	Account:           "account",
	Password:          "password",
	LastLoginAt:       "last_login_at",
	Balance:           "balance",
	Types:             "types",
	Industry:          "industry",
	CustomIndustry:    "custom_industry",
	DailyActivity:     "daily_activity",
	TransactionVolume: "transaction_volume",
	RegionCodes:       "region_codes",
	CompanyName:       "company_name",
	CompanyAddress:    "company_address",
	ContactName:       "contact_name",
	ContactPhone:      "contact_phone",
	RejectReason:      "reject_reason",
	UserId:            "user_id",
	Remark:            "remark",
	CreatedBy:         "created_by",
	UpdatedBy:         "updated_by",
	CreatedAt:         "created_at",
	UpdatedAt:         "updated_at",
	DeletedAt:         "deleted_at",
	InstanceId:        "instance_id",
	PlatformConfig:    "platform_config",
}

// NewAdMediaDao creates and returns a new DAO object for table data access.
func NewAdMediaDao() *AdMediaDao {
	return &AdMediaDao{
		group:   "default",
		table:   "ad_media",
		columns: adMediaColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdMediaDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdMediaDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdMediaDao) Columns() AdMediaColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdMediaDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdMediaDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdMediaDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
