// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ExportTasksDao is the data access object for the table export_tasks.
type ExportTasksDao struct {
	table   string             // table is the underlying table name of the DAO.
	group   string             // group is the database configuration group name of the current DAO.
	columns ExportTasksColumns // columns contains all the column names of Table for convenient usage.
}

// ExportTasksColumns defines and stores column names for the table export_tasks.
type ExportTasksColumns struct {
	Id        string //
	CreatedAt string //
	UpdatedAt string //
	MediaId   string //
	Type      string //
	Status    string //
	FileUrl   string //
	Error     string //
	Params    string //
	TotalRows string //
	CreatedBy string //
}

// exportTasksColumns holds the columns for the table export_tasks.
var exportTasksColumns = ExportTasksColumns{
	Id:        "id",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
	MediaId:   "media_id",
	Type:      "type",
	Status:    "status",
	FileUrl:   "file_url",
	Error:     "error",
	Params:    "params",
	TotalRows: "total_rows",
	CreatedBy: "created_by",
}

// NewExportTasksDao creates and returns a new DAO object for table data access.
func NewExportTasksDao() *ExportTasksDao {
	return &ExportTasksDao{
		group:   "default",
		table:   "export_tasks",
		columns: exportTasksColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *ExportTasksDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *ExportTasksDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *ExportTasksDao) Columns() ExportTasksColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *ExportTasksDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *ExportTasksDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *ExportTasksDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
