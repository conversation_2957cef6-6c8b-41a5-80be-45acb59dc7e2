// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SyncStatsLogsDao is the data access object for the table sync_stats_logs.
type SyncStatsLogsDao struct {
	table   string               // table is the underlying table name of the DAO.
	group   string               // group is the database configuration group name of the current DAO.
	columns SyncStatsLogsColumns // columns contains all the column names of Table for convenient usage.
}

// SyncStatsLogsColumns defines and stores column names for the table sync_stats_logs.
type SyncStatsLogsColumns struct {
	Id            string //
	ProcedureName string // 存储过程名称
	Message       string // 日志消息
	CreatedAt     string //
}

// syncStatsLogsColumns holds the columns for the table sync_stats_logs.
var syncStatsLogsColumns = SyncStatsLogsColumns{
	Id:            "id",
	ProcedureName: "procedure_name",
	Message:       "message",
	CreatedAt:     "created_at",
}

// NewSyncStatsLogsDao creates and returns a new DAO object for table data access.
func NewSyncStatsLogsDao() *SyncStatsLogsDao {
	return &SyncStatsLogsDao{
		group:   "default",
		table:   "sync_stats_logs",
		columns: syncStatsLogsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SyncStatsLogsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SyncStatsLogsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SyncStatsLogsDao) Columns() SyncStatsLogsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SyncStatsLogsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SyncStatsLogsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SyncStatsLogsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
