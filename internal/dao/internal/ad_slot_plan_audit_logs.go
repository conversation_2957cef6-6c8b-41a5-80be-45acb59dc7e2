// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdSlotPlanAuditLogsDao is the data access object for the table ad_slot_plan_audit_logs.
type AdSlotPlanAuditLogsDao struct {
	table   string                     // table is the underlying table name of the DAO.
	group   string                     // group is the database configuration group name of the current DAO.
	columns AdSlotPlanAuditLogsColumns // columns contains all the column names of Table for convenient usage.
}

// AdSlotPlanAuditLogsColumns defines and stores column names for the table ad_slot_plan_audit_logs.
type AdSlotPlanAuditLogsColumns struct {
	Id           string //
	PlanId       string // 计划ID
	Event        string // 事件：create-创建审核,update-变更审核
	SubmitterId  string // 发起人ID
	AuditStatus  string // 审核状态：pending-待审核,approved-已通过,rejected-已拒绝
	AuditorId    string // 审核人ID
	AuditAt      string // 审核时间
	RejectReason string // 拒绝理由
	Remark       string // 备注
	AuditDetail  string // 审核详情
	CreatedAt    string //
	UpdatedAt    string //
	DeletedAt    string //
}

// adSlotPlanAuditLogsColumns holds the columns for the table ad_slot_plan_audit_logs.
var adSlotPlanAuditLogsColumns = AdSlotPlanAuditLogsColumns{
	Id:           "id",
	PlanId:       "plan_id",
	Event:        "event",
	SubmitterId:  "submitter_id",
	AuditStatus:  "audit_status",
	AuditorId:    "auditor_id",
	AuditAt:      "audit_at",
	RejectReason: "reject_reason",
	Remark:       "remark",
	AuditDetail:  "audit_detail",
	CreatedAt:    "created_at",
	UpdatedAt:    "updated_at",
	DeletedAt:    "deleted_at",
}

// NewAdSlotPlanAuditLogsDao creates and returns a new DAO object for table data access.
func NewAdSlotPlanAuditLogsDao() *AdSlotPlanAuditLogsDao {
	return &AdSlotPlanAuditLogsDao{
		group:   "default",
		table:   "ad_slot_plan_audit_logs",
		columns: adSlotPlanAuditLogsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdSlotPlanAuditLogsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdSlotPlanAuditLogsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdSlotPlanAuditLogsDao) Columns() AdSlotPlanAuditLogsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdSlotPlanAuditLogsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdSlotPlanAuditLogsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdSlotPlanAuditLogsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
