// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserLoginLogsDao is the data access object for the table user_login_logs.
type UserLoginLogsDao struct {
	table   string               // table is the underlying table name of the DAO.
	group   string               // group is the database configuration group name of the current DAO.
	columns UserLoginLogsColumns // columns contains all the column names of Table for convenient usage.
}

// UserLoginLogsColumns defines and stores column names for the table user_login_logs.
type UserLoginLogsColumns struct {
	Id         string //
	UserId     string // 用户ID
	Ip         string // 登录IP
	Country    string // 国家
	Province   string // 省份
	City       string // 城市
	District   string // 区域
	Isp        string // 网络运营商
	UserAgent  string // 浏览器信息
	DeviceType string // 设备类型：mobile-移动端，desktop-桌面端，tablet-平板
	Os         string // 操作系统
	Browser    string // 浏览器
	Status     string // 登录状态：success-成功，failed-失败
	Remark     string // 备注
	CreatedAt  string //
	UpdatedAt  string //
}

// userLoginLogsColumns holds the columns for the table user_login_logs.
var userLoginLogsColumns = UserLoginLogsColumns{
	Id:         "id",
	UserId:     "user_id",
	Ip:         "ip",
	Country:    "country",
	Province:   "province",
	City:       "city",
	District:   "district",
	Isp:        "isp",
	UserAgent:  "user_agent",
	DeviceType: "device_type",
	Os:         "os",
	Browser:    "browser",
	Status:     "status",
	Remark:     "remark",
	CreatedAt:  "created_at",
	UpdatedAt:  "updated_at",
}

// NewUserLoginLogsDao creates and returns a new DAO object for table data access.
func NewUserLoginLogsDao() *UserLoginLogsDao {
	return &UserLoginLogsDao{
		group:   "default",
		table:   "user_login_logs",
		columns: userLoginLogsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserLoginLogsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserLoginLogsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserLoginLogsDao) Columns() UserLoginLogsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserLoginLogsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserLoginLogsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserLoginLogsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
