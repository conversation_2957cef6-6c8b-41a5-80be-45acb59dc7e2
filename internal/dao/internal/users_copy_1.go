// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UsersCopy1Dao is the data access object for the table users_copy1.
type UsersCopy1Dao struct {
	table   string            // table is the underlying table name of the DAO.
	group   string            // group is the database configuration group name of the current DAO.
	columns UsersCopy1Columns // columns contains all the column names of Table for convenient usage.
}

// UsersCopy1Columns defines and stores column names for the table users_copy1.
type UsersCopy1Columns struct {
	Id              string //
	Name            string //
	RealName        string // 用户姓名
	Email           string //
	EmailVerifiedAt string //
	Password        string //
	Role            string // 角色 1-媒介 2-运营 3-管理层
	RememberToken   string //
	CreatedAt       string //
	UpdatedAt       string //
	Status          string // 状态：1在职 2离职
	LastLoginAt     string // 最近登录时间
	LastLoginIp     string // 最近登录IP
	DingtalkUserid  string // 钉钉用户ID
}

// usersCopy1Columns holds the columns for the table users_copy1.
var usersCopy1Columns = UsersCopy1Columns{
	Id:              "id",
	Name:            "name",
	RealName:        "real_name",
	Email:           "email",
	EmailVerifiedAt: "email_verified_at",
	Password:        "password",
	Role:            "role",
	RememberToken:   "remember_token",
	CreatedAt:       "created_at",
	UpdatedAt:       "updated_at",
	Status:          "status",
	LastLoginAt:     "last_login_at",
	LastLoginIp:     "last_login_ip",
	DingtalkUserid:  "dingtalk_userid",
}

// NewUsersCopy1Dao creates and returns a new DAO object for table data access.
func NewUsersCopy1Dao() *UsersCopy1Dao {
	return &UsersCopy1Dao{
		group:   "default",
		table:   "users_copy1",
		columns: usersCopy1Columns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UsersCopy1Dao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UsersCopy1Dao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UsersCopy1Dao) Columns() UsersCopy1Columns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UsersCopy1Dao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UsersCopy1Dao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UsersCopy1Dao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
