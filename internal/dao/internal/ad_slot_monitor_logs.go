// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdSlotMonitorLogsDao is the data access object for the table ad_slot_monitor_logs.
type AdSlotMonitorLogsDao struct {
	table   string                   // table is the underlying table name of the DAO.
	group   string                   // group is the database configuration group name of the current DAO.
	columns AdSlotMonitorLogsColumns // columns contains all the column names of Table for convenient usage.
}

// AdSlotMonitorLogsColumns defines and stores column names for the table ad_slot_monitor_logs.
type AdSlotMonitorLogsColumns struct {
	Id          string //
	SlotId      string // 广告位ID
	MonitorType string // 监控类型：traffic-流量监控,quality-质量监控,cost-成本监控
	Status      string // 状态：normal-正常,warning-警告,error-异常
	MonitorData string // 监控数据
	Remark      string // 备注
	CreatedAt   string //
	UpdatedAt   string //
}

// adSlotMonitorLogsColumns holds the columns for the table ad_slot_monitor_logs.
var adSlotMonitorLogsColumns = AdSlotMonitorLogsColumns{
	Id:          "id",
	SlotId:      "slot_id",
	MonitorType: "monitor_type",
	Status:      "status",
	MonitorData: "monitor_data",
	Remark:      "remark",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

// NewAdSlotMonitorLogsDao creates and returns a new DAO object for table data access.
func NewAdSlotMonitorLogsDao() *AdSlotMonitorLogsDao {
	return &AdSlotMonitorLogsDao{
		group:   "default",
		table:   "ad_slot_monitor_logs",
		columns: adSlotMonitorLogsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdSlotMonitorLogsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdSlotMonitorLogsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdSlotMonitorLogsDao) Columns() AdSlotMonitorLogsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdSlotMonitorLogsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdSlotMonitorLogsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdSlotMonitorLogsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
