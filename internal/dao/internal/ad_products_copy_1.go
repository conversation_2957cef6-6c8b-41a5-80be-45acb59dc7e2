// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdProductsCopy1Dao is the data access object for the table ad_products_copy1.
type AdProductsCopy1Dao struct {
	table   string                 // table is the underlying table name of the DAO.
	group   string                 // group is the database configuration group name of the current DAO.
	columns AdProductsCopy1Columns // columns contains all the column names of Table for convenient usage.
}

// AdProductsCopy1Columns defines and stores column names for the table ad_products_copy1.
type AdProductsCopy1Columns struct {
	Id          string //
	Name        string // 广告产品名称
	Image       string // 广告产品图片
	Description string // 广告产品描述
	CreatedAt   string //
	UpdatedAt   string //
}

// adProductsCopy1Columns holds the columns for the table ad_products_copy1.
var adProductsCopy1Columns = AdProductsCopy1Columns{
	Id:          "id",
	Name:        "name",
	Image:       "image",
	Description: "description",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
}

// NewAdProductsCopy1Dao creates and returns a new DAO object for table data access.
func NewAdProductsCopy1Dao() *AdProductsCopy1Dao {
	return &AdProductsCopy1Dao{
		group:   "default",
		table:   "ad_products_copy1",
		columns: adProductsCopy1Columns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdProductsCopy1Dao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdProductsCopy1Dao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdProductsCopy1Dao) Columns() AdProductsCopy1Columns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdProductsCopy1Dao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdProductsCopy1Dao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdProductsCopy1Dao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
