// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdMediaBalanceLogsDao is the data access object for the table ad_media_balance_logs.
type AdMediaBalanceLogsDao struct {
	table   string                    // table is the underlying table name of the DAO.
	group   string                    // group is the database configuration group name of the current DAO.
	columns AdMediaBalanceLogsColumns // columns contains all the column names of Table for convenient usage.
}

// AdMediaBalanceLogsColumns defines and stores column names for the table ad_media_balance_logs.
type AdMediaBalanceLogsColumns struct {
	Id            string //
	MediaId       string // 媒体ID
	Type          string // 变更类型:settle-结算
	SettleMonth   string // 结算月份
	Commission    string // 佣金金额
	Subsidy       string // 补贴金额
	Amount        string // 变更金额
	BeforeBalance string // 变更前余额
	AfterBalance  string // 变更后余额
	Remark        string // 备注
	CreatedBy     string // 操作人ID
	CreatedAt     string //
	UpdatedAt     string //
	DeletedAt     string //
}

// adMediaBalanceLogsColumns holds the columns for the table ad_media_balance_logs.
var adMediaBalanceLogsColumns = AdMediaBalanceLogsColumns{
	Id:            "id",
	MediaId:       "media_id",
	Type:          "type",
	SettleMonth:   "settle_month",
	Commission:    "commission",
	Subsidy:       "subsidy",
	Amount:        "amount",
	BeforeBalance: "before_balance",
	AfterBalance:  "after_balance",
	Remark:        "remark",
	CreatedBy:     "created_by",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
	DeletedAt:     "deleted_at",
}

// NewAdMediaBalanceLogsDao creates and returns a new DAO object for table data access.
func NewAdMediaBalanceLogsDao() *AdMediaBalanceLogsDao {
	return &AdMediaBalanceLogsDao{
		group:   "default",
		table:   "ad_media_balance_logs",
		columns: adMediaBalanceLogsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdMediaBalanceLogsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdMediaBalanceLogsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdMediaBalanceLogsDao) Columns() AdMediaBalanceLogsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdMediaBalanceLogsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdMediaBalanceLogsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdMediaBalanceLogsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
