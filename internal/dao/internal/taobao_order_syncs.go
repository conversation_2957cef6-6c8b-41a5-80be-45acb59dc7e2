// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TaobaoOrderSyncsDao is the data access object for the table taobao_order_syncs.
type TaobaoOrderSyncsDao struct {
	table   string                  // table is the underlying table name of the DAO.
	group   string                  // group is the database configuration group name of the current DAO.
	columns TaobaoOrderSyncsColumns // columns contains all the column names of Table for convenient usage.
}

// TaobaoOrderSyncsColumns defines and stores column names for the table taobao_order_syncs.
type TaobaoOrderSyncsColumns struct {
	Id           string //
	StartTime    string //
	EndTime      string //
	Pid          string //
	Status       string //
	TotalCount   string //
	SuccessCount string //
	FailedCount  string //
	ErrorMessage string //
	CompletedAt  string //
	CreatedAt    string //
	UpdatedAt    string //
}

// taobaoOrderSyncsColumns holds the columns for the table taobao_order_syncs.
var taobaoOrderSyncsColumns = TaobaoOrderSyncsColumns{
	Id:           "id",
	StartTime:    "start_time",
	EndTime:      "end_time",
	Pid:          "pid",
	Status:       "status",
	TotalCount:   "total_count",
	SuccessCount: "success_count",
	FailedCount:  "failed_count",
	ErrorMessage: "error_message",
	CompletedAt:  "completed_at",
	CreatedAt:    "created_at",
	UpdatedAt:    "updated_at",
}

// NewTaobaoOrderSyncsDao creates and returns a new DAO object for table data access.
func NewTaobaoOrderSyncsDao() *TaobaoOrderSyncsDao {
	return &TaobaoOrderSyncsDao{
		group:   "default",
		table:   "taobao_order_syncs",
		columns: taobaoOrderSyncsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TaobaoOrderSyncsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TaobaoOrderSyncsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TaobaoOrderSyncsDao) Columns() TaobaoOrderSyncsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TaobaoOrderSyncsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TaobaoOrderSyncsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TaobaoOrderSyncsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
