// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// HolidaysDao is the data access object for the table holidays.
type HolidaysDao struct {
	table   string          // table is the underlying table name of the DAO.
	group   string          // group is the database configuration group name of the current DAO.
	columns HolidaysColumns // columns contains all the column names of Table for convenient usage.
}

// HolidaysColumns defines and stores column names for the table holidays.
type HolidaysColumns struct {
	Id        string //
	Date      string // 节假日日期
	Name      string // 节假日名称
	IsWorkday string // 是否调休工作日
	CreatedAt string //
	UpdatedAt string //
}

// holidaysColumns holds the columns for the table holidays.
var holidaysColumns = HolidaysColumns{
	Id:        "id",
	Date:      "date",
	Name:      "name",
	IsWorkday: "is_workday",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// NewHolidaysDao creates and returns a new DAO object for table data access.
func NewHolidaysDao() *HolidaysDao {
	return &HolidaysDao{
		group:   "default",
		table:   "holidays",
		columns: holidaysColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *HolidaysDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *HolidaysDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *HolidaysDao) Columns() HolidaysColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *HolidaysDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *HolidaysDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *HolidaysDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
