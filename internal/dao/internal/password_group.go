package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// PasswordGroupDO is the data object for password_group table
type PasswordGroupDO struct {
	Id          uint   `json:"id"           description:"Primary key"`
	Name        string `json:"name"         description:"Group name"`
	Description string `json:"description"  description:"Group description"`
	CreatedAt   string `json:"created_at"   description:"Created time"`
	UpdatedAt   string `json:"updated_at"   description:"Updated time"`
}

// PasswordGroupColumns defines the columns of password_group table
type PasswordGroupColumns struct {
	Id          string // Primary key
	Name        string // Group name
	Description string // Group description
	CategoryId  string // Category ID
	CreatedAt   string // Created time
	UpdatedAt   string // Updated time
}

var (
	// PasswordGroup is the table name
	PasswordGroup = "password_groups"
	// PasswordGroupTable is the columns of password_group table
	PasswordGroupTable = PasswordGroupColumns{
		Id:          "id",
		Name:        "name",
		Description: "description",
		CategoryId:  "category_id",
		CreatedAt:   "created_at",
		UpdatedAt:   "updated_at",
	}
)

// PasswordGroupsDao is the manager for logic model data accessing and custom defined data operations functions management.
type PasswordGroupsDao struct {
	table   string               // Table name of the dao.
	group   string               // Database configuration group name of the dao.
	columns PasswordGroupColumns // Columns makes it easier to write db operations.
}

// NewPasswordGroupsDao creates and returns a new DAO object for table data access.
func NewPasswordGroupsDao() *PasswordGroupsDao {
	return &PasswordGroupsDao{
		group:   "default",
		table:   PasswordGroup,
		columns: PasswordGroupTable,
	}
}

// DB retrieves and returns the underlying database connection object.
func (dao *PasswordGroupsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *PasswordGroupsDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *PasswordGroupsDao) Columns() PasswordGroupColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *PasswordGroupsDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *PasswordGroupsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
func (dao *PasswordGroupsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
