// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ModelColumns defines and stores column names for table model.
type ModelColumns struct {
	Id              string // 主键ID
	Name            string // 模型名称
	CategoryId      string // 分类ID
	PasswordGroupId string // 密码组ID
	CreatedAt       string // 创建时间
	UpdatedAt       string // 更新时间
}

// ModelDao is the data access object for table model.
type ModelDao struct {
	table   string       // table is the underlying table name of the DAO.
	group   string       // group is the database configuration group name of current DAO.
	columns ModelColumns // columns contains all the column names of Table for convenient usage.
}

// ModelColumns holds the columns for table model.
var modelColumns = ModelColumns{
	Id:              "id",
	Name:            "name",
	CategoryId:      "category_id",
	PasswordGroupId: "password_group_id",
	CreatedAt:       "created_at",
	UpdatedAt:       "updated_at",
}

// NewModelDao creates and returns a new DAO object for table data access.
func NewModelDao() *ModelDao {
	return &ModelDao{
		group:   "default",
		table:   "models",
		columns: modelColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *ModelDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *ModelDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *ModelDao) Columns() ModelColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *ModelDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *ModelDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error if function f returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
func (dao *ModelDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}

// ModelDecayColumns defines and stores column names for table model_decay.
type ModelDecayColumns struct {
	Id        string // 主键ID
	ModelId   string // 模型ID
	Percent   string // 衰减百分比
	CreatedAt string // 创建时间
	UpdatedAt string // 更新时间
}

// ModelDecayDao is the data access object for table model_decay.
type ModelDecayDao struct {
	table   string            // table is the underlying table name of the DAO.
	group   string            // group is the database configuration group name of current DAO.
	columns ModelDecayColumns // columns contains all the column names of Table for convenient usage.
}

// ModelDecayColumns holds the columns for table model_decay.
var modelDecayColumns = ModelDecayColumns{
	Id:        "id",
	ModelId:   "model_id",
	Percent:   "percent",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// NewModelDecayDao creates and returns a new DAO object for table data access.
func NewModelDecayDao() *ModelDecayDao {
	return &ModelDecayDao{
		group:   "default",
		table:   "model_decays",
		columns: modelDecayColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *ModelDecayDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *ModelDecayDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *ModelDecayDao) Columns() ModelDecayColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *ModelDecayDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *ModelDecayDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error if function f returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
func (dao *ModelDecayDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
