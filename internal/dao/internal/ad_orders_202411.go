// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdOrders202411Dao is the data access object for the table ad_orders_202411.
type AdOrders202411Dao struct {
	table   string                // table is the underlying table name of the DAO.
	group   string                // group is the database configuration group name of the current DAO.
	columns AdOrders202411Columns // columns contains all the column names of Table for convenient usage.
}

// AdOrders202411Columns defines and stores column names for the table ad_orders_202411.
type AdOrders202411Columns struct {
	Id                   string // 主键ID
	AdMediaId            string // 媒体ID
	AdMediaName          string // 媒体名称
	AdSlotId             string // 广告位ID
	AdPlanId             string // 计划ID
	BdId                 string // 媒介ID
	SupplierId           string // 供应商ID
	OrderType            string // 订单类型
	SubOrderType         string // 子订单类型
	OrderStatus          string // 订单状态
	ParentOrderId        string // 父订单ID
	SubOrderId           string // 子订单ID
	ItemId               string // 商品ID
	ItemTitle            string // 商品标题
	ItemCover            string // 商品封面
	ItemPrice            string // 商品价格
	ItemNum              string // 商品数量
	OrderPrice           string // 订单金额
	PayPrice             string // 实付金额
	Rate                 string // 佣金比例50%即0.5
	OriPreCommission     string // 预估佣金
	OriCommission        string // 结算佣金
	PreCommission        string // 实际预估佣金
	Commission           string // 实际结算佣金
	ServiceFee           string // 服务费
	ServiceRatio         string // 服务费比例
	ThirdServiceFee      string // 三方服务费
	ThirdServiceRatio    string // 三方服务费比例
	ActivityFee          string // 活动补贴
	ActivityServiceFee   string // 活动补贴服务费
	ActivityServiceRatio string // 活动补贴服务费比例
	CreateTime           string // 创建时间
	PayTime              string // 支付时间
	ReceiveTime          string // 收货时间
	UnionId              string // 联盟ID
	Pid                  string // PID
	AdzoneName           string // 推广位名称
	RelationId           string // RID
	Category             string // 类目
	City                 string // 城市
	ActivityType         string // 活动类型，BRAND、CONSUMPTION
	AttrOrderDesc        string // 是否归因
	Leak                 string // 是否漏单
	CreatedAt            string // 创建时间
	UpdatedAt            string // 更新时间
	PreSubsidyFee        string // 预估补贴金额
	SubsidyFee           string // 结算补贴金额
	SettleTime           string // 结算时间
	SettleLogId          string // 结算记录ID
}

// adOrders202411Columns holds the columns for the table ad_orders_202411.
var adOrders202411Columns = AdOrders202411Columns{
	Id:                   "id",
	AdMediaId:            "ad_media_id",
	AdMediaName:          "ad_media_name",
	AdSlotId:             "ad_slot_id",
	AdPlanId:             "ad_plan_id",
	BdId:                 "bd_id",
	SupplierId:           "supplier_id",
	OrderType:            "order_type",
	SubOrderType:         "sub_order_type",
	OrderStatus:          "order_status",
	ParentOrderId:        "parent_order_id",
	SubOrderId:           "sub_order_id",
	ItemId:               "item_id",
	ItemTitle:            "item_title",
	ItemCover:            "item_cover",
	ItemPrice:            "item_price",
	ItemNum:              "item_num",
	OrderPrice:           "order_price",
	PayPrice:             "pay_price",
	Rate:                 "rate",
	OriPreCommission:     "ori_pre_commission",
	OriCommission:        "ori_commission",
	PreCommission:        "pre_commission",
	Commission:           "commission",
	ServiceFee:           "service_fee",
	ServiceRatio:         "service_ratio",
	ThirdServiceFee:      "third_service_fee",
	ThirdServiceRatio:    "third_service_ratio",
	ActivityFee:          "activity_fee",
	ActivityServiceFee:   "activity_service_fee",
	ActivityServiceRatio: "activity_service_ratio",
	CreateTime:           "create_time",
	PayTime:              "pay_time",
	ReceiveTime:          "receive_time",
	UnionId:              "union_id",
	Pid:                  "pid",
	AdzoneName:           "adzone_name",
	RelationId:           "relation_id",
	Category:             "category",
	City:                 "city",
	ActivityType:         "activity_type",
	AttrOrderDesc:        "attr_order_desc",
	Leak:                 "leak",
	CreatedAt:            "created_at",
	UpdatedAt:            "updated_at",
	PreSubsidyFee:        "pre_subsidy_fee",
	SubsidyFee:           "subsidy_fee",
	SettleTime:           "settle_time",
	SettleLogId:          "settle_log_id",
}

// NewAdOrders202411Dao creates and returns a new DAO object for table data access.
func NewAdOrders202411Dao() *AdOrders202411Dao {
	return &AdOrders202411Dao{
		group:   "default",
		table:   "ad_orders_202411",
		columns: adOrders202411Columns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdOrders202411Dao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdOrders202411Dao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdOrders202411Dao) Columns() AdOrders202411Columns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdOrders202411Dao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdOrders202411Dao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdOrders202411Dao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
