// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdSlotPlanChangeRequestsDao is the data access object for the table ad_slot_plan_change_requests.
type AdSlotPlanChangeRequestsDao struct {
	table   string                          // table is the underlying table name of the DAO.
	group   string                          // group is the database configuration group name of the current DAO.
	columns AdSlotPlanChangeRequestsColumns // columns contains all the column names of Table for convenient usage.
}

// AdSlotPlanChangeRequestsColumns defines and stores column names for the table ad_slot_plan_change_requests.
type AdSlotPlanChangeRequestsColumns struct {
	Id            string //
	PlanId        string // 投放计划ID
	ApplicantId   string // 申请人ID
	ApplicantName string // 申请人姓名
	OldPrice      string // 原价格
	NewPrice      string // 新价格
	OldStartDate  string // 原开始时间
	NewStartDate  string // 新开始时间
	OldEndDate    string // 原结束时间
	NewEndDate    string // 新结束时间
	Reason        string // 申请原因
	Status        string // 审核状态：pending-待审核，approved-已通过，rejected-已拒绝
	AuditorId     string // 审核人ID
	AuditorName   string // 审核人姓名
	AuditAt       string // 审核时间
	RejectReason  string // 拒绝原因
	CreatedAt     string //
	UpdatedAt     string //
	DeletedAt     string //
}

// adSlotPlanChangeRequestsColumns holds the columns for the table ad_slot_plan_change_requests.
var adSlotPlanChangeRequestsColumns = AdSlotPlanChangeRequestsColumns{
	Id:            "id",
	PlanId:        "plan_id",
	ApplicantId:   "applicant_id",
	ApplicantName: "applicant_name",
	OldPrice:      "old_price",
	NewPrice:      "new_price",
	OldStartDate:  "old_start_date",
	NewStartDate:  "new_start_date",
	OldEndDate:    "old_end_date",
	NewEndDate:    "new_end_date",
	Reason:        "reason",
	Status:        "status",
	AuditorId:     "auditor_id",
	AuditorName:   "auditor_name",
	AuditAt:       "audit_at",
	RejectReason:  "reject_reason",
	CreatedAt:     "created_at",
	UpdatedAt:     "updated_at",
	DeletedAt:     "deleted_at",
}

// NewAdSlotPlanChangeRequestsDao creates and returns a new DAO object for table data access.
func NewAdSlotPlanChangeRequestsDao() *AdSlotPlanChangeRequestsDao {
	return &AdSlotPlanChangeRequestsDao{
		group:   "default",
		table:   "ad_slot_plan_change_requests",
		columns: adSlotPlanChangeRequestsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdSlotPlanChangeRequestsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdSlotPlanChangeRequestsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdSlotPlanChangeRequestsDao) Columns() AdSlotPlanChangeRequestsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdSlotPlanChangeRequestsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdSlotPlanChangeRequestsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdSlotPlanChangeRequestsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
