// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// JobBatchesDao is the data access object for the table job_batches.
type JobBatchesDao struct {
	table   string            // table is the underlying table name of the DAO.
	group   string            // group is the database configuration group name of the current DAO.
	columns JobBatchesColumns // columns contains all the column names of Table for convenient usage.
}

// JobBatchesColumns defines and stores column names for the table job_batches.
type JobBatchesColumns struct {
	Id           string //
	Name         string //
	TotalJobs    string //
	PendingJobs  string //
	FailedJobs   string //
	FailedJobIds string //
	Options      string //
	CancelledAt  string //
	CreatedAt    string //
	FinishedAt   string //
}

// jobBatchesColumns holds the columns for the table job_batches.
var jobBatchesColumns = JobBatchesColumns{
	Id:           "id",
	Name:         "name",
	TotalJobs:    "total_jobs",
	PendingJobs:  "pending_jobs",
	FailedJobs:   "failed_jobs",
	FailedJobIds: "failed_job_ids",
	Options:      "options",
	CancelledAt:  "cancelled_at",
	CreatedAt:    "created_at",
	FinishedAt:   "finished_at",
}

// NewJobBatchesDao creates and returns a new DAO object for table data access.
func NewJobBatchesDao() *JobBatchesDao {
	return &JobBatchesDao{
		group:   "default",
		table:   "job_batches",
		columns: jobBatchesColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *JobBatchesDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *JobBatchesDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *JobBatchesDao) Columns() JobBatchesColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *JobBatchesDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *JobBatchesDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *JobBatchesDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
