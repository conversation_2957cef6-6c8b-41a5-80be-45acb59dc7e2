// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdSlotPlanDailyStatsDao is the data access object for the table ad_slot_plan_daily_stats.
type AdSlotPlanDailyStatsDao struct {
	table   string                      // table is the underlying table name of the DAO.
	group   string                      // group is the database configuration group name of the current DAO.
	columns AdSlotPlanDailyStatsColumns // columns contains all the column names of Table for convenient usage.
}

// AdSlotPlanDailyStatsColumns defines and stores column names for the table ad_slot_plan_daily_stats.
type AdSlotPlanDailyStatsColumns struct {
	Id                     string //
	PlanId                 string // 计划ID
	MediaId                string // 媒体ID
	AdSlotId               string // 广告位ID
	AdProductId            string // 投放产品ID
	UserId                 string // 媒介ID
	Date                   string // 数据日期
	Clicks                 string // 点击量
	UnitPrice              string // 单价
	Cost                   string // 成本
	BdOrders               string // 媒介订单量
	BdRevenue              string // 媒介收入
	BdProfit               string // 媒介利润
	BdRoi                  string // 媒介ROI
	BdActivityFee          string // 媒介激励费用
	BdSettleOrders         string // 媒介结算订单量
	BdSettleRevenue        string // 媒介结算收入
	BdSettleActivityFee    string // 媒介结算激励
	BdSettleProfit         string // 媒介结算利润
	BdSettleRoi            string // 媒介结算ROI
	AdminOrders            string // 实际订单量
	AdminRevenue           string // 实际收入
	AdminProfit            string // 实际利润
	AdminRoi               string // 实际ROI
	AdminActivityFee       string // 实际激励费用
	AdminSettleOrders      string // 实际结算订单量
	AdminSettleRevenue     string // 实际结算收入
	AdminSettleActivityFee string // 实际结算激励
	AdminSettleProfit      string // 实际结算利润
	AdminSettleRoi         string // 实际结算ROI
	OrderTypeStats         string // 收益明细
	CreatedBy              string // 创建人ID
	UpdatedBy              string // 更新人ID
	CreatedAt              string //
	UpdatedAt              string //
	DeletedAt              string //
}

// adSlotPlanDailyStatsColumns holds the columns for the table ad_slot_plan_daily_stats.
var adSlotPlanDailyStatsColumns = AdSlotPlanDailyStatsColumns{
	Id:                     "id",
	PlanId:                 "plan_id",
	MediaId:                "media_id",
	AdSlotId:               "ad_slot_id",
	AdProductId:            "ad_product_id",
	UserId:                 "user_id",
	Date:                   "date",
	Clicks:                 "clicks",
	UnitPrice:              "unit_price",
	Cost:                   "cost",
	BdOrders:               "bd_orders",
	BdRevenue:              "bd_revenue",
	BdProfit:               "bd_profit",
	BdRoi:                  "bd_roi",
	BdActivityFee:          "bd_activity_fee",
	BdSettleOrders:         "bd_settle_orders",
	BdSettleRevenue:        "bd_settle_revenue",
	BdSettleActivityFee:    "bd_settle_activity_fee",
	BdSettleProfit:         "bd_settle_profit",
	BdSettleRoi:            "bd_settle_roi",
	AdminOrders:            "admin_orders",
	AdminRevenue:           "admin_revenue",
	AdminProfit:            "admin_profit",
	AdminRoi:               "admin_roi",
	AdminActivityFee:       "admin_activity_fee",
	AdminSettleOrders:      "admin_settle_orders",
	AdminSettleRevenue:     "admin_settle_revenue",
	AdminSettleActivityFee: "admin_settle_activity_fee",
	AdminSettleProfit:      "admin_settle_profit",
	AdminSettleRoi:         "admin_settle_roi",
	OrderTypeStats:         "order_type_stats",
	CreatedBy:              "created_by",
	UpdatedBy:              "updated_by",
	CreatedAt:              "created_at",
	UpdatedAt:              "updated_at",
	DeletedAt:              "deleted_at",
}

// NewAdSlotPlanDailyStatsDao creates and returns a new DAO object for table data access.
func NewAdSlotPlanDailyStatsDao() *AdSlotPlanDailyStatsDao {
	return &AdSlotPlanDailyStatsDao{
		group:   "default",
		table:   "ad_slot_plan_daily_stats",
		columns: adSlotPlanDailyStatsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdSlotPlanDailyStatsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdSlotPlanDailyStatsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdSlotPlanDailyStatsDao) Columns() AdSlotPlanDailyStatsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdSlotPlanDailyStatsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdSlotPlanDailyStatsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdSlotPlanDailyStatsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
