// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdAgentsDao is the data access object for the table ad_agents.
type AdAgentsDao struct {
	table   string          // table is the underlying table name of the DAO.
	group   string          // group is the database configuration group name of the current DAO.
	columns AdAgentsColumns // columns contains all the column names of Table for convenient usage.
}

// AdAgentsColumns defines and stores column names for the table ad_agents.
type AdAgentsColumns struct {
	Id                string //
	Code              string // 代理编号
	Name              string // 代理名称
	UserId            string // 用户ID
	AuditStatus       string // 审核状态：pending-审核中,approved-已通过,rejected-已拒绝
	CooperationStatus string // 合作状态：not_started-未开始,active-合作中,terminated-已终止
	CompanyName       string // 公司名称
	CompanyAddress    string // 公司地址
	ContactName       string // 联系人
	ContactPhone      string // 联系电话
	RejectReason      string // 拒绝原因
	Remarks           string // 备注
	PlatformConfig    string // 平台配置 {platform: "denghuoplus|xiaohongshu", info: {token: "", pid: ""}}
	CreatedBy         string // 创建人ID
	UpdatedBy         string // 更新人ID
	CreatedAt         string //
	UpdatedAt         string //
	DeletedAt         string //
	InstanceId        string // 审批实例ID
	Type              string // 代理类型：traffic-流量采买,delivery-投放
}

// adAgentsColumns holds the columns for the table ad_agents.
var adAgentsColumns = AdAgentsColumns{
	Id:                "id",
	Code:              "code",
	Name:              "name",
	UserId:            "user_id",
	AuditStatus:       "audit_status",
	CooperationStatus: "cooperation_status",
	CompanyName:       "company_name",
	CompanyAddress:    "company_address",
	ContactName:       "contact_name",
	ContactPhone:      "contact_phone",
	RejectReason:      "reject_reason",
	Remarks:           "remarks",
	PlatformConfig:    "platform_config",
	CreatedBy:         "created_by",
	UpdatedBy:         "updated_by",
	CreatedAt:         "created_at",
	UpdatedAt:         "updated_at",
	DeletedAt:         "deleted_at",
	InstanceId:        "instance_id",
	Type:              "type",
}

// NewAdAgentsDao creates and returns a new DAO object for table data access.
func NewAdAgentsDao() *AdAgentsDao {
	return &AdAgentsDao{
		group:   "default",
		table:   "ad_agents",
		columns: adAgentsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdAgentsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdAgentsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdAgentsDao) Columns() AdAgentsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdAgentsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdAgentsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdAgentsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
