// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// RegionsDao is the data access object for the table regions.
type RegionsDao struct {
	table   string         // table is the underlying table name of the DAO.
	group   string         // group is the database configuration group name of the current DAO.
	columns RegionsColumns // columns contains all the column names of Table for convenient usage.
}

// RegionsColumns defines and stores column names for the table regions.
type RegionsColumns struct {
	Id        string //
	ParentId  string // 父级ID
	Name      string // 地区名称
	Code      string // 地区编码
	Level     string // 层级：1=省份，2=城市，3=区县
	IsEnabled string // 是否启用
	Sort      string // 排序
	CreatedAt string //
	UpdatedAt string //
}

// regionsColumns holds the columns for the table regions.
var regionsColumns = RegionsColumns{
	Id:        "id",
	ParentId:  "parent_id",
	Name:      "name",
	Code:      "code",
	Level:     "level",
	IsEnabled: "is_enabled",
	Sort:      "sort",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// NewRegionsDao creates and returns a new DAO object for table data access.
func NewRegionsDao() *RegionsDao {
	return &RegionsDao{
		group:   "default",
		table:   "regions",
		columns: regionsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *RegionsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *RegionsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *RegionsDao) Columns() RegionsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *RegionsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *RegionsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *RegionsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
