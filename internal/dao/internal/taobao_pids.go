// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// TaobaoPidsDao is the data access object for the table taobao_pids.
type TaobaoPidsDao struct {
	table   string            // table is the underlying table name of the DAO.
	group   string            // group is the database configuration group name of the current DAO.
	columns TaobaoPidsColumns // columns contains all the column names of Table for convenient usage.
}

// TaobaoPidsColumns defines and stores column names for the table taobao_pids.
type TaobaoPidsColumns struct {
	Id           string //
	AccountName  string // 联盟账号
	MemberId     string // 会员ID
	ZoneName     string // 推广位名称
	Pid          string // 推广位ID
	IsUsed       string // 是否使用
	UsedAt       string // 使用时间
	UsedBy       string // 使用者ID
	AdSlotPlanId string // 关联计划ID
	AdCreativeId string // 关联创意ID
	ActType      string // 活动类型:1-飞猪 2-福利购
	CreatedAt    string //
	UpdatedAt    string //
	DeletedAt    string //
}

// taobaoPidsColumns holds the columns for the table taobao_pids.
var taobaoPidsColumns = TaobaoPidsColumns{
	Id:           "id",
	AccountName:  "account_name",
	MemberId:     "member_id",
	ZoneName:     "zone_name",
	Pid:          "pid",
	IsUsed:       "is_used",
	UsedAt:       "used_at",
	UsedBy:       "used_by",
	AdSlotPlanId: "ad_slot_plan_id",
	AdCreativeId: "ad_creative_id",
	ActType:      "act_type",
	CreatedAt:    "created_at",
	UpdatedAt:    "updated_at",
	DeletedAt:    "deleted_at",
}

// NewTaobaoPidsDao creates and returns a new DAO object for table data access.
func NewTaobaoPidsDao() *TaobaoPidsDao {
	return &TaobaoPidsDao{
		group:   "default",
		table:   "taobao_pids",
		columns: taobaoPidsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *TaobaoPidsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *TaobaoPidsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *TaobaoPidsDao) Columns() TaobaoPidsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *TaobaoPidsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *TaobaoPidsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *TaobaoPidsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
