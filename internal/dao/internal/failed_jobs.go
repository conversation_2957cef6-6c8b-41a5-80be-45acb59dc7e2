// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// FailedJobsDao is the data access object for the table failed_jobs.
type FailedJobsDao struct {
	table   string            // table is the underlying table name of the DAO.
	group   string            // group is the database configuration group name of the current DAO.
	columns FailedJobsColumns // columns contains all the column names of Table for convenient usage.
}

// FailedJobsColumns defines and stores column names for the table failed_jobs.
type FailedJobsColumns struct {
	Id         string //
	Uuid       string //
	Connection string //
	Queue      string //
	Payload    string //
	Exception  string //
	FailedAt   string //
}

// failedJobsColumns holds the columns for the table failed_jobs.
var failedJobsColumns = FailedJobsColumns{
	Id:         "id",
	Uuid:       "uuid",
	Connection: "connection",
	Queue:      "queue",
	Payload:    "payload",
	Exception:  "exception",
	FailedAt:   "failed_at",
}

// NewFailedJobsDao creates and returns a new DAO object for table data access.
func NewFailedJobsDao() *FailedJobsDao {
	return &FailedJobsDao{
		group:   "default",
		table:   "failed_jobs",
		columns: failedJobsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *FailedJobsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *FailedJobsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *FailedJobsDao) Columns() FailedJobsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *FailedJobsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *FailedJobsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *FailedJobsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
