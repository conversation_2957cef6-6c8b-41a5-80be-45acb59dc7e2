// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdMediaWithdrawsDao is the data access object for the table ad_media_withdraws.
type AdMediaWithdrawsDao struct {
	table   string                  // table is the underlying table name of the DAO.
	group   string                  // group is the database configuration group name of the current DAO.
	columns AdMediaWithdrawsColumns // columns contains all the column names of Table for convenient usage.
}

// AdMediaWithdrawsColumns defines and stores column names for the table ad_media_withdraws.
type AdMediaWithdrawsColumns struct {
	Id           string //
	MediaId      string // 媒体ID
	Amount       string // 提现金额
	TaxAmount    string // 税费金额
	ActualAmount string // 实际打款金额
	WithdrawType string // 提现方式:alipay-支付宝,bank-对公转账
	AccountInfo  string // 账户信息
	InvoiceFile  string // 发票文件
	Status       string // 状态:pending-待审核,approved-已审核,rejected-已拒绝,paid-已打款
	AuditTime    string // 审核时间
	AuditUserId  string // 审核人ID
	PayTime      string // 打款时间
	PayUserId    string // 打款操作人ID
	Remark       string // 备注
	CreatedAt    string //
	UpdatedAt    string //
	DeletedAt    string //
}

// adMediaWithdrawsColumns holds the columns for the table ad_media_withdraws.
var adMediaWithdrawsColumns = AdMediaWithdrawsColumns{
	Id:           "id",
	MediaId:      "media_id",
	Amount:       "amount",
	TaxAmount:    "tax_amount",
	ActualAmount: "actual_amount",
	WithdrawType: "withdraw_type",
	AccountInfo:  "account_info",
	InvoiceFile:  "invoice_file",
	Status:       "status",
	AuditTime:    "audit_time",
	AuditUserId:  "audit_user_id",
	PayTime:      "pay_time",
	PayUserId:    "pay_user_id",
	Remark:       "remark",
	CreatedAt:    "created_at",
	UpdatedAt:    "updated_at",
	DeletedAt:    "deleted_at",
}

// NewAdMediaWithdrawsDao creates and returns a new DAO object for table data access.
func NewAdMediaWithdrawsDao() *AdMediaWithdrawsDao {
	return &AdMediaWithdrawsDao{
		group:   "default",
		table:   "ad_media_withdraws",
		columns: adMediaWithdrawsColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *AdMediaWithdrawsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *AdMediaWithdrawsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *AdMediaWithdrawsDao) Columns() AdMediaWithdrawsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *AdMediaWithdrawsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *AdMediaWithdrawsDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *AdMediaWithdrawsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
