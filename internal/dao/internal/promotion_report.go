// =================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package internal

import (
	"context"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// PromotionReportDao is the data access object for table promotion_report.
type PromotionReportDao struct {
	table   string              // table is the underlying table name of the DAO.
	group   string              // group is the database configuration group name of current DAO.
	columns PromotionReportColumns // columns contains all the column names of Table for convenient usage.
}

// PromotionReportColumns defines and stores column names for table promotion_report.
type PromotionReportColumns struct {
	Id              string // 主键ID
	ReportDate      string // 报表日期
	GroupId         string // 口令组ID
	CategoryId   string // 口令组分类
	Cost            string // 投放费用
	TotalOrders     string // 当日总订单数
	TotalCommission string // 当日总佣金
	CreatedAt       string // 创建时间
	UpdatedAt       string // 更新时间
}

// promotionReportColumns holds the columns for table promotion_report.
var promotionReportColumns = PromotionReportColumns{
	Id:              "id",
	ReportDate:      "report_date",
	GroupId:         "group_id",
	CategoryId:      "category_id",
	Cost:            "cost",
	TotalOrders:     "total_orders",
	TotalCommission: "total_commission",
	CreatedAt:       "created_at",
	UpdatedAt:       "updated_at",
}

// NewPromotionReportDao creates and returns a new DAO object for table data access.
func NewPromotionReportDao() *PromotionReportDao {
	return &PromotionReportDao{
		group:   "default",
		table:   "promotion_reports",
		columns: promotionReportColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *PromotionReportDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *PromotionReportDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *PromotionReportDao) Columns() PromotionReportColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *PromotionReportDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *PromotionReportDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
func (dao *PromotionReportDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
} 