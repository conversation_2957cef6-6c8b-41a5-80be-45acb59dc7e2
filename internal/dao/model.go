// =================================================================================
// This file is auto-generated by GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
	"context"

	"github.com/gogf/gf/v2/database/gdb"
)

// modelDao is the data access object for table model.
// You can define custom methods on it to extend its functionality as needed.
type modelDao struct {
	*internal.ModelDao
}

var (
	// Model is a globally accessible object for table model operations.
	Model = modelDao{
		internal.NewModelDao(),
	}
)

// modelDecayDao is the data access object for table model_decay.
// You can define custom methods on it to extend its functionality as needed.
type modelDecayDao struct {
	*internal.ModelDecayDao
}

var (
	// ModelDecay is a globally accessible object for table model_decay operations.
	ModelDecay = modelDecayDao{
		internal.NewModelDecayDao(),
	}
)

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *modelDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.ModelDao.Ctx(ctx)
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *modelDecayDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.ModelDecayDao.Ctx(ctx)
}
