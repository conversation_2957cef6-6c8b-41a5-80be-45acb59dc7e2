// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdSlotPlanCostsDao is an internal type for wrapping the internal DAO implementation.
type internalAdSlotPlanCostsDao = *internal.AdSlotPlanCostsDao

// adSlotPlanCostsDao is the data access object for the table ad_slot_plan_costs.
// You can define custom methods on it to extend its functionality as needed.
type adSlotPlanCostsDao struct {
	internalAdSlotPlanCostsDao
}

var (
	// AdSlotPlanCosts is a globally accessible object for table ad_slot_plan_costs operations.
	AdSlotPlanCosts = adSlotPlanCostsDao{
		internal.NewAdSlotPlanCostsDao(),
	}
)

// Add your custom methods and functionality below.
