// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
	"context"
	"github.com/gogf/gf/v2/database/gdb"
)

// adCreativesDao is the manager for logic model data accessing and custom defined data operations functions management.
type adCreativesDao struct {
	*internal.AdCreativesDao
}

var (
	// AdCreatives is globally public accessible object for table ad_creatives operations.
	AdCreatives = adCreativesDao{
		internal.NewAdCreativesDao(),
	}
)

// Fill with you ideas below.

// 获取创意列表，支持分页和名称模糊查询
func (d *adCreativesDao) GetList(ctx context.Context, name string, page, size int) (list gdb.Result, total int, err error) {
	m := d.Ctx(ctx)

	// 构建查询条件
	if name != "" {
		m = m.Where("name LIKE ?", "%"+name+"%")
	}

	// 获取总数
	total, err = m.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取列表
	list, err = m.Order("id DESC").Page(page, size).All()
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
} 