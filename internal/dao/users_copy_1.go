// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalUsersCopy1Dao is an internal type for wrapping the internal DAO implementation.
type internalUsersCopy1Dao = *internal.UsersCopy1Dao

// usersCopy1Dao is the data access object for the table users_copy1.
// You can define custom methods on it to extend its functionality as needed.
type usersCopy1Dao struct {
	internalUsersCopy1Dao
}

var (
	// UsersCopy1 is a globally accessible object for table users_copy1 operations.
	UsersCopy1 = usersCopy1Dao{
		internal.NewUsersCopy1Dao(),
	}
)

// Add your custom methods and functionality below.
