// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalPersonalAccessTokensDao is an internal type for wrapping the internal DAO implementation.
type internalPersonalAccessTokensDao = *internal.PersonalAccessTokensDao

// personalAccessTokensDao is the data access object for the table personal_access_tokens.
// You can define custom methods on it to extend its functionality as needed.
type personalAccessTokensDao struct {
	internalPersonalAccessTokensDao
}

var (
	// PersonalAccessTokens is a globally accessible object for table personal_access_tokens operations.
	PersonalAccessTokens = personalAccessTokensDao{
		internal.NewPersonalAccessTokensDao(),
	}
)

// Add your custom methods and functionality below.
