// =================================================================================
// This file is auto-generated by GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalPlatformGroupsDao is an internal type for wrapping internal DAO implementation.
type internalPlatformGroupsDao = *internal.PlatformGroupsDao

// platformGroupsDao is the data access object for table platform_groups.
// You can define custom methods on it to extend its functionality as you wish.
type platformGroupsDao struct {
	internalPlatformGroupsDao
}

var (
	// PlatformGroups is the globally public accessible object for table platform_groups operations.
	PlatformGroups = platformGroupsDao{
		internal.NewPlatformGroupsDao(),
	}
)
