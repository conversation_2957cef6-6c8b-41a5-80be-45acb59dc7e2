// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalUserLoginLogsDao is an internal type for wrapping the internal DAO implementation.
type internalUserLoginLogsDao = *internal.UserLoginLogsDao

// userLoginLogsDao is the data access object for the table user_login_logs.
// You can define custom methods on it to extend its functionality as needed.
type userLoginLogsDao struct {
	internalUserLoginLogsDao
}

var (
	// UserLoginLogs is a globally accessible object for table user_login_logs operations.
	UserLoginLogs = userLoginLogsDao{
		internal.NewUserLoginLogsDao(),
	}
)

// Add your custom methods and functionality below.
