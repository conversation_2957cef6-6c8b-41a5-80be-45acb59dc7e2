// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalHolidaysDao is an internal type for wrapping the internal DAO implementation.
type internalHolidaysDao = *internal.HolidaysDao

// holidaysDao is the data access object for the table holidays.
// You can define custom methods on it to extend its functionality as needed.
type holidaysDao struct {
	internalHolidaysDao
}

var (
	// Holidays is a globally accessible object for table holidays operations.
	Holidays = holidaysDao{
		internal.NewHolidaysDao(),
	}
)

// Add your custom methods and functionality below.
