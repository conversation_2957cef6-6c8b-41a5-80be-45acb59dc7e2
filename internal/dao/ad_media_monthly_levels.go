// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdMediaMonthlyLevelsDao is an internal type for wrapping the internal DAO implementation.
type internalAdMediaMonthlyLevelsDao = *internal.AdMediaMonthlyLevelsDao

// adMediaMonthlyLevelsDao is the data access object for the table ad_media_monthly_levels.
// You can define custom methods on it to extend its functionality as needed.
type adMediaMonthlyLevelsDao struct {
	internalAdMediaMonthlyLevelsDao
}

var (
	// AdMediaMonthlyLevels is a globally accessible object for table ad_media_monthly_levels operations.
	AdMediaMonthlyLevels = adMediaMonthlyLevelsDao{
		internal.NewAdMediaMonthlyLevelsDao(),
	}
)

// Add your custom methods and functionality below.
