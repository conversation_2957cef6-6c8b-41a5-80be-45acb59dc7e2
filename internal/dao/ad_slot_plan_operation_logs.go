// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdSlotPlanOperationLogsDao is an internal type for wrapping the internal DAO implementation.
type internalAdSlotPlanOperationLogsDao = *internal.AdSlotPlanOperationLogsDao

// adSlotPlanOperationLogsDao is the data access object for the table ad_slot_plan_operation_logs.
// You can define custom methods on it to extend its functionality as needed.
type adSlotPlanOperationLogsDao struct {
	internalAdSlotPlanOperationLogsDao
}

var (
	// AdSlotPlanOperationLogs is a globally accessible object for table ad_slot_plan_operation_logs operations.
	AdSlotPlanOperationLogs = adSlotPlanOperationLogsDao{
		internal.NewAdSlotPlanOperationLogsDao(),
	}
)

// Add your custom methods and functionality below.
