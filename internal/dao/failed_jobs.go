// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalFailedJobsDao is an internal type for wrapping the internal DAO implementation.
type internalFailedJobsDao = *internal.FailedJobsDao

// failedJobsDao is the data access object for the table failed_jobs.
// You can define custom methods on it to extend its functionality as needed.
type failedJobsDao struct {
	internalFailedJobsDao
}

var (
	// FailedJobs is a globally accessible object for table failed_jobs operations.
	FailedJobs = failedJobsDao{
		internal.NewFailedJobsDao(),
	}
)

// Add your custom methods and functionality below.
