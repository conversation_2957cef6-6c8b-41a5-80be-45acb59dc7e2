// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// elmAccountSettingsDao is the manager for logic model data accessing
// and custom defined data operations functions management. You can define
// methods on it to extend its functionality as you wish.
type elmAccountSettingsDao struct {
	*internal.ElmAccountSettingsDao
}

var (
	// ElmAccountSettings is globally public accessible object for table elm_account_settings operations.
	ElmAccountSettings = elmAccountSettingsDao{
		internal.NewElmAccountSettingsDao(),
	}
) 