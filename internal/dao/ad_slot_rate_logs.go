// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdSlotRateLogsDao is an internal type for wrapping the internal DAO implementation.
type internalAdSlotRateLogsDao = *internal.AdSlotRateLogsDao

// adSlotRateLogsDao is the data access object for the table ad_slot_rate_logs.
// You can define custom methods on it to extend its functionality as needed.
type adSlotRateLogsDao struct {
	internalAdSlotRateLogsDao
}

var (
	// AdSlotRateLogs is a globally accessible object for table ad_slot_rate_logs operations.
	AdSlotRateLogs = adSlotRateLogsDao{
		internal.NewAdSlotRateLogsDao(),
	}
)

// Add your custom methods and functionality below.
