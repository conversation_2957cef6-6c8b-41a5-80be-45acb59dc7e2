// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdOrdersDao is an internal type for wrapping the internal DAO implementation.
type internalAdOrdersDao = *internal.AdOrdersDao

// adOrdersDao is the data access object for the table ad_orders.
// You can define custom methods on it to extend its functionality as needed.
type adOrdersDao struct {
	internalAdOrdersDao
}

var (
	// AdOrders is a globally accessible object for table ad_orders operations.
	AdOrders = adOrdersDao{
		internal.NewAdOrdersDao(),
	}
)

// Add your custom methods and functionality below.
