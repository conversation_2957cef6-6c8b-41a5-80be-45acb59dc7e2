// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdOrdersHourlyStatsDao is an internal type for wrapping the internal DAO implementation.
type internalAdOrdersHourlyStatsDao = *internal.AdOrdersHourlyStatsDao

// adOrdersHourlyStatsDao is the data access object for the table ad_orders_hourly_stats.
// You can define custom methods on it to extend its functionality as needed.
type adOrdersHourlyStatsDao struct {
	internalAdOrdersHourlyStatsDao
}

var (
	// AdOrdersHourlyStats is a globally accessible object for table ad_orders_hourly_stats operations.
	AdOrdersHourlyStats = adOrdersHourlyStatsDao{
		internal.NewAdOrdersHourlyStatsDao(),
	}
)

// Add your custom methods and functionality below.
