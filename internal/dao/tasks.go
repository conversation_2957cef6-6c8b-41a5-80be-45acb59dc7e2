// =================================================================================
// This file is auto-generated by GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalTasksDao is internal type for wrapping internal DAO implementations.
type internalTasksDao = *internal.TasksDao

// tasksDao is the data access object for table tasks.
// You can define custom methods on it to extend its functionality as you wish.
type tasksDao struct {
	internalTasksDao
}

var (
	// Tasks is globally public accessible object for table tasks operations.
	Tasks = tasksDao{
		internal.NewTasksDao(),
	}
)
