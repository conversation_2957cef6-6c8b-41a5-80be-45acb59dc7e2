// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
	"context"

	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/model/entity"
)

// internalAdMediaDao is an internal type for wrapping the internal DAO implementation.
type internalAdMediaDao = *internal.AdMediaDao

// adMediaDao is the data access object for the table ad_media.
// You can define custom methods on it to extend its functionality as needed.
type adMediaDao struct {
	internalAdMediaDao
}

var (
	// AdMedia is a globally accessible object for table ad_media operations.
	AdMedia = adMediaDao{
		internal.NewAdMediaDao(),
	}
)

// Add your custom methods and functionality below.

// GetAccountByUserId 根据用户ID获取媒体账号信息
func (d *adMediaDao) GetAccountByUserId(ctx context.Context, userId uint64) (*model.MediaAccount, error) {
	var account model.MediaAccount
	err := d.DB().Model(d.Table()).
		Where("user_id = ?", userId).
		Scan(&account)
	if err != nil {
		return nil, err
	}
	return &account, nil
}

// GetAllWithAccounts 获取所有媒体及其账号信息
func (d *adMediaDao) GetAllWithAccounts(ctx context.Context) ([]*model.Media, error) {
	var medias []*model.Media
	err := d.DB().Model(d.Table()).
		Scan(&medias)
	if err != nil {
		return nil, err
	}
	return medias, nil
}

// GetAccountsByMediaId 获取媒体下的所有账号
func (d *adMediaDao) GetAccountsByMediaId(ctx context.Context, mediaId uint64) ([]*model.MediaAccount, error) {
	var accounts []*model.MediaAccount
	err := d.DB().Model(d.Table()).
		Fields("id, name, user_id").
		Where("ad_agent_id = ? AND deleted_at IS NULL", mediaId).
		Scan(&accounts)
	if err != nil {
		return nil, err
	}

	// 设置媒体信息
	for _, account := range accounts {
		account.MediaId = mediaId
	}

	return accounts, nil
}

// GetAccountsByMediaIds 批量获取多个媒体下的所有账号
func (d *adMediaDao) GetAccountsByMediaIds(ctx context.Context, mediaIds []uint64) ([]*model.MediaAccount, error) {
	if len(mediaIds) == 0 {
		return nil, nil
	}

	var accounts []*model.MediaAccount
	err := d.DB().Model(d.Table()).
		Fields("id, name, user_id, ad_agent_id as media_id").
		Where("ad_agent_id IN(?) AND deleted_at IS NULL", mediaIds).
		Scan(&accounts)
	if err != nil {
		return nil, err
	}

	return accounts, nil
}

// GetAllMedia 获取所有媒体
func (d *adMediaDao) GetAllMedia(ctx context.Context) ([]*entity.AdMedia, error) {
	var media []*entity.AdMedia
	err := d.DB().Model(d.Table()).
		Where("deleted_at IS NULL").
		Scan(&media)
	if err != nil {
		return nil, err
	}
	return media, nil
}

// GetMediaByUserId 根据用户ID获取媒体
func (d *adMediaDao) GetMediaByUserId(ctx context.Context, userId int) ([]*entity.AdMedia, error) {
	var media []*entity.AdMedia
	err := d.DB().Model(d.Table()).
		Where("user_id = ? AND deleted_at IS NULL", userId).
		Scan(&media)
	if err != nil {
		return nil, err
	}
	return media, nil
}
