// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdSlotPlanDailyOrderStatsBackup20240318Dao is an internal type for wrapping the internal DAO implementation.
type internalAdSlotPlanDailyOrderStatsBackup20240318Dao = *internal.AdSlotPlanDailyOrderStatsBackup20240318Dao

// adSlotPlanDailyOrderStatsBackup20240318Dao is the data access object for the table ad_slot_plan_daily_order_stats_backup_20240318.
// You can define custom methods on it to extend its functionality as needed.
type adSlotPlanDailyOrderStatsBackup20240318Dao struct {
	internalAdSlotPlanDailyOrderStatsBackup20240318Dao
}

var (
	// AdSlotPlanDailyOrderStatsBackup20240318 is a globally accessible object for table ad_slot_plan_daily_order_stats_backup_20240318 operations.
	AdSlotPlanDailyOrderStatsBackup20240318 = adSlotPlanDailyOrderStatsBackup20240318Dao{
		internal.NewAdSlotPlanDailyOrderStatsBackup20240318Dao(),
	}
)

// Add your custom methods and functionality below.
