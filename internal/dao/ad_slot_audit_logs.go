// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdSlotAuditLogsDao is an internal type for wrapping the internal DAO implementation.
type internalAdSlotAuditLogsDao = *internal.AdSlotAuditLogsDao

// adSlotAuditLogsDao is the data access object for the table ad_slot_audit_logs.
// You can define custom methods on it to extend its functionality as needed.
type adSlotAuditLogsDao struct {
	internalAdSlotAuditLogsDao
}

var (
	// AdSlotAuditLogs is a globally accessible object for table ad_slot_audit_logs operations.
	AdSlotAuditLogs = adSlotAuditLogsDao{
		internal.NewAdSlotAuditLogsDao(),
	}
)

// Add your custom methods and functionality below.
