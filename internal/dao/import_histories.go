// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalImportHistoriesDao is an internal type for wrapping the internal DAO implementation.
type internalImportHistoriesDao = *internal.ImportHistoriesDao

// importHistoriesDao is the data access object for the table import_histories.
// You can define custom methods on it to extend its functionality as needed.
type importHistoriesDao struct {
	internalImportHistoriesDao
}

var (
	// ImportHistories is a globally accessible object for table import_histories operations.
	ImportHistories = importHistoriesDao{
		internal.NewImportHistoriesDao(),
	}
)

// Add your custom methods and functionality below.
