// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalPromotionZonesDao is an internal type for wrapping the internal DAO implementation.
type internalPromotionZonesDao = *internal.PromotionZonesDao

// promotionZonesDao is the data access object for the table promotion_zones.
// You can define custom methods on it to extend its functionality as needed.
type promotionZonesDao struct {
	internalPromotionZonesDao
}

var (
	// PromotionZones is a globally accessible object for table promotion_zones operations.
	PromotionZones = promotionZonesDao{
		internal.NewPromotionZonesDao(),
	}
)

// Add your custom methods and functionality below.
