// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalTaobaoOrderSyncsDao is an internal type for wrapping the internal DAO implementation.
type internalTaobaoOrderSyncsDao = *internal.TaobaoOrderSyncsDao

// taobaoOrderSyncsDao is the data access object for the table taobao_order_syncs.
// You can define custom methods on it to extend its functionality as needed.
type taobaoOrderSyncsDao struct {
	internalTaobaoOrderSyncsDao
}

var (
	// TaobaoOrderSyncs is a globally accessible object for table taobao_order_syncs operations.
	TaobaoOrderSyncs = taobaoOrderSyncsDao{
		internal.NewTaobaoOrderSyncsDao(),
	}
)

// Add your custom methods and functionality below.
