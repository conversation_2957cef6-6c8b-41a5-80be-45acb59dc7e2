// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdOrders202502Dao is an internal type for wrapping the internal DAO implementation.
type internalAdOrders202502Dao = *internal.AdOrders202502Dao

// adOrders202502Dao is the data access object for the table ad_orders_202502.
// You can define custom methods on it to extend its functionality as needed.
type adOrders202502Dao struct {
	internalAdOrders202502Dao
}

var (
	// AdOrders202502 is a globally accessible object for table ad_orders_202502 operations.
	AdOrders202502 = adOrders202502Dao{
		internal.NewAdOrders202502Dao(),
	}
)

// Add your custom methods and functionality below.
