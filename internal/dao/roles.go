// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalRolesDao is an internal type for wrapping the internal DAO implementation.
type internalRolesDao = *internal.RolesDao

// rolesDao is the data access object for the table roles.
// You can define custom methods on it to extend its functionality as needed.
type rolesDao struct {
	internalRolesDao
}

var (
	// Roles is a globally accessible object for table roles operations.
	Roles = rolesDao{
		internal.NewRolesDao(),
	}
)

// Add your custom methods and functionality below.
