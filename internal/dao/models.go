// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalModelsDao is an internal type for wrapping the internal DAO implementation.
type internalModelsDao = *internal.ModelsDao

// modelsDao is the data access object for the table models.
// You can define custom methods on it to extend its functionality as needed.
type modelsDao struct {
	internalModelsDao
}

var (
	// Models is a globally accessible object for table models operations.
	Models = modelsDao{
		internal.NewModelsDao(),
	}
)

// Add your custom methods and functionality below.
