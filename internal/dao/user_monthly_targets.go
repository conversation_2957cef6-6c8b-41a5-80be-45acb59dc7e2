// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalUserMonthlyTargetsDao is an internal type for wrapping the internal DAO implementation.
type internalUserMonthlyTargetsDao = *internal.UserMonthlyTargetsDao

// userMonthlyTargetsDao is the data access object for the table user_monthly_targets.
// You can define custom methods on it to extend its functionality as needed.
type userMonthlyTargetsDao struct {
	internalUserMonthlyTargetsDao
}

var (
	// UserMonthlyTargets is a globally accessible object for table user_monthly_targets operations.
	UserMonthlyTargets = userMonthlyTargetsDao{
		internal.NewUserMonthlyTargetsDao(),
	}
)

// Add your custom methods and functionality below.
