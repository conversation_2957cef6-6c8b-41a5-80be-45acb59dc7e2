// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdSlotPlanPriceLogsDao is an internal type for wrapping the internal DAO implementation.
type internalAdSlotPlanPriceLogsDao = *internal.AdSlotPlanPriceLogsDao

// adSlotPlanPriceLogsDao is the data access object for the table ad_slot_plan_price_logs.
// You can define custom methods on it to extend its functionality as needed.
type adSlotPlanPriceLogsDao struct {
	internalAdSlotPlanPriceLogsDao
}

var (
	// AdSlotPlanPriceLogs is a globally accessible object for table ad_slot_plan_price_logs operations.
	AdSlotPlanPriceLogs = adSlotPlanPriceLogsDao{
		internal.NewAdSlotPlanPriceLogsDao(),
	}
)

// Add your custom methods and functionality below.
