// =================================================================================
// This file is auto-generated by GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalPlatformReportsDao is an internal type for wrapping internal DAO implementation.
type internalPlatformReportsDao = *internal.PlatformReportsDao

// platformReportsDao is the data access object for table platform_reports.
// You can define custom methods on it to extend its functionality as you wish.
type platformReportsDao struct {
	internalPlatformReportsDao
}

var (
	// PlatformReports is the globally public accessible object for table platform_reports operations.
	PlatformReports = platformReportsDao{
		internal.NewPlatformReportsDao(),
	}
)
