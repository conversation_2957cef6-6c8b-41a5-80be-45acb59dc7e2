// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdSlotPlanDailyCategoryStatsDao is an internal type for wrapping the internal DAO implementation.
type internalAdSlotPlanDailyCategoryStatsDao = *internal.AdSlotPlanDailyCategoryStatsDao

// adSlotPlanDailyCategoryStatsDao is the data access object for the table ad_slot_plan_daily_category_stats.
// You can define custom methods on it to extend its functionality as needed.
type adSlotPlanDailyCategoryStatsDao struct {
	internalAdSlotPlanDailyCategoryStatsDao
}

var (
	// AdSlotPlanDailyCategoryStats is a globally accessible object for table ad_slot_plan_daily_category_stats operations.
	AdSlotPlanDailyCategoryStats = adSlotPlanDailyCategoryStatsDao{
		internal.NewAdSlotPlanDailyCategoryStatsDao(),
	}
)

// Add your custom methods and functionality below.
