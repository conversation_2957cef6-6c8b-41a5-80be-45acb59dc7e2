// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdSlotPlanDailyOrderStatsDao is an internal type for wrapping the internal DAO implementation.
type internalAdSlotPlanDailyOrderStatsDao = *internal.AdSlotPlanDailyOrderStatsDao

// adSlotPlanDailyOrderStatsDao is the data access object for the table ad_slot_plan_daily_order_stats.
// You can define custom methods on it to extend its functionality as needed.
type adSlotPlanDailyOrderStatsDao struct {
	internalAdSlotPlanDailyOrderStatsDao
}

var (
	// AdSlotPlanDailyOrderStats is a globally accessible object for table ad_slot_plan_daily_order_stats operations.
	AdSlotPlanDailyOrderStats = adSlotPlanDailyOrderStatsDao{
		internal.NewAdSlotPlanDailyOrderStatsDao(),
	}
)

// Add your custom methods and functionality below.
