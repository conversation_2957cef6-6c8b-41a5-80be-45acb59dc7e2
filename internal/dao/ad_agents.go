// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"ad-pro-v2/internal/dao/internal"
)

// internalAdAgentsDao is an internal type for wrapping the internal DAO implementation.
type internalAdAgentsDao = *internal.AdAgentsDao

// adAgentsDao is the data access object for the table ad_agents.
// You can define custom methods on it to extend its functionality as needed.
type adAgentsDao struct {
	internalAdAgentsDao
}

var (
	// AdAgents is a globally accessible object for table ad_agents operations.
	AdAgents = adAgentsDao{
		internal.NewAdAgentsDao(),
	}
)

// Add your custom methods and functionality below.
