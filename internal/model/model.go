package model

import (
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
)

// TaskListItem 任务列表项
type TaskListItem struct {
	Id        uint64      `json:"id"         dc:"任务ID"`
	Name      string      `json:"name"       dc:"任务名称"`
	Status    string      `json:"status"     dc:"任务状态"`
	CreatedAt *gtime.Time `json:"created_at" dc:"创建时间"`
}

// Model 模型表
type Model struct {
	Id        uint        `json:"id"         dc:"主键ID"`
	Name      string      `json:"name"       dc:"模型名称"`
	CreatedAt *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt *gtime.Time `json:"updated_at" dc:"更新时间"`
}

// ModelDecay 模型衰减记录表
type ModelDecay struct {
	Id        uint        `json:"id"         dc:"主键ID"`
	ModelId   uint        `json:"model_id"   dc:"模型ID"`
	Percent   float64     `json:"percent"    dc:"衰减百分比"`
	CreatedAt *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt *gtime.Time `json:"updated_at" dc:"更新时间"`
}

// ModelImportInput 模型导入请求参数
type ModelImportInput struct {
	Name    string         `json:"name" v:"required#请输入模型名称"`
	Request *ghttp.Request `json:"-"` // 请求对象，用于处理文件上传
}
