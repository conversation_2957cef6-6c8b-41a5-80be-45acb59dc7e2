package model

import (
	"ad-pro-v2/internal/model/entity"
)

// AdProduct 投放产品
type AdProduct struct {
	*entity.AdProducts
}

// 产品类型常量
const (
	PRODUCT_TYPE_NORMAL = 1 // 普通产品
	PRODUCT_TYPE_VIP    = 2 // VIP产品

	// 推广产品类型
	PRODUCT_ELE_EXTERNAL = 1 // 饿了么外投
	PRODUCT_ELE_WECHAT   = 2 // 饿了么微信导购站
	PRODUCT_ELE_FLIGGY   = 3 // 融合饿了么飞猪
	PRODUCT_FLIGGY       = 4 // 飞猪
	PRODUCT_FULI         = 5 // 福利购
)

// 产品状态常量
const (
	PRODUCT_STATUS_DISABLED = 0 // 禁用
	PRODUCT_STATUS_ENABLED  = 1 // 启用
)

// 状态映射
var (
	PRODUCT_TYPE_MAP = map[int]string{
		PRODUCT_TYPE_NORMAL: "普通产品",
		PRODUCT_TYPE_VIP:    "VIP产品",
	}

	PRODUCT_STATUS_MAP = map[int]string{
		PRODUCT_STATUS_DISABLED: "禁用",
		PRODUCT_STATUS_ENABLED:  "启用",
	}
)

// TableName 表名
func (m *AdProduct) TableName() string {
	return "ad_products"
}
