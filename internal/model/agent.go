package model

import (
	"github.com/gogf/gf/v2/frame/g"
)

// PlatformConfig 平台配置
type PlatformConfig struct {
	Platform string                 `json:"platform" description:"平台类型 denghuoplus:灯火 xiaohongshu:小红书"`
	Info     map[string]interface{} `json:"info"     description:"平台配置信息"`
}

// Agent 代理实体
type Agent struct {
	Id                int             `json:"id"                description:"ID"`
	Code              string          `json:"code"              description:"代理编号"`
	Name              string          `json:"name"              description:"代理名称"`
	Type              string          `json:"type"              description:"代理类型 traffic:流量采买 delivery:投放"`
	UserId            int             `json:"userId"            description:"用户ID"`
	AuditStatus       string          `json:"auditStatus"       description:"审核状态 pending:审核中 approved:已通过 rejected:已拒绝"`
	CooperationStatus string          `json:"cooperationStatus" description:"合作状态 not_started:未开始 active:合作中 terminated:已终止"`
	CompanyName       string          `json:"companyName"       description:"公司名称"`
	CompanyAddress    string          `json:"companyAddress"    description:"公司地址"`
	ContactName       string          `json:"contactName"       description:"联系人"`
	ContactPhone      string          `json:"contactPhone"      description:"联系电话"`
	RejectReason      string          `json:"rejectReason"      description:"拒绝原因"`
	Remarks           string          `json:"remarks"           description:"备注"`
	AdAgentId         int             `json:"ad_agent_id"       description:"代理id"`
	PlatformConfig    *PlatformConfig `json:"platformConfig"    description:"平台配置"`
	CreatedBy         int             `json:"createdBy"         description:"创建人ID"`
	UpdatedBy         int             `json:"updatedBy"         description:"更新人ID"`
	CreatedAt         string          `json:"createdAt"         description:"创建时间"`
	UpdatedAt         string          `json:"updatedAt"         description:"更新时间"`
	InstanceId        string          `json:"instanceId"        description:"审批实例ID"`
}

// AgentListReq 获取代理列表请求
type AgentListReq struct {
	g.Meta      `path:"/list" method:"get" tags:"代理管理" summary:"获取代理列表"`
	Name        string `json:"name"        description:"代理名称"`
	Type        string `json:"type"        description:"代理类型 traffic:流量采买 delivery:投放"`
	AuditStatus string `json:"auditStatus" description:"审核状态 pending:审核中 approved:已通过 rejected:已拒绝"`
	MediaId     int    `json:"mediaId"     description:"媒介ID"`
	Page        int    `json:"page"        description:"页码"`
	Size        int    `json:"size"        description:"每页数量"`
}

// AgentListRes 获取代理列表响应
type AgentListRes struct {
	List  []*Agent `json:"list"  description:"列表"`
	Total int      `json:"total" description:"总数"`
	Page  int      `json:"page"  description:"页码"`
	Size  int      `json:"size"  description:"每页数量"`
}

// AgentDetailReq 获取代理详情请求
type AgentDetailReq struct {
	g.Meta `path:"/detail" method:"get" tags:"代理管理" summary:"获取代理详情"`
	Id     int `json:"id" v:"required#请输入代理ID" description:"代理ID"`
}

// AgentDetailRes 获取代理详情响应
type AgentDetailRes struct {
	*Agent
}

// AgentCreateReq 创建代理请求
type AgentCreateReq struct {
	g.Meta         `path:"/create" method:"post" tags:"代理管理" summary:"创建代理"`
	UserId         int             `json:"userId"          description:"用户ID"`
	Name           string          `json:"name"            v:"required#请输入代理名称" description:"代理名称"`
	Type           string          `json:"type"            v:"required|in:traffic,delivery#请选择代理类型|代理类型不正确" description:"代理类型 traffic:流量采买 delivery:投放"`
	CompanyName    string          `json:"companyName"     description:"公司名称"`
	CompanyAddress string          `json:"companyAddress"  description:"公司地址"`
	ContactName    string          `json:"contactName"     description:"联系人"`
	ContactPhone   string          `json:"contactPhone"    description:"联系电话"`
	Remarks        string          `json:"remarks"         description:"备注"`
	PlatformConfig *PlatformConfig `json:"platformConfig"  description:"平台配置"`
	AdAgentId      int             `json:"ad_agent_id"     description:"代理id"`
}

// AgentCreateRes 创建代理响应
type AgentCreateRes struct {
	Id int `json:"id" description:"代理ID"`
}

// AgentUpdateReq 更新代理请求
type AgentUpdateReq struct {
	g.Meta         `path:"/update" method:"put" tags:"代理管理" summary:"更新代理"`
	Id             int             `json:"id"              v:"required#请输入代理ID" description:"代理ID"`
	Name           string          `json:"name"            v:"required#请输入代理名称" description:"代理名称"`
	Type           string          `json:"type"            v:"required|in:traffic,delivery#请选择代理类型|代理类型不正确" description:"代理类型 traffic:流量采买 delivery:投放"`
	CompanyName    string          `json:"companyName"     description:"公司名称"`
	CompanyAddress string          `json:"companyAddress"  description:"公司地址"`
	ContactName    string          `json:"contactName"     description:"联系人"`
	ContactPhone   string          `json:"contactPhone"    description:"联系电话"`
	Remarks        string          `json:"remarks"         description:"备注"`
	PlatformConfig *PlatformConfig `json:"platformConfig"  description:"平台配置"`
	AdAgentId      int             `json:"ad_agent_id"     description:"代理id"`
}

// AgentUpdateRes 更新代理响应
type AgentUpdateRes struct{}

// AgentDeleteReq 删除代理请求
type AgentDeleteReq struct {
	g.Meta `path:"/delete" method:"delete" tags:"代理管理" summary:"删除代理"`
	Id     int `json:"id" v:"required#请输入代理ID" description:"代理ID"`
}

// AgentDeleteRes 删除代理响应
type AgentDeleteRes struct{}

// AgentAuditReq 审核代理请求
type AgentAuditReq struct {
	g.Meta       `path:"/audit" method:"put" tags:"代理管理" summary:"审核代理"`
	Id           int    `json:"id"           v:"required#请输入代理ID" description:"代理ID"`
	AuditStatus  string `json:"auditStatus"  v:"required#请选择审核状态" description:"审核状态 approved:通过 rejected:拒绝"`
	RejectReason string `json:"rejectReason" description:"拒绝原因"`
}

// AgentAuditRes 审核代理响应
type AgentAuditRes struct{}
