package model

// HotArea 热区结构
type HotArea struct {
	Width     int    `json:"width"`      // 宽度
	Height    int    `json:"height"`     // 高度
	Unit      string `json:"unit"`       // 单位，默认是px
	X         int    `json:"x"`          // X坐标
	Y         int    `json:"y"`          // Y坐标
	EventType int    `json:"event_type"` // 事件类型：1-确定 2-取消
	Id        int    `json:"id"`         // 热区ID
}

// Creative 创意实体
type Creative struct {
	Id              uint64    `json:"id"`               // 主键ID
	Name            string    `json:"name"`             // 创意名称
	ImageUrl        string    `json:"image_url"`        // 素材图片地址
	ImageArea       string    `json:"image_area"`       // 图片区域
	BackgroundColor string    `json:"background_color"` // 背景颜色
	HotAreas        []HotArea `json:"hotAreas"`         // 热区列表
	CreatedAt       string    `json:"createdAt"`        // 创建时间
	UpdatedAt       string    `json:"updatedAt"`        // 更新时间
}

// CreativeListReq 获取创意列表请求
type CreativeListReq struct {
	Name string // 创意名称，支持模糊查询
	Page int    // 页码
	Size int    // 每页数量
}

// CreativeListRes 获取创意列表响应
type CreativeListRes struct {
	List  []*Creative // 列表
	Total int         // 总数
	Page  int         // 页码
	Size  int         // 每页数量
}

// CreativeDetailReq 获取创意详情请求
type CreativeDetailReq struct {
	Id uint64 // 创意ID
}

// CreativeDetailRes 获取创意详情响应
type CreativeDetailRes struct {
	*Creative
}

// CreativeCreateReq 创建创意请求
type CreativeCreateReq struct {
	Name            string    // 创意名称
	ImageUrl        string    // 素材图片地址
	ImageArea       string    // 图片区域
	BackgroundColor string    // 背景颜色
	HotAreas        []HotArea // 热区列表
}

// CreativeCreateRes 创建创意响应
type CreativeCreateRes struct {
	Id uint64 // 创意ID
}

// CreativeUpdateReq 更新创意请求
type CreativeUpdateReq struct {
	Id              uint64    // 创意ID
	Name            string    // 创意名称
	ImageUrl        string    // 素材图片地址
	ImageArea       string    // 图片区域
	BackgroundColor string    // 背景颜色
	HotAreas        []HotArea // 热区列表
}

// CreativeUpdateRes 更新创意响应
type CreativeUpdateRes struct{}

// CreativeDeleteReq 删除创意请求
type CreativeDeleteReq struct {
	Id uint64 // 创意ID
}

// CreativeDeleteRes 删除创意响应
type CreativeDeleteRes struct{}
