// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdMediaMonthlyLevels is the golang structure of table ad_media_monthly_levels for DAO operations like Where/Data.
type AdMediaMonthlyLevels struct {
	g.Meta      `orm:"table:ad_media_monthly_levels, do:true"`
	Id          interface{} //
	MediaId     interface{} // 媒体ID
	Month       *gtime.Time // 统计月份
	Orders      interface{} // 月订单数
	DailyOrders interface{} // 日均订单数
	Level       interface{} // 等级
	SubsidyRate interface{} // 补贴比例
	CreatedAt   *gtime.Time //
	UpdatedAt   *gtime.Time //
	DeletedAt   *gtime.Time //
}
