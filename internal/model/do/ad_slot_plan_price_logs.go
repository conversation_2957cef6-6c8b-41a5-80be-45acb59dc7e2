// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotPlanPriceLogs is the golang structure of table ad_slot_plan_price_logs for DAO operations like Where/Data.
type AdSlotPlanPriceLogs struct {
	g.Meta     `orm:"table:ad_slot_plan_price_logs, do:true"`
	Id         interface{} //
	PlanId     interface{} // 计划ID
	OldPrice   interface{} // 变动前价格
	NewPrice   interface{} // 当前价格
	OperatorId interface{} // 操作人ID
	Remark     interface{} // 变动原因
	CreatedAt  *gtime.Time //
	UpdatedAt  *gtime.Time //
}
