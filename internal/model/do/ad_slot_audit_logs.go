// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotAuditLogs is the golang structure of table ad_slot_audit_logs for DAO operations like Where/Data.
type AdSlotAuditLogs struct {
	g.Meta       `orm:"table:ad_slot_audit_logs, do:true"`
	Id           interface{} //
	SlotId       interface{} // 广告位ID
	Event        interface{} // 事件：create-创建审核,update-变更审核
	SubmitterId  interface{} // 发起人ID
	AuditStatus  interface{} // 审核状态：pending-待审核,approved-已通过,rejected-已拒绝
	AuditorId    interface{} // 审核人ID
	AuditAt      *gtime.Time // 审核时间
	RejectReason interface{} // 拒绝理由
	Remark       interface{} // 备注
	AuditDetail  interface{} // 审核详情
	CreatedAt    *gtime.Time //
	UpdatedAt    *gtime.Time //
	DeletedAt    *gtime.Time //
}
