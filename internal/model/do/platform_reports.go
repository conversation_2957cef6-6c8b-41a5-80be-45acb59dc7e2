// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// PlatformReports is the golang structure of table platform_reports for DAO operations like Where/Data.
type PlatformReports struct {
	g.Meta     `orm:"table:platform_reports, do:true"`
	Id         interface{} // 自增主键
	BelongId   interface{} // 计划、创意、单元表主键
	BelongType interface{} // plan/conversion/group
	BizDate    interface{} // 数据汇总时间
	Impression interface{} // 展现量
	Click      interface{} // 点击量
	Cost       interface{} // 消费金额(分)
	CreatedAt  *gtime.Time // 创建时间
	UpdatedAt  *gtime.Time // 更新时间
}
