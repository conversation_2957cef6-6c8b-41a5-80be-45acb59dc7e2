// =================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Model DO.
type Model struct {
	g.Meta          `orm:"table:model, do:true"`
	Id              interface{} `orm:"id,primary"      description:"主键ID"`
	Name            interface{} `orm:"name"            description:"模型名称"`
	CategoryId      interface{} `orm:"category_id"     description:"分类ID"`
	PasswordGroupId interface{} `orm:"password_group_id" description:"密码组ID"`
	CreatedAt       *gtime.Time `orm:"created_at"      description:"创建时间"`
	UpdatedAt       *gtime.Time `orm:"updated_at"      description:"更新时间"`
}

// ModelDecay DO.
type ModelDecay struct {
	g.Meta    `orm:"table:model_decay, do:true"`
	Id        interface{} `orm:"id,primary"   description:"主键ID"`
	ModelId   interface{} `orm:"model_id"     description:"模型ID"`
	Percent   interface{} `orm:"percent"      description:"衰减百分比"`
	CreatedAt *gtime.Time `orm:"created_at"   description:"创建时间"`
	UpdatedAt *gtime.Time `orm:"updated_at"   description:"更新时间"`
} 