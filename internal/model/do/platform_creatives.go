// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// PlatformCreatives is the golang structure of table platform_creatives for DAO operations like Where/Data.
type PlatformCreatives struct {
	g.Meta       `orm:"table:platform_creatives, do:true"`
	Id           interface{} // 自增主键
	PlatformType interface{} // 平台类型
	AgentId      interface{} // 代理id
	MediaId      interface{} // 媒体id
	DataId       interface{} // 创意ID
	PlanId       interface{} // 关联的计划ID
	GroupId      interface{} // 关联的单元ID
	Name         interface{} // 创意名称
	Remark       interface{} // 备注
	CreatedAt    *gtime.Time // 创建时间
	UpdatedAt    *gtime.Time // 更新时间
}
