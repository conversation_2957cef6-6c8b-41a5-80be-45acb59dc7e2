// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdMediaBalanceLogs is the golang structure of table ad_media_balance_logs for DAO operations like Where/Data.
type AdMediaBalanceLogs struct {
	g.Meta        `orm:"table:ad_media_balance_logs, do:true"`
	Id            interface{} //
	MediaId       interface{} // 媒体ID
	Type          interface{} // 变更类型:settle-结算
	SettleMonth   *gtime.Time // 结算月份
	Commission    interface{} // 佣金金额
	Subsidy       interface{} // 补贴金额
	Amount        interface{} // 变更金额
	BeforeBalance interface{} // 变更前余额
	AfterBalance  interface{} // 变更后余额
	Remark        interface{} // 备注
	CreatedBy     interface{} // 操作人ID
	CreatedAt     *gtime.Time //
	UpdatedAt     *gtime.Time //
	DeletedAt     *gtime.Time //
}
