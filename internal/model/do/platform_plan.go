package do

import "github.com/gogf/gf/v2/os/gtime"

// PlatformPlan is the golang structure of table platform_plans for gorm operations.
type PlatformPlan struct {
	Id               uint64      `gorm:"column:id;primaryKey;autoIncrement:true"` // 自增主键
	AgentId          uint64      `gorm:"column:agent_id"`                         // 代理id
	MediaId          uint64      `gorm:"column:media_id"`                         // 媒体id
	PlatformType     string      `gorm:"column:platform_type"`                    // 平台类型
	PlanType         string      `gorm:"column:plan_type"`                        // 计划类型：image_text-图文, video-视频
	Remark           string      `gorm:"column:remark"`                           // 备注
	DataId           string      `gorm:"column:data_id"`                          // 计划ID
	Name             string      `gorm:"column:name"`                             // 计划名称
	PrincipalAccount string      `gorm:"column:principal_account"`                // 商家账户
	PrincipalName    string      `gorm:"column:principal_name"`                   // 商家名称
	MarketTargetName string      `gorm:"column:market_target_name"`               // 营销目标名称
	SceneName        string      `gorm:"column:scene_name"`                       // 投放产品名称
	CreatedAt        *gtime.Time `gorm:"column:created_at"`                       // 创建时间
	UpdatedAt        *gtime.Time `gorm:"column:updated_at"`                       // 更新时间
}
