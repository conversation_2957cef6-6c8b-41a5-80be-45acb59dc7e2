// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Users is the golang structure of table users for DAO operations like Where/Data.
type Users struct {
	g.Meta              `orm:"table:users, do:true"`
	Id                  interface{} //
	Name                interface{} //
	RealName            interface{} // 用户姓名
	Email               interface{} //
	EmailVerifiedAt     *gtime.Time //
	Password            interface{} //
	Role                interface{} // 角色 1-媒介 2-运营 3-管理层
	Status              interface{} // 状态：1在职 2离职
	LastLoginAt         *gtime.Time // 最近登录时间
	LastLoginIp         interface{} // 最近登录IP
	RememberToken       interface{} //
	CreatedAt           *gtime.Time //
	UpdatedAt           *gtime.Time //
	ForceChangePassword interface{} //
	PasswordChangedAt   *gtime.Time //
	DingUserId          interface{} //
	DingDeptId          interface{} //
}
