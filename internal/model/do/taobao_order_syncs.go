// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// TaobaoOrderSyncs is the golang structure of table taobao_order_syncs for DAO operations like Where/Data.
type TaobaoOrderSyncs struct {
	g.Meta       `orm:"table:taobao_order_syncs, do:true"`
	Id           interface{} //
	StartTime    *gtime.Time //
	EndTime      *gtime.Time //
	Pid          interface{} //
	Status       interface{} //
	TotalCount   interface{} //
	SuccessCount interface{} //
	FailedCount  interface{} //
	ErrorMessage interface{} //
	CompletedAt  *gtime.Time //
	CreatedAt    *gtime.Time //
	UpdatedAt    *gtime.Time //
}
