// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// PromotionZones is the golang structure of table promotion_zones for DAO operations like Where/Data.
type PromotionZones struct {
	g.Meta          `orm:"table:promotion_zones, do:true"`
	Id              interface{} //
	PlanId          interface{} // 投放计划ID
	AdProductId     interface{} // 产品ID
	ZoneId          interface{} // 推广位ID
	ZoneName        interface{} // 推广位名称
	Pid             interface{} // PID
	ZoneType        interface{} // 推广位类型：ele-饿了么，fliggy-飞猪
	PromotionLink   interface{} // 推广链接
	PromotionQrcode interface{} // 推广二维码
	ZoneParams      interface{} // 推广位参数
	PromotionParams interface{} // 推广参数
	MergedPageUrl   interface{} // 融合页面URL
	MergeParams     interface{} // 融合参数
	CreatedBy       interface{} // 创建人ID
	UpdatedBy       interface{} // 更新人ID
	CreatedAt       *gtime.Time //
	UpdatedAt       *gtime.Time //
	DeletedAt       *gtime.Time //
}
