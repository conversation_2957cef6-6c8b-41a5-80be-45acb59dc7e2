// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Tasks is the golang structure of table tasks for DAO operations like Where/Data.
type Tasks struct {
	g.Meta      `orm:"table:tasks, do:true"`
	Id          interface{} // 自增主键
	TaskType    interface{} // 任务类型：1-费用导入
	Name        interface{} // 任务名称
	Params      interface{} // 任务参数JSON
	Status      interface{} // 任务状态：pending-待处理 processing-处理中 success-成功 failed-失败
	Error       interface{} // 错误信息
	CreatedAt   *gtime.Time // 创建时间
	UpdatedAt   *gtime.Time // 更新时间
	CompletedAt *gtime.Time // 完成时间
}
