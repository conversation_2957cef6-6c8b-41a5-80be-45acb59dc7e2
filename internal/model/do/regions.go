// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Regions is the golang structure of table regions for DAO operations like Where/Data.
type Regions struct {
	g.Meta    `orm:"table:regions, do:true"`
	Id        interface{} //
	ParentId  interface{} // 父级ID
	Name      interface{} // 地区名称
	Code      interface{} // 地区编码
	Level     interface{} // 层级：1=省份，2=城市，3=区县
	IsEnabled interface{} // 是否启用
	Sort      interface{} // 排序
	CreatedAt *gtime.Time //
	UpdatedAt *gtime.Time //
}
