package do

import "github.com/gogf/gf/v2/os/gtime"

// PlatformCreative is the golang structure of table platform_creatives for gorm operations.
type PlatformCreative struct {
	Id           uint64      `gorm:"column:id;primaryKey;autoIncrement:true"` // 自增主键
	PlatformType string      `gorm:"column:platform_type"`                    // 平台类型
	AgentId      uint64      `gorm:"column:agent_id"`                         // 代理id
	MediaId      uint64      `gorm:"column:media_id"`                         // 媒体id
	DataId       string      `gorm:"column:data_id"`                          // 创意ID
	PlanId       uint64      `gorm:"column:plan_id"`                          // 关联的计划ID
	GroupId      uint64      `gorm:"column:group_id"`                         // 关联的单元ID
	Name         string      `gorm:"column:name"`                             // 创意名称
	Remark       string      `gorm:"column:remark"`                           // 备注
	CreatedAt    *gtime.Time `gorm:"column:created_at"`                       // 创建时间
	UpdatedAt    *gtime.Time `gorm:"column:updated_at"`                       // 更新时间
}
