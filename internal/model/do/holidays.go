// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Holidays is the golang structure of table holidays for DAO operations like Where/Data.
type Holidays struct {
	g.Meta    `orm:"table:holidays, do:true"`
	Id        interface{} //
	Date      *gtime.Time // 节假日日期
	Name      interface{} // 节假日名称
	IsWorkday interface{} // 是否调休工作日
	CreatedAt *gtime.Time //
	UpdatedAt *gtime.Time //
}
