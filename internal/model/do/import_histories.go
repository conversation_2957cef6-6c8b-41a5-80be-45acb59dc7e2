// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ImportHistories is the golang structure of table import_histories for DAO operations like Where/Data.
type ImportHistories struct {
	g.Meta        `orm:"table:import_histories, do:true"`
	Id            interface{} //
	UserId        interface{} //
	Type          interface{} //
	FileName      interface{} //
	Status        interface{} //
	TotalRows     interface{} //
	SuccessRows   interface{} //
	FailedRows    interface{} //
	ErrorMessages interface{} //
	CompletedAt   *gtime.Time //
	CreatedAt     *gtime.Time //
	UpdatedAt     *gtime.Time //
}
