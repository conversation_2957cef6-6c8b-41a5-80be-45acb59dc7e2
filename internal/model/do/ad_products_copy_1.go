// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdProductsCopy1 is the golang structure of table ad_products_copy1 for DAO operations like Where/Data.
type AdProductsCopy1 struct {
	g.Meta      `orm:"table:ad_products_copy1, do:true"`
	Id          interface{} //
	Name        interface{} // 广告产品名称
	Image       interface{} // 广告产品图片
	Description interface{} // 广告产品描述
	CreatedAt   *gtime.Time //
	UpdatedAt   *gtime.Time //
}
