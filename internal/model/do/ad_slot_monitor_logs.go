// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotMonitorLogs is the golang structure of table ad_slot_monitor_logs for DAO operations like Where/Data.
type AdSlotMonitorLogs struct {
	g.Meta      `orm:"table:ad_slot_monitor_logs, do:true"`
	Id          interface{} //
	SlotId      interface{} // 广告位ID
	MonitorType interface{} // 监控类型：traffic-流量监控,quality-质量监控,cost-成本监控
	Status      interface{} // 状态：normal-正常,warning-警告,error-异常
	MonitorData interface{} // 监控数据
	Remark      interface{} // 备注
	CreatedAt   *gtime.Time //
	UpdatedAt   *gtime.Time //
}
