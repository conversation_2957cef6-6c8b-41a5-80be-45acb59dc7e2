// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// JobBatches is the golang structure of table job_batches for DAO operations like Where/Data.
type JobBatches struct {
	g.Meta       `orm:"table:job_batches, do:true"`
	Id           interface{} //
	Name         interface{} //
	TotalJobs    interface{} //
	PendingJobs  interface{} //
	FailedJobs   interface{} //
	FailedJobIds interface{} //
	Options      interface{} //
	CancelledAt  interface{} //
	CreatedAt    interface{} //
	FinishedAt   interface{} //
}
