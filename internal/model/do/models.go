// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Models is the golang structure of table models for DAO operations like Where/Data.
type Models struct {
	g.Meta    `orm:"table:models, do:true"`
	Id        interface{} // 主键ID
	Name      interface{} // 模型名称
	CreatedAt *gtime.Time // 创建时间
	UpdatedAt *gtime.Time // 更新时间
}
