// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// PlatformConversions is the golang structure of table platform_conversions for DAO operations like Where/Data.
type PlatformConversions struct {
	g.Meta             `orm:"table:platform_conversions, do:true"`
	Id                 interface{} // 自增主键
	BelongId           interface{} // 计划、创意、单元表主键
	BelongType         interface{} // plan/conversion/group
	PlanId             interface{} // 关联的计划ID
	BizDate            interface{} // 数据汇总时间
	ConversionType     interface{} // 转化事件类型
	ConversionTypeName interface{} // 转化事件类型名称
	ConversionResult   interface{} // 转化事件结果
	CreatedAt          *gtime.Time // 创建时间
	UpdatedAt          *gtime.Time // 更新时间
}
