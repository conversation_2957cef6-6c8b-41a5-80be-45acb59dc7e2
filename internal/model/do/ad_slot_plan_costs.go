// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotPlanCosts is the golang structure of table ad_slot_plan_costs for DAO operations like Where/Data.
type AdSlotPlanCosts struct {
	g.Meta       `orm:"table:ad_slot_plan_costs, do:true"`
	Id           interface{} //
	PlanId       interface{} // 投放计划ID
	Date         *gtime.Time // 统计日期
	MediaId      interface{} // 媒体ID
	AdSlotId     interface{} // 广告位ID
	AdProductId  interface{} // 产品ID
	UserId       interface{} // 媒介ID
	Clicks       interface{} // 点击量
	Cost         interface{} // 成本
	Remark       interface{} // 备注
	AuditStatus  interface{} // 审核状态:pending=待审核,approved=已通过,rejected=已拒绝
	AuditBy      interface{} // 审核人
	AuditAt      *gtime.Time // 审核时间
	RejectReason interface{} // 拒绝原因
	CreatedBy    interface{} // 创建人
	UpdatedBy    interface{} // 更新人
	CreatedAt    *gtime.Time //
	UpdatedAt    *gtime.Time //
	DeletedAt    *gtime.Time //
}
