// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotPlanDailyCategoryStats is the golang structure of table ad_slot_plan_daily_category_stats for DAO operations like Where/Data.
type AdSlotPlanDailyCategoryStats struct {
	g.Meta                 `orm:"table:ad_slot_plan_daily_category_stats, do:true"`
	Id                     interface{} //
	DailyStatId            interface{} //
	Category               interface{} // 商品分类
	BdOrders               interface{} // 媒介订单数
	AdminOrders            interface{} // 管理员订单数
	BdRevenue              interface{} // 媒介收入
	BdActivityFee          interface{} // 媒介激励费用
	AdminRevenue           interface{} // 管理员收入
	AdminActivityFee       interface{} // 实际激励费用
	CreatedAt              *gtime.Time //
	UpdatedAt              *gtime.Time //
	BdSettleOrders         interface{} // 媒介结算订单数
	AdminSettleOrders      interface{} // 实际结算订单数
	BdSettleRevenue        interface{} // 媒介结算收入
	AdminSettleRevenue     interface{} // 实际结算收入
	BdSettleActivityFee    interface{} // 媒介结算激励
	AdminSettleActivityFee interface{} // 实际结算激励
}
