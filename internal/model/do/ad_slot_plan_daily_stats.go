// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotPlanDailyStats is the golang structure of table ad_slot_plan_daily_stats for DAO operations like Where/Data.
type AdSlotPlanDailyStats struct {
	g.Meta                 `orm:"table:ad_slot_plan_daily_stats, do:true"`
	Id                     interface{} //
	PlanId                 interface{} // 计划ID
	MediaId                interface{} // 媒体ID
	AdSlotId               interface{} // 广告位ID
	AdProductId            interface{} // 投放产品ID
	UserId                 interface{} // 媒介ID
	Date                   *gtime.Time // 数据日期
	Clicks                 interface{} // 点击量
	UnitPrice              interface{} // 单价
	Cost                   interface{} // 成本
	BdOrders               interface{} // 媒介订单量
	BdRevenue              interface{} // 媒介收入
	BdProfit               interface{} // 媒介利润
	BdRoi                  interface{} // 媒介ROI
	BdActivityFee          interface{} // 媒介激励费用
	BdSettleOrders         interface{} // 媒介结算订单量
	BdSettleRevenue        interface{} // 媒介结算收入
	BdSettleActivityFee    interface{} // 媒介结算激励
	BdSettleProfit         interface{} // 媒介结算利润
	BdSettleRoi            interface{} // 媒介结算ROI
	AdminOrders            interface{} // 实际订单量
	AdminRevenue           interface{} // 实际收入
	AdminProfit            interface{} // 实际利润
	AdminRoi               interface{} // 实际ROI
	AdminActivityFee       interface{} // 实际激励费用
	AdminSettleOrders      interface{} // 实际结算订单量
	AdminSettleRevenue     interface{} // 实际结算收入
	AdminSettleActivityFee interface{} // 实际结算激励
	AdminSettleProfit      interface{} // 实际结算利润
	AdminSettleRoi         interface{} // 实际结算ROI
	OrderTypeStats         interface{} // 收益明细
	CreatedBy              interface{} // 创建人ID
	UpdatedBy              interface{} // 更新人ID
	CreatedAt              *gtime.Time //
	UpdatedAt              *gtime.Time //
	DeletedAt              *gtime.Time //
}
