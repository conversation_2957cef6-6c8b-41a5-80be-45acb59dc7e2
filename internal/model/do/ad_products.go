// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdProducts is the golang structure of table ad_products for DAO operations like Where/Data.
type AdProducts struct {
	g.Meta      `orm:"table:ad_products, do:true"`
	Id          interface{} //
	Code        interface{} // 产品编号
	Name        interface{} // 广告产品名称
	Image       interface{} // 广告产品图片
	Description interface{} // 广告产品描述
	Status      interface{} // 状态：active-启用,inactive-停用
	CreatedBy   interface{} // 创建人ID
	UpdatedBy   interface{} // 更新人ID
	CreatedAt   *gtime.Time //
	UpdatedAt   *gtime.Time //
	DeletedAt   *gtime.Time //
}
