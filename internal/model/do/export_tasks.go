// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ExportTasks is the golang structure of table export_tasks for DAO operations like Where/Data.
type ExportTasks struct {
	g.Meta    `orm:"table:export_tasks, do:true"`
	Id        interface{} //
	CreatedAt *gtime.Time //
	UpdatedAt *gtime.Time //
	MediaId   interface{} //
	Type      interface{} //
	Status    interface{} //
	FileUrl   interface{} //
	Error     interface{} //
	Params    interface{} //
	TotalRows interface{} //
	CreatedBy interface{} //
}
