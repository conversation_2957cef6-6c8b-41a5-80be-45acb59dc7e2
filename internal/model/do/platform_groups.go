// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// PlatformGroups is the golang structure of table platform_groups for DAO operations like Where/Data.
type PlatformGroups struct {
	g.Meta       `orm:"table:platform_groups, do:true"`
	Id           interface{} // 自增主键
	PlatformType interface{} // 平台类型
	AgentId      interface{} // 代理id
	MediaId      interface{} // 媒体id
	DataId       interface{} // 单元ID
	PlanId       interface{} // 关联的计划ID
	Name         interface{} // 单元名称
	Remark       interface{} // 备注
	CreatedAt    *gtime.Time // 创建时间
	UpdatedAt    *gtime.Time // 更新时间
}
