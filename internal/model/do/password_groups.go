// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// PasswordGroups is the golang structure of table password_groups for DAO operations like Where/Data.
type PasswordGroups struct {
	g.Meta     `orm:"table:password_groups, do:true"`
	Id         interface{} // 主键ID
	CreatedAt  *gtime.Time // 创建时间
	UpdatedAt  *gtime.Time // 更新时间
	Name       interface{} // 组名称
	CategoryId interface{} //
}
