// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdMediaSubsidyRules is the golang structure of table ad_media_subsidy_rules for DAO operations like Where/Data.
type AdMediaSubsidyRules struct {
	g.Meta          `orm:"table:ad_media_subsidy_rules, do:true"`
	Id              interface{} //
	Level           interface{} // 等级:A,B,C,D,E
	MediaType       interface{} // 媒体类型
	CooperationType interface{} // 合作类型
	OrdersFrom      interface{} // 订单数起始值
	OrdersTo        interface{} // 订单数结束值
	SubsidyRate     interface{} // 补贴比例
	CreatedAt       *gtime.Time //
	UpdatedAt       *gtime.Time //
	DeletedAt       *gtime.Time //
}
