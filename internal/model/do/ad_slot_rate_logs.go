// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotRateLogs is the golang structure of table ad_slot_rate_logs for DAO operations like Where/Data.
type AdSlotRateLogs struct {
	g.Meta    `orm:"table:ad_slot_rate_logs, do:true"`
	Id        interface{} //
	AdSlotId  interface{} // 广告位ID
	UserId    interface{} // 操作人ID
	Field     interface{} // 修改字段：leak_rate-泄漏率,discount_rate-折扣率
	OldValue  interface{} // 原值
	NewValue  interface{} // 新值
	Remark    interface{} // 修改原因
	CreatedAt *gtime.Time //
	UpdatedAt *gtime.Time //
}
