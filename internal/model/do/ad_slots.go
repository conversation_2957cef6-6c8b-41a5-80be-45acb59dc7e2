// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlots is the golang structure of table ad_slots for DAO operations like Where/Data.
type AdSlots struct {
	g.Meta        `orm:"table:ad_slots, do:true"`
	Id            interface{} //
	Code          interface{} //
	Type          interface{} //
	Name          interface{} //
	UserId        interface{} //
	MediaId       interface{} //
	MediaName     interface{} //
	ScreenshotUrl interface{} //
	VideoUrl      interface{} //
	AuditStatus   interface{} //
	RejectReason  interface{} //
	LeakRate      interface{} //
	DiscountRate  interface{} //
	Remark        interface{} //
	CreatedBy     interface{} //
	UpdatedBy     interface{} //
	CreatedAt     *gtime.Time //
	UpdatedAt     *gtime.Time //
	DeletedAt     *gtime.Time //
}
