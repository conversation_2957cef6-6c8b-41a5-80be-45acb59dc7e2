package do

import "github.com/gogf/gf/v2/os/gtime"

// PlatformConversion is the golang structure of table platform_conversions for gorm operations.
type PlatformConversion struct {
	Id                 uint64      `gorm:"column:id;primaryKey;autoIncrement:true"` // 自增主键
	BelongId           uint64      `gorm:"column:belong_id"`                        // 计划、创意、单元表主键
	BelongType         string      `gorm:"column:belong_type"`                      // plan/conversion/group
	PlanId             uint64      `gorm:"column:plan_id"`                          // 关联的计划ID
	BizDate            string      `gorm:"column:biz_date"`                         // 数据汇总时间
	ConversionType     string      `gorm:"column:conversion_type"`                  // 转化事件类型
	ConversionTypeName string      `gorm:"column:conversion_type_name"`             // 转化事件类型名称
	ConversionResult   string      `gorm:"column:conversion_result"`                // 转化事件结果
	CreatedAt          *gtime.Time `gorm:"column:created_at"`                       // 创建时间
	UpdatedAt          *gtime.Time `gorm:"column:updated_at"`                       // 更新时间
}
