// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// TaobaoPids is the golang structure of table taobao_pids for DAO operations like Where/Data.
type TaobaoPids struct {
	g.Meta       `orm:"table:taobao_pids, do:true"`
	Id           interface{} //
	AccountName  interface{} // 联盟账号
	MemberId     interface{} // 会员ID
	ZoneName     interface{} // 推广位名称
	Pid          interface{} // 推广位ID
	IsUsed       interface{} // 是否使用
	UsedAt       *gtime.Time // 使用时间
	UsedBy       interface{} // 使用者ID
	AdSlotPlanId interface{} // 关联计划ID
	AdCreativeId interface{} // 关联创意ID
	ActType      interface{} // 活动类型:1-飞猪 2-福利购
	CreatedAt    *gtime.Time //
	UpdatedAt    *gtime.Time //
	DeletedAt    *gtime.Time //
}
