// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// UserMonthlyTargets is the golang structure of table user_monthly_targets for DAO operations like Where/Data.
type UserMonthlyTargets struct {
	g.Meta       `orm:"table:user_monthly_targets, do:true"`
	Id           interface{} //
	UserId       interface{} //
	Year         interface{} //
	Month        interface{} //
	ProfitTarget interface{} //
	ActualProfit interface{} //
	Remark       interface{} //
	CreatedBy    interface{} //
	UpdatedBy    interface{} //
	CreatedAt    *gtime.Time //
	UpdatedAt    *gtime.Time //
	DeletedAt    *gtime.Time //
}
