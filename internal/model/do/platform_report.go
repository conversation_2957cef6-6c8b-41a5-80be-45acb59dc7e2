package do

import "github.com/gogf/gf/v2/os/gtime"

// PlatformReport is the golang structure of table platform_reports for gorm operations.
type PlatformReport struct {
	Id         uint64      `gorm:"column:id;primaryKey;autoIncrement:true"` // 自增主键
	BelongId   uint64      `gorm:"column:belong_id"`                        // 计划、创意、单元表主键
	BelongType string      `gorm:"column:belong_type"`                      // plan/conversion/group
	BizDate    string      `gorm:"column:biz_date"`                         // 数据汇总时间
	Impression uint64      `gorm:"column:impression"`                       // 展现量
	Click      uint64      `gorm:"column:click"`                            // 点击量
	Cost       uint64      `gorm:"column:cost"`                             // 消费金额(分)
	CreatedAt  *gtime.Time `gorm:"column:created_at"`                       // 创建时间
	UpdatedAt  *gtime.Time `gorm:"column:updated_at"`                       // 更新时间
}
