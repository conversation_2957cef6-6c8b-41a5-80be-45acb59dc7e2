// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotPlans is the golang structure of table ad_slot_plans for DAO operations like Where/Data.
type AdSlotPlans struct {
	g.Meta           `orm:"table:ad_slot_plans, do:true"`
	Id               interface{} //
	LinkId           interface{} // 历史系统关联ID
	Code             interface{} // 计划编码
	Type             interface{} // 计划类型：test-测试计划,formal-正式计划
	AdSlotId         interface{} // 广告位ID
	MediaId          interface{} //
	AdProductId      interface{} // 投放产品ID
	AdCreativeId     interface{} // 创意ID
	UserId           interface{} // 用户ID
	Price            interface{} // 价格
	AuditStatus      interface{} // 审核状态：pending-待审核,approved-已通过,rejected-已拒绝
	DeliveryStatus   interface{} // 投放状态：init-初始状态,configuring-配置中,wait-待开始,running-投放中,stopped-手动停投,stopped_auto-自动停投,completed-已完结
	DeliveryMode     interface{} // 投放策略:1=普通投放,2=分日组合,3=分区域组合
	StartDate        *gtime.Time // 开始时间
	EndDate          *gtime.Time // 结束时间
	IsEndDateEnabled interface{} // 结束时间是否生效
	PromotionLink    interface{} // 推广链接
	PromotionParams  interface{} // 推广参数
	PromotionQrcode  interface{} // 推广二维码
	ShortUrl         interface{} // 短链接
	RejectReason     interface{} // 拒绝原因
	Remark           interface{} // 备注
	CreatedBy        interface{} // 创建人ID
	UpdatedBy        interface{} // 更新人ID
	CreatedAt        *gtime.Time //
	UpdatedAt        *gtime.Time //
	DeletedAt        *gtime.Time //
	StopReason       interface{} // 停止投放原因
	TestResult       interface{} // 测试结论：success=测试成功，failed=测试失败
	TestFailedReason interface{} // 测试失败原因
	TestResultAt     *gtime.Time // 测试结论时间
	TestResultBy     interface{} // 测试结论操作人
}
