// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdAgents is the golang structure of table ad_agents for DAO operations like Where/Data.
type AdAgents struct {
	g.Meta            `orm:"table:ad_agents, do:true"`
	Id                interface{} //
	Code              interface{} // 代理编号
	Name              interface{} // 代理名称
	UserId            interface{} // 用户ID
	AuditStatus       interface{} // 审核状态：pending-审核中,approved-已通过,rejected-已拒绝
	CooperationStatus interface{} // 合作状态：not_started-未开始,active-合作中,terminated-已终止
	CompanyName       interface{} // 公司名称
	CompanyAddress    interface{} // 公司地址
	ContactName       interface{} // 联系人
	ContactPhone      interface{} // 联系电话
	RejectReason      interface{} // 拒绝原因
	Remarks           interface{} // 备注
	PlatformConfig    interface{} // 平台配置 {platform: "denghuoplus|xiaohongshu", info: {token: "", pid: ""}}
	CreatedBy         interface{} // 创建人ID
	UpdatedBy         interface{} // 更新人ID
	CreatedAt         *gtime.Time //
	UpdatedAt         *gtime.Time //
	DeletedAt         *gtime.Time //
	InstanceId        interface{} // 审批实例ID
	Type              interface{} // 代理类型：traffic-流量采买,delivery-投放
}
