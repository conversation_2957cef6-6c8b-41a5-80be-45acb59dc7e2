// =================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// PromotionReport is the data object of promotion_report table.
type PromotionReport struct {
	Id            interface{} // 主键ID
	ReportDate    interface{} // 报表日期 格式:20241201
	GroupId       interface{} // 口令组ID
	CategoryId    interface{} // 口令组分类ID
	Cost          interface{} // 投放费用
	TotalOrders   interface{} // 当日总订单数
	TotalCommission interface{} // 当日总佣金
	CreatedAt     *gtime.Time // 创建时间
	UpdatedAt     *gtime.Time // 更新时间
} 