// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// RegionsCopy1 is the golang structure of table regions_copy1 for DAO operations like Where/Data.
type RegionsCopy1 struct {
	g.Meta    `orm:"table:regions_copy1, do:true"`
	Id        interface{} //
	ParentId  interface{} // 父级ID
	Name      interface{} // 地区名称
	Code      interface{} // 地区编码
	Level     interface{} // 层级：1=省份，2=城市，3=区县
	IsEnabled interface{} // 是否启用
	Sort      interface{} // 排序
	CreatedAt *gtime.Time //
	UpdatedAt *gtime.Time //
}
