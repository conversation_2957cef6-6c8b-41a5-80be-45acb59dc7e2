// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// PlatformPlans is the golang structure of table platform_plans for DAO operations like Where/Data.
type PlatformPlans struct {
	g.Meta           `orm:"table:platform_plans, do:true"`
	Id               interface{} // 自增主键
	AgentId          interface{} // 代理id
	MediaId          interface{} // 媒体id
	PlatformType     interface{} // 平台类型
	Remark           interface{} // 备注
	DataId           interface{} // 计划ID
	Name             interface{} // 计划名称
	PlanType         interface{} // 计划类型:image_text(图文),video(视频)
	PrincipalAccount interface{} // 商家账户
	PrincipalName    interface{} // 商家名称
	MarketTargetName interface{} // 营销目标名称
	SceneName        interface{} // 投放产品名称
	CreatedAt        *gtime.Time // 创建时间
	UpdatedAt        *gtime.Time // 更新时间
}
