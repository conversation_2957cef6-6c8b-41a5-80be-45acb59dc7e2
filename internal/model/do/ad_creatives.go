// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdCreatives is the golang structure of table ad_creatives for DAO operations like Where/Data.
type AdCreatives struct {
	g.Meta          `orm:"table:ad_creatives, do:true"`
	Id              interface{} // 主键ID
	Name            interface{} // 创意名称
	ImageUrl        interface{} // 素材图片地址
	ImageArea       interface{} // 图片区域宽度
	HotAreas        interface{} // 热区，JSON字符串
	CreatedAt       *gtime.Time // 创建时间
	UpdatedAt       *gtime.Time // 更新时间
	BackgroundColor interface{} //
}
