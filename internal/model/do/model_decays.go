// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ModelDecays is the golang structure of table model_decays for DAO operations like Where/Data.
type ModelDecays struct {
	g.Meta    `orm:"table:model_decays, do:true"`
	Id        interface{} // 主键ID
	ModelId   interface{} // 模型ID
	Percent   interface{} // 衰减百分比
	CreatedAt *gtime.Time // 创建时间
	UpdatedAt *gtime.Time // 更新时间
}
