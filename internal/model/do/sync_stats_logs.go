// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SyncStatsLogs is the golang structure of table sync_stats_logs for DAO operations like Where/Data.
type SyncStatsLogs struct {
	g.Meta        `orm:"table:sync_stats_logs, do:true"`
	Id            interface{} //
	ProcedureName interface{} // 存储过程名称
	Message       interface{} // 日志消息
	CreatedAt     *gtime.Time //
}
