// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotPlanDailyOrderStatsBackup20240318 is the golang structure of table ad_slot_plan_daily_order_stats_backup_20240318 for DAO operations like Where/Data.
type AdSlotPlanDailyOrderStatsBackup20240318 struct {
	g.Meta                 `orm:"table:ad_slot_plan_daily_order_stats_backup_20240318, do:true"`
	Id                     interface{} //
	DailyStatId            interface{} //
	OrderType              interface{} // 订单类型
	BdOrders               interface{} // 媒介订单数
	BdSettleOrders         interface{} // 媒介结算订单数
	AdminOrders            interface{} // 管理员订单数
	AdminSettleOrders      interface{} // 实际结算订单数
	BdRevenue              interface{} // 媒介收入
	BdSettleRevenue        interface{} // 媒介结算收入
	BdActivityFee          interface{} // 媒介激励费用
	BdSettleActivityFee    interface{} // 媒介结算激励
	AdminRevenue           interface{} // 管理员收入
	AdminSettleRevenue     interface{} // 实际结算收入
	AdminActivityFee       interface{} // 实际激励费用
	AdminSettleActivityFee interface{} // 实际结算��励
	CreatedAt              *gtime.Time //
	UpdatedAt              *gtime.Time //
}
