// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdOrders is the golang structure of table ad_orders for DAO operations like Where/Data.
type AdOrders struct {
	g.Meta               `orm:"table:ad_orders, do:true"`
	Id                   interface{} // 主键ID
	AdMediaId            interface{} // 媒体ID
	AdMediaName          interface{} // 媒体名称
	AdSlotId             interface{} // 广告位ID
	AdPlanId             interface{} // 计划ID
	BdId                 interface{} // 媒介ID
	SupplierId           interface{} // 供应商ID
	OrderType            interface{} // 订单类型
	SubOrderType         interface{} // 子订单类型
	OrderStatus          interface{} // 订单状态
	ParentOrderId        interface{} // 父订单ID
	SubOrderId           interface{} // 子订单ID
	ItemId               interface{} // 商品ID
	ItemTitle            interface{} // 商品标题
	ItemCover            interface{} // 商品封面
	ItemPrice            interface{} // 商品价格
	ItemNum              interface{} // 商品数量
	OrderPrice           interface{} // 订单金额
	PayPrice             interface{} // 实付金额
	Rate                 interface{} // 佣金比例50%即0.5
	OriPreCommission     interface{} // 预估佣金
	OriCommission        interface{} // 结算佣金
	PreCommission        interface{} // 实际预估佣金
	Commission           interface{} // 实际结算佣金
	ServiceFee           interface{} // 服务费
	ServiceRatio         interface{} // 服务费比例
	ThirdServiceFee      interface{} // 三方服务费
	ThirdServiceRatio    interface{} // 三方服务费比例
	ActivityFee          interface{} // 活动补贴
	ActivityServiceFee   interface{} // 活动补贴服务费
	ActivityServiceRatio interface{} // 活动补贴服务费比例
	CreateTime           *gtime.Time // 创建时间
	PayTime              *gtime.Time // 支付时间
	ReceiveTime          *gtime.Time // 收货时间
	UnionId              interface{} // 联盟ID
	Pid                  interface{} // PID
	AdzoneName           interface{} // 推广位名称
	RelationId           interface{} // RID
	Category             interface{} // 类目
	City                 interface{} // 城市
	ActivityType         interface{} // 活动类型，BRAND、CONSUMPTION
	AttrOrderDesc        interface{} // 是否归因
	Leak                 interface{} // 是否漏单
	CreatedAt            *gtime.Time // 创建时间
	UpdatedAt            *gtime.Time // 更新时间
	PreSubsidyFee        interface{} // 预估补贴金额
	SubsidyFee           interface{} // 结算补贴金额
	SettleTime           *gtime.Time // 结算时间
	SettleLogId          interface{} // 结算记录ID
}
