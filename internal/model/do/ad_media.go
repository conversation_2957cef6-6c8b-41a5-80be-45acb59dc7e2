// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdMedia is the golang structure of table ad_media for DAO operations like Where/Data.
type AdMedia struct {
	g.Meta            `orm:"table:ad_media, do:true"`
	Id                interface{} //
	Code              interface{} //
	AdAgentId         interface{} //
	Name              interface{} //
	AuditStatus       interface{} //
	CooperationStatus interface{} //
	CooperationType   interface{} //
	Account           interface{} //
	Password          interface{} //
	LastLoginAt       interface{} //
	Balance           interface{} //
	Types             interface{} //
	Industry          interface{} //
	CustomIndustry    interface{} //
	DailyActivity     interface{} //
	TransactionVolume interface{} //
	RegionCodes       interface{} //
	CompanyName       interface{} //
	CompanyAddress    interface{} //
	ContactName       interface{} //
	ContactPhone      interface{} //
	RejectReason      interface{} //
	UserId            interface{} //
	Remark            interface{} //
	CreatedBy         interface{} //
	UpdatedBy         interface{} //
	CreatedAt         *gtime.Time //
	UpdatedAt         *gtime.Time //
	DeletedAt         *gtime.Time //
	InstanceId        interface{} //
	PlatformConfig    interface{} // 平台配置 仅当 cooperationType=delivery 时可配置
}
