// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// PromotionReports is the golang structure of table promotion_reports for DAO operations like Where/Data.
type PromotionReports struct {
	g.Meta          `orm:"table:promotion_reports, do:true"`
	Id              interface{} // 主键ID
	ReportDate      interface{} // 报表日期
	GroupId         interface{} // 口令组ID
	CategoryId      interface{} // 口令组分类id
	Cost            interface{} // 投放费用
	TotalOrders     interface{} // 当日总订单数
	TotalCommission interface{} // 当日总佣金
	CreatedAt       *gtime.Time // 创建时间
	UpdatedAt       *gtime.Time // 更新时间
}
