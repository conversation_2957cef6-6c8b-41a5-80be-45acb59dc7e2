// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdMediaWithdraws is the golang structure of table ad_media_withdraws for DAO operations like Where/Data.
type AdMediaWithdraws struct {
	g.Meta       `orm:"table:ad_media_withdraws, do:true"`
	Id           interface{} //
	MediaId      interface{} // 媒体ID
	Amount       interface{} // 提现金额
	TaxAmount    interface{} // 税费金额
	ActualAmount interface{} // 实际打款金额
	WithdrawType interface{} // 提现方式:alipay-支付宝,bank-对公转账
	AccountInfo  interface{} // 账户信息
	InvoiceFile  interface{} // 发票文件
	Status       interface{} // 状态:pending-待审核,approved-已审核,rejected-已拒绝,paid-已打款
	AuditTime    *gtime.Time // 审核时间
	AuditUserId  interface{} // 审核人ID
	PayTime      *gtime.Time // 打款时间
	PayUserId    interface{} // 打款操作人ID
	Remark       interface{} // 备注
	CreatedAt    *gtime.Time //
	UpdatedAt    *gtime.Time //
	DeletedAt    *gtime.Time //
}
