package do

import "github.com/gogf/gf/v2/os/gtime"

// Cost 成本管理数据对象
type Cost struct {
	Id         uint        `json:"id"           description:"主键ID"`
	GroupId    uint        `json:"group_id"     description:"口令组ID"`
	Date       *gtime.Time `json:"date"         description:"统计日期"`
	CostAmount float64     `json:"cost_amount"  description:"成本金额"`
	CreatedAt  *gtime.Time `json:"created_at"   description:"创建时间"`
	UpdatedAt  *gtime.Time `json:"updated_at"   description:"更新时间"`
	DeletedAt  *gtime.Time `json:"deleted_at"   description:"删除时间"`
}
