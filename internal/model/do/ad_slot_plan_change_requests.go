// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotPlanChangeRequests is the golang structure of table ad_slot_plan_change_requests for DAO operations like Where/Data.
type AdSlotPlanChangeRequests struct {
	g.Meta        `orm:"table:ad_slot_plan_change_requests, do:true"`
	Id            interface{} //
	PlanId        interface{} // 投放计划ID
	ApplicantId   interface{} // 申请人ID
	ApplicantName interface{} // 申请人姓名
	OldPrice      interface{} // 原价格
	NewPrice      interface{} // 新价格
	OldStartDate  *gtime.Time // 原开始时间
	NewStartDate  *gtime.Time // 新开始时间
	OldEndDate    *gtime.Time // 原结束时间
	NewEndDate    *gtime.Time // 新结束时间
	Reason        interface{} // 申请原因
	Status        interface{} // 审核状态：pending-待审核，approved-已通过，rejected-已拒绝
	AuditorId     interface{} // 审核人ID
	AuditorName   interface{} // 审核人姓名
	AuditAt       *gtime.Time // 审核时间
	RejectReason  interface{} // 拒绝原因
	CreatedAt     *gtime.Time //
	UpdatedAt     *gtime.Time //
	DeletedAt     *gtime.Time //
}
