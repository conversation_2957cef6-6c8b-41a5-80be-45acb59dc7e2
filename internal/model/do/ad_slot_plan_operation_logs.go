// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotPlanOperationLogs is the golang structure of table ad_slot_plan_operation_logs for DAO operations like Where/Data.
type AdSlotPlanOperationLogs struct {
	g.Meta          `orm:"table:ad_slot_plan_operation_logs, do:true"`
	Id              interface{} //
	PlanId          interface{} // 投放计划ID
	UserId          interface{} // 操作人ID
	Action          interface{} // 操作类型：create-创建计划,start-开始投放,stop-手动停止投放,auto_stop-自动停止投放,complete-完成投放
	OldStatus       interface{} // 原状态
	NewStatus       interface{} // 新状态
	Remark          interface{} // 备注
	CreatedAt       *gtime.Time //
	UpdatedAt       *gtime.Time //
	DeletedAt       *gtime.Time //
	OperationParams interface{} // 操作参数
}
