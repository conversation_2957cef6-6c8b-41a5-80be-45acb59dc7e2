// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Passwords is the golang structure of table passwords for DAO operations like Where/Data.
type Passwords struct {
	g.Meta    `orm:"table:passwords, do:true"`
	Id        interface{} // 主键ID
	CreatedAt *gtime.Time // 创建时间
	UpdatedAt *gtime.Time // 更新时间
	Pid       interface{} // 口令ID
	Name      interface{} // 口令名字
	GroupId   interface{} // 所属组ID
}
