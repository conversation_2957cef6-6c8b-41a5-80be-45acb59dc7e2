// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// UserLoginLogs is the golang structure of table user_login_logs for DAO operations like Where/Data.
type UserLoginLogs struct {
	g.Meta     `orm:"table:user_login_logs, do:true"`
	Id         interface{} //
	UserId     interface{} // 用户ID
	Ip         interface{} // 登录IP
	Country    interface{} // 国家
	Province   interface{} // 省份
	City       interface{} // 城市
	District   interface{} // 区域
	Isp        interface{} // 网络运营商
	UserAgent  interface{} // 浏览器信息
	DeviceType interface{} // 设备类型：mobile-移动端，desktop-桌面端，tablet-平板
	Os         interface{} // 操作系统
	Browser    interface{} // 浏览器
	Status     interface{} // 登录状态：success-成功，failed-失败
	Remark     interface{} // 备注
	CreatedAt  *gtime.Time //
	UpdatedAt  *gtime.Time //
}
