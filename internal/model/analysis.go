package model

// UserInfo 用户信息
type UserInfo struct {
	Id       int    `json:"id"`
	Username string `json:"username"`
	RealName string `json:"realName"`
}

// MediaInfo 媒体信息
type MediaInfo struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
}

// PlanInfo 计划信息
type PlanInfo struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
}

// ProductInfo 产品信息
type ProductInfo struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
}
