package model

import "github.com/gogf/gf/v2/frame/g"

// UserInfoReq 获取用户信息请求
type UserInfoReq struct {
	g.Meta `path:"/auth/info" method:"get" tags:"认证" summary:"获取用户信息"`
}

// UserInfoRes 获取用户信息响应
type UserInfoRes struct {
	Id       int    `json:"id"        description:"用户ID"`
	Name     string `json:"name"      description:"用户名"`
	RealName string `json:"realName"  description:"真实姓名"`
	Email    string `json:"email"     description:"邮箱"`
	Role     int    `json:"role"      description:"角色 1:媒介 2:运营 3:管理员"`
	Status   int    `json:"status"    description:"状态 1:在职 2:离职"`
}

// LogoutReq 登出请求
type LogoutReq struct {
	g.Meta `path:"/auth/logout" method:"post" tags:"认证" summary:"登出"`
}

// LogoutRes 登出响应
type LogoutRes struct{}
