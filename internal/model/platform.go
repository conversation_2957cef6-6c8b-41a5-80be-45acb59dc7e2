package model

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// PlatformPlan 平台计划实体
type PlatformPlan struct {
	Id               int    `json:"id"               description:"ID"`
	AgentId          int    `json:"agentId"          description:"代理ID"`
	MediaId          int    `json:"mediaId"          description:"媒体ID"`
	PlatformType     string `json:"platformType"     description:"平台类型"`
	DataId           string `json:"dataId"           description:"计划ID"`
	Name             string `json:"name"             description:"计划名称"`
	PrincipalAccount string `json:"principalAccount" description:"商家账户"`
	PrincipalName    string `json:"principalName"    description:"商家名称"`
	MarketTargetName string `json:"marketTargetName" description:"营销目标名称"`
	SceneName        string `json:"sceneName"        description:"投放产品名称"`
	Remark           string `json:"remark"           description:"备注"`
	CreatedAt        string `json:"createdAt"        description:"创建时间"`
	UpdatedAt        string `json:"updatedAt"        description:"更新时间"`
}

// PlatformReport 平台报表实体
type PlatformReport struct {
	Id         int    `json:"id"         description:"ID"`
	BelongId   int    `json:"belongId"   description:"关联ID"`
	BelongType string `json:"belongType" description:"关联类型"`
	BizDate    string `json:"bizDate"    description:"数据日期"`
	Impression int64  `json:"impression" description:"展现量"`
	Click      int64  `json:"click"      description:"点击量"`
	Cost       int64  `json:"cost"       description:"消费金额(分)"`
	CreatedAt  string `json:"createdAt"  description:"创建时间"`
	UpdatedAt  string `json:"updatedAt"  description:"更新时间"`
}

// PlatformConversion 平台转化数据实体
type PlatformConversion struct {
	Id                 int    `json:"id"                description:"ID"`
	BelongId           int    `json:"belongId"          description:"关联ID"`
	BelongType         string `json:"belongType"        description:"关联类型"`
	PlanId             int    `json:"planId"            description:"计划ID"`
	BizDate            string `json:"bizDate"           description:"数据日期"`
	ConversionType     string `json:"conversionType"    description:"转化事件类型"`
	ConversionTypeName string `json:"conversionTypeName" description:"转化事件类型名称"`
	ConversionResult   string `json:"conversionResult"  description:"转化事件结果"`
	CreatedAt          string `json:"createdAt"         description:"创建时间"`
	UpdatedAt          string `json:"updatedAt"         description:"更新时间"`
}

// PlatformGroup 广告组数据模型
type PlatformGroup struct {
	Id               int         `json:"id"                orm:"id,primary"        description:"主键"`
	AgentId          int         `json:"agent_id"          orm:"agent_id"          description:"代理ID"`
	MediaId          int         `json:"media_id"          orm:"media_id"          description:"媒体ID"`
	PlatformType     string      `json:"platform_type"     orm:"platform_type"     description:"平台类型"`
	DataId           string      `json:"data_id"           orm:"data_id"           description:"数据ID"`
	Name             string      `json:"name"              orm:"name"              description:"名称"`
	PlanId           string      `json:"plan_id"           orm:"plan_id"           description:"计划ID"`
	PrincipalAccount string      `json:"principal_account" orm:"principal_account" description:"负责人账号"`
	PrincipalName    string      `json:"principal_name"    orm:"principal_name"    description:"负责人名称"`
	MarketTargetName string      `json:"market_target_name" orm:"market_target_name" description:"营销目标名称"`
	SceneName        string      `json:"scene_name"        orm:"scene_name"        description:"场景名称"`
	CreatedAt        *gtime.Time `json:"created_at"        orm:"created_at"        description:"创建时间"`
	UpdatedAt        *gtime.Time `json:"updated_at"        orm:"updated_at"        description:"更新时间"`
}

// PlatformCreative 广告创意数据模型
type PlatformCreative struct {
	Id               int         `json:"id"                orm:"id,primary"        description:"主键"`
	AgentId          int         `json:"agent_id"          orm:"agent_id"          description:"代理ID"`
	MediaId          int         `json:"media_id"          orm:"media_id"          description:"媒体ID"`
	PlatformType     string      `json:"platform_type"     orm:"platform_type"     description:"平台类型"`
	DataId           string      `json:"data_id"           orm:"data_id"           description:"数据ID"`
	Name             string      `json:"name"              orm:"name"              description:"名称"`
	PlanId           string      `json:"plan_id"           orm:"plan_id"           description:"计划ID"`
	GroupId          string      `json:"group_id"          orm:"group_id"          description:"组ID"`
	PrincipalAccount string      `json:"principal_account" orm:"principal_account" description:"负责人账号"`
	PrincipalName    string      `json:"principal_name"    orm:"principal_name"    description:"负责人名称"`
	MarketTargetName string      `json:"market_target_name" orm:"market_target_name" description:"营销目标名称"`
	SceneName        string      `json:"scene_name"        orm:"scene_name"        description:"场景名称"`
	CreatedAt        *gtime.Time `json:"created_at"        orm:"created_at"        description:"创建时间"`
	UpdatedAt        *gtime.Time `json:"updated_at"        orm:"updated_at"        description:"更新时间"`
}
