package model

import "github.com/gogf/gf/v2/frame/g"

// User 用户实体
type User struct {
	Id       int    `json:"id"       description:"ID"`
	Username string `json:"username" description:"用户名"`
	Password string `json:"-"        description:"密码"`
	Email    string `json:"email"    description:"邮箱"`
	RealName string `json:"realName" description:"真实姓名"`
	Avatar   string `json:"avatar"   description:"头像"`
	Role     int    `json:"role"     description:"角色 1:媒介 2:运营 3:管理员 4:财务"`
	Status   int    `json:"status"   description:"状态 0:禁用 1:正常"`
}

// IsAdmin 是否是管理员
func (u *User) IsAdmin() bool {
	return u.Role == 3
}

// IsOperator 是否是运营
func (u *User) IsOperator() bool {
	return u.Role == 2
}

// IsFinance 是否是财务
func (u *User) IsFinance() bool {
	return u.Role == 4
}

// IsMedia 是否是媒介
func (u *User) IsMedia() bool {
	return u.Role == 1
}

// IsActive 是否是正常状态
func (u *User) IsActive() bool {
	return u.Status == 1
}

// MediaUserListReq 获取媒介用户列表请求
type MediaUserListReq struct {
	g.Meta `path:"/media-users" method:"get" tags:"用户管理" summary:"获取媒介用户列表"`
}

// MediaUserListRes 获取媒介用户列表响应
type MediaUserListRes struct {
	List []*MediaUser `json:"list" description:"媒介用户列表"`
}

// MediaUser 媒介用户信息
type MediaUser struct {
	Id       int    `json:"id"       description:"ID"`
	Username string `json:"username" description:"用户名"`
	RealName string `json:"realName" description:"真实姓名"`
}

// 用户角色
const (
	UserRoleAdmin  = 1 // 管理员
	UserRoleAgent  = 2 // 代理
	UserRoleMedia  = 3 // 媒介
	UserRoleNormal = 4 // 普通用户
)
