package model

import (
	"github.com/gogf/gf/v2/frame/g"
)

// 资源位类型
const (
	SlotTypeWechatH5 = "wechat_h5" // 微信H5
	SlotTypeWechatMP = "wechat_mp" // 微信小程序
	SlotTypeAlipayMP = "alipay_mp" // 支付宝小程序
	SlotTypeAlipayH5 = "alipay_h5" // 支付宝H5
	SlotTypeOther    = "other"     // 其他
)

// Slot 资源位实体
type Slot struct {
	Id           int    `json:"id"           description:"ID"`
	UserId       int    `json:"userId"       description:"用户ID"`
	MediaId      int    `json:"mediaId"      description:"媒体ID"`
	Name         string `json:"name"         description:"资源位名称"`
	Type         string `json:"type"         description:"资源位类型"`
	TypeText     string `json:"typeText"     description:"资源位类型文本"`
	MediaName    string `json:"mediaName"    description:"媒体名称"`
	UserRealName string `json:"userRealName" description:"媒介姓名"`
	AuditStatus  string `json:"auditStatus"  description:"审核状态"`
	RejectReason string `json:"rejectReason" description:"拒绝原因"`
	Size         string `json:"size"         description:"资源位尺寸"`
	Price        int    `json:"price"        description:"底价(分)"`
	Status       int    `json:"status"       description:"状态 0:待审核 1:正常 2:禁用"`
	Remark       string `json:"remark"       description:"备注"`
	CreatedAt    string `json:"createdAt"    description:"创建时间"`
	UpdatedAt    string `json:"updatedAt"    description:"更新时间"`
}

// SlotListReq 获取资源位列表请求
type SlotListReq struct {
	g.Meta  `path:"/list" method:"get" tags:"资源位管理" summary:"获取资源位列表"`
	Name    string `json:"name"         description:"资源位名称"`
	MediaId int    `json:"media_id"      description:"媒体ID"`
	UserId  int    `json:"userId"       description:"媒介ID"`
	Type    string `json:"type"         description:"资源位类型"`
	Status  int    `json:"status"       description:"状态"`
	Page    int    `json:"page"         description:"页码"`
	Size    int    `json:"size"         description:"每页数量"`
}

// SlotListRes 获取资源位列表响应
type SlotListRes struct {
	List  []*Slot `json:"list"  description:"列表"`
	Total int     `json:"total" description:"总数"`
	Page  int     `json:"page"  description:"页码"`
	Size  int     `json:"size"  description:"每页数量"`
}

// SlotDetailReq 获取资源位详情请求
type SlotDetailReq struct {
	g.Meta `path:"/detail" method:"get" tags:"资源位管理" summary:"获取资源位详情"`
	Id     int `json:"id" v:"required#请输入资源位ID" description:"资源位ID"`
}

// SlotDetailRes 获取资源位详情响应
type SlotDetailRes struct {
	*Slot
}

// SlotCreateReq 创建资源位请求
type SlotCreateReq struct {
	g.Meta  `path:"/create" method:"post" tags:"资源位管理" summary:"创建资源位"`
	UserId  int    `json:"userId"  description:"用户ID"`
	MediaId int    `json:"mediaId" v:"required#请选择媒体" description:"媒体ID"`
	Name    string `json:"name"    v:"required#请输入资源位名称" description:"资源位名称"`
	Type    string `json:"type"    v:"required#请选择资源位类型" description:"资源位类型"`
	Remark  string `json:"remark"  description:"备注"`
}

// SlotCreateRes 创建资源位响应
type SlotCreateRes struct {
	Id int `json:"id" description:"资源位ID"`
}

// SlotUpdateReq 更新资源位请求
type SlotUpdateReq struct {
	g.Meta  `path:"/update" method:"put" tags:"资源位管理" summary:"更新资源位"`
	Id      int    `json:"id"      v:"required#请输入资源位ID" description:"资源位ID"`
	MediaId int    `json:"mediaId" v:"required#请选择媒体" description:"媒体ID"`
	Name    string `json:"name"    v:"required#请输入资源位名称" description:"资源位名称"`
	Type    string `json:"type"    v:"required#请选择资源位类型" description:"资源位类型"`
	Remark  string `json:"remark"  description:"备注"`
}

// SlotUpdateRes 更新资源位响应
type SlotUpdateRes struct{}

// SlotDeleteReq 删除资源位请求
type SlotDeleteReq struct {
	g.Meta `path:"/delete" method:"delete" tags:"资源位管理" summary:"删除资源位"`
	Id     int `json:"id" v:"required#请输入资源位ID" description:"资源位ID"`
}

// SlotDeleteRes 删除资源位响应
type SlotDeleteRes struct{}

// SlotAuditReq 审核资源位请求
type SlotAuditReq struct {
	g.Meta       `path:"/audit" method:"put" tags:"资源位管理" summary:"审核资源位"`
	Id           int    `json:"id"           v:"required#请输入资源位ID" description:"资源位ID"`
	AuditStatus  string `json:"auditStatus"  v:"required#请选择审核状态" description:"审核状态 approved:通过 rejected:拒绝"`
	RejectReason string `json:"rejectReason" description:"拒绝原因"`
	Remark       string `json:"remark"       description:"备注"`
}

// SlotAuditRes 审核资源位响应
type SlotAuditRes struct{}
