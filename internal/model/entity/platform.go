package entity

import "github.com/gogf/gf/v2/os/gtime"

// Plan 广告计划实体
type Plan struct {
	Id               uint64      // 自增主键
	AgentId          uint64      // 代理id
	MediaId          uint64      // 媒体id
	PlatformType     string      // 平台类型
	Remark           string      // 备注
	DataId           string      // 计划ID
	Name             string      // 计划名称
	PrincipalAccount string      // 商家账户
	PrincipalName    string      // 商家名称
	MarketTargetName string      // 营销目标名称
	SceneName        string      // 投放产品名称
	CreatedAt        *gtime.Time // 创建时间
	UpdatedAt        *gtime.Time // 更新时间
}

// Group 广告单元实体
type Group struct {
	Id           uint64      // 自增主键
	PlatformType string      // 平台类型
	AgentId      uint64      // 代理id
	MediaId      uint64      // 媒体id
	DataId       string      // 单元ID
	PlanId       uint64      // 关联的计划ID
	Name         string      // 单元名称
	Remark       string      // 备注
	CreatedAt    *gtime.Time // 创建时间
	UpdatedAt    *gtime.Time // 更新时间
}

// Creative 广告创意实体
type Creative struct {
	Id           uint64      // 自增主键
	PlatformType string      // 平台类型
	AgentId      uint64      // 代理id
	MediaId      uint64      // 媒体id
	DataId       string      // 创意ID
	PlanId       uint64      // 关联的计划ID
	GroupId      uint64      // 关联的单元ID
	Name         string      // 创意名称
	Remark       string      // 备注
	CreatedAt    *gtime.Time // 创建时间
	UpdatedAt    *gtime.Time // 更新时间
}

// Conversion 转化数据实体
type Conversion struct {
	Id                 uint64      // 自增主键
	BelongId           uint64      // 计划、创意、单元表主键
	BelongType         string      // plan/conversion/group
	PlanId             uint64      // 关联的计划ID
	BizDate            string      // 数据汇总时间
	ConversionType     string      // 转化事件类型
	ConversionTypeName string      // 转化事件类型名称
	ConversionResult   string      // 转化事件结果
	CreatedAt          *gtime.Time // 创建时间
	UpdatedAt          *gtime.Time // 更新时间
}

// Report 报表数据实体
type Report struct {
	Id         uint64      // 自增主键
	BelongId   uint64      // 计划、创意、单元表主键
	BelongType string      // plan/conversion/group
	BizDate    string      // 数据汇总时间
	Impression uint64      // 展现量
	Click      uint64      // 点击量
	Cost       uint64      // 消费金额(分)
	CreatedAt  *gtime.Time // 创建时间
	UpdatedAt  *gtime.Time // 更新时间
}

// BelongType 归属类型常量
const (
	BelongTypePlan     = "plan"
	BelongTypeGroup    = "group"
	BelongTypeCreative = "creative"
)
