// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// UsersCopy1 is the golang structure for table users_copy1.
type UsersCopy1 struct {
	Id              uint64      `json:"id"              orm:"id"                description:""`                   //
	Name            string      `json:"name"            orm:"name"              description:""`                   //
	RealName        string      `json:"realName"        orm:"real_name"         description:"用户姓名"`               // 用户姓名
	Email           string      `json:"email"           orm:"email"             description:""`                   //
	EmailVerifiedAt *gtime.Time `json:"emailVerifiedAt" orm:"email_verified_at" description:""`                   //
	Password        string      `json:"password"        orm:"password"          description:""`                   //
	Role            int         `json:"role"            orm:"role"              description:"角色 1-媒介 2-运营 3-管理层"` // 角色 1-媒介 2-运营 3-管理层
	RememberToken   string      `json:"rememberToken"   orm:"remember_token"    description:""`                   //
	CreatedAt       *gtime.Time `json:"createdAt"       orm:"created_at"        description:""`                   //
	UpdatedAt       *gtime.Time `json:"updatedAt"       orm:"updated_at"        description:""`                   //
	Status          int         `json:"status"          orm:"status"            description:"状态：1在职 2离职"`         // 状态：1在职 2离职
	LastLoginAt     *gtime.Time `json:"lastLoginAt"     orm:"last_login_at"     description:"最近登录时间"`             // 最近登录时间
	LastLoginIp     string      `json:"lastLoginIp"     orm:"last_login_ip"     description:"最近登录IP"`             // 最近登录IP
	DingtalkUserid  string      `json:"dingtalkUserid"  orm:"dingtalk_userid"   description:"钉钉用户ID"`             // 钉钉用户ID
}
