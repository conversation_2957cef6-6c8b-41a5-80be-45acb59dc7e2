// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// PlatformConversions is the golang structure for table platform_conversions.
type PlatformConversions struct {
	Id                 uint64      `json:"id"                 orm:"id"                   description:"自增主键"`                  // 自增主键
	BelongId           uint64      `json:"belongId"           orm:"belong_id"            description:"计划、创意、单元表主键"`           // 计划、创意、单元表主键
	BelongType         string      `json:"belongType"         orm:"belong_type"          description:"plan/conversion/group"` // plan/conversion/group
	PlanId             uint64      `json:"planId"             orm:"plan_id"              description:"关联的计划ID"`               // 关联的计划ID
	BizDate            string      `json:"bizDate"            orm:"biz_date"             description:"数据汇总时间"`                // 数据汇总时间
	ConversionType     string      `json:"conversionType"     orm:"conversion_type"      description:"转化事件类型"`                // 转化事件类型
	ConversionTypeName string      `json:"conversionTypeName" orm:"conversion_type_name" description:"转化事件类型名称"`              // 转化事件类型名称
	ConversionResult   string      `json:"conversionResult"   orm:"conversion_result"    description:"转化事件结果"`                // 转化事件结果
	CreatedAt          *gtime.Time `json:"createdAt"          orm:"created_at"           description:"创建时间"`                  // 创建时间
	UpdatedAt          *gtime.Time `json:"updatedAt"          orm:"updated_at"           description:"更新时间"`                  // 更新时间
}
