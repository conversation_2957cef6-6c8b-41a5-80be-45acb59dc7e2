// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// PromotionZones is the golang structure for table promotion_zones.
type PromotionZones struct {
	Id              uint64      `json:"id"              orm:"id"               description:""`                        //
	PlanId          uint64      `json:"planId"          orm:"plan_id"          description:"投放计划ID"`                  // 投放计划ID
	AdProductId     uint64      `json:"adProductId"     orm:"ad_product_id"    description:"产品ID"`                    // 产品ID
	ZoneId          string      `json:"zoneId"          orm:"zone_id"          description:"推广位ID"`                   // 推广位ID
	ZoneName        string      `json:"zoneName"        orm:"zone_name"        description:"推广位名称"`                   // 推广位名称
	Pid             string      `json:"pid"             orm:"pid"              description:"PID"`                     // PID
	ZoneType        string      `json:"zoneType"        orm:"zone_type"        description:"推广位类型：ele-饿了么，fliggy-飞猪"` // 推广位类型：ele-饿了么，fliggy-飞猪
	PromotionLink   string      `json:"promotionLink"   orm:"promotion_link"   description:"推广链接"`                    // 推广链接
	PromotionQrcode string      `json:"promotionQrcode" orm:"promotion_qrcode" description:"推广二维码"`                   // 推广二维码
	ZoneParams      string      `json:"zoneParams"      orm:"zone_params"      description:"推广位参数"`                   // 推广位参数
	PromotionParams string      `json:"promotionParams" orm:"promotion_params" description:"推广参数"`                    // 推广参数
	MergedPageUrl   string      `json:"mergedPageUrl"   orm:"merged_page_url"  description:"融合页面URL"`                 // 融合页面URL
	MergeParams     string      `json:"mergeParams"     orm:"merge_params"     description:"融合参数"`                    // 融合参数
	CreatedBy       uint64      `json:"createdBy"       orm:"created_by"       description:"创建人ID"`                   // 创建人ID
	UpdatedBy       uint64      `json:"updatedBy"       orm:"updated_by"       description:"更新人ID"`                   // 更新人ID
	CreatedAt       *gtime.Time `json:"createdAt"       orm:"created_at"       description:""`                        //
	UpdatedAt       *gtime.Time `json:"updatedAt"       orm:"updated_at"       description:""`                        //
	DeletedAt       *gtime.Time `json:"deletedAt"       orm:"deleted_at"       description:""`                        //
}
