// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdCreatives is the golang structure for table ad_creatives.
type AdCreatives struct {
	Id              uint64      `json:"id"              orm:"id"               description:"主键ID"`       // 主键ID
	Name            string      `json:"name"            orm:"name"             description:"创意名称"`       // 创意名称
	ImageUrl        string      `json:"imageUrl"        orm:"image_url"        description:"素材图片地址"`     // 素材图片地址
	ImageArea       string      `json:"imageArea"       orm:"image_area"       description:"图片区域宽度"`     // 图片区域宽度
	HotAreas        string      `json:"hotAreas"        orm:"hot_areas"        description:"热区，JSON字符串"` // 热区，JSON字符串
	CreatedAt       *gtime.Time `json:"createdAt"       orm:"created_at"       description:"创建时间"`       // 创建时间
	UpdatedAt       *gtime.Time `json:"updatedAt"       orm:"updated_at"       description:"更新时间"`       // 更新时间
	BackgroundColor string      `json:"backgroundColor" orm:"background_color" description:""`           //
}
