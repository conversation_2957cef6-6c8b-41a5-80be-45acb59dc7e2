// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Passwords is the golang structure for table passwords.
type Passwords struct {
	Id        int         `json:"id"        orm:"id"         description:"主键ID"`  // 主键ID
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`  // 创建时间
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:"更新时间"`  // 更新时间
	Pid       string      `json:"pid"       orm:"pid"        description:"口令ID"`  // 口令ID
	Name      string      `json:"name"      orm:"name"       description:"口令名字"`  // 口令名字
	GroupId   int         `json:"groupId"   orm:"group_id"   description:"所属组ID"` // 所属组ID
}
