package entity

import (
	"time"
)

// PlatformCreative 广告创意实体
type PlatformCreative struct {
	Id          uint64    `json:"id"`          // ID
	Name        string    `json:"name"`        // 名称
	GroupId     int64     `json:"group_id"`    // 广告组ID
	Title       string    `json:"title"`       // 创意标题
	Description string    `json:"description"` // 创意描述
	ImageUrl    string    `json:"image_url"`   // 图片URL
	VideoUrl    string    `json:"video_url"`   // 视频URL
	LandingUrl  string    `json:"landing_url"` // 落地页URL
	Status      int       `json:"status"`      // 状态
	CreatedAt   time.Time `json:"created_at"`  // 创建时间
	UpdatedAt   time.Time `json:"updated_at"`  // 更新时间
	DeletedAt   time.Time `json:"deleted_at"`  // 删除时间
}
