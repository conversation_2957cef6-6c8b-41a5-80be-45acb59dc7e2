// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// TaobaoPids is the golang structure for table taobao_pids.
type TaobaoPids struct {
	Id           uint64      `json:"id"           orm:"id"              description:""`                //
	AccountName  string      `json:"accountName"  orm:"account_name"    description:"联盟账号"`            // 联盟账号
	MemberId     uint64      `json:"memberId"     orm:"member_id"       description:"会员ID"`            // 会员ID
	ZoneName     string      `json:"zoneName"     orm:"zone_name"       description:"推广位名称"`           // 推广位名称
	Pid          string      `json:"pid"          orm:"pid"             description:"推广位ID"`           // 推广位ID
	IsUsed       int         `json:"isUsed"       orm:"is_used"         description:"是否使用"`            // 是否使用
	UsedAt       *gtime.Time `json:"usedAt"       orm:"used_at"         description:"使用时间"`            // 使用时间
	UsedBy       uint64      `json:"usedBy"       orm:"used_by"         description:"使用者ID"`           // 使用者ID
	AdSlotPlanId uint64      `json:"adSlotPlanId" orm:"ad_slot_plan_id" description:"关联计划ID"`          // 关联计划ID
	AdCreativeId uint64      `json:"adCreativeId" orm:"ad_creative_id"  description:"关联创意ID"`          // 关联创意ID
	ActType      uint        `json:"actType"      orm:"act_type"        description:"活动类型:1-飞猪 2-福利购"` // 活动类型:1-飞猪 2-福利购
	CreatedAt    *gtime.Time `json:"createdAt"    orm:"created_at"      description:""`                //
	UpdatedAt    *gtime.Time `json:"updatedAt"    orm:"updated_at"      description:""`                //
	DeletedAt    *gtime.Time `json:"deletedAt"    orm:"deleted_at"      description:""`                //
}
