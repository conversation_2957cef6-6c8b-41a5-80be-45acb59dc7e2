// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// PlatformReports is the golang structure for table platform_reports.
type PlatformReports struct {
	Id         uint64      `json:"id"         orm:"id"          description:"自增主键"`                  // 自增主键
	BelongId   uint64      `json:"belongId"   orm:"belong_id"   description:"计划、创意、单元表主键"`           // 计划、创意、单元表主键
	BelongType string      `json:"belongType" orm:"belong_type" description:"plan/conversion/group"` // plan/conversion/group
	BizDate    string      `json:"bizDate"    orm:"biz_date"    description:"数据汇总时间"`                // 数据汇总时间
	Impression int64       `json:"impression" orm:"impression"  description:"展现量"`                   // 展现量
	Click      int64       `json:"click"      orm:"click"       description:"点击量"`                   // 点击量
	Cost       int64       `json:"cost"       orm:"cost"        description:"消费金额(分)"`               // 消费金额(分)
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at"  description:"创建时间"`                  // 创建时间
	UpdatedAt  *gtime.Time `json:"updatedAt"  orm:"updated_at"  description:"更新时间"`                  // 更新时间
}
