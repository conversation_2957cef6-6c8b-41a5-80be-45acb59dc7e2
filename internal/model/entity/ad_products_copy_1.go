// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdProductsCopy1 is the golang structure for table ad_products_copy1.
type AdProductsCopy1 struct {
	Id          uint64      `json:"id"          orm:"id"          description:""`       //
	Name        string      `json:"name"        orm:"name"        description:"广告产品名称"` // 广告产品名称
	Image       string      `json:"image"       orm:"image"       description:"广告产品图片"` // 广告产品图片
	Description string      `json:"description" orm:"description" description:"广告产品描述"` // 广告产品描述
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"  description:""`       //
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"  description:""`       //
}
