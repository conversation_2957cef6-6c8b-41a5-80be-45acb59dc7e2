// =================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Model is the golang structure for table model.
type Model struct {
	Id        uint        `json:"id"               description:"主键ID"`
	Name      string      `json:"name"             description:"模型名称"`
	CreatedAt *gtime.Time `json:"created_at"       description:"创建时间"`
	UpdatedAt *gtime.Time `json:"updated_at"       description:"更新时间"`
}

// ModelDecay is the golang structure for table model_decay.
type ModelDecay struct {
	Id        uint        `json:"id"         description:"主键ID"`
	ModelId   uint        `json:"model_id"   description:"模型ID"`
	Percent   float64     `json:"percent"    description:"衰减百分比"`
	CreatedAt *gtime.Time `json:"created_at" description:"创建时间"`
	UpdatedAt *gtime.Time `json:"updated_at" description:"更新时间"`
} 