// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// UserLoginLogs is the golang structure for table user_login_logs.
type UserLoginLogs struct {
	Id         uint64      `json:"id"         orm:"id"          description:""`                                      //
	UserId     uint64      `json:"userId"     orm:"user_id"     description:"用户ID"`                                  // 用户ID
	Ip         string      `json:"ip"         orm:"ip"          description:"登录IP"`                                  // 登录IP
	Country    string      `json:"country"    orm:"country"     description:"国家"`                                    // 国家
	Province   string      `json:"province"   orm:"province"    description:"省份"`                                    // 省份
	City       string      `json:"city"       orm:"city"        description:"城市"`                                    // 城市
	District   string      `json:"district"   orm:"district"    description:"区域"`                                    // 区域
	Isp        string      `json:"isp"        orm:"isp"         description:"网络运营商"`                                 // 网络运营商
	UserAgent  string      `json:"userAgent"  orm:"user_agent"  description:"浏览器信息"`                                 // 浏览器信息
	DeviceType string      `json:"deviceType" orm:"device_type" description:"设备类型：mobile-移动端，desktop-桌面端，tablet-平板"` // 设备类型：mobile-移动端，desktop-桌面端，tablet-平板
	Os         string      `json:"os"         orm:"os"          description:"操作系统"`                                  // 操作系统
	Browser    string      `json:"browser"    orm:"browser"     description:"浏览器"`                                   // 浏览器
	Status     string      `json:"status"     orm:"status"      description:"登录状态：success-成功，failed-失败"`             // 登录状态：success-成功，failed-失败
	Remark     string      `json:"remark"     orm:"remark"      description:"备注"`                                    // 备注
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at"  description:""`                                      //
	UpdatedAt  *gtime.Time `json:"updatedAt"  orm:"updated_at"  description:""`                                      //
}
