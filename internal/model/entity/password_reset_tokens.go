// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// PasswordResetTokens is the golang structure for table password_reset_tokens.
type PasswordResetTokens struct {
	Email     string      `json:"email"     orm:"email"      description:""` //
	Token     string      `json:"token"     orm:"token"      description:""` //
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:""` //
}
