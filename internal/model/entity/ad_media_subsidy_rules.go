// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdMediaSubsidyRules is the golang structure for table ad_media_subsidy_rules.
type AdMediaSubsidyRules struct {
	Id              uint64      `json:"id"              orm:"id"               description:""`             //
	Level           string      `json:"level"           orm:"level"            description:"等级:A,B,C,D,E"` // 等级:A,B,C,D,E
	MediaType       string      `json:"mediaType"       orm:"media_type"       description:"媒体类型"`         // 媒体类型
	CooperationType string      `json:"cooperationType" orm:"cooperation_type" description:"合作类型"`         // 合作类型
	OrdersFrom      int         `json:"ordersFrom"      orm:"orders_from"      description:"订单数起始值"`       // 订单数起始值
	OrdersTo        int         `json:"ordersTo"        orm:"orders_to"        description:"订单数结束值"`       // 订单数结束值
	SubsidyRate     float64     `json:"subsidyRate"     orm:"subsidy_rate"     description:"补贴比例"`         // 补贴比例
	CreatedAt       *gtime.Time `json:"createdAt"       orm:"created_at"       description:""`             //
	UpdatedAt       *gtime.Time `json:"updatedAt"       orm:"updated_at"       description:""`             //
	DeletedAt       *gtime.Time `json:"deletedAt"       orm:"deleted_at"       description:""`             //
}
