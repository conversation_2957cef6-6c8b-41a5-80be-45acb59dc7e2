// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdMedia is the golang structure for table ad_media.
type AdMedia struct {
	Id                uint64      `json:"id"                orm:"id"                 description:""`                                      //
	Code              string      `json:"code"              orm:"code"               description:""`                                      //
	AdAgentId         uint64      `json:"adAgentId"         orm:"ad_agent_id"        description:""`                                      //
	Name              string      `json:"name"              orm:"name"               description:""`                                      //
	AuditStatus       string      `json:"auditStatus"       orm:"audit_status"       description:""`                                      //
	CooperationStatus string      `json:"cooperationStatus" orm:"cooperation_status" description:""`                                      //
	CooperationType   string      `json:"cooperationType"   orm:"cooperation_type"   description:""`                                      //
	Account           string      `json:"account"           orm:"account"            description:""`                                      //
	Password          string      `json:"password"          orm:"password"           description:""`                                      //
	LastLoginAt       string      `json:"lastLoginAt"       orm:"last_login_at"      description:""`                                      //
	Balance           float64     `json:"balance"           orm:"balance"            description:""`                                      //
	Types             string      `json:"types"             orm:"types"              description:""`                                      //
	Industry          string      `json:"industry"          orm:"industry"           description:""`                                      //
	CustomIndustry    string      `json:"customIndustry"    orm:"custom_industry"    description:""`                                      //
	DailyActivity     string      `json:"dailyActivity"     orm:"daily_activity"     description:""`                                      //
	TransactionVolume string      `json:"transactionVolume" orm:"transaction_volume" description:""`                                      //
	RegionCodes       string      `json:"regionCodes"       orm:"region_codes"       description:""`                                      //
	CompanyName       string      `json:"companyName"       orm:"company_name"       description:""`                                      //
	CompanyAddress    string      `json:"companyAddress"    orm:"company_address"    description:""`                                      //
	ContactName       string      `json:"contactName"       orm:"contact_name"       description:""`                                      //
	ContactPhone      string      `json:"contactPhone"      orm:"contact_phone"      description:""`                                      //
	RejectReason      string      `json:"rejectReason"      orm:"reject_reason"      description:""`                                      //
	UserId            uint64      `json:"userId"            orm:"user_id"            description:""`                                      //
	Remark            string      `json:"remark"            orm:"remark"             description:""`                                      //
	CreatedBy         uint64      `json:"createdBy"         orm:"created_by"         description:""`                                      //
	UpdatedBy         uint64      `json:"updatedBy"         orm:"updated_by"         description:""`                                      //
	CreatedAt         *gtime.Time `json:"createdAt"         orm:"created_at"         description:""`                                      //
	UpdatedAt         *gtime.Time `json:"updatedAt"         orm:"updated_at"         description:""`                                      //
	DeletedAt         *gtime.Time `json:"deletedAt"         orm:"deleted_at"         description:""`                                      //
	InstanceId        string      `json:"instanceId"        orm:"instance_id"        description:""`                                      //
	PlatformConfig    string      `json:"platformConfig"    orm:"platform_config"    description:"平台配置 仅当 cooperationType=delivery 时可配置"` // 平台配置 仅当 cooperationType=delivery 时可配置
}
