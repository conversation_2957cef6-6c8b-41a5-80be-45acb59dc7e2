// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdMediaWithdraws is the golang structure for table ad_media_withdraws.
type AdMediaWithdraws struct {
	Id           uint64      `json:"id"           orm:"id"            description:""`                                                  //
	MediaId      uint64      `json:"mediaId"      orm:"media_id"      description:"媒体ID"`                                              // 媒体ID
	Amount       float64     `json:"amount"       orm:"amount"        description:"提现金额"`                                              // 提现金额
	TaxAmount    float64     `json:"taxAmount"    orm:"tax_amount"    description:"税费金额"`                                              // 税费金额
	ActualAmount float64     `json:"actualAmount" orm:"actual_amount" description:"实际打款金额"`                                            // 实际打款金额
	WithdrawType string      `json:"withdrawType" orm:"withdraw_type" description:"提现方式:alipay-支付宝,bank-对公转账"`                         // 提现方式:alipay-支付宝,bank-对公转账
	AccountInfo  string      `json:"accountInfo"  orm:"account_info"  description:"账户信息"`                                              // 账户信息
	InvoiceFile  string      `json:"invoiceFile"  orm:"invoice_file"  description:"发票文件"`                                              // 发票文件
	Status       string      `json:"status"       orm:"status"        description:"状态:pending-待审核,approved-已审核,rejected-已拒绝,paid-已打款"` // 状态:pending-待审核,approved-已审核,rejected-已拒绝,paid-已打款
	AuditTime    *gtime.Time `json:"auditTime"    orm:"audit_time"    description:"审核时间"`                                              // 审核时间
	AuditUserId  uint64      `json:"auditUserId"  orm:"audit_user_id" description:"审核人ID"`                                             // 审核人ID
	PayTime      *gtime.Time `json:"payTime"      orm:"pay_time"      description:"打款时间"`                                              // 打款时间
	PayUserId    uint64      `json:"payUserId"    orm:"pay_user_id"   description:"打款操作人ID"`                                           // 打款操作人ID
	Remark       string      `json:"remark"       orm:"remark"        description:"备注"`                                                // 备注
	CreatedAt    *gtime.Time `json:"createdAt"    orm:"created_at"    description:""`                                                  //
	UpdatedAt    *gtime.Time `json:"updatedAt"    orm:"updated_at"    description:""`                                                  //
	DeletedAt    *gtime.Time `json:"deletedAt"    orm:"deleted_at"    description:""`                                                  //
}
