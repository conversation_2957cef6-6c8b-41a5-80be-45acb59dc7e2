// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// TaobaoOrderSyncs is the golang structure for table taobao_order_syncs.
type TaobaoOrderSyncs struct {
	Id           uint64      `json:"id"           orm:"id"            description:""` //
	StartTime    *gtime.Time `json:"startTime"    orm:"start_time"    description:""` //
	EndTime      *gtime.Time `json:"endTime"      orm:"end_time"      description:""` //
	Pid          string      `json:"pid"          orm:"pid"           description:""` //
	Status       string      `json:"status"       orm:"status"        description:""` //
	TotalCount   int         `json:"totalCount"   orm:"total_count"   description:""` //
	SuccessCount int         `json:"successCount" orm:"success_count" description:""` //
	FailedCount  int         `json:"failedCount"  orm:"failed_count"  description:""` //
	ErrorMessage string      `json:"errorMessage" orm:"error_message" description:""` //
	CompletedAt  *gtime.Time `json:"completedAt"  orm:"completed_at"  description:""` //
	CreatedAt    *gtime.Time `json:"createdAt"    orm:"created_at"    description:""` //
	UpdatedAt    *gtime.Time `json:"updatedAt"    orm:"updated_at"    description:""` //
}
