// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Holidays is the golang structure for table holidays.
type Holidays struct {
	Id        uint64      `json:"id"        orm:"id"         description:""`        //
	Date      *gtime.Time `json:"date"      orm:"date"       description:"节假日日期"`   // 节假日日期
	Name      string      `json:"name"      orm:"name"       description:"节假日名称"`   // 节假日名称
	IsWorkday int         `json:"isWorkday" orm:"is_workday" description:"是否调休工作日"` // 是否调休工作日
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:""`        //
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:""`        //
}
