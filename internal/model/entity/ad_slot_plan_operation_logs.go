// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotPlanOperationLogs is the golang structure for table ad_slot_plan_operation_logs.
type AdSlotPlanOperationLogs struct {
	Id              uint64      `json:"id"              orm:"id"               description:""`                                                                       //
	PlanId          uint64      `json:"planId"          orm:"plan_id"          description:"投放计划ID"`                                                                 // 投放计划ID
	UserId          uint64      `json:"userId"          orm:"user_id"          description:"操作人ID"`                                                                  // 操作人ID
	Action          string      `json:"action"          orm:"action"           description:"操作类型：create-创建计划,start-开始投放,stop-手动停止投放,auto_stop-自动停止投放,complete-完成投放"` // 操作类型：create-创建计划,start-开始投放,stop-手动停止投放,auto_stop-自动停止投放,complete-完成投放
	OldStatus       string      `json:"oldStatus"       orm:"old_status"       description:"原状态"`                                                                    // 原状态
	NewStatus       string      `json:"newStatus"       orm:"new_status"       description:"新状态"`                                                                    // 新状态
	Remark          string      `json:"remark"          orm:"remark"           description:"备注"`                                                                     // 备注
	CreatedAt       *gtime.Time `json:"createdAt"       orm:"created_at"       description:""`                                                                       //
	UpdatedAt       *gtime.Time `json:"updatedAt"       orm:"updated_at"       description:""`                                                                       //
	DeletedAt       *gtime.Time `json:"deletedAt"       orm:"deleted_at"       description:""`                                                                       //
	OperationParams string      `json:"operationParams" orm:"operation_params" description:"操作参数"`                                                                   // 操作参数
}
