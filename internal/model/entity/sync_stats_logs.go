// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SyncStatsLogs is the golang structure for table sync_stats_logs.
type SyncStatsLogs struct {
	Id            uint64      `json:"id"            orm:"id"             description:""`       //
	ProcedureName string      `json:"procedureName" orm:"procedure_name" description:"存储过程名称"` // 存储过程名称
	Message       string      `json:"message"       orm:"message"        description:"日志消息"`   // 日志消息
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"     description:""`       //
}
