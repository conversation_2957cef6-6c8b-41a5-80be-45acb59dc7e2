// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Regions is the golang structure for table regions.
type Regions struct {
	Id        uint64      `json:"id"        orm:"id"         description:""`                  //
	ParentId  uint64      `json:"parentId"  orm:"parent_id"  description:"父级ID"`              // 父级ID
	Name      string      `json:"name"      orm:"name"       description:"地区名称"`              // 地区名称
	Code      string      `json:"code"      orm:"code"       description:"地区编码"`              // 地区编码
	Level     int         `json:"level"     orm:"level"      description:"层级：1=省份，2=城市，3=区县"` // 层级：1=省份，2=城市，3=区县
	IsEnabled int         `json:"isEnabled" orm:"is_enabled" description:"是否启用"`              // 是否启用
	Sort      int         `json:"sort"      orm:"sort"       description:"排序"`                // 排序
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:""`                  //
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:""`                  //
}
