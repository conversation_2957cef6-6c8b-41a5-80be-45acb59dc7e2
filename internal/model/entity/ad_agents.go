// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdAgents is the golang structure for table ad_agents.
type AdAgents struct {
	Id                uint64      `json:"id"                orm:"id"                 description:""`                                                                             //
	Code              string      `json:"code"              orm:"code"               description:"代理编号"`                                                                         // 代理编号
	Name              string      `json:"name"              orm:"name"               description:"代理名称"`                                                                         // 代理名称
	UserId            uint64      `json:"userId"            orm:"user_id"            description:"用户ID"`                                                                         // 用户ID
	AuditStatus       string      `json:"auditStatus"       orm:"audit_status"       description:"审核状态：pending-审核中,approved-已通过,rejected-已拒绝"`                                   // 审核状态：pending-审核中,approved-已通过,rejected-已拒绝
	CooperationStatus string      `json:"cooperationStatus" orm:"cooperation_status" description:"合作状态：not_started-未开始,active-合作中,terminated-已终止"`                               // 合作状态：not_started-未开始,active-合作中,terminated-已终止
	CompanyName       string      `json:"companyName"       orm:"company_name"       description:"公司名称"`                                                                         // 公司名称
	CompanyAddress    string      `json:"companyAddress"    orm:"company_address"    description:"公司地址"`                                                                         // 公司地址
	ContactName       string      `json:"contactName"       orm:"contact_name"       description:"联系人"`                                                                          // 联系人
	ContactPhone      string      `json:"contactPhone"      orm:"contact_phone"      description:"联系电话"`                                                                         // 联系电话
	RejectReason      string      `json:"rejectReason"      orm:"reject_reason"      description:"拒绝原因"`                                                                         // 拒绝原因
	Remarks           string      `json:"remarks"           orm:"remarks"            description:"备注"`                                                                           // 备注
	PlatformConfig    string      `json:"platformConfig"    orm:"platform_config"    description:"平台配置 {platform: \"denghuoplus|xiaohongshu\", info: {token: \"\", pid: \"\"}}"` // 平台配置 {platform: "denghuoplus|xiaohongshu", info: {token: "", pid: ""}}
	CreatedBy         uint64      `json:"createdBy"         orm:"created_by"         description:"创建人ID"`                                                                        // 创建人ID
	UpdatedBy         uint64      `json:"updatedBy"         orm:"updated_by"         description:"更新人ID"`                                                                        // 更新人ID
	CreatedAt         *gtime.Time `json:"createdAt"         orm:"created_at"         description:""`                                                                             //
	UpdatedAt         *gtime.Time `json:"updatedAt"         orm:"updated_at"         description:""`                                                                             //
	DeletedAt         *gtime.Time `json:"deletedAt"         orm:"deleted_at"         description:""`                                                                             //
	InstanceId        string      `json:"instanceId"        orm:"instance_id"        description:"审批实例ID"`                                                                       // 审批实例ID
	Type              string      `json:"type"              orm:"type"               description:"代理类型：traffic-流量采买,delivery-投放"`                                                // 代理类型：traffic-流量采买,delivery-投放
}
