// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotPlanDailyOrderStats is the golang structure for table ad_slot_plan_daily_order_stats.
type AdSlotPlanDailyOrderStats struct {
	Id                     uint64      `json:"id"                     orm:"id"                        description:""`        //
	DailyStatId            uint64      `json:"dailyStatId"            orm:"daily_stat_id"             description:""`        //
	OrderType              string      `json:"orderType"              orm:"order_type"                description:"订单类型"`    // 订单类型
	BdOrders               int         `json:"bdOrders"               orm:"bd_orders"                 description:"媒介订单数"`   // 媒介订单数
	BdSettleOrders         int         `json:"bdSettleOrders"         orm:"bd_settle_orders"          description:"媒介结算订单数"` // 媒介结算订单数
	AdminOrders            int         `json:"adminOrders"            orm:"admin_orders"              description:"管理员订单数"`  // 管理员订单数
	AdminSettleOrders      int         `json:"adminSettleOrders"      orm:"admin_settle_orders"       description:"实际结算订单数"` // 实际结算订单数
	BdRevenue              float64     `json:"bdRevenue"              orm:"bd_revenue"                description:"媒介收入"`    // 媒介收入
	BdSettleRevenue        float64     `json:"bdSettleRevenue"        orm:"bd_settle_revenue"         description:"媒介结算收入"`  // 媒介结算收入
	BdActivityFee          float64     `json:"bdActivityFee"          orm:"bd_activity_fee"           description:"媒介激励费用"`  // 媒介激励费用
	BdSettleActivityFee    float64     `json:"bdSettleActivityFee"    orm:"bd_settle_activity_fee"    description:"媒介结算激励"`  // 媒介结算激励
	AdminRevenue           float64     `json:"adminRevenue"           orm:"admin_revenue"             description:"管理员收入"`   // 管理员收入
	AdminSettleRevenue     float64     `json:"adminSettleRevenue"     orm:"admin_settle_revenue"      description:"实际结算收入"`  // 实际结算收入
	AdminActivityFee       float64     `json:"adminActivityFee"       orm:"admin_activity_fee"        description:"实际激励费用"`  // 实际激励费用
	AdminSettleActivityFee float64     `json:"adminSettleActivityFee" orm:"admin_settle_activity_fee" description:"实际结算��励"` // 实际结算��励
	CreatedAt              *gtime.Time `json:"createdAt"              orm:"created_at"                description:""`        //
	UpdatedAt              *gtime.Time `json:"updatedAt"              orm:"updated_at"                description:""`        //
}
