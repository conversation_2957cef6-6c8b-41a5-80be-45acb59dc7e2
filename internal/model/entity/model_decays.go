// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ModelDecays is the golang structure for table model_decays.
type ModelDecays struct {
	Id        uint64      `json:"id"        orm:"id"         description:"主键ID"`  // 主键ID
	ModelId   uint64      `json:"modelId"   orm:"model_id"   description:"模型ID"`  // 模型ID
	Percent   float64     `json:"percent"   orm:"percent"    description:"衰减百分比"` // 衰减百分比
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"`  // 创建时间
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:"更新时间"`  // 更新时间
}
