// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdProducts is the golang structure for table ad_products.
type AdProducts struct {
	Id          uint64      `json:"id"          orm:"id"          description:""`                         //
	Code        string      `json:"code"        orm:"code"        description:"产品编号"`                     // 产品编号
	Name        string      `json:"name"        orm:"name"        description:"广告产品名称"`                   // 广告产品名称
	Image       string      `json:"image"       orm:"image"       description:"广告产品图片"`                   // 广告产品图片
	Description string      `json:"description" orm:"description" description:"广告产品描述"`                   // 广告产品描述
	Status      string      `json:"status"      orm:"status"      description:"状态：active-启用,inactive-停用"` // 状态：active-启用,inactive-停用
	CreatedBy   uint64      `json:"createdBy"   orm:"created_by"  description:"创建人ID"`                    // 创建人ID
	UpdatedBy   uint64      `json:"updatedBy"   orm:"updated_by"  description:"更新人ID"`                    // 更新人ID
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"  description:""`                         //
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"  description:""`                         //
	DeletedAt   *gtime.Time `json:"deletedAt"   orm:"deleted_at"  description:""`                         //
}
