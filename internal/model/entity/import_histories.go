// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ImportHistories is the golang structure for table import_histories.
type ImportHistories struct {
	Id            uint64      `json:"id"            orm:"id"             description:""` //
	UserId        uint64      `json:"userId"        orm:"user_id"        description:""` //
	Type          string      `json:"type"          orm:"type"           description:""` //
	FileName      string      `json:"fileName"      orm:"file_name"      description:""` //
	Status        string      `json:"status"        orm:"status"         description:""` //
	TotalRows     int         `json:"totalRows"     orm:"total_rows"     description:""` //
	SuccessRows   int         `json:"successRows"   orm:"success_rows"   description:""` //
	FailedRows    int         `json:"failedRows"    orm:"failed_rows"    description:""` //
	ErrorMessages string      `json:"errorMessages" orm:"error_messages" description:""` //
	CompletedAt   *gtime.Time `json:"completedAt"   orm:"completed_at"   description:""` //
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"     description:""` //
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"     description:""` //
}
