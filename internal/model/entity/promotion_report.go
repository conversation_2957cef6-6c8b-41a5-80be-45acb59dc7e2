// =================================================================================
// Code generated by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// PromotionReport is the golang structure for table promotion_report.
type PromotionReport struct {
	Id              int64       `json:"id"               description:"主键ID"`
	ReportDate      string      `json:"reportDate"       description:"报表日期 格式:20241201"`
	GroupId         int64       `json:"groupId"          description:"口令组ID"`
	CategoryId      int64       `json:"categoryId"       description:"口令组分类ID"`
	Cost            float64     `json:"cost"             description:"投放费用"`
	TotalOrders     int         `json:"totalOrders"      description:"当日总订单数"`
	TotalCommission float64     `json:"totalCommission"  description:"当日总佣金"`
	CreatedAt       *gtime.Time `json:"createdAt"        description:"创建时间"`
	UpdatedAt       *gtime.Time `json:"updatedAt"        description:"更新时间"`
} 