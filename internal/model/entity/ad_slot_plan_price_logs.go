// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotPlanPriceLogs is the golang structure for table ad_slot_plan_price_logs.
type AdSlotPlanPriceLogs struct {
	Id         uint64      `json:"id"         orm:"id"          description:""`      //
	PlanId     uint64      `json:"planId"     orm:"plan_id"     description:"计划ID"`  // 计划ID
	OldPrice   float64     `json:"oldPrice"   orm:"old_price"   description:"变动前价格"` // 变动前价格
	NewPrice   float64     `json:"newPrice"   orm:"new_price"   description:"当前价格"`  // 当前价格
	OperatorId uint64      `json:"operatorId" orm:"operator_id" description:"操作人ID"` // 操作人ID
	Remark     string      `json:"remark"     orm:"remark"      description:"变动原因"`  // 变动原因
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at"  description:""`      //
	UpdatedAt  *gtime.Time `json:"updatedAt"  orm:"updated_at"  description:""`      //
}
