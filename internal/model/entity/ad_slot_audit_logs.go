// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotAuditLogs is the golang structure for table ad_slot_audit_logs.
type AdSlotAuditLogs struct {
	Id           uint64      `json:"id"           orm:"id"            description:""`                                           //
	SlotId       uint64      `json:"slotId"       orm:"slot_id"       description:"广告位ID"`                                      // 广告位ID
	Event        string      `json:"event"        orm:"event"         description:"事件：create-创建审核,update-变更审核"`                 // 事件：create-创建审核,update-变更审核
	SubmitterId  uint64      `json:"submitterId"  orm:"submitter_id"  description:"发起人ID"`                                      // 发起人ID
	AuditStatus  string      `json:"auditStatus"  orm:"audit_status"  description:"审核状态：pending-待审核,approved-已通过,rejected-已拒绝"` // 审核状态：pending-待审核,approved-已通过,rejected-已拒绝
	AuditorId    uint64      `json:"auditorId"    orm:"auditor_id"    description:"审核人ID"`                                      // 审核人ID
	AuditAt      *gtime.Time `json:"auditAt"      orm:"audit_at"      description:"审核时间"`                                       // 审核时间
	RejectReason string      `json:"rejectReason" orm:"reject_reason" description:"拒绝理由"`                                       // 拒绝理由
	Remark       string      `json:"remark"       orm:"remark"        description:"备注"`                                         // 备注
	AuditDetail  string      `json:"auditDetail"  orm:"audit_detail"  description:"审核详情"`                                       // 审核详情
	CreatedAt    *gtime.Time `json:"createdAt"    orm:"created_at"    description:""`                                           //
	UpdatedAt    *gtime.Time `json:"updatedAt"    orm:"updated_at"    description:""`                                           //
	DeletedAt    *gtime.Time `json:"deletedAt"    orm:"deleted_at"    description:""`                                           //
}
