// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// FailedJobs is the golang structure for table failed_jobs.
type FailedJobs struct {
	Id         uint64      `json:"id"         orm:"id"         description:""` //
	Uuid       string      `json:"uuid"       orm:"uuid"       description:""` //
	Connection string      `json:"connection" orm:"connection" description:""` //
	Queue      string      `json:"queue"      orm:"queue"      description:""` //
	Payload    string      `json:"payload"    orm:"payload"    description:""` //
	Exception  string      `json:"exception"  orm:"exception"  description:""` //
	FailedAt   *gtime.Time `json:"failedAt"   orm:"failed_at"  description:""` //
}
