// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotRateLogs is the golang structure for table ad_slot_rate_logs.
type AdSlotRateLogs struct {
	Id        uint64      `json:"id"        orm:"id"         description:""`                                     //
	AdSlotId  uint64      `json:"adSlotId"  orm:"ad_slot_id" description:"广告位ID"`                                // 广告位ID
	UserId    uint64      `json:"userId"    orm:"user_id"    description:"操作人ID"`                                // 操作人ID
	Field     string      `json:"field"     orm:"field"      description:"修改字段：leak_rate-泄漏率,discount_rate-折扣率"` // 修改字段：leak_rate-泄漏率,discount_rate-折扣率
	OldValue  float64     `json:"oldValue"  orm:"old_value"  description:"原值"`                                   // 原值
	NewValue  float64     `json:"newValue"  orm:"new_value"  description:"新值"`                                   // 新值
	Remark    string      `json:"remark"    orm:"remark"     description:"修改原因"`                                 // 修改原因
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:""`                                     //
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:""`                                     //
}
