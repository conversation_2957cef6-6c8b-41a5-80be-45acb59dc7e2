// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// PlatformGroups is the golang structure for table platform_groups.
type PlatformGroups struct {
	Id           uint64      `json:"id"           orm:"id"            description:"自增主键"`    // 自增主键
	PlatformType string      `json:"platformType" orm:"platform_type" description:"平台类型"`    // 平台类型
	AgentId      uint64      `json:"agentId"      orm:"agent_id"      description:"代理id"`    // 代理id
	MediaId      uint64      `json:"mediaId"      orm:"media_id"      description:"媒体id"`    // 媒体id
	DataId       string      `json:"dataId"       orm:"data_id"       description:"单元ID"`    // 单元ID
	PlanId       uint64      `json:"planId"       orm:"plan_id"       description:"关联的计划ID"` // 关联的计划ID
	Name         string      `json:"name"         orm:"name"          description:"单元名称"`    // 单元名称
	Remark       string      `json:"remark"       orm:"remark"        description:"备注"`      // 备注
	CreatedAt    *gtime.Time `json:"createdAt"    orm:"created_at"    description:"创建时间"`    // 创建时间
	UpdatedAt    *gtime.Time `json:"updatedAt"    orm:"updated_at"    description:"更新时间"`    // 更新时间
}
