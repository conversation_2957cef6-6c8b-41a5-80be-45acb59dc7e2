// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// JobBatches is the golang structure for table job_batches.
type JobBatches struct {
	Id           string `json:"id"           orm:"id"             description:""` //
	Name         string `json:"name"         orm:"name"           description:""` //
	TotalJobs    int    `json:"totalJobs"    orm:"total_jobs"     description:""` //
	PendingJobs  int    `json:"pendingJobs"  orm:"pending_jobs"   description:""` //
	FailedJobs   int    `json:"failedJobs"   orm:"failed_jobs"    description:""` //
	FailedJobIds string `json:"failedJobIds" orm:"failed_job_ids" description:""` //
	Options      string `json:"options"      orm:"options"        description:""` //
	CancelledAt  int    `json:"cancelledAt"  orm:"cancelled_at"   description:""` //
	CreatedAt    int    `json:"createdAt"    orm:"created_at"     description:""` //
	FinishedAt   int    `json:"finishedAt"   orm:"finished_at"    description:""` //
}
