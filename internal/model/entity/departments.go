// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Departments is the golang structure for table departments.
type Departments struct {
	Id          uint64      `json:"id"          orm:"id"           description:"部门ID"`       // 部门ID
	Name        string      `json:"name"        orm:"name"         description:"部门名称"`      // 部门名称
	ParentId    *uint64     `json:"parentId"    orm:"parent_id"    description:"上级部门ID"`    // 上级部门ID
	ManagerId   *uint64     `json:"managerId"   orm:"manager_id"   description:"部门负责人ID"`   // 部门负责人ID
	Description string      `json:"description" orm:"description"  description:"部门描述"`      // 部门描述
	Status      int         `json:"status"      orm:"status"       description:"状态：1启用 0禁用"` // 状态：1启用 0禁用
	Sort        int         `json:"sort"        orm:"sort"         description:"排序"`        // 排序
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"   description:"创建时间"`      // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"   description:"更新时间"`      // 更新时间
} 