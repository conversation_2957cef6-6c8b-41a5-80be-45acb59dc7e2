package entity

import (
	"time"
)

// PlatformGroup 广告组实体
type PlatformGroup struct {
	Id           uint64    `json:"id"`            // ID
	Name         string    `json:"name"`          // 名称
	PlanId       int64     `json:"plan_id"`       // 计划ID
	Budget       float64   `json:"budget"`        // 预算
	DailyBudget  float64   `json:"daily_budget"`  // 日预算
	Status       int       `json:"status"`        // 状态
	DeliveryMode int       `json:"delivery_mode"` // 投放模式
	CreatedAt    time.Time `json:"created_at"`    // 创建时间
	UpdatedAt    time.Time `json:"updated_at"`    // 更新时间
	DeletedAt    time.Time `json:"deleted_at"`    // 删除时间
}
