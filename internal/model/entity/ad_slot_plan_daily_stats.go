// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotPlanDailyStats is the golang structure for table ad_slot_plan_daily_stats.
type AdSlotPlanDailyStats struct {
	Id                     uint64      `json:"id"                     orm:"id"                        description:""`        //
	PlanId                 uint64      `json:"planId"                 orm:"plan_id"                   description:"计划ID"`    // 计划ID
	MediaId                uint64      `json:"mediaId"                orm:"media_id"                  description:"媒体ID"`    // 媒体ID
	AdSlotId               uint64      `json:"adSlotId"               orm:"ad_slot_id"                description:"广告位ID"`   // 广告位ID
	AdProductId            uint64      `json:"adProductId"            orm:"ad_product_id"             description:"投放产品ID"`  // 投放产品ID
	UserId                 uint64      `json:"userId"                 orm:"user_id"                   description:"媒介ID"`    // 媒介ID
	Date                   *gtime.Time `json:"date"                   orm:"date"                      description:"数据日期"`    // 数据日期
	Clicks                 uint        `json:"clicks"                 orm:"clicks"                    description:"点击量"`     // 点击量
	UnitPrice              float64     `json:"unitPrice"              orm:"unit_price"                description:"单价"`      // 单价
	Cost                   float64     `json:"cost"                   orm:"cost"                      description:"成本"`      // 成本
	BdOrders               uint        `json:"bdOrders"               orm:"bd_orders"                 description:"媒介订单量"`   // 媒介订单量
	BdRevenue              float64     `json:"bdRevenue"              orm:"bd_revenue"                description:"媒介收入"`    // 媒介收入
	BdProfit               float64     `json:"bdProfit"               orm:"bd_profit"                 description:"媒介利润"`    // 媒介利润
	BdRoi                  float64     `json:"bdRoi"                  orm:"bd_roi"                    description:"媒介ROI"`   // 媒介ROI
	BdActivityFee          float64     `json:"bdActivityFee"          orm:"bd_activity_fee"           description:"媒介激励费用"`  // 媒介激励费用
	BdSettleOrders         int         `json:"bdSettleOrders"         orm:"bd_settle_orders"          description:"媒介结算订单量"` // 媒介结算订单量
	BdSettleRevenue        float64     `json:"bdSettleRevenue"        orm:"bd_settle_revenue"         description:"媒介结算收入"`  // 媒介结算收入
	BdSettleActivityFee    float64     `json:"bdSettleActivityFee"    orm:"bd_settle_activity_fee"    description:"媒介结算激励"`  // 媒介结算激励
	BdSettleProfit         float64     `json:"bdSettleProfit"         orm:"bd_settle_profit"          description:"媒介结算利润"`  // 媒介结算利润
	BdSettleRoi            float64     `json:"bdSettleRoi"            orm:"bd_settle_roi"             description:"媒介结算ROI"` // 媒介结算ROI
	AdminOrders            uint        `json:"adminOrders"            orm:"admin_orders"              description:"实际订单量"`   // 实际订单量
	AdminRevenue           float64     `json:"adminRevenue"           orm:"admin_revenue"             description:"实际收入"`    // 实际收入
	AdminProfit            float64     `json:"adminProfit"            orm:"admin_profit"              description:"实际利润"`    // 实际利润
	AdminRoi               float64     `json:"adminRoi"               orm:"admin_roi"                 description:"实际ROI"`   // 实际ROI
	AdminActivityFee       float64     `json:"adminActivityFee"       orm:"admin_activity_fee"        description:"实际激励费用"`  // 实际激励费用
	AdminSettleOrders      int         `json:"adminSettleOrders"      orm:"admin_settle_orders"       description:"实际结算订单量"` // 实际结算订单量
	AdminSettleRevenue     float64     `json:"adminSettleRevenue"     orm:"admin_settle_revenue"      description:"实际结算收入"`  // 实际结算收入
	AdminSettleActivityFee float64     `json:"adminSettleActivityFee" orm:"admin_settle_activity_fee" description:"实际结算激励"`  // 实际结算激励
	AdminSettleProfit      float64     `json:"adminSettleProfit"      orm:"admin_settle_profit"       description:"实际结算利润"`  // 实际结算利润
	AdminSettleRoi         float64     `json:"adminSettleRoi"         orm:"admin_settle_roi"          description:"实际结算ROI"` // 实际结算ROI
	OrderTypeStats         string      `json:"orderTypeStats"         orm:"order_type_stats"          description:"收益明细"`    // 收益明细
	CreatedBy              uint64      `json:"createdBy"              orm:"created_by"                description:"创建人ID"`   // 创建人ID
	UpdatedBy              uint64      `json:"updatedBy"              orm:"updated_by"                description:"更新人ID"`   // 更新人ID
	CreatedAt              *gtime.Time `json:"createdAt"              orm:"created_at"                description:""`        //
	UpdatedAt              *gtime.Time `json:"updatedAt"              orm:"updated_at"                description:""`        //
	DeletedAt              *gtime.Time `json:"deletedAt"              orm:"deleted_at"                description:""`        //
}
