// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotPlanCosts is the golang structure for table ad_slot_plan_costs.
type AdSlotPlanCosts struct {
	Id           uint64      `json:"id"           orm:"id"            description:""`                                           //
	PlanId       uint64      `json:"planId"       orm:"plan_id"       description:"投放计划ID"`                                     // 投放计划ID
	Date         *gtime.Time `json:"date"         orm:"date"          description:"统计日期"`                                       // 统计日期
	MediaId      uint64      `json:"mediaId"      orm:"media_id"      description:"媒体ID"`                                       // 媒体ID
	AdSlotId     uint64      `json:"adSlotId"     orm:"ad_slot_id"    description:"广告位ID"`                                      // 广告位ID
	AdProductId  uint64      `json:"adProductId"  orm:"ad_product_id" description:"产品ID"`                                       // 产品ID
	UserId       uint64      `json:"userId"       orm:"user_id"       description:"媒介ID"`                                       // 媒介ID
	Clicks       int         `json:"clicks"       orm:"clicks"        description:"点击量"`                                        // 点击量
	Cost         float64     `json:"cost"         orm:"cost"          description:"成本"`                                         // 成本
	Remark       string      `json:"remark"       orm:"remark"        description:"备注"`                                         // 备注
	AuditStatus  string      `json:"auditStatus"  orm:"audit_status"  description:"审核状态:pending=待审核,approved=已通过,rejected=已拒绝"` // 审核状态:pending=待审核,approved=已通过,rejected=已拒绝
	AuditBy      uint64      `json:"auditBy"      orm:"audit_by"      description:"审核人"`                                        // 审核人
	AuditAt      *gtime.Time `json:"auditAt"      orm:"audit_at"      description:"审核时间"`                                       // 审核时间
	RejectReason string      `json:"rejectReason" orm:"reject_reason" description:"拒绝原因"`                                       // 拒绝原因
	CreatedBy    uint64      `json:"createdBy"    orm:"created_by"    description:"创建人"`                                        // 创建人
	UpdatedBy    uint64      `json:"updatedBy"    orm:"updated_by"    description:"更新人"`                                        // 更新人
	CreatedAt    *gtime.Time `json:"createdAt"    orm:"created_at"    description:""`                                           //
	UpdatedAt    *gtime.Time `json:"updatedAt"    orm:"updated_at"    description:""`                                           //
	DeletedAt    *gtime.Time `json:"deletedAt"    orm:"deleted_at"    description:""`                                           //
}
