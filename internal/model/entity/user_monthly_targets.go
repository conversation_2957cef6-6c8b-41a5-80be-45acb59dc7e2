// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// UserMonthlyTargets is the golang structure for table user_monthly_targets.
type UserMonthlyTargets struct {
	Id           uint64      `json:"id"           orm:"id"            description:""` //
	UserId       uint64      `json:"userId"       orm:"user_id"       description:""` //
	Year         int         `json:"year"         orm:"year"          description:""` //
	Month        int         `json:"month"        orm:"month"         description:""` //
	ProfitTarget float64     `json:"profitTarget" orm:"profit_target" description:""` //
	ActualProfit float64     `json:"actualProfit" orm:"actual_profit" description:""` //
	Remark       string      `json:"remark"       orm:"remark"        description:""` //
	CreatedBy    uint64      `json:"createdBy"    orm:"created_by"    description:""` //
	UpdatedBy    uint64      `json:"updatedBy"    orm:"updated_by"    description:""` //
	CreatedAt    *gtime.Time `json:"createdAt"    orm:"created_at"    description:""` //
	UpdatedAt    *gtime.Time `json:"updatedAt"    orm:"updated_at"    description:""` //
	DeletedAt    *gtime.Time `json:"deletedAt"    orm:"deleted_at"    description:""` //
}
