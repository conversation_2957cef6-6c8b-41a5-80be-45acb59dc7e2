package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// PasswordGroup is the entity for password group
type PasswordGroup struct {
	Id         int         `json:"id"         description:"主键ID"`
	CreatedAt  *gtime.Time `json:"createdAt"  description:"创建时间"`
	UpdatedAt  *gtime.Time `json:"updatedAt"  description:"更新时间"`
	Name       string      `json:"name"       description:"组名称"`
	CategoryId int         `json:"categoryId" description:"分类ID(55:小红书,173:抖音,284:闪购)"`
}
