// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Roles is the golang structure for table roles.
type Roles struct {
	Id          int         `json:"id"          orm:"id"          description:"角色ID"`                     // 角色ID
	Name        string      `json:"name"        orm:"name"        description:"角色名称"`                     // 角色名称
	Code        string      `json:"code"        orm:"code"        description:"角色代码"`                     // 角色代码
	Description string      `json:"description" orm:"description" description:"角色描述"`                     // 角色描述
	Status      int         `json:"status"      orm:"status"      description:"状态：1启用 0禁用"`              // 状态：1启用 0禁用
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"  description:"创建时间"`                     // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"  description:"更新时间"`                     // 更新时间
} 