// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// ExportTasks is the golang structure for table export_tasks.
type ExportTasks struct {
	Id        uint64      `json:"id"        orm:"id"         description:""` //
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:""` //
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:""` //
	MediaId   uint64      `json:"mediaId"   orm:"media_id"   description:""` //
	Type      string      `json:"type"      orm:"type"       description:""` //
	Status    string      `json:"status"    orm:"status"     description:""` //
	FileUrl   string      `json:"fileUrl"   orm:"file_url"   description:""` //
	Error     string      `json:"error"     orm:"error"      description:""` //
	Params    string      `json:"params"    orm:"params"     description:""` //
	TotalRows int64       `json:"totalRows" orm:"total_rows" description:""` //
	CreatedBy uint64      `json:"createdBy" orm:"created_by" description:""` //
}
