// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Tasks is the golang structure for table tasks.
type Tasks struct {
	Id          uint64      `json:"id"          orm:"id"           description:"自增主键"`                                                 // 自增主键
	TaskType    int         `json:"taskType"    orm:"task_type"    description:"任务类型：1-费用导入"`                                          // 任务类型：1-费用导入
	Name        string      `json:"name"        orm:"name"         description:"任务名称"`                                                 // 任务名称
	Params      string      `json:"params"      orm:"params"       description:"任务参数JSON"`                                             // 任务参数JSON
	Status      string      `json:"status"      orm:"status"       description:"任务状态：pending-待处理 processing-处理中 success-成功 failed-失败"` // 任务状态：pending-待处理 processing-处理中 success-成功 failed-失败
	Error       string      `json:"error"       orm:"error"        description:"错误信息"`                                                 // 错误信息
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"   description:"创建时间"`                                                 // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"   description:"更新时间"`                                                 // 更新时间
	CompletedAt *gtime.Time `json:"completedAt" orm:"completed_at" description:"完成时间"`                                                 // 完成时间
}
