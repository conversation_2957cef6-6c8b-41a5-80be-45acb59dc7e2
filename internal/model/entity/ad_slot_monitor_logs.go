// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotMonitorLogs is the golang structure for table ad_slot_monitor_logs.
type AdSlotMonitorLogs struct {
	Id          uint64      `json:"id"          orm:"id"           description:""`                                         //
	SlotId      uint64      `json:"slotId"      orm:"slot_id"      description:"广告位ID"`                                    // 广告位ID
	MonitorType string      `json:"monitorType" orm:"monitor_type" description:"监控类型：traffic-流量监控,quality-质量监控,cost-成本监控"` // 监控类型：traffic-流量监控,quality-质量监控,cost-成本监控
	Status      string      `json:"status"      orm:"status"       description:"状态：normal-正常,warning-警告,error-异常"`         // 状态：normal-正常,warning-警告,error-异常
	MonitorData string      `json:"monitorData" orm:"monitor_data" description:"监控数据"`                                     // 监控数据
	Remark      string      `json:"remark"      orm:"remark"       description:"备注"`                                       // 备注
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"   description:""`                                         //
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"   description:""`                                         //
}
