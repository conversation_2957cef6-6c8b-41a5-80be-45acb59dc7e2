// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotPlanChangeRequests is the golang structure for table ad_slot_plan_change_requests.
type AdSlotPlanChangeRequests struct {
	Id            uint64      `json:"id"            orm:"id"             description:""`                                           //
	PlanId        uint64      `json:"planId"        orm:"plan_id"        description:"投放计划ID"`                                     // 投放计划ID
	ApplicantId   uint64      `json:"applicantId"   orm:"applicant_id"   description:"申请人ID"`                                      // 申请人ID
	ApplicantName string      `json:"applicantName" orm:"applicant_name" description:"申请人姓名"`                                      // 申请人姓名
	OldPrice      float64     `json:"oldPrice"      orm:"old_price"      description:"原价格"`                                        // 原价格
	NewPrice      float64     `json:"newPrice"      orm:"new_price"      description:"新价格"`                                        // 新价格
	OldStartDate  *gtime.Time `json:"oldStartDate"  orm:"old_start_date" description:"原开始时间"`                                      // 原开始时间
	NewStartDate  *gtime.Time `json:"newStartDate"  orm:"new_start_date" description:"新开始时间"`                                      // 新开始时间
	OldEndDate    *gtime.Time `json:"oldEndDate"    orm:"old_end_date"   description:"原结束时间"`                                      // 原结束时间
	NewEndDate    *gtime.Time `json:"newEndDate"    orm:"new_end_date"   description:"新结束时间"`                                      // 新结束时间
	Reason        string      `json:"reason"        orm:"reason"         description:"申请原因"`                                       // 申请原因
	Status        string      `json:"status"        orm:"status"         description:"审核状态：pending-待审核，approved-已通过，rejected-已拒绝"` // 审核状态：pending-待审核，approved-已通过，rejected-已拒绝
	AuditorId     uint64      `json:"auditorId"     orm:"auditor_id"     description:"审核人ID"`                                      // 审核人ID
	AuditorName   string      `json:"auditorName"   orm:"auditor_name"   description:"审核人姓名"`                                      // 审核人姓名
	AuditAt       *gtime.Time `json:"auditAt"       orm:"audit_at"       description:"审核时间"`                                       // 审核时间
	RejectReason  string      `json:"rejectReason"  orm:"reject_reason"  description:"拒绝原因"`                                       // 拒绝原因
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"     description:""`                                           //
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"     description:""`                                           //
	DeletedAt     *gtime.Time `json:"deletedAt"     orm:"deleted_at"     description:""`                                           //
}
