// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// RolePermissions is the golang structure for table role_permissions.
type RolePermissions struct {
	Id           uint64      `json:"id"           orm:"id"            description:"ID"`         // ID
	Role         int         `json:"role"         orm:"role"          description:"角色ID"`       // 角色ID
	PermissionId uint64      `json:"permissionId" orm:"permission_id" description:"权限ID"`       // 权限ID
	CreatedAt    *gtime.Time `json:"createdAt"    orm:"created_at"    description:"创建时间"`       // 创建时间
} 