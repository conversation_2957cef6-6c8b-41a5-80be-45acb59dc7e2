// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlots is the golang structure for table ad_slots.
type AdSlots struct {
	Id            uint64      `json:"id"            orm:"id"             description:""` //
	Code          string      `json:"code"          orm:"code"           description:""` //
	Type          string      `json:"type"          orm:"type"           description:""` //
	Name          string      `json:"name"          orm:"name"           description:""` //
	UserId        uint64      `json:"userId"        orm:"user_id"        description:""` //
	MediaId       uint64      `json:"mediaId"       orm:"media_id"       description:""` //
	MediaName     string      `json:"mediaName"     orm:"media_name"     description:""` //
	ScreenshotUrl string      `json:"screenshotUrl" orm:"screenshot_url" description:""` //
	VideoUrl      string      `json:"videoUrl"      orm:"video_url"      description:""` //
	AuditStatus   string      `json:"auditStatus"   orm:"audit_status"   description:""` //
	RejectReason  string      `json:"rejectReason"  orm:"reject_reason"  description:""` //
	LeakRate      float64     `json:"leakRate"      orm:"leak_rate"      description:""` //
	DiscountRate  float64     `json:"discountRate"  orm:"discount_rate"  description:""` //
	Remark        string      `json:"remark"        orm:"remark"         description:""` //
	CreatedBy     uint64      `json:"createdBy"     orm:"created_by"     description:""` //
	UpdatedBy     uint64      `json:"updatedBy"     orm:"updated_by"     description:""` //
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"     description:""` //
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"     description:""` //
	DeletedAt     *gtime.Time `json:"deletedAt"     orm:"deleted_at"     description:""` //
}
