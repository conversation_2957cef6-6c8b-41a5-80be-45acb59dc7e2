// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"time"
)

// AdSlotPlan 投放计划实体
type AdSlotPlan struct {
	ID               uint64     `json:"id" description:"ID"`
	Code             string     `json:"code" description:"计划编号"`
	Type             string     `json:"type" description:"计划类型:formal正式计划,test测试计划"`
	AdProductID      uint64     `json:"ad_product_id" description:"投放产品ID"`
	AdCreativeID     uint64     `json:"ad_creative_id" description:"创意ID"`
	AdSlotID         uint64     `json:"ad_slot_id" description:"广告位ID"`
	MediaId          uint64     `json:"media_id" description:"媒体ID"`
	UserId           uint64     `json:"user_id" description:"用户ID"`
	AuditStatus      string     `json:"audit_status" description:"审核状态:pending待审核,approved已通过,rejected已拒绝"`
	DeliveryStatus   string     `json:"delivery_status" description:"投放状态:init初始化,configuring配置中,wait待开始,running投放中,stopped已停止,stopped_auto自动停止,completed已完成"`
	DeliveryMode     int        `json:"delivery_mode" description:"投放策略:1正常投放,2加速投放,3减速投放"`
	StartDate        time.Time  `json:"start_date" description:"开始时间"`
	EndDate          *time.Time `json:"end_date" description:"结束时间"`
	IsEndDateEnabled bool       `json:"is_end_date_enabled" description:"是否启用结束时间"`
	PromotionLink    string     `json:"promotion_link" description:"推广链接"`
	PromotionQrcode  string     `json:"promotion_qrcode" description:"推广二维码"`
	ShortUrl         string     `json:"short_url" description:"短链接"`
	RejectReason     string     `json:"reject_reason" description:"拒绝原因"`
	Remark           string     `json:"remark" description:"备注"`
	MergeLinks       []byte     `json:"merge_links" description:"融合链接信息"`
	CreatedAt        time.Time  `json:"created_at" description:"创建时间"`
	UpdatedAt        time.Time  `json:"updated_at" description:"更新时间"`
	MaterialType     int8       `json:"material_type" description:"素材类型"`
}

// TableName 返回表名
func (m *AdSlotPlan) TableName() string {
	return "ad_slot_plans"
}

type PlanRelateRels struct {
	PlanId     int64 `json:"plan_id" description:"计划ID"`
	RelateType int8  `json:"relate_type" description:"关联类型"`
	RelateId   int64 `json:"relate_id" description:"关联ID"`
}

func (m *PlanRelateRels) TableName() string {
	return "plan_relate_rels"
}
