package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// PlatformPlan 平台计划实体
type PlatformPlan struct {
	Id               uint64      `json:"id"                orm:"id,primary"          description:"ID"`
	AgentId          uint64      `json:"agent_id"          orm:"agent_id"            description:"代理ID"`
	MediaId          uint64      `json:"media_id"          orm:"media_id"            description:"媒体ID"`
	PlatformType     string      `json:"platform_type"     orm:"platform_type"       description:"平台类型"`
	DataId           string      `json:"data_id"           orm:"data_id"             description:"计划ID"`
	Name             string      `json:"name"              orm:"name"                description:"计划名称"`
	PrincipalAccount string      `json:"principal_account" orm:"principal_account"   description:"商家账户"`
	PrincipalName    string      `json:"principal_name"    orm:"principal_name"      description:"商家名称"`
	MarketTargetName string      `json:"market_target_name" orm:"market_target_name" description:"营销目标名称"`
	SceneName        string      `json:"scene_name"        orm:"scene_name"          description:"投放产品名称"`
	Remark           string      `json:"remark"            orm:"remark"              description:"备注"`
	CreatedAt        *gtime.Time `json:"created_at"        orm:"created_at"          description:"创建时间"`
	PlanType         string      `json:"plan_type"         orm:"plan_type"`
	UpdatedAt        *gtime.Time `json:"updated_at"        orm:"updated_at"          description:"更新时间"`
	AdCreativeID     uint64      `json:"ad_creative_id"    orm:"ad_creative_id"      description:"创意ID"`
}
