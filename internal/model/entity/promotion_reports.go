// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// PromotionReports is the golang structure for table promotion_reports.
type PromotionReports struct {
	Id              int64       `json:"id"              orm:"id"               description:"主键ID"`    // 主键ID
	ReportDate      string      `json:"reportDate"      orm:"report_date"      description:"报表日期"`    // 报表日期
	GroupId         int64       `json:"groupId"         orm:"group_id"         description:"口令组ID"`   // 口令组ID
	CategoryId      int64       `json:"categoryId"      orm:"category_id"      description:"口令组分类id"` // 口令组分类id
	Cost            float64     `json:"cost"            orm:"cost"             description:"投放费用"`    // 投放费用
	TotalOrders     int         `json:"totalOrders"     orm:"total_orders"     description:"当日总订单数"`  // 当日总订单数
	TotalCommission float64     `json:"totalCommission" orm:"total_commission" description:"当日总佣金"`   // 当日总佣金
	CreatedAt       *gtime.Time `json:"createdAt"       orm:"created_at"       description:"创建时间"`    // 创建时间
	UpdatedAt       *gtime.Time `json:"updatedAt"       orm:"updated_at"       description:"更新时间"`    // 更新时间
}
