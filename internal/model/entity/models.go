// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Models is the golang structure for table models.
type Models struct {
	Id        uint64      `json:"id"        orm:"id"         description:"主键ID"` // 主键ID
	Name      string      `json:"name"      orm:"name"       description:"模型名称"` // 模型名称
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:"创建时间"` // 创建时间
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:"更新时间"` // 更新时间
}
