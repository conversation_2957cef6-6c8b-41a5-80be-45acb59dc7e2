// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdMediaBalanceLogs is the golang structure for table ad_media_balance_logs.
type AdMediaBalanceLogs struct {
	Id            uint64      `json:"id"            orm:"id"             description:""`               //
	MediaId       uint64      `json:"mediaId"       orm:"media_id"       description:"媒体ID"`           // 媒体ID
	Type          string      `json:"type"          orm:"type"           description:"变更类型:settle-结算"` // 变更类型:settle-结算
	SettleMonth   *gtime.Time `json:"settleMonth"   orm:"settle_month"   description:"结算月份"`           // 结算月份
	Commission    float64     `json:"commission"    orm:"commission"     description:"佣金金额"`           // 佣金金额
	Subsidy       float64     `json:"subsidy"       orm:"subsidy"        description:"补贴金额"`           // 补贴金额
	Amount        float64     `json:"amount"        orm:"amount"         description:"变更金额"`           // 变更金额
	BeforeBalance float64     `json:"beforeBalance" orm:"before_balance" description:"变更前余额"`          // 变更前余额
	AfterBalance  float64     `json:"afterBalance"  orm:"after_balance"  description:"变更后余额"`          // 变更后余额
	Remark        string      `json:"remark"        orm:"remark"         description:"备注"`             // 备注
	CreatedBy     uint64      `json:"createdBy"     orm:"created_by"     description:"操作人ID"`          // 操作人ID
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"     description:""`               //
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"     description:""`               //
	DeletedAt     *gtime.Time `json:"deletedAt"     orm:"deleted_at"     description:""`               //
}
