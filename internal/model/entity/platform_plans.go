// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// PlatformPlans is the golang structure for table platform_plans.
type PlatformPlans struct {
	Id               uint64      `json:"id"               orm:"id"                 description:"自增主键"`                          // 自增主键
	AgentId          uint64      `json:"agentId"          orm:"agent_id"           description:"代理id"`                          // 代理id
	MediaId          uint64      `json:"mediaId"          orm:"media_id"           description:"媒体id"`                          // 媒体id
	PlatformType     string      `json:"platformType"     orm:"platform_type"      description:"平台类型"`                          // 平台类型
	Remark           string      `json:"remark"           orm:"remark"             description:"备注"`                            // 备注
	DataId           string      `json:"dataId"           orm:"data_id"            description:"计划ID"`                          // 计划ID
	Name             string      `json:"name"             orm:"name"               description:"计划名称"`                          // 计划名称
	PlanType         string      `json:"planType"         orm:"plan_type"          description:"计划类型:image_text(图文),video(视频)"` // 计划类型:image_text(图文),video(视频)
	PrincipalAccount string      `json:"principalAccount" orm:"principal_account"  description:"商家账户"`                          // 商家账户
	PrincipalName    string      `json:"principalName"    orm:"principal_name"     description:"商家名称"`                          // 商家名称
	MarketTargetName string      `json:"marketTargetName" orm:"market_target_name" description:"营销目标名称"`                        // 营销目标名称
	SceneName        string      `json:"sceneName"        orm:"scene_name"         description:"投放产品名称"`                        // 投放产品名称
	CreatedAt        *gtime.Time `json:"createdAt"        orm:"created_at"         description:"创建时间"`                          // 创建时间
	UpdatedAt        *gtime.Time `json:"updatedAt"        orm:"updated_at"         description:"更新时间"`                          // 更新时间
}
