// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// PasswordGroups is the golang structure for table password_groups.
type PasswordGroups struct {
	Id         int         `json:"id"         orm:"id"          description:"主键ID"` // 主键ID
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at"  description:"创建时间"` // 创建时间
	UpdatedAt  *gtime.Time `json:"updatedAt"  orm:"updated_at"  description:"更新时间"` // 更新时间
	Name       string      `json:"name"       orm:"name"        description:"组名称"`  // 组名称
	CategoryId int64       `json:"categoryId" orm:"category_id" description:""`     //
}
