package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Password is the entity for password
type Password struct {
	Id        int         `json:"id"         description:"主键ID"`
	CreatedAt *gtime.Time `json:"createdAt"  description:"创建时间"`
	UpdatedAt *gtime.Time `json:"updatedAt"  description:"更新时间"`
	Pid       string      `json:"pid"        description:"口令ID"`
	Name      string      `json:"name"       description:"口令名字"`
	GroupId   int         `json:"groupId"    description:"所属组ID"`
}
