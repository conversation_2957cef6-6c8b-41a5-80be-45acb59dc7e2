// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Permissions is the golang structure for table permissions.
type Permissions struct {
	Id          uint64      `json:"id"          orm:"id"          description:"权限ID"`                     // 权限ID
	Name        string      `json:"name"        orm:"name"        description:"权限名称"`                     // 权限名称
	Code        string      `json:"code"        orm:"code"        description:"权限代码"`                     // 权限代码
	Module      string      `json:"module"      orm:"module"      description:"模块"`                       // 模块
	Description string      `json:"description" orm:"description" description:"权限描述"`                     // 权限描述
	DataScope   string      `json:"dataScope"   orm:"data_scope"  description:"数据范围：all-全部数据，dept-本部门数据，self-仅自己的数据"`  // 数据范围
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"  description:"创建时间"`                     // 创建时间
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"  description:"更新时间"`                     // 更新时间
} 