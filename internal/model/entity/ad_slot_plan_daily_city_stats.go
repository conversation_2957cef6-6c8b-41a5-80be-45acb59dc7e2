// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdSlotPlanDailyCityStats is the golang structure for table ad_slot_plan_daily_city_stats.
type AdSlotPlanDailyCityStats struct {
	Id                     uint64      `json:"id"                     orm:"id"                        description:""`        //
	DailyStatId            uint64      `json:"dailyStatId"            orm:"daily_stat_id"             description:""`        //
	City                   string      `json:"city"                   orm:"city"                      description:"城市名称"`    // 城市名称
	BdOrders               int         `json:"bdOrders"               orm:"bd_orders"                 description:"媒介订单数"`   // 媒介订单数
	AdminOrders            int         `json:"adminOrders"            orm:"admin_orders"              description:"管理员订单数"`  // 管理员订单数
	BdRevenue              float64     `json:"bdRevenue"              orm:"bd_revenue"                description:"媒介收入"`    // 媒介收入
	BdActivityFee          float64     `json:"bdActivityFee"          orm:"bd_activity_fee"           description:"媒介激励费用"`  // 媒介激励费用
	AdminRevenue           float64     `json:"adminRevenue"           orm:"admin_revenue"             description:"管理员收入"`   // 管理员收入
	AdminActivityFee       float64     `json:"adminActivityFee"       orm:"admin_activity_fee"        description:"实际激励费用"`  // 实际激励费用
	CreatedAt              *gtime.Time `json:"createdAt"              orm:"created_at"                description:""`        //
	UpdatedAt              *gtime.Time `json:"updatedAt"              orm:"updated_at"                description:""`        //
	BdSettleOrders         int         `json:"bdSettleOrders"         orm:"bd_settle_orders"          description:"媒介结算订单数"` // 媒介结算订单数
	AdminSettleOrders      int         `json:"adminSettleOrders"      orm:"admin_settle_orders"       description:"实际结算订单数"` // 实际结算订单数
	BdSettleRevenue        float64     `json:"bdSettleRevenue"        orm:"bd_settle_revenue"         description:"媒介结算收入"`  // 媒介结算收入
	AdminSettleRevenue     float64     `json:"adminSettleRevenue"     orm:"admin_settle_revenue"      description:"实际结算收入"`  // 实际结算收入
	BdSettleActivityFee    float64     `json:"bdSettleActivityFee"    orm:"bd_settle_activity_fee"    description:"媒介结算激励"`  // 媒介结算激励
	AdminSettleActivityFee float64     `json:"adminSettleActivityFee" orm:"admin_settle_activity_fee" description:"实际结算激励"`  // 实际结算激励
}
