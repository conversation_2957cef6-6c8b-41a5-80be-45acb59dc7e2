// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Users is the golang structure for table users.
type Users struct {
	Id                  uint64      `json:"id"                  orm:"id"                    description:"用户ID"`                //
	Name                string      `json:"name"                orm:"name"                  description:"用户名"`                //
	RealName            string      `json:"realName"            orm:"real_name"             description:"用户姓名"`               // 用户姓名
	Email               string      `json:"email"               orm:"email"                 description:"邮箱"`                 //
	EmailVerifiedAt     *gtime.Time `json:"emailVerifiedAt"     orm:"email_verified_at"     description:"邮箱验证时间"`             //
	Password            string      `json:"password"            orm:"password"              description:"密码"`                 //
	Phone               string      `json:"phone"               orm:"phone"                 description:"手机号"`                // 手机号
	Department          string      `json:"department"          orm:"department"            description:"部门"`                 // 部门
	Position            string      `json:"position"            orm:"position"              description:"职位"`                 // 职位
	SupervisorId        *uint64     `json:"supervisorId"        orm:"supervisor_id"         description:"直属上级ID"`             // 直属上级ID
	Role                int         `json:"role"                orm:"role"                  description:"角色 1-媒介 2-运营 3-管理层 4-投手 5-财务"` // 角色 1-媒介 2-运营 3-管理层 4-投手 5-财务
	Status              int         `json:"status"              orm:"status"                description:"状态：1在职 2离职"`         // 状态：1在职 2离职
	IsLocked            int         `json:"isLocked"            orm:"is_locked"             description:"是否锁定：0未锁定 1已锁定"`     // 是否锁定：0未锁定 1已锁定
	LockReason          string      `json:"lockReason"          orm:"lock_reason"           description:"锁定原因"`               // 锁定原因
	Remark              string      `json:"remark"              orm:"remark"                description:"备注"`                 // 备注
	LastLoginAt         *gtime.Time `json:"lastLoginAt"         orm:"last_login_at"         description:"最近登录时间"`             // 最近登录时间
	LastLoginIp         string      `json:"lastLoginIp"         orm:"last_login_ip"         description:"最近登录IP"`             // 最近登录IP
	RememberToken       string      `json:"rememberToken"       orm:"remember_token"        description:"记住密码令牌"`             //
	CreatedAt           *gtime.Time `json:"createdAt"           orm:"created_at"            description:"创建时间"`               //
	UpdatedAt           *gtime.Time `json:"updatedAt"           orm:"updated_at"            description:"更新时间"`               //
	ForceChangePassword int         `json:"forceChangePassword" orm:"force_change_password" description:"强制修改密码"`             //
	PasswordChangedAt   *gtime.Time `json:"passwordChangedAt"   orm:"password_changed_at"   description:"密码修改时间"`             //
	DingUserId          string      `json:"dingUserId"          orm:"ding_user_id"          description:"钉钉用户ID"`             //
	DingDeptId          string      `json:"dingDeptId"          orm:"ding_dept_id"          description:"钉钉部门ID"`             //
}
