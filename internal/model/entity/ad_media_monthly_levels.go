// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdMediaMonthlyLevels is the golang structure for table ad_media_monthly_levels.
type AdMediaMonthlyLevels struct {
	Id          uint64      `json:"id"          orm:"id"           description:""`      //
	MediaId     uint64      `json:"mediaId"     orm:"media_id"     description:"媒体ID"`  // 媒体ID
	Month       *gtime.Time `json:"month"       orm:"month"        description:"统计月份"`  // 统计月份
	Orders      int         `json:"orders"      orm:"orders"       description:"月订单数"`  // 月订单数
	DailyOrders float64     `json:"dailyOrders" orm:"daily_orders" description:"日均订单数"` // 日均订单数
	Level       string      `json:"level"       orm:"level"        description:"等级"`    // 等级
	SubsidyRate float64     `json:"subsidyRate" orm:"subsidy_rate" description:"补贴比例"`  // 补贴比例
	CreatedAt   *gtime.Time `json:"createdAt"   orm:"created_at"   description:""`      //
	UpdatedAt   *gtime.Time `json:"updatedAt"   orm:"updated_at"   description:""`      //
	DeletedAt   *gtime.Time `json:"deletedAt"   orm:"deleted_at"   description:""`      //
}
