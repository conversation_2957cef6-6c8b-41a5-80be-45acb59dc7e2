// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// AdOrders202502 is the golang structure for table ad_orders_202502.
type AdOrders202502 struct {
	Id                   uint64      `json:"id"                   orm:"id"                     description:"主键ID"`                   // 主键ID
	AdMediaId            uint64      `json:"adMediaId"            orm:"ad_media_id"            description:"媒体ID"`                   // 媒体ID
	AdMediaName          string      `json:"adMediaName"          orm:"ad_media_name"          description:"媒体名称"`                   // 媒体名称
	AdSlotId             uint64      `json:"adSlotId"             orm:"ad_slot_id"             description:"广告位ID"`                  // 广告位ID
	AdPlanId             uint64      `json:"adPlanId"             orm:"ad_plan_id"             description:"计划ID"`                   // 计划ID
	BdId                 uint64      `json:"bdId"                 orm:"bd_id"                  description:"媒介ID"`                   // 媒介ID
	SupplierId           uint64      `json:"supplierId"           orm:"supplier_id"            description:"供应商ID"`                  // 供应商ID
	OrderType            uint        `json:"orderType"            orm:"order_type"             description:"订单类型"`                   // 订单类型
	SubOrderType         string      `json:"subOrderType"         orm:"sub_order_type"         description:"子订单类型"`                  // 子订单类型
	OrderStatus          uint        `json:"orderStatus"          orm:"order_status"           description:"订单状态"`                   // 订单状态
	ParentOrderId        string      `json:"parentOrderId"        orm:"parent_order_id"        description:"父订单ID"`                  // 父订单ID
	SubOrderId           string      `json:"subOrderId"           orm:"sub_order_id"           description:"子订单ID"`                  // 子订单ID
	ItemId               string      `json:"itemId"               orm:"item_id"                description:"商品ID"`                   // 商品ID
	ItemTitle            string      `json:"itemTitle"            orm:"item_title"             description:"商品标题"`                   // 商品标题
	ItemCover            string      `json:"itemCover"            orm:"item_cover"             description:"商品封面"`                   // 商品封面
	ItemPrice            float64     `json:"itemPrice"            orm:"item_price"             description:"商品价格"`                   // 商品价格
	ItemNum              uint        `json:"itemNum"              orm:"item_num"               description:"商品数量"`                   // 商品数量
	OrderPrice           float64     `json:"orderPrice"           orm:"order_price"            description:"订单金额"`                   // 订单金额
	PayPrice             float64     `json:"payPrice"             orm:"pay_price"              description:"实付金额"`                   // 实付金额
	Rate                 float64     `json:"rate"                 orm:"rate"                   description:"佣金比例50%即0.5"`            // 佣金比例50%即0.5
	OriPreCommission     float64     `json:"oriPreCommission"     orm:"ori_pre_commission"     description:"预估佣金"`                   // 预估佣金
	OriCommission        float64     `json:"oriCommission"        orm:"ori_commission"         description:"结算佣金"`                   // 结算佣金
	PreCommission        float64     `json:"preCommission"        orm:"pre_commission"         description:"实际预估佣金"`                 // 实际预估佣金
	Commission           float64     `json:"commission"           orm:"commission"             description:"实际结算佣金"`                 // 实际结算佣金
	ServiceFee           float64     `json:"serviceFee"           orm:"service_fee"            description:"服务费"`                    // 服务费
	ServiceRatio         float64     `json:"serviceRatio"         orm:"service_ratio"          description:"服务费比例"`                  // 服务费比例
	ThirdServiceFee      float64     `json:"thirdServiceFee"      orm:"third_service_fee"      description:"三方服务费"`                  // 三方服务费
	ThirdServiceRatio    float64     `json:"thirdServiceRatio"    orm:"third_service_ratio"    description:"三方服务费比例"`                // 三方服务费比例
	ActivityFee          float64     `json:"activityFee"          orm:"activity_fee"           description:"活动补贴"`                   // 活动补贴
	ActivityServiceFee   float64     `json:"activityServiceFee"   orm:"activity_service_fee"   description:"活动补贴服务费"`                // 活动补贴服务费
	ActivityServiceRatio float64     `json:"activityServiceRatio" orm:"activity_service_ratio" description:"活动补贴服务费比例"`              // 活动补贴服务费比例
	CreateTime           *gtime.Time `json:"createTime"           orm:"create_time"            description:"创建时间"`                   // 创建时间
	PayTime              *gtime.Time `json:"payTime"              orm:"pay_time"               description:"支付时间"`                   // 支付时间
	ReceiveTime          *gtime.Time `json:"receiveTime"          orm:"receive_time"           description:"收货时间"`                   // 收货时间
	UnionId              string      `json:"unionId"              orm:"union_id"               description:"联盟ID"`                   // 联盟ID
	Pid                  string      `json:"pid"                  orm:"pid"                    description:"PID"`                    // PID
	AdzoneName           string      `json:"adzoneName"           orm:"adzone_name"            description:"推广位名称"`                  // 推广位名称
	RelationId           string      `json:"relationId"           orm:"relation_id"            description:"RID"`                    // RID
	Category             string      `json:"category"             orm:"category"               description:"类目"`                     // 类目
	City                 string      `json:"city"                 orm:"city"                   description:"城市"`                     // 城市
	ActivityType         string      `json:"activityType"         orm:"activity_type"          description:"活动类型，BRAND、CONSUMPTION"` // 活动类型，BRAND、CONSUMPTION
	AttrOrderDesc        int         `json:"attrOrderDesc"        orm:"attr_order_desc"        description:"是否归因"`                   // 是否归因
	Leak                 int         `json:"leak"                 orm:"leak"                   description:"是否漏单"`                   // 是否漏单
	CreatedAt            *gtime.Time `json:"createdAt"            orm:"created_at"             description:"创建时间"`                   // 创建时间
	UpdatedAt            *gtime.Time `json:"updatedAt"            orm:"updated_at"             description:"更新时间"`                   // 更新时间
	PreSubsidyFee        float64     `json:"preSubsidyFee"        orm:"pre_subsidy_fee"        description:"预估补贴金额"`                 // 预估补贴金额
	SubsidyFee           float64     `json:"subsidyFee"           orm:"subsidy_fee"            description:"结算补贴金额"`                 // 结算补贴金额
	SettleTime           *gtime.Time `json:"settleTime"           orm:"settle_time"            description:"结算时间"`                   // 结算时间
	SettleLogId          uint64      `json:"settleLogId"          orm:"settle_log_id"          description:"结算记录ID"`                 // 结算记录ID
}
