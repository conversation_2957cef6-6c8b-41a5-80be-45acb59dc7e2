package model

import (
	"ad-pro-v2/internal/constants"
	"ad-pro-v2/internal/model/entity"

	"github.com/gogf/gf/v2/os/gtime"
)

// PromotionZone 推广位信息
type PromotionZone struct {
	ID              uint64                 `json:"id"               description:"ID"`
	PlanId          uint64                 `json:"plan_id"          description:"投放计划ID"`
	AdProductId     uint64                 `json:"ad_product_id"    description:"产品ID"`
	ZoneId          string                 `json:"zone_id"          description:"推广位ID"`
	ZoneName        string                 `json:"zone_name"        description:"推广位名称"`
	Pid             string                 `json:"pid"              description:"PID"`
	ZoneType        string                 `json:"zone_type"        description:"推广位类型：ele-饿了么，fliggy-飞猪"`
	PromotionLink   string                 `json:"promotion_link"   description:"推广链接"`
	PromotionQrcode string                 `json:"promotion_qrcode" description:"推广二维码"`
	ZoneParams      map[string]interface{} `json:"zone_params"      description:"推广位参数"`
	PromotionParams map[string]interface{} `json:"promotion_params" description:"推广参数"`
	MergedPageUrl   string                 `json:"merged_page_url"  description:"融合页面URL"`
	MergeParams     map[string]interface{} `json:"merge_params"     description:"融合参数"`
}

// AdSlotPlan 投放计划
type AdSlotPlan struct {
	*entity.AdSlotPlan
	AdProduct          *entity.AdProducts       `json:"ad_product"       description:"投放产品" orm:"with:id=ad_product_id"`
	AdSlot             *entity.AdSlots          `json:"ad_slot"          description:"广告位" orm:"with:id=ad_slot_id"`
	PromotionZones     []*entity.PromotionZones `json:"promotion_zones" description:"推广位列表" orm:"with:plan_id=id"`
	Rels               []*entity.PlanRelateRels `orm:"with:plan_id=id" json:"rels" description:"关联信息"`
	AuditStatusText    string                   `json:"audit_status_text"    description:"审核状态文本"`
	DeliveryStatusText string                   `json:"delivery_status_text" description:"投放状态文本"`
	UserName           string                   `json:"user_name"           description:"媒介名称"`
}

// TableName 表名
func (m *AdSlotPlan) TableName() string {
	return "ad_slot_plans"
}

// GetTypeText 获取计划类型文本
func (m *AdSlotPlan) GetTypeText() string {
	return constants.PlanTypeMap[m.Type]
}

// GetAuditStatusText 获取审核状态文本
func (m *AdSlotPlan) GetAuditStatusText() string {
	return constants.PlanAuditStatusMap[m.AuditStatus]
}

// GetDeliveryStatusText 获取投放状态文本
func (m *AdSlotPlan) GetDeliveryStatusText() string {
	return constants.PlanDeliveryStatusMap[m.DeliveryStatus]
}

// GetDeliveryModeText 获取投放策略文本
func (m *AdSlotPlan) GetDeliveryModeText() string {
	return constants.PlanDeliveryModeMap[m.DeliveryMode]
}

// GetStartDateText 获取开始时间文本
func (m *AdSlotPlan) GetStartDateText() string {
	return gtime.NewFromTime(m.StartDate).Format("Y-m-d")
}

// GetEndDateText 获取结束时间文本
func (m *AdSlotPlan) GetEndDateText() string {
	if m.EndDate == nil {
		return ""
	}
	return gtime.NewFromTime(*m.EndDate).Format("Y-m-d")
}

// GetDeliveryTimeText 获取投放时间文本
func (m *AdSlotPlan) GetDeliveryTimeText() string {
	if !m.IsEndDateEnabled {
		return m.GetStartDateText() + " 起"
	}
	return m.GetStartDateText() + " ~ " + m.GetEndDateText()
}

// GetDeliveryStatusColor 获取投放状态颜色
func (m *AdSlotPlan) GetDeliveryStatusColor() string {
	switch m.DeliveryStatus {
	case constants.PLAN_DELIVERY_STATUS_INIT:
		return "info"
	case constants.PLAN_DELIVERY_STATUS_CONFIGURING:
		return "warning"
	case constants.PLAN_DELIVERY_STATUS_WAIT:
		return "warning"
	case constants.PLAN_DELIVERY_STATUS_RUNNING:
		return "success"
	case constants.PLAN_DELIVERY_STATUS_STOPPED:
		return "danger"
	case constants.PLAN_DELIVERY_STATUS_STOPPED_AUTO:
		return "danger"
	case constants.PLAN_DELIVERY_STATUS_COMPLETED:
		return "info"
	default:
		return "info"
	}
}

// GetAuditStatusColor 获取审核状态颜色
func (m *AdSlotPlan) GetAuditStatusColor() string {
	switch m.AuditStatus {
	case constants.PLAN_AUDIT_STATUS_PENDING:
		return "warning"
	case constants.PLAN_AUDIT_STATUS_APPROVED:
		return "success"
	case constants.PLAN_AUDIT_STATUS_REJECTED:
		return "danger"
	default:
		return "info"
	}
}

// GetDeliveryModeColor 获取投放策略颜色
func (m *AdSlotPlan) GetDeliveryModeColor() string {
	switch m.DeliveryMode {
	case constants.PLAN_DELIVERY_MODE_NORMAL:
		return "info"
	case constants.PLAN_DELIVERY_MODE_SPEED:
		return "success"
	case constants.PLAN_DELIVERY_MODE_SLOW:
		return "warning"
	default:
		return "info"
	}
}

// GetTypeColor 获取计划类型颜色
func (m *AdSlotPlan) GetTypeColor() string {
	switch m.Type {
	case constants.PLAN_TYPE_TEST:
		return "warning"
	case constants.PLAN_TYPE_NORMAL:
		return "success"
	default:
		return "info"
	}
}

// GetDeliveryStatusTagType 获取投放状态标签类型
func (m *AdSlotPlan) GetDeliveryStatusTagType() string {
	switch m.DeliveryStatus {
	case constants.PLAN_DELIVERY_STATUS_INIT:
		return ""
	case constants.PLAN_DELIVERY_STATUS_CONFIGURING:
		return ""
	case constants.PLAN_DELIVERY_STATUS_WAIT:
		return ""
	case constants.PLAN_DELIVERY_STATUS_RUNNING:
		return "success"
	case constants.PLAN_DELIVERY_STATUS_STOPPED:
		return "danger"
	case constants.PLAN_DELIVERY_STATUS_STOPPED_AUTO:
		return "danger"
	case constants.PLAN_DELIVERY_STATUS_COMPLETED:
		return ""
	default:
		return ""
	}
}

// GetAuditStatusTagType 获取审核状态标签类型
func (m *AdSlotPlan) GetAuditStatusTagType() string {
	switch m.AuditStatus {
	case constants.PLAN_AUDIT_STATUS_PENDING:
		return "warning"
	case constants.PLAN_AUDIT_STATUS_APPROVED:
		return "success"
	case constants.PLAN_AUDIT_STATUS_REJECTED:
		return "danger"
	default:
		return ""
	}
}

// GetDeliveryModeTagType 获取投放策略标签类型
func (m *AdSlotPlan) GetDeliveryModeTagType() string {
	switch m.DeliveryMode {
	case constants.PLAN_DELIVERY_MODE_NORMAL:
		return ""
	case constants.PLAN_DELIVERY_MODE_SPEED:
		return "success"
	case constants.PLAN_DELIVERY_MODE_SLOW:
		return "warning"
	default:
		return ""
	}
}

// GetTypeTagType 获取计划类型标签类型
func (m *AdSlotPlan) GetTypeTagType() string {
	switch m.Type {
	case constants.PLAN_TYPE_TEST:
		return "warning"
	case constants.PLAN_TYPE_NORMAL:
		return "success"
	default:
		return ""
	}
}

// IsEditable 是否可编辑
func (m *AdSlotPlan) IsEditable() bool {
	return m.DeliveryStatus == constants.PLAN_DELIVERY_STATUS_INIT || m.DeliveryStatus == constants.PLAN_DELIVERY_STATUS_CONFIGURING
}

// IsAuditable 是否可审核
func (m *AdSlotPlan) IsAuditable() bool {
	return m.AuditStatus == constants.PLAN_AUDIT_STATUS_PENDING
}

// IsRejectable 是否可拒绝
func (m *AdSlotPlan) IsRejectable() bool {
	return m.AuditStatus == constants.PLAN_AUDIT_STATUS_PENDING
}

// IsApprovable 是否可通过
func (m *AdSlotPlan) IsApprovable() bool {
	return m.AuditStatus == constants.PLAN_AUDIT_STATUS_PENDING
}

// IsStoppable 是否可停止
func (m *AdSlotPlan) IsStoppable() bool {
	return m.DeliveryStatus == constants.PLAN_DELIVERY_STATUS_RUNNING
}

// IsRestartable 是否可重启
func (m *AdSlotPlan) IsRestartable() bool {
	return m.DeliveryStatus == constants.PLAN_DELIVERY_STATUS_STOPPED || m.DeliveryStatus == constants.PLAN_DELIVERY_STATUS_STOPPED_AUTO
}

// IsDeliveryModeChangeable 是否可修改投放策略
func (m *AdSlotPlan) IsDeliveryModeChangeable() bool {
	return m.DeliveryStatus == constants.PLAN_DELIVERY_STATUS_RUNNING || m.DeliveryStatus == constants.PLAN_DELIVERY_STATUS_WAIT
}

// IsViewable 是否可查看
func (m *AdSlotPlan) IsViewable() bool {
	return true
}

// IsTestable 是否可测试
func (m *AdSlotPlan) IsTestable() bool {
	return m.Type == constants.PLAN_TYPE_TEST && m.DeliveryStatus == constants.PLAN_DELIVERY_STATUS_RUNNING
}

// IsTestResultSettable 是否可设置测试结果
func (m *AdSlotPlan) IsTestResultSettable() bool {
	return m.Type == constants.PLAN_TYPE_TEST && m.DeliveryStatus == constants.PLAN_DELIVERY_STATUS_COMPLETED
}

// IsTestPlan 是否是测试计划
func (m *AdSlotPlan) IsTestPlan() bool {
	return m.Type == constants.PLAN_TYPE_TEST
}

// IsNormalPlan 是否是正式计划
func (m *AdSlotPlan) IsNormalPlan() bool {
	return m.Type == constants.PLAN_TYPE_NORMAL
}

// IsPending 是否待审核
func (m *AdSlotPlan) IsPending() bool {
	return m.AuditStatus == constants.PLAN_AUDIT_STATUS_PENDING
}

// IsApproved 是否已通过
func (m *AdSlotPlan) IsApproved() bool {
	return m.AuditStatus == constants.PLAN_AUDIT_STATUS_APPROVED
}

// IsRejected 是否已拒绝
func (m *AdSlotPlan) IsRejected() bool {
	return m.AuditStatus == constants.PLAN_AUDIT_STATUS_REJECTED
}

// IsInit 是否初始化
func (m *AdSlotPlan) IsInit() bool {
	return m.DeliveryStatus == constants.PLAN_DELIVERY_STATUS_INIT
}

// IsConfiguring 是否配置中
func (m *AdSlotPlan) IsConfiguring() bool {
	return m.DeliveryStatus == constants.PLAN_DELIVERY_STATUS_CONFIGURING
}

// IsWait 是否待开始
func (m *AdSlotPlan) IsWait() bool {
	return m.DeliveryStatus == constants.PLAN_DELIVERY_STATUS_WAIT
}

// IsRunning 是否投放中
func (m *AdSlotPlan) IsRunning() bool {
	return m.DeliveryStatus == constants.PLAN_DELIVERY_STATUS_RUNNING
}

// IsStopped 是否已停止
func (m *AdSlotPlan) IsStopped() bool {
	return m.DeliveryStatus == constants.PLAN_DELIVERY_STATUS_STOPPED
}

// IsStoppedAuto 是否自动停止
func (m *AdSlotPlan) IsStoppedAuto() bool {
	return m.DeliveryStatus == constants.PLAN_DELIVERY_STATUS_STOPPED_AUTO
}

// IsCompleted 是否已完成
func (m *AdSlotPlan) IsCompleted() bool {
	return m.DeliveryStatus == constants.PLAN_DELIVERY_STATUS_COMPLETED
}

// IsNormalMode 是否正常投放
func (m *AdSlotPlan) IsNormalMode() bool {
	return m.DeliveryMode == constants.PLAN_DELIVERY_MODE_NORMAL
}

// IsSpeedMode 是否加速投放
func (m *AdSlotPlan) IsSpeedMode() bool {
	return m.DeliveryMode == constants.PLAN_DELIVERY_MODE_SPEED
}

// IsSlowMode 是否减速投放
func (m *AdSlotPlan) IsSlowMode() bool {
	return m.DeliveryMode == constants.PLAN_DELIVERY_MODE_SLOW
}
