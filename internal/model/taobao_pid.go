package model

import "strings"

// TaobaoPid 淘宝PID模型
type TaobaoPid struct {
	MemberID string `json:"member_id"` // 会员ID
	SiteID   string `json:"site_id"`   // 站点ID
	AdzoneID string `json:"adzone_id"` // 广告位ID
}

// ParsePid 解析PID
func ParsePid(pid string) *TaobaoPid {
	parts := strings.Split(pid, "_")
	if len(parts) != 4 {
		return nil
	}

	return &TaobaoPid{
		MemberID: parts[1],
		SiteID:   parts[2],
		AdzoneID: parts[3],
	}
}
