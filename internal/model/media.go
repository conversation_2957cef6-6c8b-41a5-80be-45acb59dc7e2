package model

import (
	"github.com/gogf/gf/v2/frame/g"
)

// 自定义错误类型
type Error struct {
	code    int
	message string
}

func (e *Error) Error() string {
	return e.message
}

func (e *Error) WithMsg(msg string) *Error {
	return &Error{
		code:    e.code,
		message: msg,
	}
}

// 错误码定义
var (
	ErrInvalidParam = &Error{code: 400, message: "无效的参数"}
)

// 审核状态枚举
const (
	AuditStatusPending  = "pending"  // 待审核
	AuditStatusApproved = "approved" // 已通过
	AuditStatusRejected = "rejected" // 已拒绝
)

// MediaPlatformConfig 平台配置
type MediaPlatformConfig struct {
	Platform string `json:"platform"     description:"平台类型 denghuoplus:灯火 kuaishou:快手"`
	Info     struct {
		MerchantMark string `json:"merchantMark" description:"商家标记"`
		AppId        string `json:"appId"        description:"平台AppID"`
		PublicKey    string `json:"publicKey"    description:"平台公钥"`
		PrivateKey   string `json:"privateKey"   description:"平台私钥"`
	} `json:"info"        description:"平台配置信息"`
}

// Media 媒体实体
type Media struct {
	Id                int                  `json:"id"                description:"ID"`
	UserId            int                  `json:"userId"            description:"用户ID"`
	UserRealName      string               `json:"userRealName"      description:"媒介姓名"`
	Name              string               `json:"name"              description:"媒体名称"`
	AdAgentId         int                  `json:"ad_agent_id"       description:"代理id"`
	Types             []string             `json:"types"             description:"媒体类型列表"`
	Industry          string               `json:"industry"          description:"所属行业"`
	CustomIndustry    string               `json:"customIndustry"    description:"自定义行业"`
	DailyActivity     string               `json:"dailyActivity"     description:"日活"`
	TransactionVolume string               `json:"transactionVolume" description:"交易量"`
	RegionCodes       []string             `json:"regionCodes"       description:"地区编码列表"`
	CompanyName       string               `json:"companyName"       description:"公司名称"`
	CompanyAddress    string               `json:"companyAddress"    description:"公司地址"`
	ContactName       string               `json:"contactName"       description:"联系人"`
	ContactPhone      string               `json:"contactPhone"      description:"联系电话"`
	AuditStatus       string               `json:"auditStatus"       description:"审核状态 pending:待审核 approved:已通过 rejected:已拒绝"`
	CooperationStatus string               `json:"cooperationStatus" description:"合作状态 not_started:未开始 active:合作中 terminated:已终止"`
	CooperationType   string               `json:"cooperationType"   description:"合作类型 cps:CPS合作 traffic:流量采买 delivery:投放"`
	Account           string               `json:"account"           description:"账号"`
	Password          string               `json:"password"          description:"密码"`
	LastLoginAt       string               `json:"lastLoginAt"       description:"最后登录时间"`
	Balance           float64              `json:"balance"           description:"余额"`
	RejectReason      string               `json:"rejectReason"      description:"拒绝原因"`
	Remark            string               `json:"remark"            description:"备注"`
	PlatformConfig    *MediaPlatformConfig `json:"platformConfig"    description:"平台配置 仅当 cooperationType=delivery 时可配置"`
	ReturnRate        float64              `json:"return_rate"       description:"返点"`
	CreatedAt         string               `json:"createdAt"         description:"创建时间"`
	UpdatedAt         string               `json:"updatedAt"         description:"更新时间"`
}

// MediaListReq 获取媒体列表请求
type MediaListReq struct {
	g.Meta            `path:"/list" method:"get" tags:"媒体管理" summary:"获取媒体列表"`
	Name              string   `json:"name"              description:"媒体名称"`
	UserId            int      `json:"userId"            description:"媒介ID"`
	Types             []string `json:"types"             description:"媒体类型列表"`
	Industry          string   `json:"industry"          description:"所属行业"`
	AuditStatus       string   `json:"auditStatus"       description:"审核状态"`
	CooperationStatus string   `json:"cooperationStatus" description:"合作状态"`
	CooperationType   string   `json:"cooperationType"   description:"合作类型"`
	Page              int      `json:"page"              description:"页码"`
	Size              int      `json:"size"              description:"每页数量"`
}

// MediaListRes 获取媒体列表响应
type MediaListRes struct {
	List  []*Media `json:"list"  description:"列表"`
	Total int      `json:"total" description:"总数"`
	Page  int      `json:"page"  description:"页码"`
	Size  int      `json:"size"  description:"每页数量"`
}

// MediaDetailReq 获取媒体详情请求
type MediaDetailReq struct {
	g.Meta `path:"/detail" method:"get" tags:"媒体管理" summary:"获取媒体详情"`
	Id     int `json:"id" v:"required#请输入媒体ID" description:"媒体ID"`
}

// MediaDetailRes 获取媒体详情响应
type MediaDetailRes struct {
	*Media
}

// MediaCreateReq 创建媒体请求
type MediaCreateReq struct {
	g.Meta            `path:"/create" method:"post" tags:"媒体管理" summary:"创建媒体"`
	UserId            int                  `json:"userId"            description:"用户ID"`
	Name              string               `json:"name"              v:"required#请输入媒体名称" description:"媒体名称"`
	Types             []string             `json:"types"             v:"required#请选择媒体类型" description:"媒体类型列表"`
	Industry          string               `json:"industry"          v:"required#请选择所属行业" description:"所属行业"`
	CustomIndustry    string               `json:"customIndustry"    description:"自定义行业"`
	DailyActivity     string               `json:"dailyActivity"     description:"日活"`
	TransactionVolume string               `json:"transactionVolume" description:"交易量"`
	CompanyName       string               `json:"companyName"       description:"公司名称"`
	CompanyAddress    string               `json:"companyAddress"    description:"公司地址"`
	ContactName       string               `json:"contactName"       description:"联系人"`
	ContactPhone      string               `json:"contactPhone"      description:"联系电话"`
	CooperationType   string               `json:"cooperationType"   v:"required#请选择合作类型" description:"合作类型"`
	Account           string               `json:"account"           description:"账号"`
	Password          string               `json:"password"          description:"密码"`
	AuditStatus       string               `json:"auditStatus"       description:"审核状态"`
	CooperationStatus string               `json:"cooperationStatus" description:"合作状态"`
	PlatformConfig    *MediaPlatformConfig `json:"platformConfig"    description:"平台配置 仅当 cooperationType=dh 时可配置"`
	AdAgentId         int                  `json:"ad_agent_id"       description:"代理id"`
	Remark            string               `json:"remark"            description:"备注"`
	ReturnRate        float64              `json:"return_rate"        description:"返点"`
}

// MediaCreateRes 创建媒体响应
type MediaCreateRes struct {
	Id int `json:"id" description:"媒体ID"`
}

// MediaUpdateReq 更新媒体请求
type MediaUpdateReq struct {
	g.Meta            `path:"/update" method:"put" tags:"媒体管理" summary:"更新媒体"`
	Id                int                  `json:"id"                v:"required#请输入媒体ID" description:"媒体ID"`
	Name              string               `json:"name"              v:"required#请输入媒体名称" description:"媒体名称"`
	Types             []string             `json:"types"             v:"required#请选择媒体类型" description:"媒体类型列表"`
	Industry          string               `json:"industry"          v:"required#请选择所属行业" description:"所属行业"`
	CustomIndustry    string               `json:"customIndustry"    description:"自定义行业"`
	DailyActivity     string               `json:"dailyActivity"     description:"日活"`
	TransactionVolume string               `json:"transactionVolume" description:"交易量"`
	CompanyName       string               `json:"companyName"       description:"公司名称"`
	CompanyAddress    string               `json:"companyAddress"    description:"公司地址"`
	ContactName       string               `json:"contactName"       description:"联系人"`
	ContactPhone      string               `json:"contactPhone"      description:"联系电话"`
	CooperationType   string               `json:"cooperationType"   v:"required#请选择合作类型" description:"合作类型"`
	Account           string               `json:"account"           description:"账号"`
	Password          string               `json:"password"          description:"密码"`
	PlatformConfig    *MediaPlatformConfig `json:"platformConfig"    description:"平台配置 仅当 cooperationType=delivery 时可配置"`
	AdAgentId         int                  `json:"adAgentId"       description:"代理id"`
	Remark            string               `json:"remark"            description:"备注"`
	ReturnRate        float64              `json:"return_rate"       description:"返点"`
}

// MediaUpdateRes 更新媒体响应
type MediaUpdateRes struct{}

// MediaDeleteReq 删除媒体请求
type MediaDeleteReq struct {
	g.Meta `path:"/delete" method:"delete" tags:"媒体管理" summary:"删除媒体"`
	Id     int `json:"id" v:"required#请输入媒体ID" description:"媒体ID"`
}

// MediaDeleteRes 删除媒体响应
type MediaDeleteRes struct{}

// MediaAuditReq 审核媒体请求
type MediaAuditReq struct {
	g.Meta       `path:"/audit" method:"put" tags:"媒体管理" summary:"审核媒体"`
	Id           int    `json:"id"          v:"required#请输入媒体ID" description:"媒体ID"`
	AuditStatus  string `json:"auditStatus" v:"required#请选择审核状态" description:"审核状态"`
	RejectReason string `json:"rejectReason" description:"拒绝原因"`
	Remark       string `json:"remark"      description:"备注"`
}

// MediaAuditRes 审核媒体响应
type MediaAuditRes struct{}

// MediaAccount 媒体账号信息
type MediaAccount struct {
	Id        uint64 `json:"id"`         // 账号ID
	Name      string `json:"name"`       // 账号名称
	MediaId   uint64 `json:"media_id"`   // 所属媒体ID
	MediaName string `json:"media_name"` // 所属媒体名称
	UserId    uint64 `json:"user_id"`    // 关联用户ID
}
