package model

import "time"

// SupplierExtInfo 供应商扩展信息
type SupplierExtInfo struct {
	ID          uint64    `json:"id"`           // ID
	UnionID     string    `json:"union_id"`     // 联盟ID
	AccessToken string    `json:"access_token"` // 访问令牌
	ExpireAt    time.Time `json:"expire_at"`    // 过期时间
	CreatedAt   time.Time `json:"created_at"`   // 创建时间
	UpdatedAt   time.Time `json:"updated_at"`   // 更新时间
}

// TableName 表名
func (s *SupplierExtInfo) TableName() string {
	return "warehouse.supplier_ext_infos"
}
