package model

import "time"

// AdPlanInfo 广告计划信息，用于缓存和轮询
type AdPlanInfo struct {
	ID                uint64         `json:"id"`                  // 计划ID
	PromotionLinkInfo any            `json:"promotion_link_info"` // 推广链接
	CreativeInfo      map[string]any `json:"creative_info"`       // 创意信息
	AdCreativeID      uint64         `json:"ad_creative_id"`      // 创意ID
	StartTime         time.Time      `json:"start_time"`          // 开始时间
	EndTime           *time.Time     `json:"end_time"`            // 结束时间
}
