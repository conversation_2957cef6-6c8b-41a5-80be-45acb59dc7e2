package model

// PromotionReportUpdateInput 更新推广成本输入参数
type PromotionReportUpdateInput struct {
	GroupId    int64   // 口令组ID
	ReportDate string  // 报表日期，格式：20060102
	Cost       float64 // 推广成本
}

// PromotionReportUpdateOutput 更新推广成本输出参数
type PromotionReportUpdateOutput struct{}

// PromotionReportListReq 推广报表列表请求
type PromotionReportListReq struct {
	Page       int    `json:"page" v:"required|min:1#请输入页码|页码必须大于0" dc:"页码"`
	Size       int    `json:"size" v:"required|min:1#请输入每页数量|每页数量必须大于0" dc:"每页数量"`
	StartDate  string `json:"startDate" v:"regex:^\\d{8}$#开始日期格式不正确" dc:"开始日期，格式：20250303"`
	EndDate    string `json:"endDate" v:"regex:^\\d{8}$#结束日期格式不正确" dc:"结束日期，格式：20250304"`
	GroupId    int64  `json:"groupId" dc:"口令组ID"`
	CategoryId int64  `json:"categoryId" dc:"分类ID"`
}

// PromotionReportListRes 推广报表列表响应
type PromotionReportListRes struct {
	List  []PromotionReportListItem `json:"list"`  // 列表数据
	Total int                       `json:"total"` // 总数
	Page  int                       `json:"page"`  // 当前页码
	Size  int                       `json:"size"`  // 每页数量
}

// PromotionReportListItem 推广报表列表项
type PromotionReportListItem struct {
	Id           int64   `json:"id"`           // 报表ID
	GroupId      int64   `json:"groupId"`      // 口令组ID
	GroupName    string  `json:"groupName"`    // 口令组名称
	ReportDate   string  `json:"reportDate"`   // 报表日期
	CategoryName string  `json:"categoryName"` // 口令组分类名称
	CategoryId   int64   `json:"categoryId"`   // 口令组分类ID
	Cost         float64 `json:"cost"`         // 投放费用
	UpdatedAt    string  `json:"updatedAt"`    // 更新时间
}

// ModelReportReq 模型报表请求参数
type ModelReportReq struct {
	ModelId   uint   `json:"modelId" v:"required#请选择模型" dc:"模型ID"`
	StartDate string `json:"startDate" v:"required|regex:^\\d{8}$#请选择开始日期|开始日期格式不正确" dc:"开始日期，格式：20250303"`
	EndDate   string `json:"endDate" v:"required|regex:^\\d{8}$#请选择结束日期|结束日期格式不正确" dc:"结束日期，格式：20250304"`
}

// ModelReportRes 模型报表响应
type ModelReportRes struct {
	List []ModelReportItem `json:"list" dc:"报表数据列表"`
}

// ModelReportItem 模型报表数据项
type ModelReportItem struct {
	Date              string  `json:"date" dc:"日期"`
	TotalOrders       int     `json:"totalOrders" dc:"当日总订单数"`
	Cost              float64 `json:"cost" dc:"当日投放费用"`
	TotalCommission   float64 `json:"totalCommission" dc:"当日佣金"`
	NewOrders         int     `json:"newOrders" dc:"当日新订单数"`
	DailyCost         float64 `json:"dailyCost" dc:"当日订单成本"`
	AverageCommission float64 `json:"averageCommission" dc:"单均佣金"`
	PaybackDays       int     `json:"paybackDays" dc:"回本周期天数"`
}
