package middleware

import (
	"ad-pro-v2/internal/service"
	"context"
	"net/http"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

// Permission 权限控制中间件
func Permission(permissions ...string) ghttp.HandlerFunc {
	return func(r *ghttp.Request) {
		ctx := r.Context()

		// 获取当前用户信息
		userInfo := service.Context().GetUser(ctx)
		if userInfo == nil {
			r.Response.WriteJsonExit(g.Map{
				"code":    http.StatusUnauthorized,
				"message": "未登录或登录已过期",
				"data":    nil,
			})
		}

		// 管理层拥有所有权限
		if userInfo.Role == 3 {
			r.Middleware.Next()
			return
		}

		// 检查用户是否有所需权限
		if len(permissions) > 0 {
			hasPermission := false
			for _, permission := range permissions {
				if CheckUserPermission(ctx, userInfo.Role, permission) {
					hasPermission = true
					break
				}
			}

			if !hasPermission {
				r.Response.WriteJsonExit(g.Map{
					"code":    http.StatusForbidden,
					"message": "权限不足",
					"data":    nil,
				})
			}
		}

		r.Middleware.Next()
	}
}

// CheckUserPermission 检查用户是否有指定权限
func CheckUserPermission(ctx context.Context, userRole int, permission string) bool {
	return CheckUserPermissionFast(userRole, permission)
}

// CheckPermission 检查权限的辅助函数
func CheckPermission(ctx context.Context, permission string) error {
	userInfo := service.Context().GetUser(ctx)
	if userInfo == nil {
		return gerror.NewCode(gcode.CodeNotAuthorized, "未登录或登录已过期")
	}

	if !CheckUserPermission(ctx, userInfo.Role, permission) {
		return gerror.NewCode(gcode.CodeNotAuthorized, "权限不足")
	}

	return nil
}

// 预定义的权限映射（用于快速检查，避免频繁查询数据库）
var rolePermissionMap = map[int][]string{
	1: { // 媒介
		"dashboard:view",
		"dashboard:data:self",
		"agent:view",
		"agent:data:self",
		"media:view",
		"media:data:self",
		"slot:view",
		"slot:data:self",
		"plan:view",
		"plan:data:self",
		"report:view",
		"report:data:self",
		"creative:view",
		"creative:data:self",
	},
	2: { // 运营
		"dashboard:view",
		"dashboard:data:dept",
		"dashboard:export",
		"agent:view",
		"agent:list",
		"agent:detail",
		"agent:data:dept",
		"media:view",
		"media:list",
		"media:detail",
		"media:data:dept",
		"slot:view",
		"slot:list",
		"slot:detail",
		"slot:data:dept",
		"plan:view",
		"plan:list",
		"plan:detail",
		"plan:create",
		"plan:edit",
		"delivery:view",
		"delivery:plan:view",
		"delivery:command:view",
		"delivery:cost:view",
		"delivery:model:view",
		"delivery:report:view",
		"report:view",
		"report:lighthouse:view",
		"creative:view",
		"creative:create",
		"creative:edit",
	},
	3: { // 管理层 - 拥有所有权限
		"dashboard:view",
		"dashboard:data:all",
		"dashboard:export",
		"agent:view",
		"agent:list",
		"agent:detail",
		"agent:create",
		"agent:edit",
		"agent:delete",
		"agent:data:all",
		"media:view",
		"media:list",
		"media:detail",
		"media:create",
		"media:edit",
		"media:delete",
		"media:data:all",
		"slot:view",
		"slot:list",
		"slot:detail",
		"slot:create",
		"slot:edit",
		"slot:delete",
		"slot:data:all",
		"plan:view",
		"plan:list",
		"plan:detail",
		"plan:create",
		"plan:edit",
		"plan:delete",
		"plan:data:all",
		"delivery:view",
		"delivery:plan:view",
		"delivery:command:view",
		"delivery:cost:view",
		"delivery:model:view",
		"delivery:report:view",
		"report:view",
		"report:lighthouse:view",
		"report:export",
		"finance:view",
		"finance:taobao:view",
		"creative:view",
		"creative:create",
		"creative:edit",
		"creative:delete",
		"creative:data:all",
		"system:view",
		"system:account:view",
		"system:account:create",
		"system:account:edit",
		"system:account:delete",
		"system:lighthouse:view",
		"system:permission:view",
		"system:department:view",
		"plugin:dashboard:view",
	},
}

// CheckUserPermissionFast 快速检查用户权限（使用内存映射）
func CheckUserPermissionFast(userRole int, permission string) bool {
	// 管理层拥有所有权限
	if userRole == 3 {
		return true
	}

	permissions, exists := rolePermissionMap[userRole]
	if !exists {
		return false
	}

	for _, perm := range permissions {
		if perm == permission {
			return true
		}
	}

	return false
}

// GetUserPermissions 获取用户所有权限
func GetUserPermissions(ctx context.Context, userRole int) ([]string, error) {
	// 管理层拥有所有权限
	if userRole == 3 {
		return rolePermissionMap[3], nil
	}

	// 从内存映射获取权限
	permissions, exists := rolePermissionMap[userRole]
	if !exists {
		return []string{}, nil
	}

	return permissions, nil
}
