package middleware

import (
	"strings"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/golang-jwt/jwt/v5"

	"ad-pro-v2/internal/dao"
	"ad-pro-v2/internal/logic/auth"
	"ad-pro-v2/internal/model"
	"ad-pro-v2/internal/model/entity"
)

// Auth JWT认证中间件
func Auth(r *ghttp.Request) {
	// 获取token
	authorization := r.Header.Get("Authorization")
	if authorization == "" {
		r.Response.WriteJson(g.Map{
			"code":    gcode.CodeNotAuthorized.Code(),
			"message": "未登录或非法访问",
		})
		r.Exit()
		return
	}

	// 校验token格式
	parts := strings.SplitN(authorization, " ", 2)
	if !(len(parts) == 2 && parts[0] == "Bearer") {
		r.Response.WriteJson(g.Map{
			"code":    gcode.CodeNotAuthorized.Code(),
			"message": "请求头Authorization格式错误",
		})
		r.Exit()
		return
	}

	// 解析token
	token := parts[1]
	var claims auth.CustomClaims
	parsedToken, err := jwt.ParseWithClaims(token, &claims, func(token *jwt.Token) (interface{}, error) {
		return auth.JwtSecret, nil
	})

	if err != nil || !parsedToken.Valid {
		r.Response.WriteJson(g.Map{
			"code":    gcode.CodeNotAuthorized.Code(),
			"message": "token无效或已过期",
		})
		r.Exit()
		return
	}

	// 从数据库中获取用户信息
	dbUser := &entity.Users{}
	err = dao.Users.Ctx(r.Context()).Where(dao.Users.Columns().Id, claims.UserId).Scan(&dbUser)
	if err != nil || dbUser == nil || dbUser.Id == 0 {
		r.Response.WriteJson(g.Map{
			"code":    gcode.CodeNotAuthorized.Code(),
			"message": "用户不存在或已被删除",
		})
		r.Exit()
		return
	}

	// 将用户信息保存到上下文
	user := &model.User{
		Id:       int(dbUser.Id),
		Username: dbUser.Name,
		Role:     dbUser.Role,
		Status:   dbUser.Status,
	}
	r.SetCtxVar("user", user)
	r.Middleware.Next()
}
