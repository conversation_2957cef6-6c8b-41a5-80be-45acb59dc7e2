package cmd

import (
	"context"
	"fmt"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcmd"
	"github.com/gogf/gf/v2/os/gcron"

	"ad-pro-v2/internal/router"
	"ad-pro-v2/internal/service"
	"ad-pro-v2/internal/service/dashboard"
)

var (
	Main = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "start http server and cron jobs",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			// 创建定时任务服务实例
			cronService := service.NewCronService()
			service.SetCron(cronService)

			// 创建同步服务实例
			syncService := service.NewSyncService()
			service.SetSync(syncService)

			// 获取服务环境配置
			serverEnv := g.Cfg().MustGet(ctx, "server.env").String()

			// 仅在生产环境下启动定时任务
			if serverEnv == "prod" {
				// 注册同步小红书成本定时任务 - 每天11点,14点执行
				if gcron.Search("sync-xhs-cost") == nil {
					_, err = gcron.AddSingleton(ctx, "0 0 11,14 * * *", func(ctx context.Context) {
						if err := service.Cron().StartSyncXhsCostTask(ctx); err != nil {
							g.Log().Errorf(ctx, "同步小红书成本数据失败: %v", err)
						} else {
							g.Log().Info(ctx, "同步小红书成本数据成功")
						}
					}, "sync-xhs-cost")
					if err != nil {
						g.Log().Fatalf(ctx, "注册同步小红书成本定时任务失败: %v", err)
						return err
					}
				}

				// 每天 11点、14点、18点、20点、22点 执行计划利润通知定时任务
				if gcron.Search("plan-profit-notice") == nil {
					_, err = gcron.AddSingleton(ctx, "0 0 9,12,13,14,16,18,20,22 * * *", func(ctx context.Context) {
						if err := service.Cron().StartPlanProfitNoticeTask(ctx); err != nil {
							g.Log().Errorf(ctx, "执行计划利润通知定时任务失败: %v", err)
						} else {
							g.Log().Info(ctx, "执行计划利润通知定时任务成功")
						}
					}, "plan-profit-notice")

					if err != nil {
						g.Log().Fatalf(ctx, "注册计划利润通知定时任务失败: %v", err)
						return err
					}
				}

				if gcron.Search("generate-pwd-daily-report") == nil {
					_, err = gcron.AddSingleton(ctx, "0 */10 * * * *", func(ctx context.Context) {
						if err := service.Cron().GeneratePwdDailyReport(ctx); err != nil {
							g.Log().Errorf(ctx, "生成口令日报定时任务失败: %v", err)
						} else {
							g.Log().Info(ctx, "生成口令日报定时任务成功")
						}
					}, "generate-pwd-daily-report")

					if err != nil {
						g.Log().Fatalf(ctx, "注册生成口令日报定时任务失败: %v", err)
						return err
					}
				}

				// 启动定时任务
				gcron.Start()
				fmt.Println("定时任务已启动")
				g.Log().Info(ctx, "定时任务已启动")
			} else {
				fmt.Println("非生产环境，定时任务未启动")
				g.Log().Info(ctx, "非生产环境，定时任务未启动")
			}

			s := g.Server()

			// 初始化服务
			if err := dashboard.InitService(ctx); err != nil {
				return err
			}

			// 注册路由
			router.RegisterRouter(s)

			// 启动服务
			s.Run()
			return nil
		},
	}
)
