package constants

// AdSlotPlan 投放计划相关常量

// 计划类型常量
const (
	PLAN_TYPE_TEST   = "test"   // 测试计划
	PLAN_TYPE_NORMAL = "formal" // 正式计划
)

// 审核状态常量
const (
	PLAN_AUDIT_STATUS_PENDING  = "pending"  // 待审核
	PLAN_AUDIT_STATUS_APPROVED = "approved" // 已通过
	PLAN_AUDIT_STATUS_REJECTED = "rejected" // 已拒绝
)

// 投放状态常量
const (
	PLAN_DELIVERY_STATUS_INIT         = "init"         // 初始化
	PLAN_DELIVERY_STATUS_CONFIGURING  = "configuring"  // 配置中
	PLAN_DELIVERY_STATUS_WAIT         = "wait"         // 待开始
	PLAN_DELIVERY_STATUS_RUNNING      = "running"      // 投放中
	PLAN_DELIVERY_STATUS_STOPPED      = "stopped"      // 已停止
	PLAN_DELIVERY_STATUS_STOPPED_AUTO = "stopped_auto" // 自动停止
	PLAN_DELIVERY_STATUS_COMPLETED    = "completed"    // 已完成
)

// 投放策略常量
const (
	PLAN_DELIVERY_MODE_NORMAL = 1 // 正常投放
	PLAN_DELIVERY_MODE_SPEED  = 2 // 加速投放
	PLAN_DELIVERY_MODE_SLOW   = 3 // 减速投放
)

// 状态映射
var (
	PlanTypeMap = map[string]string{
		PLAN_TYPE_TEST:   "测试计划",
		PLAN_TYPE_NORMAL: "正式计划",
	}

	PlanAuditStatusMap = map[string]string{
		PLAN_AUDIT_STATUS_PENDING:  "待审核",
		PLAN_AUDIT_STATUS_APPROVED: "已通过",
		PLAN_AUDIT_STATUS_REJECTED: "已拒绝",
	}

	PlanDeliveryStatusMap = map[string]string{
		PLAN_DELIVERY_STATUS_INIT:         "初始化",
		PLAN_DELIVERY_STATUS_CONFIGURING:  "配置中",
		PLAN_DELIVERY_STATUS_WAIT:         "待开始",
		PLAN_DELIVERY_STATUS_RUNNING:      "投放中",
		PLAN_DELIVERY_STATUS_STOPPED:      "已停止",
		PLAN_DELIVERY_STATUS_STOPPED_AUTO: "自动停止",
		PLAN_DELIVERY_STATUS_COMPLETED:    "已完成",
	}

	PlanDeliveryModeMap = map[int]string{
		PLAN_DELIVERY_MODE_NORMAL: "正常投放",
		PLAN_DELIVERY_MODE_SPEED:  "加速投放",
		PLAN_DELIVERY_MODE_SLOW:   "减速投放",
	}
)

// 操作类型常量
const (
	PLAN_ACTION_APPROVE              = "approve"              // 审核通过
	PLAN_ACTION_REJECT               = "reject"               // 审核拒绝
	PLAN_ACTION_UPDATE_DELIVERY_MODE = "update_delivery_mode" // 更新投放策略
	PLAN_ACTION_UPDATE_STATUS        = "update_status"        // 更新投放状态
)
