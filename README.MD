# GoFrame Template For SingleRepo

[![GoFrame](https://img.shields.io/badge/powered%20by-GoFrame-green)](https://goframe.org)
[![Go Report Card](https://goreportcard.com/badge/github.com/gogf/gf)](https://goreportcard.com/report/github.com/gogf/gf)
[![License](https://img.shields.io/github/license/gogf/gf.svg?style=flat)](https://github.com/gogf/gf)

基于 GoFrame 框架的项目脚手架,遵循 GoFrame 推荐的工程设计规范。

## 项目架构

### 分层设计

采用改进的三层架构(3-Tier Architecture)设计模式,通过分层解耦提高系统的灵活性和可维护性:

- **表示层(UI)**
  - 处理HTTP请求响应
  - 参数校验和响应封装
  - 不包含业务逻辑
  - 对应 `api` 和 `internal/controller` 目录

- **业务逻辑层(BLL)**
  - 实现具体的业务逻辑
  - 调用数据访问层获取数据
  - 实现业务规则和流程
  - 对应 `internal/service` 和 `internal/logic` 目录

- **数据访问层(DAL)**
  - 封装数据库操作
  - 实现数据的CRUD
  - 不包含业务逻辑
  - 对应 `internal/dao` 目录

- **模型层(Model)**
  - 定义数据结构
  - 区分数据模型(DO)和业务模型(BO)
  - 实现模型转换
  - 对应 `internal/model` 目录

### 目录结构

```
.
├── api                 # 对外接口定义
├── internal           # 内部逻辑代码
│   ├── controller     # 接口实现层
│   ├── model         # 模型定义
│   ├── service       # 业务接口定义
│   ├── logic         # 业务逻辑实现
│   └── dao           # 数据访问封装
├── manifest          # 配置文件
├── resource          # 静态资源
└── hack              # 工具脚本
```

### DAO层设计

- 采用DO(Data Object)作为数据模型
- 通过DAO封装数据库操作细节
- 支持组合扩展DAO能力
- 实现数据操作的可复用性

### 参数规范

- 请求参数采用结构体定义
- 支持参数校验和默认值
- 遵循统一的命名规范
- 实现参数绑定和类型转换

### 模型设计

- DO(Data Object): 数据库实体对象
- BO(Business Object): 业务逻辑对象  
- VO(View Object): 视图对象

通过模型转换实现数据在不同层级间的传递。

## Quick Start

- https://goframe.org/quick