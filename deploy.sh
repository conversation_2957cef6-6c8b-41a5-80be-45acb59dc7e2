#!/bin/bash

# 定义颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 使用说明
usage() {
    echo -e "${BLUE}使用方法:${NC}"
    echo "  $0 [goframe|gin|frontend] [options]"
    echo ""
    echo -e "${BLUE}参数:${NC}"
    echo "  goframe    部署GoFrame老版本"
    echo "  gin        部署Gin新版本"
    echo "  frontend   仅部署前端"
    echo ""
    echo -e "${BLUE}选项:${NC}"
    echo "  --no-frontend    跳过前端构建和部署(仅对goframe/gin有效)"
    echo "  --dry-run        模拟运行，不实际执行"
    echo ""
    echo -e "${BLUE}示例:${NC}"
    echo "  $0 gin                    # 部署Gin新版本(包含前端)"
    echo "  $0 goframe --no-frontend  # 仅部署GoFrame后端"
    echo "  $0 frontend               # 仅部署前端"
    echo "  $0 frontend --dry-run     # 模拟部署前端"
    exit 1
}

# 解析命令行参数
VERSION=""
SKIP_FRONTEND=false
DRY_RUN=false

# 解析第一个参数作为版本
if [[ "$1" == "goframe" || "$1" == "gin" || "$1" == "frontend" ]]; then
    VERSION="$1"
    shift
else
    echo -e "${RED}错误：必须指定版本 (goframe、gin 或 frontend)${NC}"
    usage
fi

# 解析其他参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --no-frontend)
            SKIP_FRONTEND=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo -e "${RED}未知参数: $1${NC}"
            usage
            ;;
    esac
done

# 定义基础变量
REMOTE_HOST="*************"
REMOTE_USER="root"
REMOTE_DIR="/home/<USER>/adprov2"
FRONTEND_DIR="frontend/admin"
FRONTEND_DIST="frontend/admin/dist"
REMOTE_FRONTEND_DIR="${REMOTE_DIR}/frontend"
BACKUP_DIR="${REMOTE_DIR}/bak"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 根据版本设置不同的配置
if [[ "$VERSION" == "goframe" ]]; then
    APP_NAME="ad-pro-v2-goframe"
    GO_BINARY_NAME="adprov2-goframe"
    BUILD_ENTRY="."
    SERVICE_PORT="8080"
    info_prefix="[GoFrame版本]"
elif [[ "$VERSION" == "gin" ]]; then
    APP_NAME="ad-pro-v2-gin"
    GO_BINARY_NAME="adprov2-gin"
    BUILD_ENTRY="."
    SERVICE_PORT="8081"
    info_prefix="[Gin版本]"
elif [[ "$VERSION" == "frontend" ]]; then
    APP_NAME=""
    GO_BINARY_NAME=""
    BUILD_ENTRY=""
    SERVICE_PORT=""
    info_prefix="[前端版本]"
    SKIP_FRONTEND=false  # 强制启用前端部署
fi

# 输出信息函数
info() {
    echo -e "${GREEN}[INFO]${NC} ${info_prefix} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} ${info_prefix} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} ${info_prefix} $1"
    exit 1
}

dry_run() {
    if [[ "$DRY_RUN" == "true" ]]; then
        echo -e "${BLUE}[DRY-RUN]${NC} $1"
        return 0
    else
        return 1
    fi
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        error "$1 command not found. Please install it first."
    fi
}

# 执行命令（考虑dry-run模式）
execute() {
    if dry_run "$1"; then
        return 0
    else
        eval "$1"
        return $?
    fi
}

# 执行SSH命令（考虑dry-run模式）
execute_ssh() {
    if dry_run "SSH: $1"; then
        return 0
    else
        ssh ${REMOTE_USER}@${REMOTE_HOST} "$1"
        return $?
    fi
}

# 执行SCP命令（考虑dry-run模式）
execute_scp() {
    if dry_run "SCP: $1 -> $2"; then
        return 0
    else
        scp "$1" "$2"
        return $?
    fi
}

# 检查必要的命令
info "检查必要的命令..."
check_command ssh
check_command scp

if [[ "$VERSION" != "frontend" ]]; then
    check_command go
fi

if [[ "$SKIP_FRONTEND" == "false" ]]; then
    check_command node
    check_command npm
fi

# 显示部署信息
info "部署配置信息:"
echo "  版本类型: ${VERSION}"
echo "  应用名称: ${APP_NAME}"
echo "  二进制文件: ${GO_BINARY_NAME}"
echo "  服务端口: ${SERVICE_PORT}"
echo "  跳过前端: ${SKIP_FRONTEND}"
echo "  模拟运行: ${DRY_RUN}"
echo ""

# 开始部署
info "开始部署流程..."

# 构建前端（如果需要）
if [[ "$SKIP_FRONTEND" == "false" ]]; then
    info "构建前端..."
    
    if [[ ! -d "${FRONTEND_DIR}" ]]; then
        error "前端目录不存在: ${FRONTEND_DIR}"
    fi
    
    cd ${FRONTEND_DIR}
    execute "npm install" || error "前端依赖安装失败"
    execute "npm run build" || error "前端构建失败"
    cd ../../
    
    if [[ ! -d "${FRONTEND_DIST}" ]] && [[ "$DRY_RUN" == "false" ]]; then
        error "前端构建输出目录不存在: ${FRONTEND_DIST}"
    fi
    
    info "前端构建完成"
else
    info "跳过前端构建"
fi

# 编译Go程序（仅当非前端模式时）
if [[ "$VERSION" != "frontend" ]]; then
    info "构建Go二进制文件..."

    # 根据版本选择不同的构建策略
    if [[ "$VERSION" == "goframe" ]]; then
        # GoFrame版本的构建
        execute "CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o ${GO_BINARY_NAME} ${BUILD_ENTRY}" || error "GoFrame版本构建失败"
    elif [[ "$VERSION" == "gin" ]]; then
        # Gin版本的构建
        execute "CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -tags gin -o ${GO_BINARY_NAME} ${BUILD_ENTRY}" || error "Gin版本构建失败"
    fi

    info "Go程序构建完成"
else
    info "前端模式，跳过Go程序构建"
fi

# 创建远程目录结构
info "创建远程目录..."
execute_ssh "mkdir -p ${REMOTE_DIR}/logs ${REMOTE_FRONTEND_DIR} ${BACKUP_DIR}"

# 创建备份（仅当非前端模式时）
if [[ "$VERSION" != "frontend" ]]; then
    info "创建备份..."
    execute_ssh "cd ${REMOTE_DIR} && \
        if [ -f ${GO_BINARY_NAME} ]; then \
            tar czf ${BACKUP_DIR}/backup_${VERSION}_${TIMESTAMP}.tar.gz ${GO_BINARY_NAME} manifest 2>/dev/null || true; \
        fi"
else
    info "前端模式，跳过后端备份"
fi

# 检查是否首次部署
info "检查是否首次部署..."
if [[ "$DRY_RUN" == "false" ]]; then
    FIRST_DEPLOY=$(ssh ${REMOTE_USER}@${REMOTE_HOST} "[ ! -d ${REMOTE_DIR}/manifest ] && echo 'true' || echo 'false'")
else
    FIRST_DEPLOY="false"
fi

# 停止服务（仅当非前端模式时）
if [[ "$VERSION" != "frontend" ]]; then
    info "停止服务..."
    execute_ssh "supervisorctl stop ${APP_NAME} 2>/dev/null || true"

    # 等待进程完全停止
    info "等待进程停止..."
    execute_ssh "sleep 3"
else
    info "前端模式，无需停止后端服务"
fi

# 传输文件到服务器
info "上传文件到服务器..."

# 上传Go二进制文件（仅当非前端模式时）
if [[ "$VERSION" != "frontend" ]]; then
    execute_scp "${GO_BINARY_NAME}" "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_DIR}/"

    # 上传配置文件（仅首次部署或干运行时）
    if [[ "$FIRST_DEPLOY" = "true" ]]; then
        info "首次部署，上传配置文件..."
        execute_scp "-r manifest" "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_DIR}/"
    else
        info "跳过配置文件上传以保留生产环境设置..."
    fi
fi

# 上传前端文件（如果需要）
if [[ "$SKIP_FRONTEND" == "false" ]]; then
    info "上传前端文件..."
    if dry_run "SCP: ${FRONTEND_DIST}/* -> ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_FRONTEND_DIR}/"; then
        # dry run mode, do nothing
        :
    else
        scp -r ${FRONTEND_DIST}/* ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_FRONTEND_DIR}/ || error "前端文件上传失败"
    fi
fi

# 设置权限
info "设置文件权限..."
if [[ "$VERSION" != "frontend" ]]; then
    execute_ssh "chmod +x ${REMOTE_DIR}/${GO_BINARY_NAME}"
fi
if [[ "$SKIP_FRONTEND" == "false" ]]; then
    execute_ssh "chmod -R 755 ${REMOTE_FRONTEND_DIR}"
fi

# 检查并更新supervisor配置（仅当非前端模式时）
if [[ "$VERSION" != "frontend" ]]; then
    info "检查supervisor配置..."
    SUPERVISOR_CONF="/etc/supervisor/conf.d/${APP_NAME}.conf"

    # 创建supervisor配置文件（如果不存在）
    execute_ssh "if [ ! -f ${SUPERVISOR_CONF} ]; then
    cat > ${SUPERVISOR_CONF} << EOF
[program:${APP_NAME}]
command=${REMOTE_DIR}/${GO_BINARY_NAME}
directory=${REMOTE_DIR}
autostart=true
autorestart=true
user=root
redirect_stderr=true
stdout_logfile=${REMOTE_DIR}/logs/${APP_NAME}.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
environment=GF_ENV=prod
EOF
    supervisorctl reread
    supervisorctl update
    fi"
else
    info "前端模式，跳过supervisor配置"
fi

# 清理旧备份(保留最近10个)（仅当非前端模式时）
if [[ "$VERSION" != "frontend" ]]; then
    info "清理旧备份文件..."
    execute_ssh "cd ${BACKUP_DIR} && ls -t backup_${VERSION}_*.tar.gz 2>/dev/null | tail -n +11 | xargs -r rm -f"

    # 启动服务
    info "启动服务..."
    execute_ssh "supervisorctl start ${APP_NAME}"

    # 等待服务启动
    info "等待服务启动..."
    execute_ssh "sleep 5"

    # 检查服务状态
    info "检查服务状态..."
    execute_ssh "supervisorctl status ${APP_NAME}"
else
    info "前端模式，无需管理后端服务"
fi

# 健康检查（仅当非前端模式时）
if [[ "$VERSION" != "frontend" ]]; then
    info "执行健康检查..."
    if [[ "$DRY_RUN" == "false" ]]; then
        HEALTH_CHECK=$(ssh ${REMOTE_USER}@${REMOTE_HOST} "curl -s -o /dev/null -w '%{http_code}' http://localhost:${SERVICE_PORT}/health || echo 'failed'")
        if [[ "$HEALTH_CHECK" == "200" ]]; then
            info "健康检查通过 ✓"
        else
            warn "健康检查失败，HTTP状态码: ${HEALTH_CHECK}"
            warn "请检查服务日志: tail -f ${REMOTE_DIR}/logs/${APP_NAME}.log"
        fi
    fi
else
    info "前端模式，跳过后端健康检查"
fi

# 清理本地编译文件
if [[ "$VERSION" != "frontend" ]]; then
    info "清理本地文件..."
    execute "rm -f ${GO_BINARY_NAME}"
fi

# 部署完成
info "部署完成! 🎉"

if [[ "$VERSION" != "frontend" ]]; then
    info "服务信息:"
    echo "  应用名称: ${APP_NAME}"
    echo "  服务端口: ${SERVICE_PORT}"
    echo "  日志文件: ${REMOTE_DIR}/logs/${APP_NAME}.log"
    echo ""
    info "常用命令:"
    echo "  查看状态: supervisorctl status ${APP_NAME}"
    echo "  重启服务: supervisorctl restart ${APP_NAME}"
    echo "  查看日志: tail -f ${REMOTE_DIR}/logs/${APP_NAME}.log"
else
    info "前端部署信息:"
    echo "  前端目录: ${REMOTE_FRONTEND_DIR}"
    echo "  本地构建: ${FRONTEND_DIST}"
    echo ""
    info "前端文件已成功部署到服务器"
fi 