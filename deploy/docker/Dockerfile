FROM harbor.biyingniao.com/warehouse/golang:1.24-alpine AS builder

LABEL stage=gobuilder

ENV CGO_ENABLED 0
ENV GOPROXY https://goproxy.cn,direct
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

RUN apk update --no-cache && apk add --no-cache tzdata

WORKDIR /build

COPY gin-backend/go.mod .
COPY gin-backend/go.sum .
RUN go mod download
COPY gin-backend/ ./
RUN ls -la && find . -name "*.go"
RUN mkdir -p /app/config
COPY gin-backend/config/config.yaml /app/config/config.yaml
RUN go build -ldflags="-s -w" -o /app/app cmd/main.go

FROM scratch

COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=builder /usr/share/zoneinfo/Asia/Shanghai /usr/share/zoneinfo/Asia/Shanghai
ENV TZ Asia/Shanghai

WORKDIR /app
COPY --from=builder /app/app /app/app
COPY --from=builder /app/config/config.yaml /app/config/config.yaml

ENTRYPOINT ["./app"]

CMD [""]
