package pkg

import (
	"fmt"
	"github.com/shopspring/decimal"
	"strconv"
)

func Fen2Yuan(fen interface{}) (yuan float64) {
	var yuanString string
	number := decimal.NewFromInt(100)
	switch v := fen.(type) {
	case string:
		yuanFloat, _ := decimal.NewFromString(v)
		yuan, _ = yuanFloat.Div(number).Float64()
	case int64:
		yuan, _ = decimal.NewFromInt(v).Div(number).Float64()
	case int:
		yuan, _ = decimal.NewFromInt(int64(v)).Div(number).Float64()
	case float64:
		yuan, _ = decimal.NewFromFloat(v).Div(number).Float64()
	case float32:
		yuan, _ = decimal.NewFromFloat32(v).Div(number).Float64()
	}
	yuanString = fmt.Sprintf("%.2f", yuan)
	yuan, _ = strconv.ParseFloat(yuanString, 64)
	return
}

func Yuan2Fen(yuan interface{}) (fen int64) {
	switch v := yuan.(type) {
	case float64:
		// 解决精度丢失问题 例如：19.9 * 100 = 1889
		fen = decimal.NewFromFloat(v).Mul(decimal.NewFromInt(100)).IntPart()
	case float32:
		fen = decimal.NewFromFloat32(v).Mul(decimal.NewFromInt(100)).IntPart()
	case string:
		fenInt, _ := decimal.NewFromString(v)
		fen = fenInt.Mul(decimal.NewFromInt(100)).IntPart()
	case int64:
		fen = decimal.NewFromInt(v).Mul(decimal.NewFromInt(100)).IntPart()
	case int:
		fen = decimal.NewFromInt(int64(v)).Mul(decimal.NewFromInt(100)).IntPart()
	}
	return
}
