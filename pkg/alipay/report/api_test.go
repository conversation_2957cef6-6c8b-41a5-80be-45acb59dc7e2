package report

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"testing"
	"time"
)

type RequestBody struct {
	SoltID string `json:"solt_id"`
	OpenID string `json:"open_id"`
}

func TestConcurrentAPIRequest(t *testing.T) {
	url := "https://adpub.biyingniao.com/api/v1/get-adpage"
	concurrency := 200

	// 创建通道来收集响应时间
	responseTimes := make(chan time.Duration, concurrency)
	var wg sync.WaitGroup

	// 准备请求体
	reqBody := RequestBody{
		SoltID: "268",
		OpenID: "test",
	}

	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		t.Fatalf("Failed to marshal request body: %v", err)
	}

	// 启动并发请求
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			start := time.Now()

			// 创建请求
			req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
			if err != nil {
				t.Errorf("Failed to create request: %v", err)
				return
			}

			// 设置请求头
			req.Header.Set("Content-Type", "application/json")

			// 发送请求
			client := &http.Client{}
			resp, err := client.Do(req)
			if err != nil {
				t.Errorf("Request failed: %v", err)
				return
			}
			defer resp.Body.Close()

			// 打印 body
			body, err := io.ReadAll(resp.Body)
			if err != nil {
				t.Errorf("Failed to read response body: %v", err)
				return
			}
			fmt.Println(string(body))

			duration := time.Since(start)
			responseTimes <- duration

			// 打印单个请求的响应时间
			fmt.Printf("Request %d completed in %v, Status: %s\n", id+1, duration, resp.Status)
		}(i)
	}

	// 等待所有请求完成
	wg.Wait()
	close(responseTimes)

	// 计算统计信息
	var total time.Duration
	var min, max time.Duration
	count := 0

	for duration := range responseTimes {
		if count == 0 {
			min = duration
			max = duration
		}
		if duration < min {
			min = duration
		}
		if duration > max {
			max = duration
		}
		total += duration
		count++
	}

	avg := total / time.Duration(count)

	// 打印统计结果
	fmt.Printf("\nTest Results:\n")
	fmt.Printf("Total Requests: %d\n", count)
	fmt.Printf("Average Response Time: %v\n", avg)
	fmt.Printf("Minimum Response Time: %v\n", min)
	fmt.Printf("Maximum Response Time: %v\n", max)
}
