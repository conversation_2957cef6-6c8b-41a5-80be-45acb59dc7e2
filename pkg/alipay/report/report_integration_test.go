package report

import (
	"ad-pro-v2/pkg/alipay"
	"fmt"
	"testing"
	"time"
)

func TestAdReportQueryIntegration(t *testing.T) {
	// 如果不是集成测试环境，跳过测试
	if testing.Short() {
		t.Skip("Skipping integration test")
	}

	// 创建支付宝客户端配置
	config := &alipay.Config{
		AppId:      "2021004153620789",                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             // 替换为测试环境的 AppId
		PrivateKey: "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQDw9T1aI3IB7PBRkOoM4YiJnEozXSEuUI4LnagaZ51zQRzb3l2jQ4Hwpp2fwyGfCuJiyeF4idCPmszdC5j53qUWBx+aYqmk0V/5jBzAXoPkBEb8Vm2pB4AGHvchds0gWXgz+x8fNwXjTNnFFwrRnqi3LcXzLGkYZorzM4Vqlpir9aeDwOmSm1HPSh9NkpNOB06/v+vB04LWPOAn0A3iwuXHsZS4w5SRMyyHkmYFE+J41suGg424ppegiJ46bysJoOWlGglagjb3cyTPB/RPwag6kD0Prnj85Cf0wFnMpoeczDxI4OFsrhMDMFeGSfvZX8MrHnl05c4UsGHBiKpa4jV9AgMBAAECggEBAOJhRZbeHkp41P+M6pfXfHPtBjljkbz1qb54DGtiVabm0bowHEE7N2Vk5EopBflqPG8bxF1+/RcuFLLIJpz69JbRcnuVnlRe8ZB+drS1F6yt6BNDEBvyE/GsPfqOpwFxjWEo2YB2LW3z0dU9ZMi0C0auFn/0hXiLPphnGz2c5ysF+ds4IifCk+eyLl6CmUNj8F5mH5Eg70P0QUEXrhdjw2jCuXkzbEe/mghXy9cJrsqaBjDy+ME0kaf05GIV6gARi1MnwRb2u/kOp0jpdqJs9OD/ufKF/8BlYDVoW6DoKXtbIZW9RQd7s3+ycR7/8+dAb6CNgUfwupeAabYYzg86IvECgYEA/qZprF+F/9LBDs8WpPRLXXLFI6EXFKlIgyDR599ODNaVGYQafMrRLHQtV2prmqnlOjihKQ+gWZa9kts6XFIVxH+T+t8yvB/JsjcFZPk5qq7nVlnWUaKfkXww2yJGpBHS6JZxY0PIy7y+LrYix9KqGeJNZB+dGOV/OL68ELePwpMCgYEA8jw+yS69U85xQckJXInxNmR8QxVgArgskihVex4stRmZRUFYoFGARx+T31PFHGaDu8zwmwaq7TWwLx20P/+oODGziomDlsiETzA2lVedKhu7JlS4S8fxut1O8iD8vez6PNvvOTis4xwLrRsv/Sn/wbpRcyzt02bcI0eKghaj4a8CgYEAkB7Y4dCyQwYhc2Mq+rK0ULAa+L48FNp2Cpixk9yjDMsJWYaVgxk6Z0FdRAMb4U3USS4xr86DPxD8O7hNgGCIp0xZEPvvd985xN409F/FDl+s2AdCL7OtQaaa8/qjJkpcJny2sap0YzJwJHm6l4MX+ExG5qqpu6Rva0svnvp0p3cCgYBw7NT4ROnOH/d8i5PpqATjLoUfYN+ZZPKiUFtJQg/lfUGOn6AJXPVIMthkzFCfpMrUyf2Ax+L3Iog37skQ7MYnLERe2SyXJnNH11oa1PVXpAXjIKup6FQwoOskxWwNvmQJ6x3ityEh3vkygfIlO2cXkRxXbSKR4unNKJTRgGx10QKBgQD2B9ODsds0BISzKIb+vbckgMAI+cYf3PrzAHz2nLL9liHwr0FuEJ7CBmxFNoa23vOHEyazHhEWVpN0vND5CtZm2NYsRlbEYP8gurL37b0Y3eHJCOtm5GupA9n4Mor7l0lckbrfDJ4IkFv2UMHtOSKqAleS8ovvtxzUjDEzvJkNow==", // 替换为测试环境的私钥
		PublicKey:  "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAovFvjRgDWfYZwp14atKniEsga62Grhq2RlkQnsgwrK0emXPfz5FQuvr734SYOTipgcbx7kEFyiERRTVcKuIL4QkWoPk8xGLKkw4EefwnVcdyUEYk54a9D82TuxtBuh4aBeqFfL4MgCk3R/8WIIw8LVpx5ySymaF42pqA9937XFMfZMALKTz9VfuD2TOqu4maXIZ0y/oDI9vzbhs1aeJR/rNu39se5hvfZXpZg3+t2jfan2hJOFFcOwaMdd5EEFfo+Lajr+voUqdqJ0dhKWJqXLD/XPt1TS8Xdi9GF1GpNJvR/kFBOn2EtfUzpanNcqFIOr/V62o6/cuszZjNFf8f8QIDAQAB",
		IsProd:     true,
	}

	// 创建客户端
	client := alipay.NewClient(config)

	// 创建查询请求
	startDate := time.Now().AddDate(0, 0, -6) // 6天前
	endDate := time.Now()
	req := NewAdReportQueryRequest(startDate, endDate)
	req.PageSize = 10
	req.QueryType = "DETAIL"
	req.BizToken = "27e38cd568534674aa159f055cebc214"
	req.PrincipalTag = "00a25001e590464f8d2d92035bb17242"
	req.AlipayPid = "2088521167190268"
	req.AdLevel = "CREATIVE"

	// 执行请求
	var resp AdReportQueryResponse
	_ = client.Execute(req.GetMethod(), req, &resp)
	fmt.Println(resp)
}
