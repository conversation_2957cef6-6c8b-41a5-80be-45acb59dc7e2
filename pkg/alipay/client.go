package alipay

import (
	"encoding/json"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// Client 支付宝开放平台客户端
type Client struct {
	config *Config
	client *http.Client
}

// NewClient 创建支付宝客户端
func NewClient(config *Config) *Client {
	return &Client{
		config: config,
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// Execute 执行API请求
func (c *Client) Execute(method string, bizContent interface{}, response interface{}) error {
	// 1. 构建请求参数
	params := make(map[string]string)
	params["app_id"] = c.config.AppId
	params["method"] = method
	params["format"] = "JSON"
	params["charset"] = "utf-8"
	params["sign_type"] = "RSA2"
	params["timestamp"] = time.Now().Format("2006-01-02 15:04:05")
	params["version"] = "1.0"

	// 2. 序列化业务参数
	if bizContent != nil {
		bizContentJson, err := json.Marshal(bizContent)
		if err != nil {
			return err
		}
		params["biz_content"] = string(bizContentJson)
	}

	// 3. 生成签名
	sign, err := SignParams(params, c.config.PrivateKey)
	if err != nil {
		return err
	}
	params["sign"] = sign

	// 4. 构建请求URL
	values := url.Values{}
	for k, v := range params {
		values.Set(k, v)
	}

	// 5. 发送HTTP请求
	resp, err := c.client.Post(c.config.GetServerUrl(),
		"application/x-www-form-urlencoded;charset=utf-8",
		strings.NewReader(values.Encode()))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// 6. 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	// 8. 解析响应结果
	if err := json.Unmarshal(body, response); err != nil {
		return err
	}

	return nil
}

// SetTimeout 设置超时时间
func (c *Client) SetTimeout(timeout time.Duration) {
	c.client.Timeout = timeout
}

// Clone 克隆客户端
func (c *Client) Clone() *Client {
	return &Client{
		config: c.config,
		client: &http.Client{
			Timeout: c.client.Timeout,
		},
	}
}
