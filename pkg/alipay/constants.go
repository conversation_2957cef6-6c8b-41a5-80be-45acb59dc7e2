package alipay

// API响应码
const (
	CodeSuccess = "10000" // 接口调用成功
	CodeError   = "20000" // 服务不可用
	CodeUnauth  = "20001" // 授权权限不足
	CodeMissing = "40001" // 缺少必选参数
	CodeInvalid = "40002" // 非法的参数
)

// 查询类型
const (
	QueryTypeDay   = "DAY"   // 按天查询
	QueryTypeWeek  = "WEEK"  // 按周查询
	QueryTypeMonth = "MONTH" // 按月查询
)

// 业务产品类型
const (
	BizProductSearch = "SEARCH"           // 搜索推广
	BizProductFeed   = "INFORMATION_FLOW" // 信息流推广
)
