package alipay

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"errors"
	"fmt"
	"net/url"
	"sort"
	"strings"
)

// formatPrivateKey 格式化私钥
func formatPrivateKey(privateKey string) string {
	if strings.Contains(privateKey, "BEGIN PRIVATE KEY") {
		return privateKey
	}
	return fmt.Sprintf("-----BEGIN PRIVATE KEY-----\n%s\n-----END PRIVATE KEY-----", privateKey)
}

// SignParams 生成请求签名
func SignParams(params map[string]string, privateKey string) (string, error) {
	if params == nil {
		return "", errors.New("params is nil")
	}

	// 格式化私钥
	privateKey = formatPrivateKey(privateKey)

	// 1. 参数排序
	keys := make([]string, 0, len(params))
	for k := range params {
		if k == "sign" {
			continue
		}
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 2. 构建待签名字符串
	var signContent strings.Builder
	for i, k := range keys {
		if i > 0 {
			signContent.WriteString("&")
		}
		v := params[k]
		if v != "" {
			signContent.WriteString(fmt.Sprintf("%s=%s", k, v))
		}
	}

	// 3. RSA2签名
	block, _ := pem.Decode([]byte(privateKey))
	if block == nil {
		return "", errors.New("private key error")
	}

	priKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return "", err
	}

	h := sha256.New()
	h.Write([]byte(signContent.String()))
	hash := h.Sum(nil)

	signature, err := rsa.SignPKCS1v15(rand.Reader, priKey.(*rsa.PrivateKey), crypto.SHA256, hash)
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(signature), nil
}

// VerifySign 验证签名
func VerifySign(params map[string]string, sign, publicKey string) (bool, error) {
	// 1. 参数排序并构建待验签字符串
	keys := make([]string, 0, len(params))
	for k := range params {
		if k == "sign" || k == "sign_type" {
			continue
		}
		keys = append(keys, k)
	}
	sort.Strings(keys)

	var signContent strings.Builder
	for i, k := range keys {
		if i > 0 {
			signContent.WriteString("&")
		}
		v := params[k]
		if v != "" {
			signContent.WriteString(fmt.Sprintf("%s=%s", k, url.QueryEscape(v)))
		}
	}

	// 2. 解析公钥
	block, _ := pem.Decode([]byte(publicKey))
	if block == nil {
		return false, errors.New("public key error")
	}

	pubKey, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return false, err
	}

	// 3. 验证签名
	signBytes, err := base64.StdEncoding.DecodeString(sign)
	if err != nil {
		return false, err
	}

	h := sha256.New()
	h.Write([]byte(signContent.String()))
	hash := h.Sum(nil)

	err = rsa.VerifyPKCS1v15(pubKey.(*rsa.PublicKey), crypto.SHA256, hash, signBytes)
	return err == nil, err
}
