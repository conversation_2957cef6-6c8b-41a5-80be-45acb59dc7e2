package v1

import (
	"time"

	"github.com/gogf/gf/v2/frame/g"

	"ad-pro-v2/internal/model"
)

// ListReq 获取列表请求参数
type ListReq struct {
	g.Meta   `path:"/list" method:"get" tags:"投放计划" summary:"获取投放计划列表"`
	Page     int    `json:"page" v:"required|min:1#页码不能为空|页码必须大于0" dc:"页码"`
	Size     int    `json:"size" v:"required|min:1#每页数量不能为空|每页数量必须大于0" dc:"每页数量"`
	Code     string `json:"code" dc:"计划编号"`
	MediaID  uint64 `json:"media_id" dc:"媒体ID"`
	AdSlotID uint64 `json:"ad_slot_id" dc:"广告位ID"`
	UserId   int    `json:"user_id" dc:"用户ID,用于管理员查询指定用户的数据"`
}

// GetAdLinkReq 获取广告链接请求
type GetAdLinkReq struct {
	g.Meta `path:"/get-adlink" method:"post" tags:"投放计划" summary:"获取广告链接"`
	SoltId int64  `json:"solt_id" v:"required#资源位ID不能为空" dc:"资源位ID"`
	OpenId string `json:"open_id" v:"required#open_id不能为空" dc:"open_id"`
	Way    int    `json:"way" dc:"way"`
}

// GetAdPageReq 获取广告页面请求
type GetAdPageReq struct {
	g.Meta `path:"/get-adpage" method:"post" tags:"投放计划" summary:"获取广告页面"`
	SoltId int64  `json:"solt_id" v:"required#资源位ID不能为空" dc:"资源位ID"`
	OpenId string `json:"open_id" v:"required#open_id不能为空" dc:"open_id"`
	Way    int    `json:"way" dc:"way"`
}

// GetAdPageRes 获取广告页面响应
type GetAdPageRes struct {
	BackgroundImage string `json:"background_image" dc:"中间页背景图"`
	BackgroundLink  any    `json:"background_link" dc:"中间页背景图链接"`
	DialogType      int8   `json:"dialog_type" dc:"弹窗类型：1-系统弹窗，2-自定义弹窗，3-无弹窗"`
	Dialog          any    `json:"dialog" dc:"弹窗信息"`
	DialogLinkInfo  any    `json:"dialog_link_info" dc:"弹窗链接信息"`
}

// GetAdLinkRes 获取广告链接响应
type GetAdLinkRes struct {
	ID                uint64         `json:"id"`
	PromotionLinkInfo any            `json:"promotion_link_info"` // 推广链接信息
	CreativeInfo      map[string]any `json:"creative_info"`       // 创意信息
}

// ListRes 获取投放计划列表响应
type ListRes struct {
	List  []model.AdSlotPlan `json:"list"`  // 列表数据
	Total int                `json:"total"` // 总数
}

// GetReq 获取投放计划详情请求
type GetReq struct {
	g.Meta `path:"/get" method:"get" tags:"投放计划" summary:"获取投放计划详情"`
	ID     uint64 `v:"required#ID不能为空" dc:"ID"`
}

// GetRes 获取投放计划详情响应
type GetRes struct {
	Plan *model.AdSlotPlan `json:"plan"` // 计划信息
}

// CreateReq 创建投放计划请求
type CreateReq struct {
	g.Meta           `path:"/create" method:"post" tags:"投放计划" summary:"创建投放计划"`
	Type             string           `v:"required|in:normal,test#类型不能为空|类型无效" dc:"计划类型:normal正式计划,test测试计划"`
	AdProductID      uint64           `v:"required#投放产品不能为空" dc:"投放产品ID"`
	AdCreativeID     uint64           `dc:"创意ID"`
	AdSlotID         uint64           `v:"required#广告位不能为空" dc:"广告位ID"`
	StartDate        time.Time        `v:"required#开始时间不能为空" dc:"开始时间"`
	EndDate          *time.Time       `dc:"结束时间"`
	IsEndDateEnabled bool             `dc:"是否启用结束时间"`
	Remark           string           `dc:"备注"`
	MaterialType     int8             `dc:"素材类型:1图文,2视频"`
	Rels             []*PlanRelateRel `dc:"关联上游计划"`
}

type PlanRelateRel struct {
	Type int8
	Id   int64
}

// CreateRes 创建投放计划响应
type CreateRes struct {
	ID uint64 `json:"id"` // 计划ID
}

// UpdateReq 更新投放计划请求
type UpdateReq struct {
	g.Meta       `path:"/update" method:"post" tags:"投放计划" summary:"更新投放计划"`
	ID           uint64           `v:"required#ID不能为空" dc:"ID"`
	AdCreativeID uint64           `dc:"创意ID"`
	Remark       string           `dc:"备注"`
	MaterialType int8             `dc:"素材类型:1图文,2视频"`
	Rels         []*PlanRelateRel `dc:"关联上游计划"`
}

// UpdateRes 更新投放计划响应
type UpdateRes struct{}

// DeleteReq 删除投放计划请求
type DeleteReq struct {
	g.Meta `path:"/delete" method:"post" tags:"投放计划" summary:"删除投放计划"`
	ID     uint64 `v:"required#ID不能为空" dc:"ID"`
}

// DeleteRes 删除投放计划响应
type DeleteRes struct{}

// ApproveReq 审核通过请求
type ApproveReq struct {
	g.Meta `path:"/approve" method:"post" tags:"投放计划" summary:"审核通过"`
	ID     uint64 `v:"required#ID不能为空" dc:"ID"`
}

// ApproveRes 审核通过响应
type ApproveRes struct{}

// RejectReq 审核拒绝请求
type RejectReq struct {
	g.Meta `path:"/reject" method:"post" tags:"投放计划" summary:"审核拒绝"`
	ID     uint64 `v:"required#ID不能为空" dc:"ID"`
	Reason string `v:"required#拒绝原因不能为空" dc:"拒绝原因"`
}

// RejectRes 审核拒绝响应
type RejectRes struct{}

// UpdateDeliveryModeReq 更新投放策略请求
type UpdateDeliveryModeReq struct {
	g.Meta `path:"/update-delivery-mode" method:"post" tags:"投放计划" summary:"更新投放策略"`
	ID     uint64 `v:"required#ID不能为空" dc:"ID"`
	Mode   int    `v:"required|in:1,2,3#投放策略不能为空|投放策略无效" dc:"投放策略"`
}

// UpdateDeliveryModeRes 更新投放策略响应
type UpdateDeliveryModeRes struct{}

// GeneratePromotionLinkReq 生成推广链接请求
type GeneratePromotionLinkReq struct {
	g.Meta    `path:"/generate-promotion-link" method:"post" tags:"投放计划" summary:"生成推广链接"`
	ID        uint64 `v:"required#ID不能为空" dc:"ID"`
	ProductID uint64 `dc:"投放产品ID"`
}

// GeneratePromotionLinkRes 生成推广链接响应
type GeneratePromotionLinkRes struct {
	PromotionLink   string `json:"promotion_link"`   // 推广链接
	PromotionQrcode string `json:"promotion_qrcode"` // 推广二维码
}

// GenerateShortUrlReq 生成短链接请求
type GenerateShortUrlReq struct {
	g.Meta `path:"/ad-slot-plan/generate-short-url" method:"post" tags:"投放计划" summary:"生成短链接"`
	ID     uint64 `v:"required#ID不能为空" dc:"ID"`
}

// GenerateShortUrlRes 生成短链接响应
type GenerateShortUrlRes struct {
	ShortUrl string `json:"short_url"` // 短链接
}

// UpdatePromotionLinkReq 更新推广链接请求
type UpdatePromotionLinkReq struct {
	g.Meta `path:"/update-promotion-link" method:"post" tags:"投放计划" summary:"更新推广链接"`
	ID     uint64 `v:"required#计划ID不能为空" dc:"计划ID"`
	Link   string `v:"required#推广链接不能为空" dc:"推广链接"`
}

// UpdatePromotionLinkRes 更新推广链接响应
type UpdatePromotionLinkRes struct{}

// UpdateShortUrlReq 更新短链接请求
type UpdateShortUrlReq struct {
	g.Meta `path:"/update-short-url" method:"post" tags:"投放计划" summary:"更新短链接"`
	ID     uint64 `v:"required#计划ID不能为空" dc:"计划ID"`
	URL    string `v:"required#短链接不能为空" dc:"短链接"`
}

// UpdateShortUrlRes 更新短链接响应
type UpdateShortUrlRes struct{}

// UpdateMergeLinksReq 更新融合链接请求
type UpdateMergeLinksReq struct {
	g.Meta     `path:"/update-merge-links" method:"post" tags:"投放计划" summary:"更新融合链接"`
	ID         uint64      `v:"required#计划ID不能为空" dc:"计划ID"`
	MergeLinks interface{} `v:"required#融合链接不能为空" dc:"融合链接信息"`
}

// UpdateMergeLinksRes 更新融合链接响应
type UpdateMergeLinksRes struct{}

// GetMergeLinksReq 获取融合链接请求
type GetMergeLinksReq struct {
	g.Meta `path:"/get-merge-links" method:"get" tags:"投放计划" summary:"获取融合链接信息"`
	Code   string `v:"required#编号不能为空" dc:"计划编号"`
}

// GetMergeLinksRes 获取融合链接响应
type GetMergeLinksRes struct {
	MergeLinks interface{} `json:"merge_links" dc:"融合链接信息"`
}

type GetPlatformObjectDataReq struct {
	g.Meta `path:"/platform_object_data" method:"get" tags:"投放计划" summary:"获取平台对象数据"`
	Type   int8 `dc:"平台对象数据类型"`
}

type GetPlatformObjectDataRes struct {
	Data any `json:"data"`
}
