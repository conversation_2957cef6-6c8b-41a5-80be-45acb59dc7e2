package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type GetProfitDataReq struct {
	g.<PERSON>a    `path:"/profit" method:"get"`
	StartDate string `json:"start_date" v:"required"`
	EndDate   string `json:"end_date" v:"required"`
	Type      string `json:"type"`
	UserId    int    `json:"user_id"`
	MediaId   int    `json:"media_id"`
	PlanId    int    `json:"plan_id"`
	ProductId int    `json:"product_id"`
}

type ProfitData struct {
	Date             string  `json:"date"`
	ElemeCommission  float64 `json:"eleme_commission"`
	FliggyCommission float64 `json:"fliggy_commission"`
	Cost             float64 `json:"cost"`
}

type GetProfitDataRes struct {
	List []ProfitData `json:"list"`
}
