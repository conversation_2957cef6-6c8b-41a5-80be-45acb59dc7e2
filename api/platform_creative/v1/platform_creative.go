package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// ListReq 获取广告创意列表请求
type ListReq struct {
	g.Meta           `path:"/list" method:"get" tags:"广告创意" summary:"获取广告创意列表"`
	Page             int    `json:"page" dc:"页码"`
	PageSize         int    `json:"pageSize" dc:"每页数量"`
	PlatformType     string `json:"platformType" dc:"平台类型"`
	Keyword          string `json:"keyword" dc:"关键词，支持创意名称模糊搜索和平台ID精确匹配"`
	MarketTargetName string `json:"marketTargetName" dc:"营销目标"`
	PlanKeyword      string `json:"planKeyword" dc:"计划关键词，支持计划ID和计划名称搜索"`
	GroupKeyword     string `json:"groupKeyword" dc:"广告组关键词，支持广告组ID和广告组名称搜索"`
}

// ListRes 获取广告创意列表响应
type ListRes struct {
	List  []PlatformCreativeItem `json:"list"`  // 列表数据
	Total int                    `json:"total"` // 总数
}

// PlatformCreativeItem 广告创意列表项
type PlatformCreativeItem struct {
	ID           uint64 `json:"id"`       // ID
	Name         string `json:"name"`     // 名称
	GroupId      int64  `json:"group_id"` // 广告组ID
	PladId       int64  `json:"plan_id"`  // 计划ID
	PlanName     string `json:"plan_name"`
	CreatedAt    string `json:"created_at"`    // 创建时间
	UpdatedAt    string `json:"updated_at"`    // 更新时间
	MediaName    string `json:"media_name"`    // 媒体名称
	AgentName    string `json:"agent_name"`    // 代理名称
	DataId       string `json:"data_id"`       // 平台数据ID
	PlatformType string `json:"platform_type"` // 平台类型
	Remark       string `json:"remark"`        // 备注
}

// CreateReq 创建广告创意请求
type CreateReq struct {
	g.Meta      `path:"/create" method:"post" tags:"广告创意" summary:"创建广告创意"`
	Name        string `v:"required" dc:"创意名称"`
	GroupId     int64  `v:"required" dc:"广告组ID"`
	Title       string `v:"required" dc:"创意标题"`
	Description string `dc:"创意描述"`
	ImageUrl    string `dc:"图片URL"`
	VideoUrl    string `dc:"视频URL"`
	LandingUrl  string `v:"required" dc:"落地页URL"`
	Status      int    `dc:"状态 0:暂停 1:投放中"`
}

// CreateRes 创建广告创意响应
type CreateRes struct {
	Id int64 `json:"id"` // 创建的创意ID
}

// UpdateReq 更新广告创意请求
type UpdateReq struct {
	g.Meta `path:"/:id" method:"put" tags:"广告创意" summary:"更新广告创意"`
	Id     int64  `v:"required" dc:"创意ID"`
	Remark string `p:"description"`
}

// UpdateRes 更新广告创意响应
type UpdateRes struct{}

// DeleteReq 删除广告创意请求
type DeleteReq struct {
	g.Meta `path:"/delete" method:"delete" tags:"广告创意" summary:"删除广告创意"`
	Id     int64 `v:"required" dc:"创意ID"`
}

// DeleteRes 删除广告创意响应
type DeleteRes struct{}
