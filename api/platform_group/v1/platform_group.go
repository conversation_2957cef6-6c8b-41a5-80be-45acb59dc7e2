package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// ListReq 获取广告组列表请求
type ListReq struct {
	g.Meta           `path:"/list" method:"get" tags:"广告组" summary:"获取广告组列表"`
	Page             int    `json:"page" dc:"页码"`
	PageSize         int    `json:"pageSize" dc:"每页数量"`
	PlanKeyword      string `json:"planKeyword" dc:"计划关键词，支持计划ID和计划名称搜索"`
	PlatformType     string `json:"platformType" dc:"平台类型"`
	MarketTargetName string `json:"marketTargetName" dc:"营销目标"`
	Keyword          string `json:"keyword" dc:"关键词，支持单元名称模糊搜索和平台ID精确匹配"`
}

// ListRes 获取广告组列表响应
type ListRes struct {
	List  []PlatformGroupItem `json:"list"`  // 列表数据
	Total int                 `json:"total"` // 总数
}

// PlatformGroupItem 广告组列表项
type PlatformGroupItem struct {
	ID           uint64 `json:"id"`            // ID
	Name         string `json:"name"`          // 单元名称
	PlanName     string `json:"plan_name"`     // 计划名称
	MediaName    string `json:"media_name"`    // 媒体名称
	AgentName    string `json:"agent_name"`    // 代理名称
	Description  string `json:"description"`   // 备注信息
	DataId       string `json:"data_id"`       // 数据ID
	PlatformType string `json:"platform_type"` // 平台类型
}

// CreateReq 创建广告组请求
type CreateReq struct {
	g.Meta       `path:"/create" method:"post" tags:"广告组" summary:"创建广告组"`
	Name         string  `v:"required" dc:"广告组名称"`
	PlanId       int64   `v:"required" dc:"计划ID"`
	Budget       float64 `v:"required" dc:"预算"`
	DailyBudget  float64 `v:"required" dc:"日预算"`
	Status       int     `dc:"状态 0:暂停 1:投放中"`
	DeliveryMode int     `dc:"投放模式 1:加速 2:匀速"`
}

// CreateRes 创建广告组响应
type CreateRes struct {
	Id int64 `json:"id"` // 创建的广告组ID
}

// UpdateReq 更新广告组请求
type UpdateReq struct {
	g.Meta       `path:"/:id" method:"put" tags:"广告组" summary:"更新广告组"`
	Id           int64   `v:"required" dc:"广告组ID"`
	Name         string  `dc:"广告组名称"`
	Budget       float64 `dc:"预算"`
	DailyBudget  float64 `dc:"日预算"`
	Status       int     `dc:"状态"`
	DeliveryMode int     `dc:"投放模式"`
	Remark       string  `p:"description"`
}

// UpdateRes 更新广告组响应
type UpdateRes struct{}

// DeleteReq 删除广告组请求
type DeleteReq struct {
	g.Meta `path:"/delete" method:"delete" tags:"广告组" summary:"删除广告组"`
	Id     int64 `v:"required" dc:"广告组ID"`
}

// DeleteRes 删除广告组响应
type DeleteRes struct{}
