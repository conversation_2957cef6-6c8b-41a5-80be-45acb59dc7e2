package v1

import "github.com/gogf/gf/v2/frame/g"

type DenghuoPlusReq struct {
	g.<PERSON>a    `path:"/sync/denghuo-plus" method:"post" tags:"同步" summary:"同步灯火数据"`
	MediaId   uint64 `json:"media_id" v:"required|min:1" dc:"媒体ID"`
	AgentId   uint64 `json:"agent_id" v:"required|min:1" dc:"代理商ID"`
	StartDate string `json:"start_date" v:"required" dc:"开始日期 格式:2006-01-02"`
	EndDate   string `json:"end_date" v:"required" dc:"结束日期 格式:2006-01-02"`
}

type DenghuoPlusRes struct {
	Status string `json:"status" dc:"同步状态"`
}
