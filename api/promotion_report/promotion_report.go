package promotion_report

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
)

// ImportCostReq 导入推广成本请求
type ImportCostReq struct {
	g.Meta     `path:"/import" method:"post" mime:"multipart/form-data" tags:"推广报表" summary:"导入推广成本"`
	CategoryId uint              `json:"category_id" v:"required#分类ID不能为空" dc:"分类ID"`
	File       *ghttp.UploadFile `json:"file" v:"required#请上传Excel文件" dc:"Excel文件"`
}

// ImportCostRes 导入推广成本响应
type ImportCostRes struct {
	Message string `json:"tip" dc:"提示信息"`
}

// UpdateCostReq 更新推广成本请求
type UpdateCostReq struct {
	g.Meta     `path:"/update-cost" method:"post" tags:"推广报表" summary:"更新推广成本"`
	GroupId    int64   `v:"required#分组ID不能为空" dc:"分组ID"`
	ReportDate string  `v:"required#报表日期不能为空" dc:"报表日期"`
	Cost       float64 `v:"required#成本不能为空" dc:"成本"`
}

// UpdateCostRes 更新推广成本响应
type UpdateCostRes struct{}

// GetCostListReq 获取推广成本列表请求
type GetCostListReq struct {
	g.Meta     `path:"/list" method:"get" tags:"推广报表" summary:"获取推广成本列表"`
	Page       int    `d:"1" v:"min:1#页码最小值为1" dc:"页码"`
	PageSize   int    `d:"10" v:"max:50#每页最大50条" dc:"每页数量"`
	GroupId    int64  `dc:"分组ID"`
	CategoryId int64  `dc:"分类ID"`
	StartDate  string `dc:"开始日期"`
	EndDate    string `dc:"结束日期"`
}

// GetCostListRes 获取推广成本列表响应
type GetCostListRes struct {
	List  []GetCostListItem `json:"list"`  // 列表数据
	Total int               `json:"total"` // 总数
	Page  int               `json:"page"`  // 当前页码
	Size  int               `json:"size"`  // 每页数量
}

// GetCostListItem 推广成本列表项
type GetCostListItem struct {
	Id           int64   `json:"id"`            // ID
	GroupId      int64   `json:"group_id"`      // 分组ID
	GroupName    string  `json:"group_name"`    // 分组名称
	ReportDate   string  `json:"report_date"`   // 报表日期
	CategoryName string  `json:"category_name"` // 口令组分类名称
	CategoryId   int64   `json:"category_id"`   // 口令组分类ID
	Cost         float64 `json:"cost"`          // 成本
	UpdateAt     string  `json:"update_at"`     // 更新时间
}

// GetModelReportReq 获取模型报表请求
type GetModelReportReq struct {
	g.Meta     `path:"/list" method:"get" tags:"推广报表" summary:"获取模型报表数据"`
	ModelId    uint64 `v:"required#模型ID不能为空" dc:"模型ID"`
	CategoryId uint64 `dc:"分类ID，与口令组ID二选一"`
	GroupId    int64  `dc:"口令组ID，与分类ID二选一"`
}

// GetModelReportRes 获取模型报表响应
type GetModelReportRes struct {
	List []ReportItem `json:"list" dc:"报表数据列表"`
}

// ReportItem 报表数据项
type ReportItem struct {
	Date              string  `json:"date" dc:"日期"`
	TotalOrders       int     `json:"total_orders" dc:"总订单数"`
	Cost              float64 `json:"cost" dc:"成本"`
	TotalCommission   float64 `json:"total_commission" dc:"总佣金"`
	NewOrders         int     `json:"new_orders" dc:"新订单数"`
	DailyCost         float64 `json:"daily_cost" dc:"当日订单成本"`
	AverageCommission float64 `json:"average_commission" dc:"单均佣金"`
	PaybackDays       int     `json:"payback_days" dc:"回本周期天数"`
}

// ImportXhsCostReq 导入小红书成本请求
type ImportXhsCostReq struct {
	g.Meta `path:"/import-xhs" method:"post" tags:"推广报表" summary:"导入小红书成本"`
}

// ImportXhsCostRes 导入小红书成本响应
type ImportXhsCostRes struct{}

// GetTaskListReq 获取任务列表请求
type GetTaskListReq struct {
	g.Meta `path:"/tasks" method:"get" tags:"推广报表" summary:"获取任务列表"`
}

// GetTaskListRes 获取任务列表响应
type GetTaskListRes struct {
	List []TaskListItem `json:"list" dc:"任务列表"`
}

// TaskListItem 任务列表项
type TaskListItem struct {
	Id        uint64      `json:"id"         dc:"任务ID"`
	Name      string      `json:"name"       dc:"任务名称"`
	Status    string      `json:"status"     dc:"任务状态"`
	CreatedAt *gtime.Time `json:"created_at" dc:"创建时间"`
}
