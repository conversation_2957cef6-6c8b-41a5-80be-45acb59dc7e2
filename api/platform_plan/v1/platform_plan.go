package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// ListReq 获取平台计划列表请求
type ListReq struct {
	g.Meta           `path:"/list" method:"get" tags:"平台计划" summary:"获取平台计划列表"`
	Page             int    `json:"page" dc:"页码"`
	PageSize         int    `json:"pageSize" dc:"每页数量"`
	PlatformType     string `json:"platformType" dc:"平台类型"`
	PlanName         string `json:"planName" dc:"计划名称"`
	AgentId          uint64 `json:"agentId" dc:"代理商ID"`
	MediaId          uint64 `json:"mediaId" dc:"媒体ID"`
	MarketTargetName string `json:"marketTargetName" dc:"营销目标"`
	Keyword          string `json:"keyword" dc:"关键词，支持计划名称模糊搜索和平台ID精确匹配"`
}

// ListRes 获取平台计划列表响应
type ListRes struct {
	List  []PlatformPlanItem `json:"list"`  // 列表数据
	Total int                `json:"total"` // 总数
}

// PlatformPlanItem 平台计划列表项
type PlatformPlanItem struct {
	ID               uint64 `json:"id"`                 // ID
	DataId           string `json:"data_id"`            // 计划ID
	Name             string `json:"name"`               // 名称
	MediaName        string `json:"media_name"`         // 媒体名称
	AgentName        string `json:"agent_name"`         // 代理名称
	PlatformType     string `json:"platform_type"`      // 平台类型
	PlanType         string `json:"plan_type"`          // 计划类型：image_text-图文, video-视频
	MarketTargetName string `json:"market_target_name"` // 营销目标名称
	CreatedAt        string `json:"created_at"`         // 创建时间
	UpdatedAt        string `json:"updated_at"`         // 更新时间
}

// CreateReq 创建平台计划请求
type CreateReq struct {
	g.Meta       `path:"/create" method:"post" tags:"平台计划" summary:"创建平台计划"`
	Name         string  `v:"required" dc:"计划名称"`
	Description  string  `dc:"计划描述"`
	Budget       float64 `v:"required" dc:"预算"`
	Status       int     `dc:"状态 0:禁用 1:启用"`
	AdCreativeID uint64  `dc:"创意ID"`
}

// CreateRes 创建平台计划响应
type CreateRes struct {
	Id int64 `json:"id"` // 创建的计划ID
}

// UpdateReq 更新平台计划请求
type UpdateReq struct {
	g.Meta       `path:"/update" method:"put" tags:"平台计划" summary:"更新平台计划"`
	Id           int64   `v:"required" dc:"计划ID"`
	Name         string  `dc:"计划名称"`
	Description  string  `dc:"计划描述"`
	Budget       float64 `dc:"预算"`
	Status       int     `dc:"状态 0:禁用 1:启用"`
	PlanType     string  `v:"in:image_text,video" dc:"计划类型：image_text-图文, video-视频"`
	AdCreativeID uint64  `dc:"创意ID"`
}

// UpdateRes 更新平台计划响应
type UpdateRes struct{}

// DeleteReq 删除平台计划请求
type DeleteReq struct {
	g.Meta `path:"/delete" method:"delete" tags:"平台计划" summary:"删除平台计划"`
	Id     int64 `v:"required" dc:"计划ID"`
}

// DeleteRes 删除平台计划响应
type DeleteRes struct{}

type UpdatePlanTypeReq struct {
	g.Meta   `path:"/:id" method:"put" tags:"投放计划" summary:"更新计划类型"`
	ID       uint   `v:"required#ID不能为空" dc:"计划ID"`
	PlanType string `v:"required|in:image_text,video#计划类型不能为空|计划类型无效" dc:"计划类型:image_text(图文),video(视频)"`
}

type UpdatePlanTypeRes struct{}

// MarketTargetsReq 获取市场目标列表请求
type MarketTargetsReq struct {
	g.Meta       `path:"/market-targets" method:"get" tags:"平台计划" summary:"获取市场目标列表"`
	PlatformType string `v:"required#平台类型不能为空" dc:"平台类型"`
}

// MarketTargetsRes 获取市场目标列表响应
type MarketTargetsRes struct {
	Targets []string `json:"targets" dc:"市场目标列表"`
}
