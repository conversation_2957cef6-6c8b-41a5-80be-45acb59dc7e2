package v1

import (
	"mime/multipart"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// CreateReq 创建模型请求
type CreateReq struct {
	g.Meta `path:"/create" method:"post" tags:"模型管理" summary:"创建模型"`
	Name   string `v:"required#请输入模型名称" dc:"模型名称"`
}

// CreateRes 创建模型响应
type CreateRes struct {
	Id uint64 `json:"id" dc:"模型ID"`
}

// DeleteReq 删除模型请求
type DeleteReq struct {
	g.Meta `path:"/delete" method:"post" tags:"模型管理" summary:"删除模型"`
	Id     uint64 `v:"required#请选择要删除的模型" dc:"模型ID"`
}

// DeleteRes 删除模型响应
type DeleteRes struct{}

// UpdateReq 更新模型请求
type UpdateReq struct {
	g.Meta `path:"/update" method:"post" tags:"模型管理" summary:"更新模型"`
	Id     uint64 `v:"required#请选择要更新的模型" dc:"模型ID"`
	Name   string `v:"required#请输入模型名称" dc:"模型名称"`
}

// UpdateRes 更新模型响应
type UpdateRes struct{}

// GetReq 获取模型详情请求
type GetReq struct {
	g.Meta `path:"/get" method:"get" tags:"模型管理" summary:"获取模型详情"`
	Id     uint64 `v:"required#请选择要查看的模型" dc:"模型ID"`
}

// GetRes 获取模型详情响应
type GetRes struct {
	Model *ModelInfo `json:"model" dc:"模型信息"`
}

// ListReq 获取模型列表请求
type ListReq struct {
	g.Meta    `path:"/list" method:"get" tags:"模型管理" summary:"获取模型列表"`
	Page      int    `json:"page" d:"1" v:"min:1#页码最小值为1" dc:"当前页码"`
	PageSize  int    `json:"pageSize" d:"20" v:"max:50#每页最大50条" dc:"每页数量"`
	ModelName string `dc:"模型名称"`
}

// ListRes 获取模型列表响应
type ListRes struct {
	List  []*ModelInfo `json:"list" dc:"模型列表"`
	Total int          `json:"total" dc:"总数"`
	Page  int          `json:"page" dc:"当前页码"`
}

// ModelInfo 模型信息
type ModelInfo struct {
	Id        uint        `json:"id" dc:"模型ID"`
	Name      string      `json:"name" dc:"模型名称"`
	CreatedAt *gtime.Time `json:"created_at" dc:"创建时间"`
}

// DecayInfo 衰减记录信息
type DecayInfo struct {
	Id        uint        `json:"id" dc:"记录ID"`
	ModelId   uint        `json:"model_id" dc:"模型ID"`
	Percent   float64     `json:"percent" dc:"衰减百分比"`
	CreatedAt *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt *gtime.Time `json:"updated_at" dc:"更新时间"`
}

// ImportReq 导入模型请求
type ImportReq struct {
	g.Meta    `path:"/import" method:"post" tags:"模型管理" summary:"导入模型"`
	ModelName string                `v:"required#请输入模型名称" dc:"模型名称"`
	File      *multipart.FileHeader `v:"required#请上传Excel文件" dc:"Excel文件"`
}

// ImportRes 导入模型响应
type ImportRes struct{}
