package v1

type GetOrderTypeDataReq struct {
	Metric    string `json:"metric"`
	StartDate string `json:"start_date"`
	EndDate   string `json:"end_date"`
	UserId    string `json:"user_id,omitempty"`
	MediaId   string `json:"media_id,omitempty"`
	PlanId    string `json:"plan_id,omitempty"`
	ProductId string `json:"product_id,omitempty"`
	Type      string `json:"type,omitempty"`
	Category  string `json:"category,omitempty"`
}

type GetOrderTypeDataRes struct {
	OrderTypes []OrderTypeData `json:"order_types"`
}

type OrderTypeData struct {
	Name  string  `json:"name"`  // 订单类型名称
	Value float64 `json:"value"` // 对应的值(数量或金额)
}
