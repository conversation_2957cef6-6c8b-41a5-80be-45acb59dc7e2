package v1

import "github.com/gogf/gf/v2/frame/g"

type GetFilterOptionsReq struct {
	g.Meta `path:"/filter-options" method:"get" tags:"仪表盘" summary:"获取仪表盘筛选项数据"`
}

type GetFilterOptionsRes struct {
	ShowMediaList bool             `json:"showMediaList"` // 是否显示媒介列表
	MediaList     []*MediaListItem `json:"mediaList"`     // 媒介列表
	ProductList   []*ProductItem   `json:"productList"`   // 产品列表
}

type MediaListItem struct {
	Id   string `json:"id"`   // 媒介ID
	Name string `json:"name"` // 媒介名称
}

type MediaAccountItem struct {
	Id     string      `json:"id"`      // 媒体账号ID
	Name   string      `json:"name"`    // 媒体账号名称
	UserId string      `json:"user_id"` // 用户ID
	Plans  []*PlanItem `json:"plans"`   // 投放计划列表
}

type PlanItem struct {
	Id   string `json:"id"`   // 计划ID
	Name string `json:"name"` // 计划名称
}

type ProductItem struct {
	Id   string `json:"id"`   // 产品ID
	Name string `json:"name"` // 产品名称
}

type GetMetricsReq struct {
	g.Meta    `path:"/metrics" method:"get" tags:"仪表盘" summary:"获取指标数据"`
	UserId    int    `json:"user_id"`    // 用户ID
	StartDate string `json:"start_date"` // 开始日期,可选,默认为当天
	EndDate   string `json:"end_date"`   // 结束日期,可选,默认为当天
	MediaId   string `json:"media_id"`   // 媒体ID
	PlanId    string `json:"plan_id"`    // 计划ID
	ProductId string `json:"product_id"` // 产品ID
	Type      string `json:"type"`       // 类型 1；普通投放 2；CPS合作 3:灯火
	Category  string `json:"category"`   // 大类 alipay:支付宝 wechat:微信
}

type GetMetricsRes struct {
	Metrics map[string]*MetricItem `json:"metrics"` // 指标数据
}

// MetricItem 指标项
type MetricItem struct {
	Label      string  `json:"label"`      // 指标名称
	Value      float64 `json:"value"`      // 指标值
	Change     float64 `json:"change"`     // 环比变化
	WeekChange float64 `json:"weekChange"` // 周同比变化
	HasTrend   bool    `json:"hasTrend"`   // 是否有趋势
}

type GetMediaListReq struct {
	g.Meta   `path:"/media-list" method:"get" tags:"仪表盘" summary:"获取媒体列表"`
	UserId   string `json:"user_id" dc:"用户ID"` // 可选,按用户ID筛选
	Type     string `json:"type" dc:"投放类型"`    // 可选,1:普通投放 2:CPS合作 3:灯火
	Category string `json:"category" dc:"大类"`  // 可选,alipay:支付宝 wechat:微信 other:其他
}

type GetMediaListRes struct {
	List []*MediaListItem `json:"list"` // 媒体列表
}

type GetPlansReq struct {
	g.Meta         `path:"/plans" method:"get" tags:"仪表盘" summary:"获取投放计划列表"`
	MediaAccountId string `json:"media_account_id" v:"required" dc:"媒体账号ID"`
}

type GetPlansRes struct {
	List []*PlanItem `json:"list"` // 投放计划列表
}

type GetProductsReq struct {
	g.Meta `path:"/products" method:"get" tags:"仪表盘" summary:"获取产品列表"`
}

type GetProductsRes struct {
	List []*ProductItem `json:"list"` // 产品列表
}

type GetPlanStatsReq struct {
	g.Meta    `path:"/plan-stats" method:"get" tags:"仪表盘" summary:"获取分计划数据"`
	StartDate string `json:"start_date" v:"required" dc:"开始日期"`
	EndDate   string `json:"end_date" v:"required" dc:"结束日期"`
	UserId    string `json:"user_id" dc:"用户ID"`
	MediaId   string `json:"media_id" dc:"媒体ID"`
	PlanId    string `json:"plan_id" dc:"计划ID"`
	ProductId string `json:"product_id" dc:"产品ID"`
	Type      string `json:"type" dc:"类型 1:普通投放 2:CPS合作 3:灯火"`
	Category  string `json:"category" dc:"大类"`
}

type GetPlanStatsRes struct {
	List []*PlanStatsItem `json:"list"`
}

type PlanStatsItem struct {
	PlanId        string  `json:"plan_id"`        // 计划ID
	PlanCode      string  `json:"plan_code"`      // 计划编码
	MediaName     string  `json:"media_name"`     // 媒体名称
	ProductId     string  `json:"product_id"`     // 产品ID
	Clicks        int     `json:"clicks"`         // 点击量
	Cost          float64 `json:"cost"`           // 成本
	Orders        int     `json:"orders"`         // 订单量
	Revenue       float64 `json:"revenue"`        // 收入
	Profit        float64 `json:"profit"`         // 利润
	Roi           float64 `json:"roi"`            // ROI
	SettledProfit float64 `json:"settled_profit"` // 结算利润
	Pv            int     `json:"pv"`             // PV
	Uv            int     `json:"uv"`             // UV
}
