package v1

import "github.com/gogf/gf/v2/frame/g"

type GetRegionDataReq struct {
	g.<PERSON>a    `path:"/region" method:"get" tags:"仪表盘" summary:"获取地域分析数据"`
	Metric    string `json:"metric" v:"required|in:orders,estimated_commission,settled_commission,order_amount" dc:"指标类型"`
	StartDate string `json:"start_date" v:"date" dc:"开始日期"`
	EndDate   string `json:"end_date" v:"date" dc:"结束日期"`
	UserId    string `json:"user_id" dc:"用户ID"`
	MediaId   string `json:"media_id" dc:"媒体ID"`
	PlanId    string `json:"plan_id" dc:"计划ID"`
	ProductId string `json:"product_id" dc:"产品ID"`
	Type      string `json:"type" dc:"合作类型"`
	Category  string `json:"category" dc:"渠道分类"`
}

type GetRegionDataRes struct {
	Regions []RegionData `json:"regions"`
}

type RegionData struct {
	Name  string  `json:"name"`
	Value float64 `json:"value"`
}
