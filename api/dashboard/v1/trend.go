package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type GetTrendDataReq struct {
	g.<PERSON>a    `path:"/trend" method:"get" tags:"仪表盘" summary:"获取趋势数据"`
	UserId    string `json:"user_id" dc:"媒介ID"`
	MediaId   string `json:"media_id" dc:"媒体账号ID"`
	PlanId    string `json:"plan_id" dc:"计划ID"`
	ProductId string `json:"product_id" dc:"产品ID"`
	Metric    string `json:"metric" v:"required" dc:"指标名称"`
	StartDate string `json:"start_date" dc:"开始日期"`
	EndDate   string `json:"end_date" dc:"结束日期"`
	Type      string `json:"type" dc:"类型"`
	Category  string `json:"category" dc:"大类"`
}

type GetTrendDataRes struct {
	g.Meta     `mime:"application/json"`
	TimePoints []string  `json:"time_points" dc:"时间点"`
	Current    []float64 `json:"current" dc:"当前数据"`
	Previous   []float64 `json:"previous" dc:"前期数据"`
	SamePeriod []float64 `json:"same_period" dc:"同期数据"`
	Labels     struct {
		Current    string `json:"current" dc:"当前数据标签"`
		Previous   string `json:"previous" dc:"前期数据标签"`
		SamePeriod string `json:"same_period" dc:"同期数据标签"`
	} `json:"labels" dc:"数据标签"`
}
