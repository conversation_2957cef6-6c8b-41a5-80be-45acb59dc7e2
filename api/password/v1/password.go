package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// CreateReq 创建口令请求
type CreateReq struct {
	g.<PERSON>a     `path:"/create" method:"post" tags:"口令管理" summary:"创建口令组及口令"`
	GroupName  string         `json:"groupName" v:"required#组名称不能为空" dc:"组名称"`
	CategoryId int            `json:"categoryId" v:"required|in:55,173,284#分类ID不能为空|分类ID只能是55或173或284" dc:"分类ID(55:小红书,173:抖音,284:闪购)"`
	Passwords  []PasswordInfo `json:"passwords" v:"required#口令列表不能为空" dc:"口令列表"`
}

type PasswordInfo struct {
	Name string `json:"name" v:"required#口令名称不能为空" dc:"口令名称"`
	Pid  string `json:"pid"  v:"required#口令ID不能为空"  dc:"口令ID"`
}

// CreateRes 创建口令响应
type CreateRes struct {
	g.Meta `mime:"application/json"`
}

// ListReq 获取口令组列表请求
type ListReq struct {
	g.Meta     `path:"/list" method:"get" tags:"口令管理" summary:"获取口令组列表"`
	Page       int    `json:"page"      d:"1"  v:"min:1#页码最小值为1"     dc:"当前页码"`
	PageSize   int    `json:"pageSize"  d:"20" v:"max:1000#每页最大1000条"    dc:"每页数量"`
	GroupName  string `json:"groupName"       dc:"组名称(模糊搜索)"`
	CategoryId int64  `json:"categoryId"      dc:"分类ID(55:小红书,173:抖音,284:闪购)"`
}

// ListRes 获取口令组列表响应
type ListRes struct {
	g.Meta `mime:"application/json"`
	List   []GroupInfo `json:"list"   dc:"口令组列表"`
	Total  int         `json:"total"  dc:"总数"`
	Page   int         `json:"page"   dc:"当前页码"`
}

// GroupInfo 口令组信息
type GroupInfo struct {
	Id         int           `json:"id"        dc:"组ID"`
	Name       string        `json:"groupName"      dc:"组名称"`
	CategoryId int64         `json:"categoryId" dc:"分类id"`
	CreatedAt  *gtime.Time   `json:"createdAt" dc:"创建时间"`
	UpdatedAt  *gtime.Time   `json:"updatedAt" dc:"更新时间"`
	Passwords  []PasswordRes `json:"passwords" dc:"口令列表"`
}

// PasswordRes 口令信息响应
type PasswordRes struct {
	Id   int64  `json:"id"        dc:"口令ID"`
	Pid  string `json:"pid"       dc:"口令标识"`
	Name string `json:"name"      dc:"口令名称"`
}

// OriListReq 获取原始口令列表请求
type OriListReq struct {
	g.Meta     `path:"/oriList" method:"get" tags:"口令管理" summary:"获取原始口令列表"`
	Page       int   `json:"page"      d:"1"  v:"min:1#页码最小值为1"     dc:"当前页码"`
	PageSize   int   `json:"pageSize"  d:"20" v:"max:2000#每页最大2000条"    dc:"每页数量"`
	CategoryId []int `json:"categoryId" v:"required#分类ID不能为空" dc:"分类ID(55:小红书,173:抖音,284:闪购)"`
}

// OriListRes 获取原始口令列表响应
type OriListRes struct {
	g.Meta `mime:"application/json"`
	List   []OriPasswordInfo `json:"list"   dc:"口令列表"`
	Total  int               `json:"total"  dc:"总数"`
	Page   int               `json:"page"   dc:"当前页码"`
}

// OriPasswordInfo 原始口令信息
type OriPasswordInfo struct {
	Name string `json:"name" dc:"口令名称"`
	Pid  string `json:"pid"  dc:"口令ID"`
}

// UpdateReq 编辑口令组请求
type UpdateReq struct {
	g.Meta     `path:"/:id" method:"put" tags:"口令管理" summary:"编辑口令组"`
	Id         int           `json:"id" v:"required#请选择要编辑的口令组" dc:"口令组ID"`
	GroupName  string        `json:"groupName" v:"required#请输入口令组名称" dc:"口令组名称"`
	CategoryId int           `json:"categoryId" v:"required#请选择分类" dc:"分类ID"`
	Passwords  []PasswordRes `json:"passwords" v:"required#请添加口令" dc:"口令列表"`
}

// UpdateRes 编辑口令组响应
type UpdateRes struct {
}

// DeleteReq 删除口令组请求
type DeleteReq struct {
	g.Meta `path:"/:id" method:"delete" tags:"口令管理" summary:"删除口令组"`
	Id     int `json:"id" v:"required#请选择要删除的口令组" dc:"口令组ID"`
}

// DeleteRes 删除口令组响应
type DeleteRes struct {
	g.Meta `mime:"application/json"`
}
