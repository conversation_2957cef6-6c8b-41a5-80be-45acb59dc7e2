package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// GetPlanPvUvReq 获取计划PV/UV数据请求
type GetPlanPvUvReq struct {
	g.Meta `path:"/get-pv-uv" method:"get" tags:"计划统计" summary:"获取计划PV/UV数据"`
	PlanId int    `v:"required#计划ID不能为空" json:"plan_id" dc:"计划ID"`
	Date   string `v:"required#日期不能为空" json:"date" dc:"日期，格式：YYYY-MM-DD"`
}

// GetPlanPvUvRes 获取计划PV/UV数据响应
type GetPlanPvUvRes struct {
	PV int `json:"pv" dc:"PV数据"`
	UV int `json:"uv" dc:"UV数据"`
}
