package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// LoginReq 登录请求参数
type LoginReq struct {
	g.Meta   `path:"/auth/login" method:"post" tags:"Auth" summary:"用户登录"`
	Email    string `v:"required|email" dc:"邮箱地址"`
	Password string `v:"required" dc:"登录密码"`
}

// LoginRes 登录响应结果
type LoginRes struct {
	Token string `json:"token" dc:"JWT token"`
	User  *User  `json:"user" dc:"用户信息"`
}

// User 用户信息
type User struct {
	Id    uint   `json:"id" dc:"用户ID"`
	Email string `json:"email" dc:"邮箱"`
	Name  string `json:"name" dc:"用户名"`
}

// ChangePasswordReq 修改密码请求参数
type ChangePasswordReq struct {
	g.Meta      `path:"/auth/change-password" method:"post" tags:"Auth" summary:"修改密码"`
	OldPassword string `v:"required" dc:"旧密码"`
	NewPassword string `v:"required|min:6" dc:"新密码"`
}

// ChangePasswordRes 修改密码响应结果
type ChangePasswordRes struct {
	Message string `json:"message" dc:"操作结果"`
}

type UserInfoReq struct {
	g.Meta `path:"/auth/info" method:"get" tags:"认证" summary:"获取用户信息"`
}

type UserInfoRes struct {
	Id       int    `json:"id"       description:"用户ID"`
	Name     string `json:"name"     description:"用户名"`
	Email    string `json:"email"    description:"邮箱"`
	RealName string `json:"realName" description:"真实姓名"`
	Avatar   string `json:"avatar"   description:"头像"`
	Role     int    `json:"role"     description:"角色 1:媒介 2:运营 3:管理员 4:财务"`
	Status   int    `json:"status"   description:"状态 0:禁用 1:正常"`
}
