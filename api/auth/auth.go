// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package auth

import (
	"context"
	
	"ad-pro-v2/api/auth/v1"
)

type IAuthV1 interface {
	// Login 用户登录
	Login(ctx context.Context, req *v1.LoginReq) (res *v1.LoginRes, err error)
} 