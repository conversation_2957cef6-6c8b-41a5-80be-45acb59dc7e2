package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UploadReq 上传文件请求
type UploadReq struct {
	g.Meta `path:"/upload" method:"post" mime:"multipart/form-data" tags:"上传" summary:"上传文件"`
}

// UploadRes 上传文件响应
type UploadRes struct {
	Url string `json:"url" dc:"文件URL"`
}

// UploadImageReq 上传图片请求
type UploadImageReq struct {
	g.Meta `path:"/image" method:"post" mime:"multipart/form-data" tags:"上传" summary:"上传图片"`
}

// UploadImageRes 上传图片响应
type UploadImageRes struct {
	Url string `json:"url" dc:"图片URL"`
}
