package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// HotArea 热区结构
type HotArea struct {
	Width     int    `json:"width"`      // 宽度
	Height    int    `json:"height"`     // 高度
	Unit      string `json:"unit"`       // 单位，默认是px
	X         int    `json:"x"`          // X坐标
	Y         int    `json:"y"`          // Y坐标
	EventType int    `json:"event_type"` // 事件类型：1-确定 2-取消
	Id        int    `json:"id"`         // 热区ID
}

// Creative 创意实体
type Creative struct {
	Id              uint64    `json:"id"`               // 主键ID
	Name            string    `json:"name"`             // 创意名称
	ImageUrl        string    `json:"image_url"`        // 素材图片地址
	ImageArea       string    `json:"image_area"`       // 图片区域
	BackgroundColor string    `json:"background_color"` // 背景颜色
	HotAreas        []HotArea `json:"hotAreas"`         // 热区列表
	CreatedAt       string    `json:"createdAt"`        // 创建时间
	UpdatedAt       string    `json:"updatedAt"`        // 更新时间
}

// CreativeListReq 获取创意列表请求
type CreativeListReq struct {
	g.Meta `path:"/list" method:"get" tags:"创意管理" summary:"获取创意列表"`
	Name   string `json:"name"   description:"创意名称，支持模糊查询"`
	Page   int    `json:"page"   description:"页码" d:"1" v:"min:1#页码必须大于0"`
	Size   int    `json:"size"   description:"每页数量" d:"10" v:"min:1#每页数量必须大于0"`
}

// CreativeListRes 获取创意列表响应
type CreativeListRes struct {
	List  []*Creative `json:"list"  description:"列表"`
	Total int         `json:"total" description:"总数"`
	Page  int         `json:"page"  description:"页码"`
	Size  int         `json:"size"  description:"每页数量"`
}

// CreativeDetailReq 获取创意详情请求
type CreativeDetailReq struct {
	g.Meta `path:"/detail" method:"get" tags:"创意管理" summary:"获取创意详情"`
	Id     uint64 `json:"id" v:"required#请输入创意ID" description:"创意ID"`
}

// CreativeDetailRes 获取创意详情响应
type CreativeDetailRes struct {
	*Creative
}

// CreativeCreateReq 创建创意请求
type CreativeCreateReq struct {
	g.Meta          `path:"/create" method:"post" tags:"创意管理" summary:"创建创意"`
	Name            string    `json:"name"           v:"required#请输入创意名称" description:"创意名称"`
	ImageUrl        string    `json:"image_url"      v:"required#请上传素材图片" description:"素材图片地址"`
	ImageArea       string    `json:"image_area"     description:"图片区域"`
	BackgroundColor string    `json:"background_color" description:"背景颜色"`
	HotAreas        []HotArea `json:"hotAreas"       description:"热区列表"`
}

// CreativeCreateRes 创建创意响应
type CreativeCreateRes struct {
	Id uint64 `json:"id" description:"创意ID"`
}

// CreativeUpdateReq 更新创意请求
type CreativeUpdateReq struct {
	g.Meta          `path:"/update" method:"put" tags:"创意管理" summary:"更新创意"`
	Id              uint64    `json:"id"             v:"required#请输入创意ID" description:"创意ID"`
	Name            string    `json:"name"           v:"required#请输入创意名称" description:"创意名称"`
	ImageUrl        string    `json:"image_url"      v:"required#请上传素材图片" description:"素材图片地址"`
	ImageArea       string    `json:"image_area"     description:"图片区域"`
	BackgroundColor string    `json:"background_color" description:"背景颜色"`
	HotAreas        []HotArea `json:"hotAreas"       description:"热区列表"`
}

// CreativeUpdateRes 更新创意响应
type CreativeUpdateRes struct{}

// CreativeDeleteReq 删除创意请求
type CreativeDeleteReq struct {
	g.Meta `path:"/delete" method:"delete" tags:"创意管理" summary:"删除创意"`
	Id     uint64 `json:"id" v:"required#请输入创意ID" description:"创意ID"`
}

// CreativeDeleteRes 删除创意响应
type CreativeDeleteRes struct{}
