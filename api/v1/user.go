package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// 用户管理相关的API接口定义

// GetUsersReq 获取用户列表请求
type GetUsersReq struct {
	g.Meta     `path:"/users" method:"get" tags:"用户管理" summary:"获取用户列表"`
	Page       int    `json:"page" v:"min:1" d:"1" dc:"页码"`
	PageSize   int    `json:"page_size" v:"min:1|max:100" d:"20" dc:"每页数量"`
	Name       string `json:"name" dc:"用户名筛选"`
	Role       int    `json:"role" dc:"角色筛选"`
	Status     int    `json:"status" dc:"状态筛选"`
	Department string `json:"department" dc:"部门筛选"`
	IsLocked   int    `json:"is_locked" dc:"锁定状态筛选"`
}

// GetUsersRes 获取用户列表响应
type GetUsersRes struct {
	g.Meta `mime:"application/json"`
	List   []*UserInfo `json:"list" dc:"用户列表"`
	Total  int         `json:"total" dc:"总数"`
	Page   int         `json:"page" dc:"当前页"`
	Size   int         `json:"size" dc:"每页数量"`
}

// UserInfo 用户信息
type UserInfo struct {
	Id             uint64      `json:"id" dc:"用户ID"`
	Name           string      `json:"name" dc:"用户名"`
	RealName       string      `json:"real_name" dc:"真实姓名"`
	Email          string      `json:"email" dc:"邮箱"`
	Phone          string      `json:"phone" dc:"手机号"`
	Department     string      `json:"department" dc:"部门"`
	Position       string      `json:"position" dc:"职位"`
	SupervisorId   *uint64     `json:"supervisor_id" dc:"直属上级ID"`
	SupervisorName string      `json:"supervisor_name" dc:"直属上级姓名"`
	Role           int         `json:"role" dc:"角色"`
	RoleName       string      `json:"role_name" dc:"角色名称"`
	Status         int         `json:"status" dc:"状态"`
	IsLocked       int         `json:"is_locked" dc:"是否锁定"`
	LockReason     string      `json:"lock_reason" dc:"锁定原因"`
	Remark         string      `json:"remark" dc:"备注"`
	LastLoginAt    *gtime.Time `json:"last_login_at" dc:"最后登录时间"`
	LastLoginIp    string      `json:"last_login_ip" dc:"最后登录IP"`
	CreatedAt      *gtime.Time `json:"created_at" dc:"创建时间"`
	UpdatedAt      *gtime.Time `json:"updated_at" dc:"更新时间"`
}

// CreateUserReq 创建用户请求
type CreateUserReq struct {
	g.Meta       `path:"/users" method:"post" tags:"用户管理" summary:"创建用户"`
	Name         string  `json:"name" v:"required|length:3,50" dc:"用户名"`
	RealName     string  `json:"real_name" v:"required|length:2,20" dc:"真实姓名"`
	Email        string  `json:"email" v:"required|email" dc:"邮箱"`
	Phone        string  `json:"phone" v:"phone" dc:"手机号"`
	Department   string  `json:"department" dc:"部门"`
	Position     string  `json:"position" dc:"职位"`
	SupervisorId *uint64 `json:"supervisor_id" dc:"直属上级ID"`
	Role         int     `json:"role" v:"required|in:1,2,3,4,5" dc:"角色"`
	Password     string  `json:"password" v:"required|length:6,30" dc:"密码"`
	Remark       string  `json:"remark" dc:"备注"`
}

// CreateUserRes 创建用户响应
type CreateUserRes struct {
	g.Meta `mime:"application/json"`
	Id     uint64 `json:"id" dc:"用户ID"`
}

// UpdateUserReq 更新用户请求
type UpdateUserReq struct {
	g.Meta       `path:"/users/{id}" method:"put" tags:"用户管理" summary:"更新用户"`
	Id           uint64  `json:"id" v:"required" dc:"用户ID"`
	Name         string  `json:"name" v:"required|length:3,50" dc:"用户名"`
	RealName     string  `json:"real_name" v:"required|length:2,20" dc:"真实姓名"`
	Email        string  `json:"email" v:"required|email" dc:"邮箱"`
	Phone        string  `json:"phone" v:"phone" dc:"手机号"`
	Department   string  `json:"department" dc:"部门"`
	Position     string  `json:"position" dc:"职位"`
	SupervisorId *uint64 `json:"supervisor_id" dc:"直属上级ID"`
	Role         int     `json:"role" v:"required|in:1,2,3,4,5" dc:"角色"`
	Status       int     `json:"status" v:"required|in:1,2" dc:"状态"`
	Remark       string  `json:"remark" dc:"备注"`
}

// UpdateUserRes 更新用户响应
type UpdateUserRes struct {
	g.Meta `mime:"application/json"`
}

// DeleteUserReq 删除用户请求
type DeleteUserReq struct {
	g.Meta `path:"/users/{id}" method:"delete" tags:"用户管理" summary:"删除用户"`
	Id     uint64 `json:"id" v:"required" dc:"用户ID"`
}

// DeleteUserRes 删除用户响应
type DeleteUserRes struct {
	g.Meta `mime:"application/json"`
}

// LockUserReq 锁定用户请求
type LockUserReq struct {
	g.Meta     `path:"/users/{id}/lock" method:"post" tags:"用户管理" summary:"锁定用户"`
	Id         uint64 `json:"id" v:"required" dc:"用户ID"`
	LockReason string `json:"lock_reason" v:"required|length:1,200" dc:"锁定原因"`
}

// LockUserRes 锁定用户响应
type LockUserRes struct {
	g.Meta `mime:"application/json"`
}

// UnlockUserReq 解锁用户请求
type UnlockUserReq struct {
	g.Meta `path:"/users/{id}/unlock" method:"post" tags:"用户管理" summary:"解锁用户"`
	Id     uint64 `json:"id" v:"required" dc:"用户ID"`
}

// UnlockUserRes 解锁用户响应
type UnlockUserRes struct {
	g.Meta `mime:"application/json"`
}

// ResetPasswordReq 重置密码请求
type ResetPasswordReq struct {
	g.Meta      `path:"/users/{id}/password/reset" method:"post" tags:"用户管理" summary:"重置用户密码"`
	Id          uint64 `json:"id" v:"required" dc:"用户ID"`
	NewPassword string `json:"new_password" v:"required|length:6,30" dc:"新密码"`
}

// ResetPasswordRes 重置密码响应
type ResetPasswordRes struct {
	g.Meta `mime:"application/json"`
}

// GetUserOptionsReq 获取用户选项请求
type GetUserOptionsReq struct {
	g.Meta `path:"/users/options" method:"get" tags:"用户管理" summary:"获取用户选项"`
}

// GetUserOptionsRes 获取用户选项响应
type GetUserOptionsRes struct {
	g.Meta      `mime:"application/json"`
	Roles       []*RoleOption       `json:"roles" dc:"角色选项"`
	Departments []*DepartmentOption `json:"departments" dc:"部门选项"`
	Supervisors []*UserOption       `json:"supervisors" dc:"上级选项"`
}

// RoleOption 角色选项
type RoleOption struct {
	Value int    `json:"value" dc:"角色值"`
	Label string `json:"label" dc:"角色名称"`
}

// DepartmentOption 部门选项
type DepartmentOption struct {
	Value string `json:"value" dc:"部门名称"`
	Label string `json:"label" dc:"部门显示名"`
}

// UserOption 用户选项
type UserOption struct {
	Value uint64 `json:"value" dc:"用户ID"`
	Label string `json:"label" dc:"用户名称"`
}
