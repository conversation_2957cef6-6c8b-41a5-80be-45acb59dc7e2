package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// TaobaoPidsListReq 获取淘联链接列表请求
type TaobaoPidsListReq struct {
	g.Meta  `path:"" method:"get" summary:"获取淘联链接列表"`
	Page    int    `json:"page" d:"1" v:"min:1#页码必须大于0"`     // 页码
	Limit   int    `json:"limit" d:"10" v:"min:1#每页数量必须大于0"` // 每页数量
	Keyword string `json:"keyword"`                          // 关键词搜索
	IsUsed  int    `json:"is_used"`                          // 是否已使用
	ActType uint   `json:"act_type"`                         // 活动类型
	Sort    string `json:"sort" d:"+id"`                     // 排序
}

// TaobaoPidsListRes 获取淘联链接列表响应
type TaobaoPidsListRes struct {
	Items       interface{} `json:"items"`        // 列表数据
	Total       int         `json:"total"`        // 总数
	UsedCount   int         `json:"used_count"`   // 已使用数量
	UnusedCount int         `json:"unused_count"` // 未使用数量
}

// TaobaoPidItem 淘联链接列表项
type TaobaoPidItem struct {
	Id           uint64      `json:"id"`              // ID
	AccountName  string      `json:"account_name"`    // 联盟账号
	MemberId     uint64      `json:"member_id"`       // 会员ID
	ZoneName     string      `json:"zone_name"`       // 推广位名称
	Pid          string      `json:"pid"`             // 推广位ID
	IsUsed       int         `json:"is_used"`         // 是否使用
	UsedAt       *gtime.Time `json:"used_at"`         // 使用时间
	UsedBy       uint64      `json:"used_by"`         // 使用者ID
	AdSlotPlanId uint64      `json:"ad_slot_plan_id"` // 关联计划ID
	AdCreativeId uint64      `json:"ad_creative_id"`  // 关联创意ID
	ActType      uint        `json:"act_type"`        // 活动类型:1-飞猪 2-福利购
	CreatedAt    *gtime.Time `json:"created_at"`      // 创建时间
	UpdatedAt    *gtime.Time `json:"updated_at"`      // 更新时间
}

// TaobaoPidCreateReq 创建淘联链接请求
type TaobaoPidCreateReq struct {
	g.Meta   `path:"/create" method:"post" summary:"创建淘联链接"`
	ZoneName string `json:"zone_name" v:"required#推广位名称不能为空"` // 推广位名称
	Pid      string `json:"pid" v:"required#推广位ID不能为空"`       // 推广位ID
	ActType  uint   `json:"act_type" v:"required#活动类型不能为空"`   // 活动类型
}

// TaobaoPidCreateRes 创建淘联链接响应
type TaobaoPidCreateRes struct {
	Id uint64 `json:"id"` // 新创建的ID
}

// TaobaoPidUpdateReq 更新淘联链接请求
type TaobaoPidUpdateReq struct {
	g.Meta   `path:"/:id" method:"put" summary:"更新淘联链接"`
	ZoneName string `json:"zone_name" v:"required#推广位名称不能为空"` // 推广位名称
	Pid      string `json:"pid" v:"required#推广位ID不能为空"`       // 推广位ID
	ActType  uint   `json:"act_type" v:"required#活动类型不能为空"`   // 活动类型
}

// TaobaoPidUpdateRes 更新淘联链接响应
type TaobaoPidUpdateRes struct{}

// TaobaoPidDeleteReq 删除淘联链接请求
type TaobaoPidDeleteReq struct {
	g.Meta `path:"/:id" method:"delete" summary:"删除淘联链接"`
}

// TaobaoPidDeleteRes 删除淘联链接响应
type TaobaoPidDeleteRes struct{}
