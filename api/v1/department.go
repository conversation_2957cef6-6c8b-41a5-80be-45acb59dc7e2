package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// 部门管理相关的API接口定义

// GetDepartmentsReq 获取部门列表请求
type GetDepartmentsReq struct {
	g.Meta   `path:"/departments" method:"get" tags:"部门管理" summary:"获取部门列表"`
	ParentId *uint64 `json:"parent_id" dc:"上级部门ID，不传获取所有"`
	Status   int     `json:"status" dc:"状态筛选"`
}

// GetDepartmentsRes 获取部门列表响应
type GetDepartmentsRes struct {
	g.Meta `mime:"application/json"`
	List   []*DepartmentInfo `json:"list" dc:"部门列表"`
}

// DepartmentInfo 部门信息
type DepartmentInfo struct {
	Id          uint64            `json:"id" dc:"部门ID"`
	Name        string            `json:"name" dc:"部门名称"`
	ParentId    *uint64           `json:"parent_id" dc:"上级部门ID"`
	ParentName  string            `json:"parent_name" dc:"上级部门名称"`
	ManagerId   *uint64           `json:"manager_id" dc:"部门负责人ID"`
	ManagerName string            `json:"manager_name" dc:"部门负责人姓名"`
	Description string            `json:"description" dc:"部门描述"`
	Status      int               `json:"status" dc:"状态"`
	Sort        int               `json:"sort" dc:"排序"`
	UserCount   int               `json:"user_count" dc:"用户数量"`
	Children    []*DepartmentInfo `json:"children" dc:"子部门"`
	CreatedAt   *gtime.Time       `json:"created_at" dc:"创建时间"`
	UpdatedAt   *gtime.Time       `json:"updated_at" dc:"更新时间"`
}

// GetDepartmentTreeReq 获取部门树请求
type GetDepartmentTreeReq struct {
	g.Meta `path:"/departments/tree" method:"get" tags:"部门管理" summary:"获取部门树"`
}

// GetDepartmentTreeRes 获取部门树响应
type GetDepartmentTreeRes struct {
	g.Meta `mime:"application/json"`
	Tree   []*DepartmentInfo `json:"tree" dc:"部门树"`
}

// CreateDepartmentReq 创建部门请求
type CreateDepartmentReq struct {
	g.Meta      `path:"/departments" method:"post" tags:"部门管理" summary:"创建部门"`
	Name        string  `json:"name" v:"required|length:2,50" dc:"部门名称"`
	ParentId    *uint64 `json:"parent_id" dc:"上级部门ID"`
	ManagerId   *uint64 `json:"manager_id" dc:"部门负责人ID"`
	Description string  `json:"description" dc:"部门描述"`
	Sort        int     `json:"sort" d:"100" dc:"排序"`
}

// CreateDepartmentRes 创建部门响应
type CreateDepartmentRes struct {
	g.Meta `mime:"application/json"`
	Id     uint64 `json:"id" dc:"部门ID"`
}

// UpdateDepartmentReq 更新部门请求
type UpdateDepartmentReq struct {
	g.Meta      `path:"/departments/{id}" method:"put" tags:"部门管理" summary:"更新部门"`
	Id          uint64  `json:"id" v:"required" dc:"部门ID"`
	Name        string  `json:"name" v:"required|length:2,50" dc:"部门名称"`
	ParentId    *uint64 `json:"parent_id" dc:"上级部门ID"`
	ManagerId   *uint64 `json:"manager_id" dc:"部门负责人ID"`
	Description string  `json:"description" dc:"部门描述"`
	Status      int     `json:"status" v:"required|in:0,1" dc:"状态"`
	Sort        int     `json:"sort" d:"100" dc:"排序"`
}

// UpdateDepartmentRes 更新部门响应
type UpdateDepartmentRes struct {
	g.Meta `mime:"application/json"`
}

// DeleteDepartmentReq 删除部门请求
type DeleteDepartmentReq struct {
	g.Meta `path:"/departments/{id}" method:"delete" tags:"部门管理" summary:"删除部门"`
	Id     uint64 `json:"id" v:"required" dc:"部门ID"`
}

// DeleteDepartmentRes 删除部门响应
type DeleteDepartmentRes struct {
	g.Meta `mime:"application/json"`
}

// GetDepartmentOptionsReq 获取部门选项请求
type GetDepartmentOptionsReq struct {
	g.Meta    `path:"/departments/options" method:"get" tags:"部门管理" summary:"获取部门选项"`
	ExcludeId *uint64 `json:"exclude_id" dc:"排除的部门ID（编辑时排除自己和子部门）"`
}

// GetDepartmentOptionsRes 获取部门选项响应
type GetDepartmentOptionsRes struct {
	g.Meta `mime:"application/json"`
	List   []*DepartmentTreeOption `json:"list" dc:"部门选项"`
}

// DepartmentTreeOption 部门树选项
type DepartmentTreeOption struct {
	Value    uint64                  `json:"value" dc:"部门ID"`
	Label    string                  `json:"label" dc:"部门名称"`
	Children []*DepartmentTreeOption `json:"children" dc:"子部门"`
}
