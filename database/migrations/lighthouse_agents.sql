-- 灯火代理商账号管理表
CREATE TABLE `lighthouse_agents` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `agent_name` varchar(100) NOT NULL DEFAULT '' COMMENT '代理商名称',
  `agent_pid` varchar(100) NOT NULL DEFAULT '' COMMENT '代理商PID',
  `agent_token` text NOT NULL COMMENT '代理商Token',
  `api_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'API类型：1-直连，2-代理商封装',
  `balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '代理商余额',
  `status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '状态：1-有效，-1-失效',
  `remark` text COMMENT '备注信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_pid` (`agent_pid`) COMMENT '代理商PID唯一索引',
  KEY `idx_status` (`status`) COMMENT '状态索引',
  KEY `idx_api_type` (`api_type`) COMMENT 'API类型索引',
  KEY `idx_created_at` (`created_at`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='灯火代理商账号管理表';