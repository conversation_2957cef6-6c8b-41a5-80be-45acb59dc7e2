-- 简化的角色权限系统扩展
-- 在现有权限系统基础上，增加数据范围控制，但不创建复杂的部门权限表

-- 1. 扩展权限表，增加数据范围字段
ALTER TABLE `permissions` ADD COLUMN `data_scope` varchar(20) DEFAULT NULL COMMENT '数据范围：all-全部数据，dept-本部门数据，self-仅自己的数据' AFTER `description`;

-- 2. 更新现有权限，设置数据范围
UPDATE `permissions` SET `data_scope` = CASE 
  WHEN `module` = 'system' AND `code` LIKE '%account%' THEN 'dept'
  WHEN `module` = 'lighthouse' THEN 'dept'
  WHEN `code` LIKE '%.view' THEN 'dept'
  WHEN `code` LIKE '%.edit' THEN 'dept'
  WHEN `code` LIKE '%.delete' THEN 'dept'
  ELSE 'all'
END;

-- 3. 根据角色特性，为不同角色设置不同的数据范围权限
-- 管理层(5)：全部数据权限
UPDATE `permissions` p 
JOIN `role_permissions` rp ON p.id = rp.permission_id 
SET p.data_scope = 'all' 
WHERE rp.role = 5;

-- 4. 添加新的权限来明确数据范围
INSERT INTO `permissions` (`name`, `code`, `module`, `data_scope`, `description`) VALUES
-- 系统管理 - 不同数据范围的权限
('查看所有账号', 'system.account.view.all', 'system', 'all', '可以查看所有账号信息'),
('查看本部门账号', 'system.account.view.dept', 'system', 'dept', '只能查看本部门账号信息'),
('查看自己账号', 'system.account.view.self', 'system', 'self', '只能查看自己的账号信息'),

-- 灯火管理 - 不同数据范围的权限  
('管理所有灯火账号', 'lighthouse.account.manage.all', 'lighthouse', 'all', '可以管理所有灯火账号'),
('管理本部门灯火账号', 'lighthouse.account.manage.dept', 'lighthouse', 'dept', '只能管理本部门灯火账号'),

-- 代理商管理 - 不同数据范围的权限
('管理所有代理商', 'lighthouse.agent.manage.all', 'lighthouse', 'all', '可以管理所有代理商'),
('管理本部门代理商', 'lighthouse.agent.manage.dept', 'lighthouse', 'dept', '只能管理本部门代理商');

-- 5. 为不同角色分配对应的数据范围权限

-- 管理层(5)：全部数据权限
INSERT IGNORE INTO `role_permissions` (`role`, `permission_id`) 
SELECT 5, `id` FROM `permissions` WHERE `data_scope` = 'all';

-- 运营(2)：本部门的灯火管理权限
INSERT IGNORE INTO `role_permissions` (`role`, `permission_id`) 
SELECT 2, `id` FROM `permissions` 
WHERE `module` = 'lighthouse' AND `data_scope` IN ('dept', 'all') 
AND `code` NOT LIKE '%.delete%'; -- 运营不能删除

-- 投手(4)：本部门数据查看权限
INSERT IGNORE INTO `role_permissions` (`role`, `permission_id`) 
SELECT 4, `id` FROM `permissions` 
WHERE `data_scope` = 'dept' AND `code` LIKE '%.view%';

-- 财务(5)：所有财务相关和部门数据权限
INSERT IGNORE INTO `role_permissions` (`role`, `permission_id`) 
SELECT 5, `id` FROM `permissions` 
WHERE `module` = 'finance' OR (`data_scope` IN ('all', 'dept') AND `module` IN ('system', 'lighthouse'));

-- 媒介(1)：本部门数据查看权限
INSERT IGNORE INTO `role_permissions` (`role`, `permission_id`) 
SELECT 1, `id` FROM `permissions` 
WHERE `data_scope` = 'dept' AND `code` LIKE '%.view%';

-- 6. 创建一个视图来方便查询用户的有效权限（包含数据范围）
CREATE OR REPLACE VIEW `user_permissions_with_scope` AS
SELECT 
    u.id as user_id,
    u.name as user_name,
    u.department,
    u.role,
    p.id as permission_id,
    p.code as permission_code,
    p.name as permission_name,
    p.module,
    p.data_scope,
    CASE 
        WHEN p.data_scope = 'all' THEN '全部数据'
        WHEN p.data_scope = 'dept' THEN CONCAT('本部门数据(', COALESCE(u.department, '未设置'), ')')
        WHEN p.data_scope = 'self' THEN '仅自己的数据'
        ELSE '未定义'
    END as scope_description
FROM users u
JOIN role_permissions rp ON u.role = rp.role
JOIN permissions p ON rp.permission_id = p.id
WHERE u.status = 1 AND u.is_locked = 0;

-- 7. 添加索引优化查询
ALTER TABLE `permissions` ADD INDEX `idx_data_scope` (`data_scope`);
ALTER TABLE `permissions` ADD INDEX `idx_module_scope` (`module`, `data_scope`);

-- 8. 创建函数用于检查用户是否有权限访问特定数据
DELIMITER //
CREATE FUNCTION CheckUserDataPermission(
    user_id BIGINT,
    permission_code VARCHAR(100),
    target_department VARCHAR(100)
) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE user_dept VARCHAR(100);
    DECLARE user_role TINYINT;
    DECLARE data_scope VARCHAR(20);
    DECLARE has_permission BOOLEAN DEFAULT FALSE;
    
    -- 获取用户信息
    SELECT department, role INTO user_dept, user_role 
    FROM users 
    WHERE id = user_id AND status = 1 AND is_locked = 0;
    
    -- 检查用户是否有该权限
    SELECT p.data_scope INTO data_scope
    FROM permissions p
    JOIN role_permissions rp ON p.id = rp.permission_id
    WHERE p.code = permission_code AND rp.role = user_role
    LIMIT 1;
    
    -- 根据数据范围判断权限
    IF data_scope IS NOT NULL THEN
        CASE data_scope
            WHEN 'all' THEN SET has_permission = TRUE;
            WHEN 'dept' THEN 
                IF user_dept = target_department OR target_department IS NULL THEN 
                    SET has_permission = TRUE; 
                END IF;
            WHEN 'self' THEN 
                IF target_department IS NULL THEN 
                    SET has_permission = TRUE; 
                END IF;
        END CASE;
    END IF;
    
    RETURN has_permission;
END //
DELIMITER ;

-- 9. 示例查询：获取用户在特定模块的权限和数据范围
-- SELECT * FROM user_permissions_with_scope WHERE user_id = 1 AND module = 'system';

-- 10. 示例：检查用户是否可以查看特定部门的账号
-- SELECT CheckUserDataPermission(1, 'system.account.view', '技术部') as can_view; 