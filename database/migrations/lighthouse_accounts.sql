-- 灯火账号管理表
CREATE TABLE `lighthouse_accounts` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_name` varchar(100) NOT NULL DEFAULT '' COMMENT '账号名称',
  `account_email` varchar(100) NOT NULL DEFAULT '' COMMENT '账号邮箱',
  `subject` varchar(200) NOT NULL DEFAULT '' COMMENT '主体',
  `balance` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '账号余额',
  `merchant_mark` varchar(100) DEFAULT '' COMMENT '商家标记',
  `app_id` varchar(100) NOT NULL DEFAULT '' COMMENT '账号APPID',
  `public_key` text COMMENT '账号公钥',
  `private_key` text COMMENT '账号私钥',
  `consumption_ratio` decimal(5,2) NOT NULL DEFAULT '1.00' COMMENT '消耗比例',
  `operator_id` int(11) DEFAULT NULL COMMENT '负责投手ID（关联users表）',
  `agent_id` int(11) DEFAULT NULL COMMENT '代理商ID（关联lighthouse_agents表）',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
  `remark` text COMMENT '备注信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_email` (`account_email`) COMMENT '账号邮箱唯一索引',
  UNIQUE KEY `uk_app_id` (`app_id`) COMMENT 'APPID唯一索引',
  KEY `idx_operator_id` (`operator_id`) COMMENT '负责投手索引',
  KEY `idx_agent_id` (`agent_id`) COMMENT '代理商索引',
  KEY `idx_status` (`status`) COMMENT '状态索引',
  KEY `idx_created_at` (`created_at`) COMMENT '创建时间索引',
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='灯火账号管理表';