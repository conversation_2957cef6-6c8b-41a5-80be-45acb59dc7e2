-- 更新角色权限系统
-- 支持5个角色：管理层、运营、媒介、投手、财务

-- 1. 更新users表的role字段注释
ALTER TABLE `users` MODIFY COLUMN `role` tinyint(4) NOT NULL DEFAULT '1' COMMENT '角色 1-媒介 2-运营 3-管理层 4-投手 5-财务';

-- 2. 清空现有的角色权限配置
DELETE FROM `role_permissions`;

-- 3. 更新权限表，增加更多权限
INSERT INTO `permissions` (`name`, `code`, `module`, `description`) VALUES
-- 仪表盘
('查看仪表盘', 'dashboard.view', 'dashboard', '查看仪表盘页面'),
('查看数据统计', 'dashboard.stats', 'dashboard', '查看数据统计信息'),

-- 系统管理
('查看账号管理', 'system.account.view', 'system', '查看账号管理页面'),
('新增账号', 'system.account.create', 'system', '创建新账号'),
('编辑账号', 'system.account.edit', 'system', '编辑账号信息'),
('删除账号', 'system.account.delete', 'system', '删除账号'),
('重置密码', 'system.account.reset_password', 'system', '重置用户密码'),
('查看权限管理', 'system.permission.view', 'system', '查看权限管理页面'),
('配置角色权限', 'system.permission.config', 'system', '配置角色权限'),

-- 灯火账号管理  
('查看灯火账号', 'lighthouse.account.view', 'lighthouse', '查看灯火账号列表'),
('新增灯火账号', 'lighthouse.account.create', 'lighthouse', '创建灯火账号'),
('编辑灯火账号', 'lighthouse.account.edit', 'lighthouse', '编辑灯火账号'),
('删除灯火账号', 'lighthouse.account.delete', 'lighthouse', '删除灯火账号'),
('同步灯火账号', 'lighthouse.account.sync', 'lighthouse', '同步灯火账号数据'),

-- 代理商管理
('查看代理商', 'lighthouse.agent.view', 'lighthouse', '查看代理商列表'),
('新增代理商', 'lighthouse.agent.create', 'lighthouse', '创建代理商'),
('编辑代理商', 'lighthouse.agent.edit', 'lighthouse', '编辑代理商'),
('删除代理商', 'lighthouse.agent.delete', 'lighthouse', '删除代理商'),
('查询代理商余额', 'lighthouse.agent.balance', 'lighthouse', '查询代理商余额'),

-- 投放管理
('查看投放计划', 'campaign.plan.view', 'campaign', '查看投放计划'),
('创建投放计划', 'campaign.plan.create', 'campaign', '创建投放计划'),
('编辑投放计划', 'campaign.plan.edit', 'campaign', '编辑投放计划'),
('删除投放计划', 'campaign.plan.delete', 'campaign', '删除投放计划'),
('执行投放操作', 'campaign.operate', 'campaign', '执行投放操作'),

-- 数据报表
('查看投放报表', 'report.campaign.view', 'report', '查看投放报表'),
('导出投放报表', 'report.campaign.export', 'report', '导出投放报表'),
('查看财务报表', 'report.finance.view', 'report', '查看财务报表'),
('导出财务报表', 'report.finance.export', 'report', '导出财务报表'),

-- 财务管理
('查看账户余额', 'finance.balance.view', 'finance', '查看账户余额'),
('充值操作', 'finance.recharge', 'finance', '执行充值操作'),
('查看消费记录', 'finance.consumption.view', 'finance', '查看消费记录'),
('财务审核', 'finance.audit', 'finance', '财务审核操作')

ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `description` = VALUES(`description`);

-- 4. 创建角色表（标准化角色管理）
CREATE TABLE `roles` (
  `id` tinyint(4) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `code` varchar(50) NOT NULL COMMENT '角色编码',
  `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 5. 插入角色数据
INSERT INTO `roles` (`id`, `name`, `code`, `description`, `sort_order`, `status`) VALUES
(1, '媒介', 'MEDIA', '媒介投放人员', 1, 1),
(2, '运营', 'OPERATION', '运营推广人员', 2, 1),
(3, '管理层', 'MANAGER', '管理层人员', 3, 1),
(4, '投手', 'TRADER', '投放执行人员', 4, 1),
(5, '财务', 'FINANCE', '财务管理人员', 5, 1);

-- 6. 更新角色权限关联表，支持更灵活的配置
ALTER TABLE `role_permissions` MODIFY COLUMN `role` tinyint(4) NOT NULL COMMENT '角色：1-媒介，2-运营，3-管理层，4-投手，5-财务';

-- 7. 设置默认权限配置（可后续通过界面调整）

-- 管理层：全部权限
INSERT INTO `role_permissions` (`role`, `permission_id`) 
SELECT 3, `id` FROM `permissions`;

-- 财务：财务相关权限
INSERT INTO `role_permissions` (`role`, `permission_id`) 
SELECT 5, `id` FROM `permissions` 
WHERE `code` IN (
    'dashboard.view', 'dashboard.stats',
    'lighthouse.account.view', 'lighthouse.agent.view', 'lighthouse.agent.balance',
    'report.finance.view', 'report.finance.export',
    'finance.balance.view', 'finance.recharge', 'finance.consumption.view', 'finance.audit'
);

-- 运营：运营相关权限
INSERT INTO `role_permissions` (`role`, `permission_id`) 
SELECT 2, `id` FROM `permissions` 
WHERE `code` IN (
    'dashboard.view', 'dashboard.stats',
    'lighthouse.account.view', 'lighthouse.account.create', 'lighthouse.account.edit', 'lighthouse.account.sync',
    'lighthouse.agent.view', 'lighthouse.agent.create', 'lighthouse.agent.edit', 'lighthouse.agent.balance',
    'campaign.plan.view', 'campaign.plan.create', 'campaign.plan.edit',
    'report.campaign.view', 'report.campaign.export'
);

-- 投手：投放相关权限
INSERT INTO `role_permissions` (`role`, `permission_id`) 
SELECT 4, `id` FROM `permissions` 
WHERE `code` IN (
    'dashboard.view', 'dashboard.stats',
    'lighthouse.account.view', 'lighthouse.agent.view',
    'campaign.plan.view', 'campaign.plan.create', 'campaign.plan.edit', 'campaign.operate',
    'report.campaign.view', 'finance.balance.view'
);

-- 媒介：基础查看权限
INSERT INTO `role_permissions` (`role`, `permission_id`) 
SELECT 1, `id` FROM `permissions` 
WHERE `code` IN (
    'dashboard.view', 'dashboard.stats',
    'lighthouse.account.view', 'lighthouse.agent.view',
    'campaign.plan.view', 'report.campaign.view',
    'finance.balance.view'
);

-- 8. 创建权限管理日志表
CREATE TABLE `permission_logs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `operator_id` bigint(20) unsigned NOT NULL COMMENT '操作人ID',
  `role` tinyint(4) NOT NULL COMMENT '被操作的角色',
  `action` varchar(50) NOT NULL COMMENT '操作类型：grant-授权，revoke-撤销',
  `permission_id` int(11) unsigned NOT NULL COMMENT '权限ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_role` (`role`),
  KEY `idx_permission_id` (`permission_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_perm_log_operator` FOREIGN KEY (`operator_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_perm_log_permission` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限管理日志表'; 