-- 用户表功能增强
-- 添加建议的字段来更好地管理账号

-- 1. 添加部门管理字段
ALTER TABLE `users` ADD COLUMN `department` varchar(100) DEFAULT NULL COMMENT '部门名称' AFTER `role`;

-- 2. 添加职位字段  
ALTER TABLE `users` ADD COLUMN `position` varchar(100) DEFAULT NULL COMMENT '职位' AFTER `department`;

-- 3. 添加手机号字段
ALTER TABLE `users` ADD COLUMN `phone` varchar(20) DEFAULT NULL COMMENT '手机号码' AFTER `email`;

-- 4. 添加直属上级字段
ALTER TABLE `users` ADD COLUMN `supervisor_id` bigint(20) unsigned DEFAULT NULL COMMENT '直属上级ID' AFTER `position`;

-- 7. 添加备注字段
ALTER TABLE `users` ADD COLUMN `remark` text COMMENT '备注信息' AFTER `ding_dept_id`;

-- 8. 添加账号锁定字段
ALTER TABLE `users` ADD COLUMN `is_locked` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否锁定：0-正常，1-锁定' AFTER `status`;

-- 9. 添加锁定原因字段
ALTER TABLE `users` ADD COLUMN `lock_reason` varchar(255) DEFAULT NULL COMMENT '锁定原因' AFTER `is_locked`;

-- 10. 添加最后密码修改时间（如果不存在）
-- ALTER TABLE `users` ADD COLUMN `password_changed_at` timestamp NULL DEFAULT NULL COMMENT '最后密码修改时间' AFTER `force_change_password`;

-- 添加索引
ALTER TABLE `users` ADD INDEX `idx_department` (`department`) COMMENT '部门索引';
ALTER TABLE `users` ADD INDEX `idx_role_status` (`role`, `status`) COMMENT '角色状态组合索引';
ALTER TABLE `users` ADD INDEX `idx_supervisor_id` (`supervisor_id`) COMMENT '上级ID索引';
ALTER TABLE `users` ADD INDEX `idx_phone` (`phone`) COMMENT '手机号索引';

-- 创建部门表（可选，如果需要标准化部门管理）
CREATE TABLE `departments` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '部门名称',
  `code` varchar(50) NOT NULL COMMENT '部门编码',
  `parent_id` int(11) unsigned DEFAULT NULL COMMENT '上级部门ID',
  `manager_id` bigint(20) unsigned DEFAULT NULL COMMENT '部门负责人ID',
  `description` text COMMENT '部门描述',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dept_code` (`code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_manager_id` (`manager_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_dept_parent` FOREIGN KEY (`parent_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_dept_manager` FOREIGN KEY (`manager_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门表';

-- 插入示例部门数据
INSERT INTO `departments` (`name`, `code`, `parent_id`, `manager_id`, `description`, `sort_order`, `status`) VALUES
('技术部', 'TECH', NULL, NULL, '技术研发部门', 1, 1),
('市场部', 'MARKET', NULL, NULL, '市场推广部门', 2, 1),
('财务部', 'FINANCE', NULL, NULL, '财务管理部门', 3, 1),
('前端组', 'TECH_FE', 1, NULL, '前端开发组', 1, 1),
('后端组', 'TECH_BE', 1, NULL, '后端开发组', 2, 1),
('媒介组', 'MARKET_MEDIA', 2, NULL, '媒介投放组', 1, 1),
('运营组', 'MARKET_OPS', 2, NULL, '运营推广组', 2, 1);

-- 创建权限表（为后续角色权限管理做准备）
CREATE TABLE `permissions` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '权限名称',
  `code` varchar(100) NOT NULL COMMENT '权限编码',
  `module` varchar(50) NOT NULL COMMENT '所属模块',
  `description` text COMMENT '权限描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_permission_code` (`code`),
  KEY `idx_module` (`module`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- 创建角色权限关联表
CREATE TABLE `role_permissions` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `role` tinyint(4) NOT NULL COMMENT '角色：1-媒介，2-运营，3-管理层',
  `permission_id` int(11) unsigned NOT NULL COMMENT '权限ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permission` (`role`, `permission_id`),
  KEY `idx_role` (`role`),
  KEY `idx_permission_id` (`permission_id`),
  CONSTRAINT `fk_role_permission` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- 插入基础权限数据
INSERT INTO `permissions` (`name`, `code`, `module`, `description`) VALUES
-- 系统管理
('查看账号管理', 'system.account.view', 'system', '查看账号管理页面'),
('新增账号', 'system.account.create', 'system', '创建新账号'),
('编辑账号', 'system.account.edit', 'system', '编辑账号信息'),
('删除账号', 'system.account.delete', 'system', '删除账号'),
('重置密码', 'system.account.reset_password', 'system', '重置用户密码'),

-- 灯火账号管理  
('查看灯火账号', 'lighthouse.account.view', 'lighthouse', '查看灯火账号列表'),
('新增灯火账号', 'lighthouse.account.create', 'lighthouse', '创建灯火账号'),
('编辑灯火账号', 'lighthouse.account.edit', 'lighthouse', '编辑灯火账号'),
('删除灯火账号', 'lighthouse.account.delete', 'lighthouse', '删除灯火账号'),
('同步灯火账号', 'lighthouse.account.sync', 'lighthouse', '同步灯火账号数据'),

-- 代理商管理
('查看代理商', 'lighthouse.agent.view', 'lighthouse', '查看代理商列表'),
('新增代理商', 'lighthouse.agent.create', 'lighthouse', '创建代理商'),
('编辑代理商', 'lighthouse.agent.edit', 'lighthouse', '编辑代理商'),
('删除代理商', 'lighthouse.agent.delete', 'lighthouse', '删除代理商'),
('查询代理商余额', 'lighthouse.agent.balance', 'lighthouse', '查询代理商余额');

-- 分配默认权限
-- 管理层：所有权限
INSERT INTO `role_permissions` (`role`, `permission_id`) 
SELECT 3, `id` FROM `permissions`;

-- 运营：灯火账号和代理商管理权限
INSERT INTO `role_permissions` (`role`, `permission_id`) 
SELECT 2, `id` FROM `permissions` WHERE `module` = 'lighthouse';

-- 媒介：查看权限
INSERT INTO `role_permissions` (`role`, `permission_id`) 
SELECT 1, `id` FROM `permissions` WHERE `code` LIKE '%.view'; 