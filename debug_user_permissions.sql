-- 调试用户13的权限问题

-- 1. 查看用户基本信息
SELECT 
    u.id, u.name, u.email, u.role_id,
    r.name as role_name, r.code as role_code
FROM users u 
LEFT JOIN roles r ON u.role_id = r.id 
WHERE u.id = 13;

-- 2. 查看该用户应该有的所有权限
SELECT 
    p.code as permission_code,
    p.name as permission_name,
    p.module,
    p.type
FROM users u
JOIN roles r ON u.role_id = r.id
JOIN role_permissions rp ON r.id = rp.role_id  
JOIN permissions p ON rp.permission_id = p.id
WHERE u.id = 13
ORDER BY p.module, p.code;

-- 3. 特别检查plan模块权限
SELECT 
    'plan权限检查' as title,
    p.code,
    p.name,
    CASE WHEN rp.permission_id IS NOT NULL THEN '✅已分配' ELSE '❌未分配' END as status
FROM permissions p
LEFT JOIN (
    SELECT rp.permission_id 
    FROM users u
    JOIN roles r ON u.role_id = r.id
    JOIN role_permissions rp ON r.id = rp.role_id
    WHERE u.id = 13
) rp ON p.id = rp.permission_id
WHERE p.module = 'plan'
ORDER BY p.code; 