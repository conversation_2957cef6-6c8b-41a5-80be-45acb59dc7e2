{"level":"error","ts":1750679796.193563,"caller":"bootstrap/redis.go:34","msg":"Redis连接失败","error":"dial tcp: lookup  127.0.0.1: i/o timeout"}
{"level":"info","ts":1750679796.1994932,"caller":"bootstrap/job.go:128","msg":"非生产环境，定时任务未注册"}
{"level":"info","ts":1750679798.207291,"caller":"bootstrap/cmd.go:74","msg":"web 服务启动成功"}
{"level":"warn","ts":1750680932.3154528,"caller":"service/xhs_creative_report_svc.go:763","msg":"获取最后更新时间失败","error":"sql: Scan error on column index 0, name \"MAX(updated_at)\": unsupported Scan, storing driver.Value type <nil> into type *time.Time"}
{"level":"warn","ts":1750680949.2380328,"caller":"service/xhs_creative_report_svc.go:763","msg":"获取最后更新时间失败","error":"sql: Scan error on column index 0, name \"MAX(updated_at)\": unsupported Scan, storing driver.Value type <nil> into type *time.Time"}
{"level":"warn","ts":1750681004.460277,"caller":"service/xhs_creative_report_svc.go:763","msg":"获取最后更新时间失败","error":"sql: Scan error on column index 0, name \"MAX(updated_at)\": unsupported Scan, storing driver.Value type <nil> into type *time.Time"}
{"level":"warn","ts":1750681169.450722,"caller":"service/xhs_creative_report_svc.go:763","msg":"获取最后更新时间失败","error":"sql: Scan error on column index 0, name \"MAX(updated_at)\": unsupported Scan, storing driver.Value type <nil> into type *time.Time"}
{"level":"warn","ts":1750681170.0865111,"caller":"service/xhs_creative_report_svc.go:763","msg":"获取最后更新时间失败","error":"sql: Scan error on column index 0, name \"MAX(updated_at)\": unsupported Scan, storing driver.Value type <nil> into type *time.Time"}
{"level":"warn","ts":1750681320.1568708,"caller":"service/xhs_creative_report_svc.go:763","msg":"获取最后更新时间失败","error":"sql: Scan error on column index 0, name \"MAX(updated_at)\": unsupported Scan, storing driver.Value type <nil> into type *time.Time"}
{"level":"warn","ts":1750681320.787599,"caller":"service/xhs_creative_report_svc.go:763","msg":"获取最后更新时间失败","error":"sql: Scan error on column index 0, name \"MAX(updated_at)\": unsupported Scan, storing driver.Value type <nil> into type *time.Time"}
{"level":"warn","ts":1750681618.2509341,"caller":"service/xhs_creative_report_svc.go:763","msg":"获取最后更新时间失败","error":"sql: Scan error on column index 0, name \"MAX(updated_at)\": unsupported Scan, storing driver.Value type <nil> into type *time.Time"}
{"level":"warn","ts":1750681622.3440142,"caller":"service/xhs_creative_report_svc.go:763","msg":"获取最后更新时间失败","error":"sql: Scan error on column index 0, name \"MAX(updated_at)\": unsupported Scan, storing driver.Value type <nil> into type *time.Time"}
{"level":"warn","ts":1750681633.613558,"caller":"service/xhs_creative_report_svc.go:763","msg":"获取最后更新时间失败","error":"sql: Scan error on column index 0, name \"MAX(updated_at)\": unsupported Scan, storing driver.Value type <nil> into type *time.Time"}
{"level":"warn","ts":1750681824.600733,"caller":"service/xhs_creative_report_svc.go:763","msg":"获取最后更新时间失败","error":"sql: Scan error on column index 0, name \"MAX(updated_at)\": unsupported Scan, storing driver.Value type <nil> into type *time.Time"}
{"level":"warn","ts":1750681832.087347,"caller":"service/xhs_creative_report_svc.go:763","msg":"获取最后更新时间失败","error":"sql: Scan error on column index 0, name \"MAX(updated_at)\": unsupported Scan, storing driver.Value type <nil> into type *time.Time"}
{"level":"info","ts":1750682819.866005,"caller":"bootstrap/cmd.go:81","msg":"正在尝试关闭 web 服务 ..."}
{"level":"info","ts":1750682819.869298,"caller":"bootstrap/cmd.go:88","msg":"关闭 web 服务成功"}
{"level":"error","ts":1750682831.630673,"caller":"bootstrap/redis.go:34","msg":"Redis连接失败","error":"dial tcp: lookup  127.0.0.1: i/o timeout"}
{"level":"info","ts":1750682831.6340601,"caller":"bootstrap/job.go:128","msg":"非生产环境，定时任务未注册"}
{"level":"info","ts":1750682833.635744,"caller":"bootstrap/cmd.go:74","msg":"web 服务启动成功"}
{"level":"info","ts":1750682906.300813,"caller":"bootstrap/cmd.go:81","msg":"正在尝试关闭 web 服务 ..."}
{"level":"info","ts":1750682906.3013499,"caller":"bootstrap/cmd.go:88","msg":"关闭 web 服务成功"}
{"level":"error","ts":1750682914.957833,"caller":"bootstrap/redis.go:34","msg":"Redis连接失败","error":"dial tcp: lookup  127.0.0.1: no such host"}
{"level":"info","ts":1750682915.014199,"caller":"bootstrap/job.go:128","msg":"非生产环境，定时任务未注册"}
{"level":"info","ts":1750682917.027296,"caller":"bootstrap/cmd.go:74","msg":"web 服务启动成功"}
{"level":"info","ts":1750682950.2565958,"caller":"bootstrap/cmd.go:81","msg":"正在尝试关闭 web 服务 ..."}
{"level":"info","ts":1750682950.257104,"caller":"bootstrap/cmd.go:88","msg":"关闭 web 服务成功"}
{"level":"error","ts":1750682956.114054,"caller":"bootstrap/redis.go:34","msg":"Redis连接失败","error":"dial tcp: lookup  127.0.0.1: no such host"}
{"level":"info","ts":1750682956.1185842,"caller":"bootstrap/job.go:128","msg":"非生产环境，定时任务未注册"}
{"level":"info","ts":1750682958.120237,"caller":"bootstrap/cmd.go:74","msg":"web 服务启动成功"}
{"level":"info","ts":1750731919.662534,"caller":"bootstrap/cmd.go:81","msg":"正在尝试关闭 web 服务 ..."}
{"level":"info","ts":1750731919.665427,"caller":"bootstrap/cmd.go:88","msg":"关闭 web 服务成功"}
{"level":"error","ts":1750731948.3944142,"caller":"bootstrap/redis.go:34","msg":"Redis连接失败","error":"dial tcp: lookup  127.0.0.1: no such host"}
{"level":"info","ts":1750731948.398914,"caller":"bootstrap/job.go:128","msg":"非生产环境，定时任务未注册"}
{"level":"info","ts":1750731950.400616,"caller":"bootstrap/cmd.go:74","msg":"web 服务启动成功"}
{"level":"info","ts":1750734466.244925,"caller":"bootstrap/cmd.go:81","msg":"正在尝试关闭 web 服务 ..."}
{"level":"info","ts":1750734466.252405,"caller":"bootstrap/cmd.go:88","msg":"关闭 web 服务成功"}
{"level":"error","ts":1750734478.8817751,"caller":"bootstrap/redis.go:34","msg":"Redis连接失败","error":"dial tcp: lookup  127.0.0.1: no such host"}
{"level":"info","ts":1750734478.889212,"caller":"bootstrap/job.go:128","msg":"非生产环境，定时任务未注册"}
{"level":"info","ts":**********.8924842,"caller":"bootstrap/cmd.go:74","msg":"web 服务启动成功"}
{"level":"info","ts":**********.894762,"caller":"service/xhs_auth_svc.go:26","msg":"开始生成小红书授权链接"}
{"level":"info","ts":**********.895203,"caller":"service/xhs_auth_svc.go:65","msg":"小红书授权链接生成成功","auth_url":"https://ad-market.xiaohongshu.com/auth?appId=your-xiaohongshu-app-id&redirectUri=http%3A%2F%2Fwww.baidu.com&scope=%5B%22report_service%22%2C%22ad_query%22%2C%22ad_manage%22%2C%22account_manage%22%5D&state=xhs_auth_state_abcd","app_id":"your-xiaohongshu-app-id","redirect_uri":"http://www.baidu.com","state":"xhs_auth_state_abcd"}
{"level":"info","ts":**********.381284,"caller":"bootstrap/cmd.go:81","msg":"正在尝试关闭 web 服务 ..."}
{"level":"info","ts":**********.382338,"caller":"bootstrap/cmd.go:88","msg":"关闭 web 服务成功"}
{"level":"error","ts":**********.5534909,"caller":"bootstrap/redis.go:34","msg":"Redis连接失败","error":"dial tcp: lookup  127.0.0.1: i/o timeout"}
{"level":"info","ts":**********.558062,"caller":"bootstrap/job.go:128","msg":"非生产环境，定时任务未注册"}
{"level":"info","ts":**********.5616388,"caller":"bootstrap/cmd.go:74","msg":"web 服务启动成功"}
{"level":"info","ts":**********.069349,"caller":"service/xhs_auth_svc.go:26","msg":"开始生成小红书授权链接"}
{"level":"info","ts":**********.069653,"caller":"service/xhs_auth_svc.go:65","msg":"小红书授权链接生成成功","auth_url":"https://ad-market.xiaohongshu.com/auth?appId=1234&redirectUri=http%3A%2F%2Fwww.baidu.com&scope=%5B%22report_service%22%2C%22ad_query%22%2C%22ad_manage%22%2C%22account_manage%22%5D&state=xhs_auth_state_abcd","app_id":"1234","redirect_uri":"http://www.baidu.com","state":"xhs_auth_state_abcd"}
{"level":"info","ts":**********.6142159,"caller":"service/xhs_auth_svc.go:26","msg":"开始生成小红书授权链接"}
{"level":"info","ts":**********.614848,"caller":"service/xhs_auth_svc.go:65","msg":"小红书授权链接生成成功","auth_url":"https://ad-market.xiaohongshu.com/auth?appId=1234&redirectUri=http%3A%2F%2Fwww.baidu.com&scope=%5B%22report_service%22%2C%22ad_query%22%2C%22ad_manage%22%2C%22account_manage%22%5D&state=xhs_auth_state_abcd","app_id":"1234","redirect_uri":"http://www.baidu.com","state":"xhs_auth_state_abcd"}
{"level":"info","ts":**********.922776,"caller":"service/xhs_auth_svc.go:26","msg":"开始生成小红书授权链接"}
{"level":"info","ts":**********.922996,"caller":"service/xhs_auth_svc.go:65","msg":"小红书授权链接生成成功","auth_url":"https://ad-market.xiaohongshu.com/auth?appId=1234&redirectUri=http%3A%2F%2Fwww.baidu.com&scope=%5B%22report_service%22%2C%22ad_query%22%2C%22ad_manage%22%2C%22account_manage%22%5D&state=xhs_auth_state_abcd","app_id":"1234","redirect_uri":"http://www.baidu.com","state":"xhs_auth_state_abcd"}
{"level":"info","ts":**********.9304729,"caller":"service/xhs_auth_svc.go:26","msg":"开始生成小红书授权链接"}
{"level":"info","ts":**********.9311922,"caller":"service/xhs_auth_svc.go:65","msg":"小红书授权链接生成成功","auth_url":"https://ad-market.xiaohongshu.com/auth?appId=1234&redirectUri=http%3A%2F%2Fwww.baidu.com&scope=%5B%22report_service%22%2C%22ad_query%22%2C%22ad_manage%22%2C%22account_manage%22%5D&state=xhs_auth_state_abcd","app_id":"1234","redirect_uri":"http://www.baidu.com","state":"xhs_auth_state_abcd"}
{"level":"info","ts":**********.935134,"caller":"bootstrap/cmd.go:81","msg":"正在尝试关闭 web 服务 ..."}
{"level":"info","ts":**********.935895,"caller":"bootstrap/cmd.go:88","msg":"关闭 web 服务成功"}
{"level":"error","ts":**********.5163462,"caller":"bootstrap/redis.go:34","msg":"Redis连接失败","error":"dial tcp: lookup  127.0.0.1: i/o timeout"}
{"level":"info","ts":**********.523011,"caller":"bootstrap/job.go:128","msg":"非生产环境，定时任务未注册"}
{"level":"info","ts":**********.3427992,"caller":"bootstrap/cmd.go:74","msg":"web 服务启动成功"}
{"level":"info","ts":**********.3428,"caller":"service/xhs_auth_svc.go:76","msg":"开始生成自定义参数的小红书授权链接","custom_scopes":[],"custom_state":"1"}
{"level":"info","ts":**********.343822,"caller":"service/xhs_auth_svc.go:122","msg":"自定义小红书授权链接生成成功","auth_url":"https://ad-market.xiaohongshu.com/auth?appId=1234&redirectUri=http%3A%2F%2Fwww.baidu.com&scope=%5B%22report_service%22%2C%22ad_query%22%2C%22ad_manage%22%2C%22account_manage%22%5D&state=1","app_id":"1234","redirect_uri":"http://www.baidu.com","state":"1"}
{"level":"warn","ts":**********.2995799,"caller":"service/xhs_creative_report_svc.go:763","msg":"获取最后更新时间失败","error":"sql: Scan error on column index 0, name \"MAX(updated_at)\": unsupported Scan, storing driver.Value type <nil> into type *time.Time"}
{"level":"warn","ts":**********.4209569,"caller":"service/xhs_creative_report_svc.go:763","msg":"获取最后更新时间失败","error":"sql: Scan error on column index 0, name \"MAX(updated_at)\": unsupported Scan, storing driver.Value type <nil> into type *time.Time"}
{"level":"error","ts":**********.054592,"caller":"controller/ad_plan_list_ctrl.go:164","msg":"获取计划统计失败","error":"查询计划统计失败: Error 1146 (42S02): Table 'adpro_test.ad_plan_stats' doesn't exist"}
{"level":"error","ts":**********.054692,"caller":"controller/ad_plan_list_ctrl.go:99","msg":"获取计划列表失败","error":"统计计划总数失败: Error 1146 (42S02): Table 'adpro_test.ad_plan_stats' doesn't exist"}
{"level":"error","ts":1750737523.830611,"caller":"controller/ad_plan_list_ctrl.go:99","msg":"获取计划列表失败","error":"统计计划总数失败: Error 1146 (42S02): Table 'adpro_test.ad_plan_stats' doesn't exist"}
{"level":"error","ts":1750737524.296586,"caller":"controller/ad_plan_list_ctrl.go:164","msg":"获取计划统计失败","error":"查询计划统计失败: Error 1146 (42S02): Table 'adpro_test.ad_plan_stats' doesn't exist"}
{"level":"error","ts":1750737561.2730591,"caller":"controller/ad_plan_list_ctrl.go:164","msg":"获取计划统计失败","error":"查询计划统计失败: sql: Scan error on column index 1, name \"total_cost\": converting NULL to float64 is unsupported"}
{"level":"warn","ts":1750737570.0857449,"caller":"service/xhs_creative_report_svc.go:763","msg":"获取最后更新时间失败","error":"sql: Scan error on column index 0, name \"MAX(updated_at)\": unsupported Scan, storing driver.Value type <nil> into type *time.Time"}
{"level":"error","ts":1750737570.924746,"caller":"controller/ad_plan_list_ctrl.go:164","msg":"获取计划统计失败","error":"查询计划统计失败: sql: Scan error on column index 1, name \"total_cost\": converting NULL to float64 is unsupported"}
{"level":"error","ts":1750737581.45898,"caller":"controller/ad_plan_list_ctrl.go:164","msg":"获取计划统计失败","error":"查询计划统计失败: sql: Scan error on column index 1, name \"total_cost\": converting NULL to float64 is unsupported"}
{"level":"error","ts":1750737585.178331,"caller":"controller/ad_plan_list_ctrl.go:164","msg":"获取计划统计失败","error":"查询计划统计失败: sql: Scan error on column index 1, name \"total_cost\": converting NULL to float64 is unsupported"}
{"level":"error","ts":1750737586.6765351,"caller":"controller/ad_plan_list_ctrl.go:164","msg":"获取计划统计失败","error":"查询计划统计失败: sql: Scan error on column index 1, name \"total_cost\": converting NULL to float64 is unsupported"}
{"level":"error","ts":1750737590.25198,"caller":"controller/ad_plan_list_ctrl.go:164","msg":"获取计划统计失败","error":"查询计划统计失败: sql: Scan error on column index 1, name \"total_cost\": converting NULL to float64 is unsupported"}
{"level":"error","ts":1750737597.565286,"caller":"controller/ad_plan_list_ctrl.go:164","msg":"获取计划统计失败","error":"查询计划统计失败: sql: Scan error on column index 1, name \"total_cost\": converting NULL to float64 is unsupported"}
{"level":"error","ts":1750737598.824991,"caller":"controller/ad_plan_list_ctrl.go:164","msg":"获取计划统计失败","error":"查询计划统计失败: sql: Scan error on column index 1, name \"total_cost\": converting NULL to float64 is unsupported"}
{"level":"error","ts":1750738025.83872,"caller":"controller/ad_plan_list_ctrl.go:164","msg":"获取计划统计失败","error":"查询计划统计失败: sql: Scan error on column index 1, name \"total_cost\": converting NULL to float64 is unsupported"}
{"level":"error","ts":1750738031.132743,"caller":"controller/ad_plan_list_ctrl.go:164","msg":"获取计划统计失败","error":"查询计划统计失败: sql: Scan error on column index 1, name \"total_cost\": converting NULL to float64 is unsupported"}
{"level":"error","ts":1750738051.849154,"caller":"controller/ad_plan_list_ctrl.go:164","msg":"获取计划统计失败","error":"查询计划统计失败: sql: Scan error on column index 1, name \"total_cost\": converting NULL to float64 is unsupported"}
{"level":"info","ts":1750739018.658802,"caller":"bootstrap/cmd.go:81","msg":"正在尝试关闭 web 服务 ..."}
{"level":"info","ts":1750739018.661257,"caller":"bootstrap/cmd.go:88","msg":"关闭 web 服务成功"}
{"level":"error","ts":1750739028.136631,"caller":"bootstrap/redis.go:34","msg":"Redis连接失败","error":"dial tcp: lookup  127.0.0.1: no such host"}
{"level":"info","ts":1750739028.1445591,"caller":"bootstrap/job.go:128","msg":"非生产环境，定时任务未注册"}
{"level":"info","ts":1750739030.1462789,"caller":"bootstrap/cmd.go:74","msg":"web 服务启动成功"}
{"level":"error","ts":1750739094.395413,"caller":"controller/ad_plan_list_ctrl.go:164","msg":"获取计划统计失败","error":"查询计划统计失败: sql: Scan error on column index 1, name \"total_cost\": converting NULL to float64 is unsupported"}
{"level":"error","ts":1750739114.06817,"caller":"controller/ad_plan_list_ctrl.go:164","msg":"获取计划统计失败","error":"查询计划统计失败: sql: Scan error on column index 1, name \"total_cost\": converting NULL to float64 is unsupported"}
{"level":"error","ts":1750739118.975936,"caller":"controller/ad_plan_list_ctrl.go:164","msg":"获取计划统计失败","error":"查询计划统计失败: sql: Scan error on column index 1, name \"total_cost\": converting NULL to float64 is unsupported"}
{"level":"error","ts":1750739863.997797,"caller":"controller/password_list_ctrl.go:164","msg":"获取口令统计失败","error":"查询口令统计失败: sql: Scan error on column index 1, name \"total_consumption\": converting NULL to float64 is unsupported"}
{"level":"error","ts":1750739864.032809,"caller":"controller/password_list_ctrl.go:99","msg":"获取口令列表失败","error":"查询口令统计数据失败: Error 1054 (42S22): Unknown column 'password_name' in 'order clause'"}
{"level":"info","ts":1750739872.23523,"caller":"bootstrap/cmd.go:81","msg":"正在尝试关闭 web 服务 ..."}
{"level":"info","ts":1750739872.238776,"caller":"bootstrap/cmd.go:88","msg":"关闭 web 服务成功"}
{"level":"error","ts":1750739885.372309,"caller":"bootstrap/redis.go:34","msg":"Redis连接失败","error":"dial tcp: lookup  127.0.0.1: i/o timeout"}
{"level":"info","ts":1750739885.376215,"caller":"bootstrap/job.go:128","msg":"非生产环境，定时任务未注册"}
{"level":"info","ts":1750739887.381118,"caller":"bootstrap/cmd.go:74","msg":"web 服务启动成功"}
{"level":"error","ts":1750739908.181479,"caller":"controller/password_list_ctrl.go:164","msg":"获取口令统计失败","error":"查询口令统计失败: sql: Scan error on column index 1, name \"total_consumption\": converting NULL to float64 is unsupported"}
{"level":"error","ts":1750739908.2151418,"caller":"controller/password_list_ctrl.go:99","msg":"获取口令列表失败","error":"查询口令统计数据失败: Error 1054 (42S22): Unknown column 'password_name' in 'order clause'"}
{"level":"error","ts":1750739915.122187,"caller":"controller/password_list_ctrl.go:164","msg":"获取口令统计失败","error":"查询口令统计失败: sql: Scan error on column index 1, name \"total_consumption\": converting NULL to float64 is unsupported"}
{"level":"error","ts":1750739915.142245,"caller":"controller/password_list_ctrl.go:99","msg":"获取口令列表失败","error":"查询口令统计数据失败: Error 1054 (42S22): Unknown column 'password_name' in 'order clause'"}
{"level":"info","ts":1750740092.356638,"caller":"bootstrap/cmd.go:81","msg":"正在尝试关闭 web 服务 ..."}
{"level":"info","ts":1750740092.357862,"caller":"bootstrap/cmd.go:88","msg":"关闭 web 服务成功"}
{"level":"error","ts":1750740111.520843,"caller":"bootstrap/redis.go:34","msg":"Redis连接失败","error":"dial tcp: lookup  127.0.0.1: i/o timeout"}
{"level":"info","ts":1750740111.5300071,"caller":"bootstrap/job.go:128","msg":"非生产环境，定时任务未注册"}
{"level":"info","ts":1750740113.531988,"caller":"bootstrap/cmd.go:74","msg":"web 服务启动成功"}
{"level":"error","ts":1750740115.411694,"caller":"controller/password_list_ctrl.go:164","msg":"获取口令统计失败","error":"查询口令统计失败: sql: Scan error on column index 1, name \"total_consumption\": converting NULL to float64 is unsupported"}
{"level":"error","ts":1750740125.802917,"caller":"controller/password_list_ctrl.go:164","msg":"获取口令统计失败","error":"查询口令统计失败: sql: Scan error on column index 1, name \"total_consumption\": converting NULL to float64 is unsupported"}
{"level":"info","ts":**********.705483,"caller":"service/xhs_auth_svc.go:76","msg":"开始生成自定义参数的小红书授权链接","custom_scopes":[],"custom_state":"1"}
{"level":"info","ts":**********.716806,"caller":"service/xhs_auth_svc.go:122","msg":"自定义小红书授权链接生成成功","auth_url":"https://ad-market.xiaohongshu.com/auth?appId=1234&redirectUri=http%3A%2F%2Fwww.baidu.com&scope=%5B%22report_service%22%2C%22ad_query%22%2C%22ad_manage%22%2C%22account_manage%22%5D&state=1","app_id":"1234","redirect_uri":"http://www.baidu.com","state":"1"}
{"level":"info","ts":**********.931898,"caller":"bootstrap/cmd.go:81","msg":"正在尝试关闭 web 服务 ..."}
{"level":"info","ts":**********.933522,"caller":"bootstrap/cmd.go:88","msg":"关闭 web 服务成功"}
{"level":"error","ts":**********.595818,"caller":"bootstrap/redis.go:34","msg":"Redis连接失败","error":"dial tcp: lookup  127.0.0.1: i/o timeout"}
{"level":"info","ts":**********.6029959,"caller":"bootstrap/job.go:128","msg":"非生产环境，定时任务未注册"}
{"level":"info","ts":**********.605129,"caller":"bootstrap/cmd.go:74","msg":"web 服务启动成功"}
{"level":"info","ts":**********.511876,"caller":"service/xhs_auth_svc.go:76","msg":"开始生成自定义参数的小红书授权链接","custom_scopes":[],"custom_state":"2"}
{"level":"info","ts":**********.512257,"caller":"service/xhs_auth_svc.go:122","msg":"自定义小红书授权链接生成成功","auth_url":"https://ad-market.xiaohongshu.com/auth?appId=3946&redirectUri=http%3A%2F%2Fwww.baidu.com&scope=%5B%22report_service%22%2C%22ad_query%22%2C%22ad_manage%22%2C%22account_manage%22%5D&state=2","app_id":"3946","redirect_uri":"http://www.baidu.com","state":"2"}
{"level":"info","ts":**********.618782,"caller":"bootstrap/cmd.go:81","msg":"正在尝试关闭 web 服务 ..."}
{"level":"info","ts":**********.621813,"caller":"bootstrap/cmd.go:88","msg":"关闭 web 服务成功"}
{"level":"error","ts":**********.793227,"caller":"bootstrap/redis.go:34","msg":"Redis连接失败","error":"dial tcp: lookup  127.0.0.1: i/o timeout"}
{"level":"info","ts":**********.816867,"caller":"bootstrap/job.go:128","msg":"非生产环境，定时任务未注册"}
{"level":"info","ts":**********.818381,"caller":"bootstrap/cmd.go:74","msg":"web 服务启动成功"}
{"level":"info","ts":**********.1231492,"caller":"bootstrap/cmd.go:81","msg":"正在尝试关闭 web 服务 ..."}
{"level":"info","ts":**********.1241229,"caller":"bootstrap/cmd.go:88","msg":"关闭 web 服务成功"}
{"level":"error","ts":**********.479918,"caller":"bootstrap/redis.go:34","msg":"Redis连接失败","error":"dial tcp: lookup  127.0.0.1: no such host"}
{"level":"info","ts":**********.485122,"caller":"bootstrap/job.go:128","msg":"非生产环境，定时任务未注册"}
{"level":"info","ts":1750745372.4875278,"caller":"bootstrap/cmd.go:74","msg":"web 服务启动成功"}
{"level":"warn","ts":1750745425.45392,"caller":"service/xhs_creative_report_svc.go:763","msg":"获取最后更新时间失败","error":"sql: Scan error on column index 0, name \"MAX(updated_at)\": unsupported Scan, storing driver.Value type <nil> into type *time.Time"}
{"level":"info","ts":1750745447.7480981,"caller":"bootstrap/cmd.go:81","msg":"正在尝试关闭 web 服务 ..."}
{"level":"info","ts":1750745447.751423,"caller":"bootstrap/cmd.go:88","msg":"关闭 web 服务成功"}
{"level":"error","ts":1750745459.529614,"caller":"bootstrap/redis.go:34","msg":"Redis连接失败","error":"dial tcp: lookup  127.0.0.1: i/o timeout"}
{"level":"info","ts":1750745459.536436,"caller":"bootstrap/job.go:128","msg":"非生产环境，定时任务未注册"}
{"level":"info","ts":**********.538107,"caller":"bootstrap/cmd.go:74","msg":"web 服务启动成功"}
{"level":"info","ts":**********.654403,"caller":"service/xhs_auth_svc.go:26","msg":"开始生成小红书授权链接"}
{"level":"info","ts":**********.654675,"caller":"service/xhs_auth_svc.go:65","msg":"小红书授权链接生成成功","auth_url":"https://ad-market.xiaohongshu.com/auth?appId=3946&redirectUri=http%3A%2F%2Fwww.baidu.com&scope=%5B%22report_service%22%2C%22ad_query%22%2C%22ad_manage%22%2C%22account_manage%22%5D&state=xhs_auth_state_","app_id":"3946","redirect_uri":"http://www.baidu.com","state":"xhs_auth_state_"}
{"level":"info","ts":**********.8290932,"caller":"bootstrap/cmd.go:81","msg":"正在尝试关闭 web 服务 ..."}
{"level":"info","ts":**********.830581,"caller":"bootstrap/cmd.go:88","msg":"关闭 web 服务成功"}
