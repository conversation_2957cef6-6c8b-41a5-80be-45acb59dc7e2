基于 GoFrame 框架的项目脚手架,遵循 GoFrame 推荐的工程设计规范。

## 项目架构

### 分层设计

采用改进的三层架构(3-Tier Architecture)设计模式,通过分层解耦提高系统的灵活性和可维护性:

- **表示层(UI)**
  - 处理HTTP请求响应
  - 参数校验和响应封装
  - 不包含业务逻辑
  - 对应 `api` 和 `internal/controller` 目录

- **业务逻辑层(BLL)**
  - 实现具体的业务逻辑
  - 调用数据访问层获取数据
  - 实现业务规则和流程
  - 对应 `internal/service` 和 `internal/logic` 目录

- **数据访问层(DAL)**
  - 封装数据库操作
  - 实现数据的CRUD
  - 不包含业务逻辑
  - 对应 `internal/dao` 目录

- **模型层(Model)**
  - 定义数据结构
  - 区分数据模型(DO)和业务模型(BO)
  - 实现模型转换
  - 对应 `internal/model` 目录

### 目录结构

```
.
├── frontend            # 前端代码
├── api                 # 对外接口定义
├── internal           # 内部逻辑代码
│   ├── controller     # 接口实现层
│   ├── model         # 模型定义
│   ├── service       # 业务接口定义
│   ├── logic         # 业务逻辑实现
│   └── dao           # 数据访问封装
├── manifest          # 配置文件
├── resource          # 静态资源
└── hack              # 工具脚本
```

### DAO层设计

- 采用DO(Data Object)作为数据模型
- 通过DAO封装数据库操作细节
- 支持组合扩展DAO能力
- 实现数据操作的可复用性

### 参数规范

- 请求参数采用结构体定义
- 支持参数校验和默认值
- 遵循统一的命名规范
- 实现参数绑定和类型转换

### 模型设计

- DO(Data Object): 数据库实体对象
- BO(Business Object): 业务逻辑对象  
- VO(View Object): 视图对象

通过模型转换实现数据在不同层级间的传递。

### 建议
用最佳实践来写代码
代码结构清晰，不要堆砌冗余
全局考虑问题，不要有太多重复代码

# GoFrame 项目代码规范与架构指导

## 项目架构原则

### 核心设计理念
- **单一职责原则**：每个模块、类、方法只负责一个职责
- **依赖倒置原则**：高层模块不依赖低层模块，都依赖抽象
- **开闭原则**：对扩展开放，对修改关闭
- **接口隔离原则**：使用多个专门的接口，而不是单一的总接口

### 分层架构设计

#### 1. Controller层 (表示层)
**职责**：
- HTTP请求参数绑定和验证
- 调用Logic层处理业务逻辑
- 统一的响应格式化和错误处理
- 权限检查和用户身份验证

**规范**：
```go
// ❌ 错误示例 - 直接调用service
func (c *Controller) GetMetrics(ctx context.Context, req *v1.GetMetricsReq) (res *v1.GetMetricsRes, err error) {
    return service.Dashboard().GetMetrics(ctx, req)
}

// ✅ 正确示例 - 完整的controller实现
func (c *Controller) GetMetrics(ctx context.Context, req *v1.GetMetricsReq) (res *v1.GetMetricsRes, err error) {
    // 1. 参数验证
    if err = g.Validator().Data(req).Run(ctx); err != nil {
        return nil, gerror.WrapCode(gcode.CodeValidationFailed, err, "参数验证失败")
    }
    
    // 2. 权限检查
    if err = middleware.CheckPermission(ctx, "dashboard:metrics:read"); err != nil {
        return nil, err
    }
    
    // 3. 调用Logic层
    return logic.Dashboard().GetMetrics(ctx, req)
}
```

#### 2. Logic层 (业务逻辑层)
**职责**：
- 实现具体的业务逻辑
- 组合调用多个Service
- 业务规则验证和流程控制
- 数据转换和业务模型处理

**规范**：
```go
// ✅ Logic层应该包含业务逻辑
type IDashboardLogic interface {
    GetMetrics(ctx context.Context, req *v1.GetMetricsReq) (*v1.GetMetricsRes, error)
}

type sLogic struct{}

func (s *sLogic) GetMetrics(ctx context.Context, req *v1.GetMetricsReq) (*v1.GetMetricsRes, error) {
    // 1. 业务规则验证
    if err := s.validateMetricsRequest(req); err != nil {
        return nil, err
    }
    
    // 2. 获取用户信息并应用业务规则
    userInfo := consts.GetUserFromCtx(ctx)
    req = s.applyUserBusinessRules(req, userInfo)
    
    // 3. 调用Service层获取数据
    metrics, err := service.Dashboard().GetMetrics(ctx, req)
    if err != nil {
        return nil, err
    }
    
    // 4. 业务数据处理和转换
    return s.processMetricsResponse(metrics, userInfo), nil
}
```

#### 3. Service层 (服务接口层)
**职责**：
- 定义业务服务接口
- 轻量级的数据获取和简单处理
- 调用DAO层进行数据操作
- 缓存策略实现

**规范**：
```go
// ✅ Service层应该轻量化，专注数据获取
type IDashboardService interface {
    GetMetrics(ctx context.Context, req *v1.GetMetricsReq) (*v1.GetMetricsRes, error)
    GetFilterOptions(ctx context.Context) (*v1.GetFilterOptionsRes, error)
}

func (s *sDashboard) GetMetrics(ctx context.Context, req *v1.GetMetricsReq) (*v1.GetMetricsRes, error) {
    // 1. 获取基础数据
    dailyStats, err := s.getDailyStats(ctx, req)
    if err != nil {
        return nil, err
    }
    
    // 2. 简单的数据聚合
    metrics := s.aggregateMetrics(dailyStats)
    
    return &v1.GetMetricsRes{Metrics: metrics}, nil
}
```

#### 4. DAO层 (数据访问层)
**职责**：
- 数据库操作封装
- SQL查询和事务管理
- 数据模型转换(Entity <-> DO)

### 代码组织规范

#### 1. 文件大小控制
- **单个文件不超过500行**
- **单个方法不超过50行**
- **复杂逻辑必须拆分为多个小方法**

```go
// ❌ 错误示例 - 方法过长
func (s *sDashboard) GetMetrics(ctx context.Context, req *v1.GetMetricsReq) {
    // 200行代码...
}

// ✅ 正确示例 - 拆分为多个小方法
func (s *sDashboard) GetMetrics(ctx context.Context, req *v1.GetMetricsReq) (*v1.GetMetricsRes, error) {
    dailyStats := s.getDailyStats(ctx, req)
    metrics := s.calculateMetrics(dailyStats)
    changes := s.calculateChanges(ctx, metrics, req)
    return s.buildMetricsResponse(metrics, changes), nil
}
```

#### 2. 错误处理规范
```go
// ✅ 统一错误处理
func (s *sDashboard) GetMetrics(ctx context.Context, req *v1.GetMetricsReq) (*v1.GetMetricsRes, error) {
    dailyStats, err := s.getDailyStats(ctx, req)
    if err != nil {
        return nil, gerror.WrapCode(gcode.CodeInternalError, err, "获取日统计数据失败")
    }
    // 后续处理...
}
```

#### 3. 参数验证规范
```go
// ✅ API参数验证
type GetMetricsReq struct {
    g.Meta    `path:"/metrics" method:"get" tags:"仪表盘" summary:"获取指标数据"`
    StartDate string `json:"start_date" v:"required|date" dc:"开始日期"`
    EndDate   string `json:"end_date" v:"required|date|gte:StartDate" dc:"结束日期"`
    MediaId   string `json:"media_id" v:"integer" dc:"媒体ID"`
}
```

### 模块重构指导

#### 1. 重构优先级
1. **紧急重构**：超过1000行的文件
2. **高优先级**：Controller层缺少验证和错误处理
3. **中优先级**：Logic层缺失导致的业务逻辑分散
4. **低优先级**：代码风格和命名规范

#### 2. Dashboard模块重构计划
```
当前问题：
- service/dashboard.go 文件过大(2000+行)
- controller层功能不完整
- 缺少logic层实现

重构步骤：
1. 拆分service/dashboard.go为多个文件：
   - dashboard_metrics.go (指标相关)
   - dashboard_trend.go (趋势相关)  
   - dashboard_filter.go (筛选相关)
   - dashboard_stats.go (统计相关)

2. 实现logic/dashboard/dashboard.go
3. 完善controller/dashboard/dashboard.go
4. 添加统一的错误处理和参数验证
```

#### 3. 重构模板

**Logic层模板**：
```go
package dashboard

type ILogic interface {
    GetMetrics(ctx context.Context, req *v1.GetMetricsReq) (*v1.GetMetricsRes, error)
}

type sLogic struct{}

var insLogic = sLogic{}

func Logic() ILogic {
    return &insLogic
}

func (s *sLogic) GetMetrics(ctx context.Context, req *v1.GetMetricsReq) (*v1.GetMetricsRes, error) {
    // 业务逻辑实现
}
```

**Service层模板**：
```go
package service

type IDashboardService interface {
    GetDailyStats(ctx context.Context, req *DailyStatsReq) (*DailyStats, error)
}

type sDashboardService struct{}

func DashboardService() IDashboardService {
    return &sDashboardService{}
}
```

### 代码质量标准

#### 1. 命名规范
- **包名**：小写，简短，有意义
- **接口名**：I前缀，如 `IDashboard`
- **结构体**：大驼峰，如 `DashboardController`
- **方法名**：大驼峰，动词开头，如 `GetMetrics`
- **变量名**：小驼峰，有意义，如 `userInfo`

#### 2. 注释规范
```go
// GetMetrics 获取仪表盘指标数据
// 支持按日期范围、媒体、计划等维度筛选
// 返回包含环比和同比数据的指标集合
func (s *sLogic) GetMetrics(ctx context.Context, req *v1.GetMetricsReq) (*v1.GetMetricsRes, error) {
    // 实现...
}
```

#### 3. 性能优化原则
- **避免N+1查询**：使用JOIN或批量查询
- **合理使用缓存**：热点数据缓存策略
- **数据库查询优化**：添加索引，优化SQL
- **内存使用优化**：避免大对象长时间持有

### 测试规范

#### 1. 单元测试
```go
func TestDashboardLogic_GetMetrics(t *testing.T) {
    // 准备测试数据
    // 执行测试
    // 验证结果
}
```

#### 2. 测试覆盖率要求
- **Logic层**：90%以上
- **Service层**：80%以上  
- **DAO层**：70%以上

### 重构执行指南

#### 1. 逐步重构策略
1. **不破坏现有功能**：确保重构过程中系统可用
2. **先测试后重构**：为现有代码添加测试用例
3. **小步快跑**：每次重构一个小模块
4. **及时验证**：每次重构后进行功能验证

#### 2. 重构检查清单
- [ ] 是否拆分过大的文件
- [ ] 是否完善了Controller层的功能
- [ ] 是否实现了Logic层
- [ ] 是否添加了参数验证
- [ ] 是否统一了错误处理
- [ ] 是否编写了单元测试
- [ ] 是否更新了文档

## 代码Review标准

#### Pull Request要求
1. **代码行数**：单个PR不超过500行
2. **必须包含**：单元测试、文档更新
3. **Review通过标准**：至少2人review通过

使用中文进行代码注释和文档编写。